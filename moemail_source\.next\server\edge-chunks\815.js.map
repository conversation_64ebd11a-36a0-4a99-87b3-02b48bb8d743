{"version": 3, "file": "edge-chunks/815.js", "mappings": "iLA+CO,IAAMA,EAAY,UAEvB,IAAMC,EAASC,CADK,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,EAAAA,EACNC,GAAG,CAAC,aAE/B,GAAIH,EAAQ,OAAOA,EAEnB,IAAMI,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAE1B,OAAOD,GAASE,KAAKC,EACvB,EAAC,oOCxDD,IAAMC,EAAS,CACb,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACD,eCGD,IAAMC,EAA0C,CAC9C,CAACC,EAAAA,EAAKA,CAACC,OAAO,CAAC,CAAE,YACjB,CAACD,EAAAA,EAAKA,CAACE,IAAI,CAAC,CAAE,WACd,CAACF,EAAAA,EAAKA,CAACG,MAAM,CAAC,CAAE,WAChB,CAACH,EAAAA,EAAKA,CAACI,QAAQ,CAAC,CAAE,UACpB,EAEMC,EAAiB,UACrB,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACC,WAAW,CAAChB,GAAG,CAAC,uBAElE,IACkBO,EAAAA,EAAKA,CAACE,IAAI,EAC1BI,IAAgBN,EAAAA,EAAKA,CAACG,MAAM,EAC5BG,IAAgBN,EAAAA,EAAKA,CAACI,QAAQ,CAEvBE,CADP,CAIKN,EAAAA,EAAKA,CAACI,QAAQ,EAGvB,eAAeM,EAAiBC,CAAM,CAAEC,CAAc,EACpD,IAAIC,EAAO,MAAMF,EAAGG,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC,CACxCC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,KAAKA,CAACI,IAAI,CAAEP,EACxB,GAEA,GAAI,CAACC,EAAM,CACT,GAAM,CAACO,EAAQ,CAAG,MAAMT,EAAGU,MAAM,CAACN,EAAAA,KAAKA,EACpCO,MAAM,CAAC,CACNH,KAAMP,EACNW,YAAaxB,CAAiB,CAACa,EACjC,GACCY,SAAS,GACZX,EAAOO,CACT,CAEA,OAAOP,CACT,CAEO,eAAeY,EAAiBd,CAAM,CAAErB,CAAc,CAAEoC,CAAc,EAC3E,MAAMf,EAAGgB,MAAM,CAACC,EAAAA,SAASA,EACtBX,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACtC,MAAM,CAAEA,IAE9B,MAAMqB,EAAGU,MAAM,CAACO,EAAAA,SAASA,EACtBN,MAAM,CAAC,CACNhC,gBACAoC,CACF,EACJ,CAEO,eAAeG,EAAYvC,CAAc,EAC9C,IAAMqB,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,MAJwB,OAAMnB,EAAGG,KAAK,CAACc,SAAS,CAACG,QAAQ,CAAC,CACxDd,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACtC,MAAM,CAAEA,GAC5B0C,KAAM,CAAEnB,MAAM,CAAK,CACrB,GACsB,CAAC,EAAE,CAACA,IAAI,CAACM,IAAI,CAG9B,eAAec,EAAgBC,CAAsB,EAC1D,IAAM5C,EAAS,MAAMD,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE9B,GAAI,CAACC,EAAQ,OAAO,EAEpB,IAAMqB,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMbK,EAAgBC,CALE,MAAMzB,EAAGG,KAAK,CAACc,SAAS,CAACG,QAAQ,CAAC,CACxDd,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACtC,MAAM,CAAEA,GAC5B0C,KAAM,CAAEnB,MAAM,CAAK,CACrB,IAEsCwB,GAAG,CAACC,GAAMA,EAAGzB,IAAI,CAACM,IAAI,EAC5D,MAAOoB,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAACJ,EAAyBD,EAChD,CAEO,GAAM,CACXM,SAAU,KAAEC,CAAG,MAAEC,CAAI,CAAE,MACvB/C,CAAI,QACJgD,CAAM,SACNC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,IAAO,EAClBC,OAAQC,QAAQvC,GAAG,CAACwC,WAAW,CAC/BC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACpB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAAI,CAClCqB,WAAYC,EAAAA,KAAKA,CACjBC,cAAeC,EAAAA,QAAQA,GAEzBC,UAAW,CACTC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC,CACLC,SAAUV,QAAQvC,GAAG,CAACkD,cAAc,CACpCC,aAAcZ,QAAQvC,GAAG,CAACoD,kBAC5B,GACAC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CAClB1C,KAAM,cACN2C,YAAa,CACXC,SAAU,CAAEC,MAAO,MAAOC,KAAM,OAAQC,YAAa,QAAS,EAC9DC,SAAU,CAAEH,MAAO,KAAMC,KAAM,WAAYC,YAAa,OAAQ,CAClE,EACA,MAAME,UAAUN,CAAW,EACzB,GAAI,CAACA,EACH,MAAM,KADU,CACA,aAGlB,GAAM,UAAEC,CAAQ,UAAEI,CAAQ,CAAE,CAAGL,EAE/B,GAAI,CACFO,EAAAA,CAAUA,CAACC,KAAK,CAAC,UAAEP,WAAUI,CAAS,EAExC,CAAE,MAAOI,EAAO,CACd,MAAM,MAAU,UAClB,CAEA,IAAM5D,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEblC,EAAO,MAAMe,EAAGG,KAAK,CAACsC,KAAK,CAACpC,SAAS,CAAC,CAC1CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACkC,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAEA,GAAI,CAACnE,GAKD,CADY,EAJL,IAIW4E,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAACL,EAAoBvE,EAAKuE,QAAQ,EAHrE,MAAM,MAAU,YAQlB,MAAO,CACL,GAAGvE,CAAI,CACPuE,cAAUM,CACZ,CACF,CACF,GACD,CACDC,OAAQ,CACN,MAAM/B,OAAO,MAAE/C,CAAI,CAAE,EACnB,GAAKA,CAAD,CAAMC,EAAE,CAEZ,CAFc,EAEV,CACF,IAAMc,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,GAJqB,CAIjB6C,KAJuBhE,EAAGG,KAAK,CAACc,SAAS,CAACZ,SAAS,CAAC,CACtDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACtC,MAAM,CAAEM,EAAKC,EAAE,CACrC,GAEkB,OAElB,IAAMS,EAAc,MAAMD,IACpBQ,EAAO,MAAMH,EAAiBC,EAAIL,EACxC,OAAMmB,EAAiBd,EAAIf,EAAKC,EAAE,CAAEgB,EAAKhB,EAAE,CAC7C,CAAE,MAAO0E,EAAO,CACdK,QAAQL,KAAK,CAAC,wBAAyBA,EACzC,CACF,CACF,EACAM,UAAW,CACHC,IAAN,MAAU,OAAEC,CAAK,CAAEnF,MAAI,CAAE,IACnBA,IACFmF,EADQ,EACA,CAAGnF,EAAKC,EAAE,CAClBkF,EAAM5D,IAAI,CAAGvB,EAAKuB,IAAI,EAAIvB,EAAKmE,QAAQ,CACvCgB,EAAMhB,QAAQ,CAAGnE,EAAKmE,QAAQ,CAC9BgB,EAAMC,KAAK,CAAGpF,EAAKoF,KAAK,ED/JzB,SAA2B7D,CAAY,EAC5C,IAAM8D,CC8J6CC,CD9JnC/D,CAAI,CAAC,EAAE,CAACgE,WAAW,GAE7BC,EAAaC,MAAMC,IAAI,CAACnE,GAAMoE,MAAM,CACxC,CAACC,EAAKC,IAASD,EAAMC,EAAKC,UAAU,CAAC,GAAI,GACvC5F,EAAO6F,MAAM,CAEXC,EAAkB9F,CAAM,CAACsF,EAAW,CAEpCS,EAAM,CAAC;;2CAE4B,EAAED,EAAgB;;;;;;;;;;;;;QAarD,EAAEX,QAAQ;;;EAGhB,CAAC,CAACa,IAAI,GAGAC,EADU,IAAIC,cACEC,MAAM,CAACJ,GACvBK,EAASC,EAAOb,IAADa,CAAMJ,GAAOK,QAAQ,CAAC,UAE3C,MAAO,CAAC,0BAA0B,EAAEF,EAAAA,CAAQ,EC8HQnB,EAAM5D,IAAI,GAEnD4D,GAET,MAAMrF,QAAQ,SAAEA,CAAO,OAAEqF,CAAK,CAAE,EAC9B,GAAIA,GAASrF,EAAQE,IAAI,CAAE,CACzBF,EAAQE,IAAI,CAACC,EAAE,CAAGkF,EAAMlF,EAAE,CAC1BH,EAAQE,IAAI,CAACuB,IAAI,CAAG4D,EAAM5D,IAAI,CAC9BzB,EAAQE,IAAI,CAACmE,QAAQ,CAAGgB,EAAMhB,QAAQ,CACtCrE,EAAQE,IAAI,CAACoF,KAAK,CAAGD,EAAMC,KAAK,CAEhC,IAAMrE,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACfM,EAAkB,MAAMzB,EAAGG,KAAK,CAACc,SAAS,CAACG,QAAQ,CAAC,CACtDd,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACtC,MAAM,CAAEI,EAAQE,IAAI,CAACC,EAAE,EAC3CmC,KAAM,CAAEnB,MAAM,CAAK,CACrB,GAEA,GAAI,CAACuB,EAAgBuD,MAAM,CAAE,CAC3B,IAAMrF,EAAc,MAAMD,IACpBQ,EAAO,MAAMH,EAAiBC,EAAIL,EACxC,OAAMmB,EAAiBd,EAAIjB,EAAQE,IAAI,CAACC,EAAE,CAAEgB,EAAKhB,EAAE,EACnDuC,EAAkB,CAAC,CACjB9C,OAAQI,EAAQE,IAAI,CAACC,EAAE,CACvB6B,OAAQb,EAAKhB,EAAE,CACfwG,UAAW,IAAIC,KACfzF,KAAMA,CACR,EACF,CAEAnB,EAAQE,IAAI,CAACmB,KAAK,CAAGqB,EAAgBC,GAAG,CAACC,GAAO,OACxCA,EAAGzB,IAAI,CAACM,IAAI,CACpB,EACF,CAEA,OAAOzB,CACT,CACF,EACAA,QAAS,CACP6G,SAAU,KACZ,EACF,GAEO,CAFJ,cAEmBC,EAASzC,CAAgB,CAAEI,CAAgB,EAC/D,IAAMxD,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,GAJiB,CAIb2E,KAJmB9F,EAAGG,GAIZ,EAJiB,CAACsC,KAAK,CAACpC,SAAS,CAAC,CAC9CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACkC,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAGE,MAAM,MAAU,UAGlB,IAAM2C,EAAiB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACxC,GAEpC,CAACvE,EAAK,CAAG,MAAMe,EAAGU,MAAM,CAAC+B,EAAAA,KAAKA,EACjC9B,MAAM,CAAC,UACNyC,EACAI,SAAUuC,CACZ,GACClF,SAAS,GAEZ,OAAO5B,CACT,gFCvOO,IAAMkC,EAAW,IAAM8E,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAACrG,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACqG,EAAE,CAAE,CAAEC,MAAMA,CAAAA,CAAC,GAAE,gCAAHA,oDCE1E,IAAMC,EAAqBhE,QAAQvC,GAAG,CAACuG,kBAAkB,CAUlD,eAAeC,EAA0BC,CAAoB,CAAEC,CAAiB,EACrF,GAAI,CAACH,EACH,MAAM,MAAU,MADO,mCAIzB,IAAMjE,EAAS,IAAIkD,cAAcC,MAAM,CAACc,GAWxC,OAVY,MAAM,IAAII,EAAAA,CAAOA,CAAC,CAAC,GAC5BC,kBAAkB,CAAC,CAAEC,IAAK,OAAQ,GAClCC,UAAU,CAACL,GACXM,MAAM,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,IACbC,WAAW,GACXC,SAAS,CAACC,WACVC,WAAW,CAACX,GACZY,iBAAiB,CAAe,IAAdX,EAAkB,OAAS,GAAGA,EAAU,CAAC,CAAC,EAC5DY,IAAI,CAAChF,EAGV,kECjCO,IAAM9C,EAAQ,CACnBC,QAAS,UACTC,KAAM,OACNC,OAAQ,SACRC,SAAU,UACZ,EAAW,EAIgB,CACzB2H,aAAc,eACdC,eAAgB,iBAChBC,aAAc,eACdC,cAAe,gBACfC,eAAgB,gBAClB,EAIaC,EAA+C,CAC1D,CAACpI,EAAMC,OAAO,CAAC,CAAEoI,OAAO/G,MAAM,CAACgH,GAC/B,CAACtI,EAAME,IAAI,CAAC,CAAE,CACZoI,EAAYP,YAAY,CACxBO,EAAYN,cAAc,CAC1BM,EAAYH,cAAc,CAC3B,CACD,CAACnI,EAAMG,MAAM,CAAC,CAAE,CACdmI,EAAYP,YAAY,CACxBO,EAAYN,cAAc,CAC3B,CACD,CAAChI,EAAMI,QAAQ,CAAC,CAAE,EACpB,EAAW,SAEKmC,EAAcX,CAAiB,CAAEM,CAAsB,EACrE,OAAON,EAAU2G,IAAI,CAAC1H,GAAQuH,CAAgB,CAACvH,EAAK,EAAE2H,SAAStG,GACjE,kVC9BO,IAAMkB,EAAQqF,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvC5I,GAAI6I,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MACNC,UAAU,GACVC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IACrC3H,KAAMuH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXK,MAAOL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASM,MAAM,GAC3BC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,gBAAiB,CAAEC,KAAM,cAAe,GAC/DnE,MAAO0D,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZ3E,SAAU2E,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYM,MAAM,GACjC7E,SAAUuE,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACjB,GACapF,EAAWmF,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CACjC,UACA,CACEnJ,OAAQoJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UACVU,OAAO,GACPC,UAAU,CAAC,IAAMjG,EAAMvD,EAAE,CAAE,CAAEyJ,SAAU,SAAU,GACpDrF,KAAMyE,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQa,KAAK,GAAuBH,OAAO,GACtDI,SAAUd,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYU,OAAO,GAClCK,kBAAmBf,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,qBAAqBU,OAAO,GACpDM,cAAehB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBACpBiB,aAAcjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACnBkB,WAAYV,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cACpBW,WAAYnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,cACjBoB,MAAOpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZqB,SAAUrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YACfsB,cAAetB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACtB,EACA,GAAc,EACZuB,KADY,OACCtB,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CACtBuB,QAAS,CAACC,EAAQX,QAAQ,CAAEW,EAAQV,iBAAiB,CAAC,GAE1D,GAGWW,CAFZ,CAEqB3B,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,QAAS,CACzC5I,GAAI6I,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DuB,QAAS3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGJ,MAAM,GACzC1J,OAAQoJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UAAUW,UAAU,CAAC,IAAMjG,EAAMvD,EAAE,CAAE,CAAEyJ,SAAU,SAAU,GACxEjD,UAAW6C,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAItC,MACxBgE,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbmB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAMJ,SAAS,EAChE,GAEaK,CAFV,CAEqBlC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7C5I,GAAI6I,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D8B,QAASlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACXU,OAAO,GACPC,UAAU,CAAC,IAAMe,EAAOvK,EAAE,CAAE,CAAEyJ,SAAU,SAAU,GACrDuB,YAAanC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBAAgBU,OAAO,GACzC0B,QAASpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC2B,QAASrC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC4B,KAAMtC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXuC,WAAY/B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cAAe,CAAEC,KAAM,cAAe,GACvDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAItC,KAC1B,EAAIoE,GAAW,EACbQ,GADa,QACDV,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAME,OAAO,CAC5D,IAAG,EAEqBnC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7C5I,GAAI6I,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DxJ,OAAQoJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACVU,OAAO,GACPC,UAAU,CAAC,IAAMjG,EAAMvD,EAAE,CAAE,CAAEyJ,SAAU,SAAU,GACpD6B,IAAKzC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GACxBgC,QAASlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGiC,OAAO,EAAC,GACnEhF,UAAW6C,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAItC,MACxBgF,UAAWpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAItC,KAC1B,GAAE,EAEmBmC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvC5I,GAAI6I,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D3H,KAAMuH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1B7H,YAAamH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,eAClBrC,UAAW6C,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAItC,MAC7EgF,UAAWpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAItC,KAC/E,GAAG,EAEsBmC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,YAAa,CAChDnJ,OAAQoJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMjG,EAAMvD,EAAE,CAAE,CAAEyJ,SAAU,SAAU,GACnF5H,OAAQgH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMtI,EAAMlB,EAAE,CAAE,CAAEyJ,SAAU,SAAU,GACnFjD,UAAW6C,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAItC,KAC/E,EAAG,GAAY,EACbiF,GADa,CACT5C,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CAAEuB,QAAS,CAACQ,EAAMpL,MAAM,CAAEoL,EAAMhJ,MAAM,CAAC,GACxD,GAEa8J,CAFT,CAEmB/C,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,WAAY,CAC7C5I,GAAI6I,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DxJ,OAAQoJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMjG,EAAMvD,EAAE,EAC3DsB,KAAMuH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BqC,IAAK/C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GAAGJ,MAAM,GACjC3C,UAAW6C,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAItC,MAC7EgE,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrDiC,QAASlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGiC,OAAO,EAAC,EACrE,EAAG,GAAY,EACbK,GADa,cACKC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,uBAAuBlB,EAAE,CAACC,EAAMvJ,IAAI,CAAEuJ,EAAMpL,MAAM,EAClF,GAEasM,CAFT,CAE+BnD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,qBAAsB,CACnEoD,IAAKnD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOC,UAAU,GAC3B2B,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbmB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,0BAA0BC,EAAE,CAACC,EAAMJ,SAAS,EAClE,GAEawB,CAFT,CAE4BC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACP,EAAS,CAAC,KAAEQ,CAAG,CAAE,GAAM,EAC/DpM,KAAMoM,EAAI5I,EAAO,CACf6I,OAAQ,CAACT,EAAQlM,MAAM,CAAC,CACxB+J,WAAY,CAACjG,EAAMvD,EAAE,CAAC,GAE1B,GAEaqM,CAFT,CAE8BH,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACnK,EAAW,CAAC,CAAEoK,KAAG,CAAE,GAAM,EACnEpM,KAAMoM,EAAI5I,EAAO,CACf6I,OAAQ,CAACrK,EAAUtC,MAAM,CAAC,CAC1B+J,WAAY,CAACjG,EAAMvD,EAAE,CAAC,GAExBgB,KAAMmL,EAAIjL,EAAO,CACfkL,OAAQ,CAACrK,EAAUF,MAAM,CAAC,CAC1B2H,WAAY,CAACtI,EAAMlB,EAAE,CAAC,GAE1B,GAEasM,CAFT,CAE0BJ,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAC3I,EAAO,CAAC,MAAEgJ,CAAI,CAAE,GAAM,EAC5DxK,UAAWwK,EAAKxK,GAChB4J,QAASY,EAAKZ,GAChB,GAEaa,CAFT,CAE0BN,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAChL,EAAO,CAAC,MAAEqL,CAAI,CAAE,GAAM,EAC5DxK,UAAWwK,EAAKxK,GAClB,IAAI,sFC5IG,SAAS0K,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,CAEO,eAAe5F,EAAaxC,CAAgB,EACjD,IAAMuI,EAAU,IAAI1G,YACd2G,EAAO5J,QAAQvC,GAAG,CAACwC,WAAW,EAAI,GAClC4J,EAAOF,EAAQzG,MAAM,CAAC9B,EAAWwI,GAEvC,OAAOE,KAAKC,OAAOC,YAAY,IAAI,IAAIC,WAAWC,MAD/BpE,OAAOqE,MAAM,CAACC,MAAM,CAAC,UAAWP,KAErD,CAEO,eAAepI,EAAgBL,CAAgB,CAAEuC,CAAsB,EAE5E,OADa,MAAMC,EAAaxC,KAChBuC,CAClB,8DChBO,IAAMrC,EAAa+I,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACjCrJ,SAAUqJ,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,WACPC,GAAG,CAAC,GAAI,gBACRC,KAAK,CAAC,mBAAoB,uBAC1BC,MAAM,CAACC,GAAO,CAACA,EAAIjF,QAAQ,CAAC,KAAM,cACrCrE,SAAUiJ,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,eACZ,GAAE", "sources": ["webpack://_N_E/./app/lib/apiKey.ts", "webpack://_N_E/./app/lib/avatar.ts", "webpack://_N_E/./app/lib/auth.ts", "webpack://_N_E/./app/lib/db.ts", "webpack://_N_E/./app/lib/jwt.ts", "webpack://_N_E/./app/lib/permissions.ts", "webpack://_N_E/./app/lib/schema.ts", "webpack://_N_E/./app/lib/utils.ts", "webpack://_N_E/./app/lib/validation.ts"], "sourcesContent": ["import { createDb } from \"./db\"\r\nimport { api<PERSON><PERSON>s } from \"./schema\"\r\nimport { eq, and, gt } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport type { User } from \"next-auth\"\r\nimport { auth } from \"./auth\"\r\nimport { headers } from \"next/headers\"\r\n\r\nasync function getUserByApiKey(key: string): Promise<User | null> {\r\n  const db = createDb()\r\n  const apiKey = await db.query.apiKeys.findFirst({\r\n    where: and(\r\n      eq(apiKeys.key, key),\r\n      eq(apiKeys.enabled, true),\r\n      gt(apiKeys.expiresAt, new Date())\r\n    ),\r\n    with: {\r\n      user: true\r\n    }\r\n  })\r\n\r\n  if (!apiKey) return null\r\n\r\n  return apiKey.user\r\n}\r\n\r\nexport async function handleApiKeyAuth(apiKey: string, pathname: string) {\r\n  if (!pathname.startsWith('/api/emails') && !pathname.startsWith('/api/config')) {\r\n    return NextResponse.json(\r\n      { error: \"无权限查看\" },\r\n      { status: 403 }\r\n    )\r\n  }\r\n\r\n  const user = await getUserByApiKey(apiKey)\r\n  if (!user?.id) {\r\n    return NextResponse.json(\r\n      { error: \"无效的 API Key\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  const response = NextResponse.next()\r\n  response.headers.set(\"X-User-Id\", user.id)\r\n  return response\r\n}\r\n\r\nexport const getUserId = async () => {\r\n  const headersList = await headers()\r\n  const userId = headersList.get(\"X-User-Id\")\r\n  \r\n  if (userId) return userId\r\n\r\n  const session = await auth()\r\n\r\n  return session?.user.id\r\n}\r\n", "const COLORS = [\r\n  '#2196F3', // 蓝色\r\n  '#009688', // 青色\r\n  '#9C27B0', // 紫色\r\n  '#F44336', // 红色\r\n  '#673AB7', // 深紫色\r\n  '#3F51B5', // 靛蓝\r\n  '#4CAF50', // 绿色\r\n  '#FF5722', // 深橙\r\n  '#795548', // 棕色\r\n  '#607D8B', // 蓝灰\r\n];\r\n\r\nexport function generateAvatarUrl(name: string): string {\r\n  const initial = name[0].toUpperCase();\r\n  \r\n  const colorIndex = Array.from(name).reduce(\r\n    (acc, char) => acc + char.charCodeAt(0), 0\r\n  ) % COLORS.length;\r\n  \r\n  const backgroundColor = COLORS[colorIndex];\r\n  \r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\" width=\"100\" height=\"100\">\r\n      <rect width=\"100\" height=\"100\" fill=\"${backgroundColor}\"/>\r\n      <text \r\n        x=\"50%\" \r\n        y=\"50%\" \r\n        fill=\"white\" \r\n        font-family=\"system-ui, -apple-system, sans-serif\" \r\n        font-size=\"45\" \r\n        font-weight=\"500\"\r\n        text-anchor=\"middle\"\r\n        alignment-baseline=\"central\"\r\n        dominant-baseline=\"central\"\r\n        style=\"text-transform: uppercase\"\r\n      >\r\n        ${initial}\r\n      </text>\r\n    </svg>\r\n  `.trim();\r\n\r\n  const encoder = new TextEncoder();\r\n  const bytes = encoder.encode(svg);\r\n  const base64 = Buffer.from(bytes).toString('base64');\r\n  \r\n  return `data:image/svg+xml;base64,${base64}`;\r\n} ", "import NextAuth from \"next-auth\"\r\nimport GitHub from \"next-auth/providers/github\"\r\nimport { Dr<PERSON>zleAdapter } from \"@auth/drizzle-adapter\"\r\nimport { createDb, Db } from \"./db\"\r\nimport { accounts, users, roles, userRoles } from \"./schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { Permission, hasPermission, ROLES, Role } from \"./permissions\"\r\nimport CredentialsProvider from \"next-auth/providers/credentials\"\r\nimport { hashPassword, comparePassword } from \"@/lib/utils\"\r\nimport { authSchema } from \"@/lib/validation\"\r\nimport { generateAvatarUrl } from \"./avatar\"\r\nimport { getUserId } from \"./apiKey\"\r\n\r\nconst ROLE_DESCRIPTIONS: Record<Role, string> = {\r\n  [ROLES.EMPEROR]: \"皇帝（网站所有者）\",\r\n  [ROLES.DUKE]: \"公爵（超级用户）\",\r\n  [ROLES.KNIGHT]: \"骑士（高级用户）\",\r\n  [ROLES.CIVILIAN]: \"平民（普通用户）\",\r\n}\r\n\r\nconst getDefaultRole = async (): Promise<Role> => {\r\n  const defaultRole = await getRequestContext().env.SITE_CONFIG.get(\"DEFAULT_ROLE\")\r\n\r\n  if (\r\n    defaultRole === ROLES.DUKE ||\r\n    defaultRole === ROLES.KNIGHT ||\r\n    defaultRole === ROLES.CIVILIAN\r\n  ) {\r\n    return defaultRole as Role\r\n  }\r\n  \r\n  return ROLES.CIVILIAN\r\n}\r\n\r\nasync function findOrCreateRole(db: Db, roleName: Role) {\r\n  let role = await db.query.roles.findFirst({\r\n    where: eq(roles.name, roleName),\r\n  })\r\n\r\n  if (!role) {\r\n    const [newRole] = await db.insert(roles)\r\n      .values({\r\n        name: roleName,\r\n        description: ROLE_DESCRIPTIONS[roleName],\r\n      })\r\n      .returning()\r\n    role = newRole\r\n  }\r\n\r\n  return role\r\n}\r\n\r\nexport async function assignRoleToUser(db: Db, userId: string, roleId: string) {\r\n  await db.delete(userRoles)\r\n    .where(eq(userRoles.userId, userId))\r\n\r\n  await db.insert(userRoles)\r\n    .values({\r\n      userId,\r\n      roleId,\r\n    })\r\n}\r\n\r\nexport async function getUserRole(userId: string) {\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n  return userRoleRecords[0].role.name\r\n}\r\n\r\nexport async function checkPermission(permission: Permission) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) return false\r\n\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n\r\n  const userRoleNames = userRoleRecords.map(ur => ur.role.name)\r\n  return hasPermission(userRoleNames as Role[], permission)\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  auth,\r\n  signIn,\r\n  signOut\r\n} = NextAuth(() => ({\r\n  secret: process.env.AUTH_SECRET,\r\n  adapter: DrizzleAdapter(createDb(), {\r\n    usersTable: users,\r\n    accountsTable: accounts,\r\n  }),\r\n  providers: [\r\n    GitHub({\r\n      clientId: process.env.AUTH_GITHUB_ID,\r\n      clientSecret: process.env.AUTH_GITHUB_SECRET,\r\n    }),\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        username: { label: \"用户名\", type: \"text\", placeholder: \"请输入用户名\" },\r\n        password: { label: \"密码\", type: \"password\", placeholder: \"请输入密码\" },\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials) {\r\n          throw new Error(\"请输入用户名和密码\")\r\n        }\r\n\r\n        const { username, password } = credentials\r\n\r\n        try {\r\n          authSchema.parse({ username, password })\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        } catch (error) {\r\n          throw new Error(\"输入格式不正确\")\r\n        }\r\n\r\n        const db = createDb()\r\n\r\n        const user = await db.query.users.findFirst({\r\n          where: eq(users.username, username as string),\r\n        })\r\n\r\n        if (!user) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        const isValid = await comparePassword(password as string, user.password as string)\r\n        if (!isValid) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        return {\r\n          ...user,\r\n          password: undefined,\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  events: {\r\n    async signIn({ user }) {\r\n      if (!user.id) return\r\n\r\n      try {\r\n        const db = createDb()\r\n        const existingRole = await db.query.userRoles.findFirst({\r\n          where: eq(userRoles.userId, user.id),\r\n        })\r\n\r\n        if (existingRole) return\r\n\r\n        const defaultRole = await getDefaultRole()\r\n        const role = await findOrCreateRole(db, defaultRole)\r\n        await assignRoleToUser(db, user.id, role.id)\r\n      } catch (error) {\r\n        console.error('Error assigning role:', error)\r\n      }\r\n    },\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id\r\n        token.name = user.name || user.username\r\n        token.username = user.username\r\n        token.image = user.image || generateAvatarUrl(token.name as string)\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token && session.user) {\r\n        session.user.id = token.id as string\r\n        session.user.name = token.name as string\r\n        session.user.username = token.username as string\r\n        session.user.image = token.image as string\r\n\r\n        const db = createDb()\r\n        let userRoleRecords = await db.query.userRoles.findMany({\r\n          where: eq(userRoles.userId, session.user.id),\r\n          with: { role: true },\r\n        })\r\n  \r\n        if (!userRoleRecords.length) {\r\n          const defaultRole = await getDefaultRole()\r\n          const role = await findOrCreateRole(db, defaultRole)\r\n          await assignRoleToUser(db, session.user.id, role.id)\r\n          userRoleRecords = [{\r\n            userId: session.user.id,\r\n            roleId: role.id,\r\n            createdAt: new Date(),\r\n            role: role\r\n          }]\r\n        }\r\n  \r\n        session.user.roles = userRoleRecords.map(ur => ({\r\n          name: ur.role.name,\r\n        }))\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: \"jwt\",\r\n  },\r\n}))\r\n\r\nexport async function register(username: string, password: string) {\r\n  const db = createDb()\r\n  \r\n  const existing = await db.query.users.findFirst({\r\n    where: eq(users.username, username)\r\n  })\r\n\r\n  if (existing) {\r\n    throw new Error(\"用户名已存在\")\r\n  }\r\n\r\n  const hashedPassword = await hashPassword(password)\r\n  \r\n  const [user] = await db.insert(users)\r\n    .values({\r\n      username,\r\n      password: hashedPassword,\r\n    })\r\n    .returning()\r\n\r\n  return user\r\n}\r\n", "import { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { drizzle } from \"drizzle-orm/d1\"\r\nimport * as schema from \"./schema\"\r\n\r\nexport const createDb = () => drizzle(getRequestContext().env.DB, { schema })\r\n\r\nexport type Db = ReturnType<typeof createDb>\r\n", "import { SignJWT, jwtVerify } from 'jose';\r\nimport { nanoid } from 'nanoid';\r\nimport { createDb } from './db';\r\nimport { revoked_credentials } from './schema';\r\nimport { eq } from 'drizzle-orm';\r\n\r\nconst JWT_ADDRESS_SECRET = process.env.JWT_ADDRESS_SECRET;\r\nconst ISSUER = 'moemail';\r\n\r\n/**\r\n * Generates a JWT (Address Credential) for a given email address.\r\n *\r\n * @param emailAddress The email address to be the subject of the JWT.\r\n * @param expiresIn The lifetime of the token in seconds. If 0, it creates a \"permanent\" token (expires in 100 years).\r\n * @returns A signed JWT string.\r\n */\r\nexport async function generateAddressCredential(emailAddress: string, expiresIn: number): Promise<string> {\r\n  if (!JWT_ADDRESS_SECRET) {\r\n    throw new Error('JWT_ADDRESS_SECRET is not set in .env');\r\n  }\r\n\r\n  const secret = new TextEncoder().encode(JWT_ADDRESS_SECRET);\r\n  const jwt = await new SignJWT({})\r\n    .setProtectedHeader({ alg: 'HS256' })\r\n    .setSubject(emailAddress)\r\n    .setJti(nanoid())\r\n    .setIssuedAt()\r\n    .setIssuer(ISSUER)\r\n    .setAudience(emailAddress)\r\n    .setExpirationTime(expiresIn === 0 ? '365d' : `${expiresIn}s`)\r\n    .sign(secret);\r\n\r\n  return jwt;\r\n}\r\n\r\nexport async function verifyAddressCredential(token: string) {\r\n  if (!JWT_ADDRESS_SECRET) {\r\n    throw new Error('JWT_ADDRESS_SECRET is not set in .env');\r\n  }\r\n\r\n  const secret = new TextEncoder().encode(JWT_ADDRESS_SECRET);\r\n\r\n  try {\r\n    const { payload } = await jwtVerify(token, secret, {\r\n      issuer: ISSUER,\r\n    });\r\n\r\n    // Check for revocation\r\n    const db = createDb();\r\n    const isRevoked = await db.query.revoked_credentials.findFirst({\r\n      where: eq(revoked_credentials.jti, payload.jti!),\r\n    });\r\n\r\n    if (isRevoked) {\r\n      return null;\r\n    }\r\n\r\n    return payload;\r\n  } catch (error) {\r\n    // This will catch expired tokens, invalid signatures, etc.\r\n    return null;\r\n  }\r\n} ", "export const ROLES = {\r\n  EMPEROR: 'emperor',\r\n  <PERSON>U<PERSON>: 'duke',\r\n  <PERSON><PERSON><PERSON><PERSON>: 'knight',\r\n  CIVILIAN: 'civilian',\r\n} as const;\r\n\r\nexport type Role = typeof ROLES[keyof typeof ROLES];\r\n\r\nexport const PERMISSIONS = {\r\n  MANAGE_EMAIL: 'manage_email',\r\n  <PERSON><PERSON>GE_WEBHOOK: 'manage_webhook',\r\n  PROMOTE_USER: 'promote_user',\r\n  MANAGE_CONFIG: 'manage_config',\r\n  MANAGE_API_KEY: 'manage_api_key',\r\n} as const;\r\n\r\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\r\n\r\nexport const ROLE_PERMISSIONS: Record<Role, Permission[]> = {\r\n  [ROLES.EMPEROR]: Object.values(PERMISSIONS),\r\n  [ROLES.DUKE]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_API_KEY,\r\n  ],\r\n  [ROLES.KNIGHT]: [\r\n    PERMISSIONS.<PERSON>NAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n  ],\r\n  [ROLES.CIVILIAN]: [],\r\n} as const;\r\n\r\nexport function hasPermission(userRoles: Role[], permission: Permission): boolean {\r\n  return userRoles.some(role => ROLE_PERMISSIONS[role]?.includes(permission));\r\n} ", "import { integer, sqliteTable, text, primaryKey, uniqueIndex, index } from \"drizzle-orm/sqlite-core\"\r\nimport type { AdapterAccountType } from \"next-auth/adapters\"\r\nimport { relations } from 'drizzle-orm';\r\n\r\n// https://authjs.dev/getting-started/adapters/drizzle\r\nexport const users = sqliteTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: integer(\"emailVerified\", { mode: \"timestamp_ms\" }),\r\n  image: text(\"image\"),\r\n  username: text(\"username\").unique(),\r\n  password: text(\"password\"),\r\n})\r\nexport const accounts = sqliteTable(\r\n  \"account\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => users.id, { onDelete: \"cascade\" }),\r\n    type: text(\"type\").$type<AdapterAccountType>().notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n  },\r\n  (account) => ({\r\n    compoundKey: primaryKey({\r\n      columns: [account.provider, account.providerAccountId],\r\n    }),\r\n  })\r\n)\r\n\r\nexport const emails = sqliteTable(\"email\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  address: text(\"address\").notNull().unique(),\r\n  userId: text(\"userId\").references(() => users.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp_ms\" }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"email_expires_at_idx\").on(table.expiresAt),\r\n}))\r\n\r\nexport const messages = sqliteTable(\"message\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  emailId: text(\"emailId\")\r\n    .notNull()\r\n    .references(() => emails.id, { onDelete: \"cascade\" }),\r\n  fromAddress: text(\"from_address\").notNull(),\r\n  subject: text(\"subject\").notNull(),\r\n  content: text(\"content\").notNull(),\r\n  html: text(\"html\"),\r\n  receivedAt: integer(\"received_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  emailIdIdx: index(\"message_email_id_idx\").on(table.emailId),\r\n}))\r\n\r\nexport const webhooks = sqliteTable('webhook', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id')\r\n    .notNull()\r\n    .references(() => users.id, { onDelete: \"cascade\" }),\r\n  url: text('url').notNull(),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n})\r\n\r\nexport const roles = sqliteTable(\"role\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\").notNull(),\r\n  description: text(\"description\"),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n});\r\n\r\nexport const userRoles = sqliteTable(\"user_role\", {\r\n  userId: text(\"user_id\").notNull().references(() => users.id, { onDelete: \"cascade\" }),\r\n  roleId: text(\"role_id\").notNull().references(() => roles.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  pk: primaryKey({ columns: [table.userId, table.roleId] }),\r\n}));\r\n\r\nexport const apiKeys = sqliteTable('api_keys', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id),\r\n  name: text('name').notNull(),\r\n  key: text('key').notNull().unique(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n}, (table) => ({\r\n  nameUserIdUnique: uniqueIndex('name_user_id_unique').on(table.name, table.userId)\r\n}));\r\n\r\nexport const revoked_credentials = sqliteTable('revoked_credential', {\r\n  jti: text('jti').primaryKey(),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp_ms' }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"revoked_expires_at_idx\").on(table.expiresAt),\r\n}));\r\n\r\nexport const apiKeysRelations = relations(apiKeys, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [apiKeys.userId],\r\n    references: [users.id],\r\n  }),\r\n}));\r\n\r\nexport const userRolesRelations = relations(userRoles, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userRoles.userId],\r\n    references: [users.id],\r\n  }),\r\n  role: one(roles, {\r\n    fields: [userRoles.roleId],\r\n    references: [roles.id],\r\n  }),\r\n}));\r\n\r\nexport const usersRelations = relations(users, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n  apiKeys: many(apiKeys),\r\n}));\r\n\r\nexport const rolesRelations = relations(roles, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n}));", "import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport async function hashPassword(password: string): Promise<string> {\r\n  const encoder = new TextEncoder()\r\n  const salt = process.env.AUTH_SECRET || ''\r\n  const data = encoder.encode(password + salt)\r\n  const hash = await crypto.subtle.digest('SHA-256', data)\r\n  return btoa(String.fromCharCode(...new Uint8Array(hash)))\r\n}\r\n\r\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\r\n  const hash = await hashPassword(password)\r\n  return hash === hashedPassword\r\n}", "import { z } from \"zod\"\r\n\r\nexport const authSchema = z.object({\r\n  username: z.string()\r\n    .min(1, \"用户名不能为空\")\r\n    .max(20, \"用户名不能超过20个字符\")\r\n    .regex(/^[a-zA-Z0-9_-]+$/, \"用户名只能包含字母、数字、下划线和横杠\")\r\n    .refine(val => !val.includes('@'), \"用户名不能是邮箱格式\"),\r\n  password: z.string()\r\n    .min(8, \"密码长度必须大于等于8位\")\r\n})\r\n\r\nexport type AuthSchema = z.infer<typeof authSchema>"], "names": ["getUserId", "userId", "headersList", "headers", "get", "session", "auth", "user", "id", "COLORS", "ROLE_DESCRIPTIONS", "ROLES", "EMPEROR", "DUKE", "KNIGHT", "CIVILIAN", "getDefaultRole", "defaultRole", "getRequestContext", "env", "SITE_CONFIG", "findOrCreateRole", "db", "<PERSON><PERSON><PERSON>", "role", "query", "roles", "<PERSON><PERSON><PERSON><PERSON>", "where", "eq", "name", "newRole", "insert", "values", "description", "returning", "assignRoleToUser", "roleId", "delete", "userRoles", "getUserRole", "createDb", "find<PERSON>any", "with", "checkPermission", "permission", "userRoleNames", "userRoleRecords", "map", "ur", "hasPermission", "handlers", "GET", "POST", "signIn", "signOut", "NextAuth", "secret", "process", "AUTH_SECRET", "adapter", "DrizzleAdapter", "usersTable", "users", "accountsTable", "accounts", "providers", "GitHub", "clientId", "AUTH_GITHUB_ID", "clientSecret", "AUTH_GITHUB_SECRET", "CredentialsProvider", "credentials", "username", "label", "type", "placeholder", "password", "authorize", "authSchema", "parse", "error", "comparePassword", "undefined", "events", "existingRole", "console", "callbacks", "jwt", "token", "image", "initial", "generateAvatarUrl", "toUpperCase", "colorIndex", "Array", "from", "reduce", "acc", "char", "charCodeAt", "length", "backgroundColor", "svg", "trim", "bytes", "TextEncoder", "encode", "base64", "<PERSON><PERSON><PERSON>", "toString", "createdAt", "Date", "strategy", "register", "existing", "hashedPassword", "hashPassword", "drizzle", "DB", "schema", "JWT_ADDRESS_SECRET", "generateAddressCredential", "emailAddress", "expiresIn", "SignJWT", "setProtectedHeader", "alg", "setSubject", "<PERSON><PERSON><PERSON>", "nanoid", "setIssuedAt", "<PERSON><PERSON><PERSON><PERSON>", "ISSUER", "setAudience", "setExpirationTime", "sign", "MANAGE_EMAIL", "MANAGE_WEBHOOK", "PROMOTE_USER", "MANAGE_CONFIG", "MANAGE_API_KEY", "ROLE_PERMISSIONS", "Object", "PERMISSIONS", "some", "includes", "sqliteTable", "text", "<PERSON><PERSON><PERSON>", "$defaultFn", "crypto", "randomUUID", "email", "unique", "emailVerified", "integer", "mode", "notNull", "references", "onDelete", "$type", "provider", "providerAccountId", "refresh_token", "access_token", "expires_at", "token_type", "scope", "id_token", "session_state", "<PERSON><PERSON><PERSON>", "columns", "account", "emails", "address", "expiresAt", "expiresAtIdx", "index", "on", "table", "messages", "emailId", "fromAddress", "subject", "content", "html", "receivedAt", "emailIdIdx", "url", "enabled", "default", "updatedAt", "pk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "nameUserIdUnique", "uniqueIndex", "revoked_credentials", "jti", "apiKeysRelations", "relations", "one", "fields", "userRolesRelations", "usersRelations", "many", "rolesRelations", "cn", "inputs", "twMerge", "clsx", "encoder", "salt", "data", "btoa", "String", "fromCharCode", "Uint8Array", "hash", "subtle", "digest", "z", "min", "max", "regex", "refine", "val"], "sourceRoot": "", "ignoreList": []}