import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import request from 'supertest';
import dotenv from 'dotenv';
import path from 'path';
import { createDb } from '@/lib/db';
import { users, apiKeys, emails } from '@/lib/schema';
import { eq } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// Load environment variables from .env.local
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

const API_URL = 'http://localhost:3000';
const db = createDb();

describe('API Integration Tests for Credentials', () => {
  let createdAddress: string | null = null;
  let credentialToken: string | null = null;
  let testUser: any = null;
  let testApiKey: string | null = null;

  beforeAll(async () => {
    // 1. Create a test user
    const testEmail = `test-user-${nanoid(8)}@example.com`;
    [testUser] = await db.insert(users).values({ email: testEmail, name: 'Test User' }).returning();

    // 2. Create a non-expiring API key for the test user
    testApiKey = `test-key-${nanoid(16)}`;
    await db.insert(apiKeys).values({
      name: 'E2E Test Key',
      userId: testUser.id,
      key: testApiKey,
      expiresAt: new Date('9999-12-31T23:59:59Z'), // Far future expiry
    });
  });

  afterAll(async () => {
    // 3. Clean up the database
    if (testUser) {
      // Cascade delete should handle apiKeys and emails associated with the user
      await db.delete(users).where(eq(users.id, testUser.id));
    }
    // Also clean any created emails just in case
    if (createdAddress) {
        await db.delete(emails).where(eq(emails.address, createdAddress));
    }
  });

  it('should generate a new email address and credential', async () => {
    const response = await request(API_URL)
      .post('/api/emails/generate')
      .set('X-API-Key', testApiKey!)
      .send({ name: 'integration-test', expiryTime: 3600000, domain: 'moemail.app' })
      .expect(200);

    expect(response.body.email).toBeDefined();
    expect(response.body.credential).toBeDefined();

    createdAddress = response.body.email;
    credentialToken = response.body.credential;
  });

  it('should fail to access a protected route with no credentials', async () => {
    expect(createdAddress).toBeDefined();
    await request(API_URL)
      .get(`/api/emails/${createdAddress}/messages`)
      .set('X-API-Key', testApiKey!) // Still need API key for user context if middleware doesn't run first
      .expect(401);
  });

  it('should access a protected route with a valid credential', async () => {
    expect(createdAddress).toBeDefined();
    expect(credentialToken).toBeDefined();

    await request(API_URL)
      .get(`/api/emails/${createdAddress}/messages`)
      .set('Authorization', `Bearer ${credentialToken}`)
      .expect(200);
  });

  it('should fail to access a protected route with an invalid credential', async () => {
    expect(createdAddress).toBeDefined();

    await request(API_URL)
      .get(`/api/emails/${createdAddress}/messages`)
      .set('Authorization', 'Bearer invalidtoken')
      .expect(401);
  });

  it('should fail to access a route for a different address with a valid credential', async () => {
    expect(credentialToken).toBeDefined();
    const anotherAddress = '<EMAIL>';

    await request(API_URL)
      .get(`/api/emails/${anotherAddress}/messages`)
      .set('Authorization', `Bearer ${credentialToken}`)
      .expect(403); // Forbidden
  });
}); 