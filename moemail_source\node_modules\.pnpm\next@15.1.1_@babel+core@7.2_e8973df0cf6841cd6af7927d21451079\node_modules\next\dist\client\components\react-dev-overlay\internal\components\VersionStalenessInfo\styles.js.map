{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/styles.ts"], "sourcesContent": ["import { noop as css } from '../../helpers/noop-template'\n\nconst styles = css`\n  .nextjs-container-build-error-version-status {\n    flex: 1;\n    text-align: right;\n    font-size: var(--size-font-small);\n  }\n  .nextjs-container-build-error-version-status span {\n    display: inline-block;\n    width: 10px;\n    height: 10px;\n    border-radius: 5px;\n    background: var(--color-ansi-bright-black);\n  }\n  .nextjs-container-build-error-version-status span.fresh {\n    background: var(--color-ansi-green);\n  }\n  .nextjs-container-build-error-version-status span.stale {\n    background: var(--color-ansi-yellow);\n  }\n  .nextjs-container-build-error-version-status span.outdated {\n    background: var(--color-ansi-red);\n  }\n`\n\nexport { styles }\n"], "names": ["styles", "css"], "mappings": ";;;;+BA0BSA;;;eAAAA;;;;8BA1BmB;;;;;;;;;;AAE5B,MAAMA,aAASC,kBAAG"}