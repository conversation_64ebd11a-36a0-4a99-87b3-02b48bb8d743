{"version": 3, "file": "app/api/emails/generate/route.js", "mappings": "qFAAA,6DCAA,mHIAA,ySHKO,IAAMA,EAAiC,CAC5C,CAAEC,MAAO,MAAOC,MAAO,IAAe,EACtC,CAD8B,KAAK,CAC1B,OAAQA,MAAO,KAAoB,EAAb,CAC7BD,IADkC,EAC3B,GADgC,EAC1BC,MAAO,MAAwB,CAAjB,CAC7B,CAAED,GADgC,GACzB,EAD8B,GACxBC,EAD6B,IACtB,CAAE,EACzB,6DCGM,IAAMC,EAAU,OAAM,eAEPC,EAAKC,CAAgB,EACzC,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACbC,EAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGD,GAAG,CAE7BE,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GACxBC,EAAW,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAACH,GAEnC,GAAI,CACF,GAAIE,IAAaE,EAAAA,EAAKA,CAACC,OAAO,CAAE,CAC9B,IAAMC,EAAY,MAAMR,EAAIS,WAAW,CAACC,GAAG,CAAC,eAAiBC,EAAAA,CAAYA,CAACC,iBAAiB,CAACC,QAAQ,GAC9FC,EAAoB,MAAMhB,EAC7BiB,MAAM,CAAC,CAAEC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAW,CAAC,QAAQ,CAAC,GACrCC,IAAI,CAACC,EAAAA,MAAMA,EACXC,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,MAAMA,CAACjB,MAAM,CAAEA,GAClBqB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACK,SAAS,CAAE,IAAIC,QAI/B,GAAIC,OAAOZ,CAAiB,CAAC,EAAE,CAACE,KAAK,GAAKU,OAAOlB,GAC/C,OAAOmB,EADoD,EACxCA,CAACC,IAAI,CACtB,CAAEC,MAAO,CAAC,aAAa,EAAErB,EAAU,CAAC,CAAC,EACrC,CAAEsB,OAAQ,GAAI,EAGpB,CAEA,GAAM,CAAEC,MAAI,CAAEC,YAAU,QAAEC,CAAM,CAAE,CAAG,MAAMpC,EAAQ+B,IAAI,GAMvD,GAAI,CAACpC,EAAe0C,IAAI,CAACC,GAAUA,EAAOzC,EAAvBF,GAA4B,GAAKwC,GAClD,OAAOL,EAAAA,CADwD,CAC5CA,CAACC,IAAI,CACtB,CAAEC,MAAO,SAAU,EACnB,CAAEC,OAAQ,GAAI,GAIlB,IAAMM,EAAe,MAAMpC,EAAIS,WAAW,CAACC,GAAG,CAAC,iBACzC2B,EAAUD,EAAeA,EAAaE,KAAK,CAAC,KAAO,CAAC,cAAc,CAExE,GAAI,CAACD,GAAW,CAACA,EAAQE,QAAQ,CAACN,GAChC,MADyC,CAClCN,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,OAAQ,EACjB,CAAEC,OAAQ,GAAI,GAIlB,IAAMU,EAAU,GAAGT,GAAQU,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,CAAC,GAAG,CAAC,EAAER,EAAAA,CAAQ,CAKhD,GAJsB,CAIlBS,KAJwB5C,EAAG6C,KAAK,CAACxB,EAIlB,IAJwB,CAACyB,SAAS,CAAC,CACpDxB,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACL,CAAAA,EAAAA,EAAAA,EAAAA,CAAG,CAAC,MAAM,EAAEE,EAAAA,MAAMA,CAACqB,OAAO,CAAC,CAAC,CAAC,CAAEA,EAAQK,WAAW,GAC9D,GAGE,OAAOlB,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,WAAY,EACrB,CAAEC,OAAQ,GAAI,GAIlB,IAAMgB,EAAM,IAAIrB,KACVsB,MACEtB,KADuB,IAC3B,EAAS,2BACAqB,EAAIE,CAAb,IAAIvB,EAAgB,GAAKO,GASvBiB,EAAS,MAAMnD,EAAGoD,MAAM,CAAC/B,EAAAA,MAAMA,EAClCgC,MAAM,CARqC,SAC5CX,EACAY,UAAWN,EACXtB,UAAWuB,EACX7C,OAAQA,CACV,GAIGmD,SAAS,CAAC,CAAEC,GAAInC,EAAAA,MAAMA,CAACmC,EAAE,CAAEd,QAASrB,EAAAA,MAAMA,CAACqB,OAAO,GAE/Ce,EAAa,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAyBA,CAChDP,CAAM,CAAC,EAAE,CAACT,OAAO,CACF,IAAfR,EAAmB,EAAIA,EAAa,KAGtC,OAAOL,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvB0B,GAAIL,CAAM,CAAC,EAAE,CAACK,EAAE,CAChBG,MAAOR,CAAM,CAAC,EAAE,CAACT,OAAO,YACxBe,CACF,EACF,CAAE,MAAO1B,EAAO,CAEd,OADA6B,QAAQ7B,KAAK,CAAC,4BAA6BA,GACpCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,QAAS,EAClB,CAAEC,OAAQ,GAAI,EAElB,CACF,CCxGA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,kCACA,gCACA,iBACA,0CACA,CAAK,CACL,iGACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,2EACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,kCACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,uIAiBnxJ,CAAC,CAAC,EAAC,oDCvBI,IAAMnB,EAAe,CAC1BC,kBAAmB,GACnB+C,cAAe,GACjB,EAAU,ECHoB,CAC5BC,YAAa,EACbC,QAAS,IACTC,YAAa,IACbC,OAAQ,CACNC,YAAa,aACf,CACF,EAAU", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/types/email.ts", "webpack://_N_E/./app/api/emails/generate/route.ts", "webpack://_N_E/./app/api/emails/generate/route.ts?f3e8", "webpack://_N_E/?50f8", "webpack://_N_E/./app/config/email.ts", "webpack://_N_E/./app/config/webhook.ts", "webpack://_N_E/./app/config/index.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "export interface ExpiryOption {\r\n  label: string\r\n  value: number\r\n}\r\n\r\nexport const EXPIRY_OPTIONS: ExpiryOption[] = [\r\n  { label: '1小时', value: 1000 * 60 * 60 },\r\n  { label: '24小时', value: 1000 * 60 * 60 * 24 },\r\n  { label: '3天', value: 1000 * 60 * 60 * 24 * 3 },\r\n  { label: '永久', value: 0 }\r\n]\r\n", "import { NextResponse } from \"next/server\"\r\nimport { nanoid } from \"nanoid\"\r\nimport { createDb } from \"@/lib/db\"\r\nimport { emails } from \"@/lib/schema\"\r\nimport { eq, and, gt, sql } from \"drizzle-orm\"\r\nimport { EXPIRY_OPTIONS } from \"@/types/email\"\r\nimport { EMAIL_CONFIG } from \"@/config\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { getUserId } from \"@/lib/apiKey\"\r\nimport { getUserRole } from \"@/lib/auth\"\r\nimport { ROLES } from \"@/lib/permissions\"\r\nimport { generateAddressCredential } from \"@/lib/jwt\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nexport async function POST(request: Request) {\r\n  const db = createDb()\r\n  const env = getRequestContext().env\r\n\r\n  const userId = await getUserId()\r\n  const userRole = await getUserRole(userId!)\r\n\r\n  try {\r\n    if (userRole !== ROLES.EMPEROR) {\r\n      const maxEmails = await env.SITE_CONFIG.get(\"MAX_EMAILS\") || EMAIL_CONFIG.MAX_ACTIVE_EMAILS.toString()\r\n      const activeEmailsCount = await db\r\n        .select({ count: sql<number>`count(*)` })\r\n        .from(emails)\r\n        .where(\r\n          and(\r\n            eq(emails.userId, userId!),\r\n            gt(emails.expiresAt, new Date())\r\n          )\r\n        )\r\n      \r\n      if (Number(activeEmailsCount[0].count) >= Number(maxEmails)) {\r\n        return NextResponse.json(\r\n          { error: `已达到最大邮箱数量限制 (${maxEmails})` },\r\n          { status: 403 }\r\n        )\r\n      }\r\n    }\r\n\r\n    const { name, expiryTime, domain } = await request.json<{ \r\n      name: string\r\n      expiryTime: number\r\n      domain: string\r\n    }>()\r\n\r\n    if (!EXPIRY_OPTIONS.some(option => option.value === expiryTime)) {\r\n      return NextResponse.json(\r\n        { error: \"无效的过期时间\" },\r\n        { status: 400 }\r\n      )\r\n    }\r\n\r\n    const domainString = await env.SITE_CONFIG.get(\"EMAIL_DOMAINS\")\r\n    const domains = domainString ? domainString.split(',') : [\"moemail.app\"]\r\n\r\n    if (!domains || !domains.includes(domain)) {\r\n      return NextResponse.json(\r\n        { error: \"无效的域名\" },\r\n        { status: 400 }\r\n      )\r\n    }\r\n\r\n    const address = `${name || nanoid(8)}@${domain}`\r\n    const existingEmail = await db.query.emails.findFirst({\r\n      where: eq(sql`LOWER(${emails.address})`, address.toLowerCase())\r\n    })\r\n\r\n    if (existingEmail) {\r\n      return NextResponse.json(\r\n        { error: \"该邮箱地址已被使用\" },\r\n        { status: 409 }\r\n      )\r\n    }\r\n\r\n    const now = new Date()\r\n    const expires = expiryTime === 0 \r\n      ? new Date('9999-01-01T00:00:00.000Z')\r\n      : new Date(now.getTime() + expiryTime)\r\n    \r\n    const emailData: typeof emails.$inferInsert = {\r\n      address,\r\n      createdAt: now,\r\n      expiresAt: expires,\r\n      userId: userId!\r\n    }\r\n    \r\n    const result = await db.insert(emails)\r\n      .values(emailData)\r\n      .returning({ id: emails.id, address: emails.address })\r\n    \r\n    const credential = await generateAddressCredential(\r\n      result[0].address,\r\n      expiryTime === 0 ? 0 : expiryTime / 1000\r\n    )\r\n\r\n    return NextResponse.json({ \r\n      id: result[0].id,\r\n      email: result[0].address,\r\n      credential\r\n    })\r\n  } catch (error) {\r\n    console.error('Failed to generate email:', error)\r\n    return NextResponse.json(\r\n      { error: \"创建邮箱失败\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\emails\\\\generate\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/emails/generate/route\",\n        pathname: \"/api/emails/generate\",\n        filename: \"route\",\n        bundlePath: \"app/api/emails/generate/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\emails\\\\generate\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Femails%2Fgenerate%2Froute&page=%2Fapi%2Femails%2Fgenerate%2Froute&pagePath=private-next-app-dir%2Fapi%2Femails%2Fgenerate%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fapi%2Femails%2Fgenerate%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/emails/generate/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/emails/generate/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/emails/generate/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "export const EMAIL_CONFIG = {\r\n  MAX_ACTIVE_EMAILS: 30, // Maximum number of active emails\r\n  POLL_INTERVAL: 10_000, // Polling interval in milliseconds\r\n} as const\r\n\r\nexport type EmailConfig = typeof EMAIL_CONFIG ", "export const WEBHOOK_CONFIG = {\r\n  MAX_RETRIES: 3, // Maximum retry count\r\n  TIMEOUT: 10_000, // Timeout time (milliseconds)\r\n  RETRY_DELAY: 1000, // Retry delay (milliseconds)\r\n  EVENTS: {\r\n    NEW_MESSAGE: 'new_message',\r\n  }\r\n} as const\r\n\r\nexport type WebhookConfig = typeof WEBHOOK_CONFIG ", "export * from './email'\r\nexport * from './webhook'"], "names": ["EXPIRY_OPTIONS", "label", "value", "runtime", "POST", "request", "db", "createDb", "env", "getRequestContext", "userId", "getUserId", "userRole", "getUserRole", "ROLES", "EMPEROR", "maxEmails", "SITE_CONFIG", "get", "EMAIL_CONFIG", "MAX_ACTIVE_EMAILS", "toString", "activeEmailsCount", "select", "count", "sql", "from", "emails", "where", "and", "eq", "gt", "expiresAt", "Date", "Number", "NextResponse", "json", "error", "status", "name", "expiryTime", "domain", "some", "option", "domainString", "domains", "split", "includes", "address", "nanoid", "existingEmail", "query", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "now", "expires", "getTime", "result", "insert", "values", "createdAt", "returning", "id", "credential", "generateAddressCredential", "email", "console", "POLL_INTERVAL", "MAX_RETRIES", "TIMEOUT", "RETRY_DELAY", "EVENTS", "NEW_MESSAGE"], "sourceRoot": "", "ignoreList": []}