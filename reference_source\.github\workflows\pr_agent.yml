name: Codium PR Agent

on:
  pull_request:
    types: [opened, reopened, ready_for_review]
jobs:
  pr_agent_job:
    if: ${{ github.event.sender.type != 'Bot' }}
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    name: Run pr agent on every pull request, respond to user comments
    steps:
      - name: PR Agent action step
        id: pragent
        uses: Codium-ai/pr-agent@main
        env:
          PR_REVIEWER.REQUIRE_TESTS_REVIEW: "false"
          OPENAI_KEY: ${{ secrets.OPENAI_KEY }}
          OPENAI_API_BASE: ${{ secrets.OPENAI_API_BASE }}
          CONFIG.MODEL: "gpt-4o"
          CONFIG.MODEL_TURBO: "gpt-4o"
          OPENAI.API_BASE: ${{ secrets.OPENAI_API_BASE }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
