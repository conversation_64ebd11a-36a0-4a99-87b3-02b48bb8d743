{"version": 3, "file": "app/api/webhook/route.js", "mappings": "qFAAA,6DCAA,mHGAA,0SFMO,IAAMA,EAAU,OAAM,EAEPC,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CAC7BC,IAAKD,EAAAA,CAAAA,CAAAA,MAAQ,GAAGC,GAAG,GACnBC,QAASF,EAAAA,CAAAA,CAAAA,OAAS,EACpB,GAEO,eAAeG,IACpB,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAEpBC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACbC,EAAU,MAAMF,EAAGG,KAAK,CAACC,QAAQ,CAACC,SAAS,CAAC,CAChDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,QAAQA,CAACI,MAAM,CAAEV,EAASW,IAAI,CAAEC,EAAE,CAC9C,GAEA,OAAOC,SAASC,IAAI,CAACV,GAAW,CAAEN,SAAS,EAAOD,IAAK,EAAG,EAC5D,CAEO,eAAekB,EAAKC,CAAgB,EACzC,IAAMhB,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAC1B,GAAI,CAACD,GAASW,MAAMC,GAClB,CADsB,MACfC,SAASC,IAAI,CAAC,CAAEG,MAAO,cAAe,EAAG,CAAEC,OAAQ,GAAI,GAGhE,GAAI,CACF,IAAMC,EAAO,MAAMH,EAAQF,IAAI,GACzB,KAAEjB,CAAG,SAAEC,CAAO,CAAE,CAAGsB,EAAcC,KAAK,CAACF,GAEvCjB,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACbmB,EAAM,IAAIC,KAyBhB,OAvBwB,MAAMrB,EAAGG,KAAK,CAACC,QAAQ,CAACC,SAAS,CAAC,CACxDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,QAAQA,CAACI,MAAM,CAAEV,EAAQW,IAAI,CAACC,EAAE,CAC5C,GAGE,MAAMV,EACHsB,MAAM,CAAClB,EAAAA,QAAQA,EACfmB,GAAG,CAAC,KACH5B,UACAC,EACA4B,UAAWJ,CACb,GACCd,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,QAAQA,CAACI,MAAM,CAAEV,EAAQW,IAAI,CAACC,EAAE,GAE5C,MAAMV,EACHyB,MAAM,CAACrB,EAAAA,QAAQA,EACfsB,MAAM,CAAC,CACNlB,OAAQV,EAAQW,IAAI,CAACC,EAAE,CACvBf,MACAC,SACF,GAGGe,SAASC,IAAI,CAAC,CAAEe,SAAS,CAAK,EACvC,CAAE,MAAOZ,EAAO,CAEd,OADAa,QAAQb,KAAK,CAAC,0BAA2BA,GAClCJ,SAASC,IAAI,CAClB,CAAEG,MAAO,iBAAkB,EAC3B,CAAEC,OAAQ,GAAI,EAElB,CACF,CC7DA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,0BACA,wBACA,iBACA,kCACA,CAAK,CACL,wFACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,mEACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,0BACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,uIAiBnxJ,CAAC,CAAC,EAAC,kHCwBI,IAAMa,EAAY,UAEvB,IAAMrB,EAASsB,CADK,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,EAAAA,EACNC,GAAG,CAAC,aAE/B,GAAIxB,EAAQ,OAAOA,EAEnB,IAAMV,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAE1B,OAAOD,GAASW,KAAKC,EACvB,EAAC,2NCxDD,IAAMuB,EAAS,CACb,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACD,eCGD,IAAMC,EAA0C,CAC9C,CAACC,EAAAA,EAAKA,CAACC,OAAO,CAAC,CAAE,YACjB,CAACD,EAAAA,EAAKA,CAACE,IAAI,CAAC,CAAE,WACd,CAACF,EAAAA,EAAKA,CAACG,MAAM,CAAC,CAAE,WAChB,CAACH,EAAAA,EAAKA,CAACI,QAAQ,CAAC,CAAE,UACpB,EAEMC,EAAiB,UACrB,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACC,WAAW,CAACZ,GAAG,CAAC,uBAElE,IACkBG,EAAAA,EAAKA,CAACE,IAAI,EAC1BI,IAAgBN,EAAAA,EAAKA,CAACG,MAAM,EAC5BG,IAAgBN,EAAAA,EAAKA,CAACI,QAAQ,CAEvBE,CADP,CAIKN,EAAAA,EAAKA,CAACI,QAAQ,EAGvB,eAAeM,EAAiB7C,CAAM,CAAE8C,CAAc,EACpD,IAAIC,EAAO,MAAM/C,EAAGG,KAAK,CAAC6C,KAAK,CAAC3C,SAAS,CAAC,CACxCC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACyC,EAAAA,KAAKA,CAACC,IAAI,CAAEH,EACxB,GAEA,GAAI,CAACC,EAAM,CACT,GAAM,CAACG,EAAQ,CAAG,MAAMlD,EAAGyB,MAAM,CAACuB,EAAAA,KAAKA,EACpCtB,MAAM,CAAC,CACNuB,KAAMH,EACNK,YAAajB,CAAiB,CAACY,EAAS,GAEzCM,SAAS,GACZL,EAAOG,CACT,CAEA,OAAOH,CACT,CAEO,eAAeM,EAAiBrD,CAAM,CAAEQ,CAAc,CAAE8C,CAAc,EAC3E,MAAMtD,EAAGuD,MAAM,CAACC,EAAAA,SAASA,EACtBlD,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACiD,EAAAA,SAASA,CAAChD,MAAM,CAAEA,IAE9B,MAAMR,EAAGyB,MAAM,CAAC+B,EAAAA,SAASA,EACtB9B,MAAM,CAAC,QACNlB,SACA8C,CACF,EACJ,CAWO,eAAeG,EAAgBC,CAAsB,EAC1D,IAAMlD,EAAS,MAAMqB,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE9B,GAAI,CAACrB,EAAQ,OAAO,EAEpB,IAAMR,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMb0D,EAAgBC,CALE,MAAM5D,EAAGG,KAAK,CAACqD,SAAS,CAACK,QAAQ,CAAC,CACxDvD,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACiD,EAAAA,SAASA,CAAChD,MAAM,CAAEA,GAC5BsD,KAAM,CAAEf,MAAM,CAAK,CACrB,IAEsCgB,GAAG,CAACC,GAAMA,EAAGjB,IAAI,CAACE,IAAI,EAC5D,MAAOgB,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAACN,EAAyBD,EAChD,CAEO,GAAM,CACXQ,SAAU,KAAErE,CAAG,MAAEgB,CAAI,CAAE,MACvBd,CAAI,QACJoE,CAAM,SACNC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,IAAO,EAClBC,OAAQC,QAAQ5B,GAAG,CAAC6B,WAAW,CAC/BC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACzE,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAAI,CAClC0E,WAAYC,EAAAA,KAAKA,CACjBC,cAAeC,EAAAA,QAAQA,GAEzBC,UAAW,CACTC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC,CACLC,SAAUV,QAAQ5B,GAAG,CAACuC,cAAc,CACpCC,aAAcZ,QAAQ5B,GAAG,CAACyC,kBAAkB,GAE9CC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CAClBpC,KAAM,cACNqC,YAAa,CACXC,SAAU,CAAEC,MAAO,MAAOC,KAAM,OAAQC,YAAa,QAAS,EAC9DC,SAAU,CAAEH,MAAO,KAAMC,KAAM,WAAYC,YAAa,OAAQ,CAClE,EACA,MAAME,UAAUN,CAAW,EACzB,GAAI,CAACA,EACH,MAAM,KADU,CACA,aAGlB,GAAM,UAAEC,CAAQ,UAAEI,CAAQ,CAAE,CAAGL,EAE/B,GAAI,CACFO,EAAAA,CAAUA,CAAC1E,KAAK,CAAC,UAAEoE,WAAUI,CAAS,EAExC,CAAE,MAAO5E,EAAO,CACd,MAAM,MAAU,UAClB,CAEA,IAAMf,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEbQ,EAAO,MAAMT,EAAGG,KAAK,CAACyE,KAAK,CAACvE,SAAS,CAAC,CAC1CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACqE,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAEA,GAAI,CAAC9E,GAKD,CADY,EAJL,IAIWqF,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAACH,EAAoBlF,EAAKkF,QAAQ,EAHrE,MAAM,MAAU,YAQlB,MAAO,CACL,GAAGlF,CAAI,CACPkF,cAAUI,CACZ,CACF,CACF,GACD,CACDC,OAAQ,CACN,MAAM7B,OAAO,MAAE1D,CAAI,CAAE,EACnB,GAAKA,CAAD,CAAMC,EAAE,CAEZ,CAFc,EAEV,CACF,IAAMV,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,GAJqB,CAIjBgG,KAJuBjG,EAAGG,KAAK,CAACqD,SAAS,CAACnD,SAAS,CAAC,CACtDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACiD,EAAAA,SAASA,CAAChD,MAAM,CAAEC,EAAKC,EAAE,CACrC,GAEkB,OAElB,IAAM+B,EAAc,MAAMD,IACpBO,EAAO,MAAMF,EAAiB7C,EAAIyC,EACxC,OAAMY,EAAiBrD,EAAIS,EAAKC,EAAE,CAAEqC,EAAKrC,EAAE,CAC7C,CAAE,MAAOK,EAAO,CACda,QAAQb,KAAK,CAAC,wBAAyBA,EACzC,CACF,CACF,EACAmF,UAAW,CACHC,IAAN,MAAU,OAAEC,CAAK,CAAE3F,MAAI,CAAE,IACnBA,IACF2F,EADQ,EACA,CAAG3F,EAAKC,EAAE,CAClB0F,EAAMnD,IAAI,CAAGxC,EAAKwC,IAAI,EAAIxC,EAAK8E,QAAQ,CACvCa,EAAMb,QAAQ,CAAG9E,EAAK8E,QAAQ,CAC9Ba,EAAMC,KAAK,CAAG5F,EAAK4F,KAAK,ED/JzB,SAA2BpD,CAAY,EAC5C,IAAMqD,CC8J6CC,CD9JnCtD,CAAI,CAAC,EAAE,CAACuD,WAAW,GAE7BC,EAAaC,MAAMC,IAAI,CAAC1D,GAAM2D,MAAM,CACxC,CAACC,EAAKC,IAASD,EAAMC,EAAKC,UAAU,CAAC,GAAI,GACvC9E,EAAO+E,MAAM,CAEXC,EAAkBhF,CAAM,CAACwE,EAAW,CAEpCS,EAAM,CAAC;;2CAE4B,EAAED,EAAgB;;;;;;;;;;;;;QAarD,EAAEX,QAAQ;;;EAGhB,CAAC,CAACa,IAAI,GAGAC,EADU,IAAIC,cACEC,MAAM,CAACJ,GACvBK,EAASC,EAAOb,IAADa,CAAMJ,GAAOK,QAAQ,CAAC,UAE3C,MAAO,CAAC,0BAA0B,EAAEF,EAAAA,CAAQ,EC8HQnB,EAAMnD,IAAI,GAEnDmD,GAET,MAAMtG,QAAQ,SAAEA,CAAO,OAAEsG,CAAK,CAAE,EAC9B,GAAIA,GAAStG,EAAQW,IAAI,CAAE,CACzBX,EAAQW,IAAI,CAACC,EAAE,CAAG0F,EAAM1F,EAAE,CAC1BZ,EAAQW,IAAI,CAACwC,IAAI,CAAGmD,EAAMnD,IAAI,CAC9BnD,EAAQW,IAAI,CAAC8E,QAAQ,CAAGa,EAAMb,QAAQ,CACtCzF,EAAQW,IAAI,CAAC4F,KAAK,CAAGD,EAAMC,KAAK,CAEhC,IAAMrG,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACf2D,EAAkB,MAAM5D,EAAGG,KAAK,CAACqD,SAAS,CAACK,QAAQ,CAAC,CACtDvD,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACiD,EAAAA,SAASA,CAAChD,MAAM,CAAEV,EAAQW,IAAI,CAACC,EAAE,EAC3CoD,KAAM,CAAEf,KAAM,EAAK,CACrB,GAEA,GAAI,CAACa,EAAgBoD,MAAM,CAAE,CAC3B,IAAMvE,EAAc,MAAMD,IACpBO,EAAO,MAAMF,EAAiB7C,EAAIyC,EACxC,OAAMY,EAAiBrD,EAAIF,EAAQW,IAAI,CAACC,EAAE,CAAEqC,EAAKrC,EAAE,EACnDkD,EAAkB,CAAC,CACjBpD,OAAQV,EAAQW,IAAI,CAACC,EAAE,CACvB4C,OAAQP,EAAKrC,EAAE,CACfgH,UAAW,IAAIrG,KACf0B,KAAMA,CACR,EACF,CAEAjD,EAAQW,IAAI,CAACuC,KAAK,CAAGY,EAAgBG,GAAG,CAACC,GAAO,OACxCA,EAAGjB,IAAI,CAACE,IAAI,CACpB,EACF,CAEA,OAAOnD,CACT,CACF,EACAA,QAAS,CACP6H,SAAU,KACZ,EACF,GAEO,CAFJ,cAEmBC,EAASrC,CAAgB,CAAEI,CAAgB,EAC/D,IAAM3F,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,GAJiB,CAIb4H,KAJmB7H,EAAGG,GAIZ,EAJiB,CAACyE,KAAK,CAACvE,SAAS,CAAC,CAC9CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACqE,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAGE,MAAM,MAAU,UAGlB,IAAMuC,EAAiB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACpC,GAEpC,CAAClF,EAAK,CAAG,MAAMT,EAAGyB,MAAM,CAACmD,EAAAA,KAAKA,EACjClD,MAAM,CAAC,CACN6D,WACAI,SAAUmC,CACZ,GACC1E,SAAS,GAEZ,OAAO3C,CACT,gFCvOO,IAAMR,EAAW,IAAM+H,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAACtF,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACsF,EAAE,CAAE,CAAEC,MAAMA,CAAAA,CAAC,GAAE,gCAAHA,gCCJnE,IAAM/F,EAAQ,CACnBC,QAAS,UACTC,KAAM,OACNC,OAAQ,SACRC,SAAU,UACZ,EAAW,EAIgB,CACzB4F,aAAc,eACdC,eAAgB,iBAChBC,aAAc,eACdC,cAAe,gBACfC,eAAgB,gBAClB,EAAW,EAIiD,CAC1D,CAACpG,EAAMC,OAAO,CAAC,CAAEoG,OAAO9G,MAAM,CAAC+G,GAC/B,CAACtG,EAAME,IAAI,CAAC,CAAE,CACZoG,EAAYN,YAAY,CACxBM,EAAYL,cAAc,CAC1BK,EAAYF,cAAc,CAC3B,CACD,CAACpG,EAAMG,MAAM,CAAC,CAAE,CACdmG,EAAYN,YAAY,CACxBM,EAAYL,cAAc,CAC3B,CACD,CAACjG,EAAMI,QAAQ,CAAC,CAAE,EAAE,EACX,SAEK0B,EAAcT,CAAiB,CAAEE,CAAsB,EACrE,OAAOF,EAAUkF,IAAI,CAAC3F,GAAQ4F,CAAgB,CAAC5F,EAAK,EAAE6F,SAASlF,GACjE,kVC9BO,IAAMkB,EAAQiE,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvCnI,GAAIoI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MACNC,UAAU,GACVC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IACrCjG,KAAM6F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXK,MAAOL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASM,MAAM,GAC3BC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,gBAAiB,CAAEC,KAAM,cAAe,GAC/DlD,MAAOyC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZvD,SAAUuD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYM,MAAM,GACjCzD,SAAUmD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACjB,GAAE,EACsBD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CACjC,UACA,CACErI,OAAQsI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UACVU,OAAO,GACPC,UAAU,CAAC,IAAM7E,EAAMlE,EAAE,CAAE,CAAEgJ,SAAU,SAAU,GACpDjE,KAAMqD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQa,KAAK,GAAuBH,OAAO,GACtDI,SAAUd,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYU,OAAO,GAClCK,kBAAmBf,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,qBAAqBU,OAAO,GACpDM,cAAehB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBACpBiB,aAAcjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACnBkB,WAAYV,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cACpBW,WAAYnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,cACjBoB,MAAOpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZqB,SAAUrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YACfsB,cAAetB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACtB,EACA,GAAc,EACZuB,KADY,OACCtB,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CACtBuB,QAAS,CAACC,EAAQX,QAAQ,CAAEW,EAAQV,iBAAiB,CAAC,GAE1D,GAGWW,CAFZ,CAEqB3B,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,QAAS,CACzCnI,GAAIoI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DuB,QAAS3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGJ,MAAM,GACzC5I,OAAQsI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UAAUW,UAAU,CAAC,IAAM7E,EAAMlE,EAAE,CAAE,CAAEgJ,SAAU,SAAU,GACxEhC,UAAW4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAI3H,MACxBqJ,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbmB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAMJ,SAAS,EAChE,GAEaK,CAFV,CAEqBlC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7CnI,GAAIoI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D8B,QAASlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACXU,OAAO,GACPC,UAAU,CAAC,IAAMe,EAAO9J,EAAE,CAAE,CAAEgJ,SAAU,SAAU,GACrDuB,YAAanC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBAAgBU,OAAO,GACzC0B,QAASpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC2B,QAASrC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC4B,KAAMtC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXuC,WAAY/B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cAAe,CAAEC,KAAM,cAAe,GACvDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAI3H,KAC1B,EAAG,GAAY,EACbiK,GADa,QACDV,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAME,OAAO,EAC5D,GAEa5K,CAFV,CAEqByI,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7CnI,GAAIoI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D1I,OAAQsI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACVU,OAAO,GACPC,UAAU,CAAC,IAAM7E,EAAMlE,EAAE,CAAE,CAAEgJ,SAAU,SAAU,GACpD/J,IAAKmJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GACxB5J,QAAS0J,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAG+B,OAAO,EAAC,GACnE7D,UAAW4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAI3H,MACxBG,UAAW8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAI3H,KAC1B,GAAE,EAEmBwH,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvCnI,GAAIoI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DjG,KAAM6F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BrG,YAAa2F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,eAClBpB,UAAW4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAI3H,MAC7EG,UAAW8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAI3H,KAC/E,GAAG,EAEsBwH,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,YAAa,CAChDrI,OAAQsI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM7E,EAAMlE,EAAE,CAAE,CAAEgJ,SAAU,SAAU,GACnFpG,OAAQwF,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMzG,EAAMtC,EAAE,CAAE,CAAEgJ,SAAU,SAAU,GACnFhC,UAAW4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAI3H,KAC/E,EAAG,GAAY,EACbmK,GAAIzC,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CAAEuB,QAAS,CAACQ,EAAMtK,MAAM,CAAEsK,EAAMxH,MAAM,CAAC,GACxD,GAEamI,CAFT,CAEmB5C,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,WAAY,CAC7CnI,GAAIoI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D1I,OAAQsI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM7E,EAAMlE,EAAE,EAC3DuC,KAAM6F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BkC,IAAK5C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GAAGJ,MAAM,GACjC1B,UAAW4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAI3H,MAC7EqJ,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrD3J,QAAS0J,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAG+B,OAAO,EAAC,EACrE,EAAIT,GAAW,EACba,GADa,cACKC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,uBAAuBf,EAAE,CAACC,EAAM7H,IAAI,CAAE6H,EAAMtK,MAAM,CAClF,IAAI,EAE+BqI,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,qBAAsB,CACnEgD,IAAK/C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOC,UAAU,GAC3B2B,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbmB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,0BAA0BC,EAAE,CAACC,EAAMJ,SAAS,EAClE,GAEaoB,CAFT,CAE4BC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACN,EAAS,CAAC,KAAEO,CAAG,CAAE,GAAM,EAC/DvL,KAAMuL,EAAIpH,EAAO,CACfqH,OAAQ,CAACR,EAAQjL,MAAM,CAAC,CACxBiJ,WAAY,CAAC7E,EAAMlE,EAAE,CACvB,GACF,GAEawL,CAFT,CAE8BH,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACvI,EAAW,CAAC,KAAEwI,CAAG,CAAE,GAAM,EACnEvL,KAAMuL,EAAIpH,EAAO,CACfqH,OAAQ,CAACzI,EAAUhD,MAAM,CAAC,CAC1BiJ,WAAY,CAAC7E,EAAMlE,EAAE,CAAC,GAExBqC,KAAMiJ,EAAIhJ,EAAO,CACfiJ,OAAQ,CAACzI,EAAUF,MAAM,CAAC,CAC1BmG,WAAY,CAACzG,EAAMtC,EAAE,CAAC,GAE1B,GAEayL,CAFT,CAE0BJ,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACnH,EAAO,CAAC,CAAEwH,MAAI,CAAE,GAAM,EAC5D5I,UAAW4I,EAAK5I,GAChBiI,QAASW,EAAKX,GAChB,GAEaY,CAFT,CAE0BN,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAC/I,EAAO,CAAC,MAAEoJ,CAAI,CAAE,GAAM,EAC5D5I,UAAW4I,EAAK5I,GAClB,IAAI,sFC5IG,SAAS8I,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,CAEO,eAAexE,EAAapC,CAAgB,EACjD,IAAM+G,EAAU,IAAIrF,YACdsF,EAAOpI,QAAQ5B,GAAG,CAAC6B,WAAW,EAAI,GAClCoI,EAAOF,EAAQpF,MAAM,CAAC3B,EAAWgH,GAEvC,OAAOE,KAAKC,OAAOC,YAAY,IAAI,IAAIC,WAD1B,MAAM/D,OAAOgE,MAAM,CAACC,MAAM,CAAC,UAAWN,KAErD,CAEO,eAAe9G,EAAgBH,CAAgB,CAAEmC,CAAsB,EAE5E,OADa,MAAMC,EAAapC,KAChBmC,CAClB,8DChBO,IAAMjC,EAAanG,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACjC6F,SAAU7F,EAAAA,CAAAA,CAAAA,MAAQ,GACfyN,GAAG,CAAC,EAAG,WACPC,GAAG,CAAC,GAAI,gBACRC,KAAK,CAAC,mBAAoB,uBAC1BC,MAAM,CAACC,GAAO,CAACA,EAAI3E,QAAQ,CAAC,KAAM,cACrCjD,SAAUjG,EAAAA,CAAAA,CAAAA,MAAQ,GACfyN,GAAG,CAAC,EAAG,eACZ,GAAE", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/webhook/route.ts", "webpack://_N_E/./app/api/webhook/route.ts?8a3b", "webpack://_N_E/?e554", "webpack://_N_E/./app/lib/apiKey.ts", "webpack://_N_E/./app/lib/avatar.ts", "webpack://_N_E/./app/lib/auth.ts", "webpack://_N_E/./app/lib/db.ts", "webpack://_N_E/./app/lib/permissions.ts", "webpack://_N_E/./app/lib/schema.ts", "webpack://_N_E/./app/lib/utils.ts", "webpack://_N_E/./app/lib/validation.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { auth } from \"@/lib/auth\"\r\nimport { createDb } from \"@/lib/db\"\r\nimport { webhooks } from \"@/lib/schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { z } from \"zod\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nconst webhookSchema = z.object({\r\n  url: z.string().url(),\r\n  enabled: z.boolean()\r\n})\r\n\r\nexport async function GET() {\r\n  const session = await auth()\r\n\r\n  const db = createDb()\r\n  const webhook = await db.query.webhooks.findFirst({\r\n    where: eq(webhooks.userId, session!.user!.id!)\r\n  })\r\n\r\n  return Response.json(webhook || { enabled: false, url: \"\" })\r\n}\r\n\r\nexport async function POST(request: Request) {\r\n  const session = await auth()\r\n  if (!session?.user?.id) {\r\n    return Response.json({ error: \"Unauthorized\" }, { status: 401 })\r\n  }\r\n\r\n  try {\r\n    const body = await request.json()\r\n    const { url, enabled } = webhookSchema.parse(body)\r\n    \r\n    const db = createDb()\r\n    const now = new Date()\r\n\r\n    const existingWebhook = await db.query.webhooks.findFirst({\r\n      where: eq(webhooks.userId, session.user.id)\r\n    })\r\n\r\n    if (existingWebhook) {\r\n      await db\r\n        .update(webhooks)\r\n        .set({\r\n          url,\r\n          enabled,\r\n          updatedAt: now\r\n        })\r\n        .where(eq(webhooks.userId, session.user.id))\r\n    } else {\r\n      await db\r\n        .insert(webhooks)\r\n        .values({\r\n          userId: session.user.id,\r\n          url,\r\n          enabled,\r\n        })\r\n    }\r\n\r\n    return Response.json({ success: true })\r\n  } catch (error) {\r\n    console.error(\"Failed to save webhook:\", error)\r\n    return Response.json(\r\n      { error: \"Invalid request\" },\r\n      { status: 400 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\webhook\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/webhook/route\",\n        pathname: \"/api/webhook\",\n        filename: \"route\",\n        bundlePath: \"app/api/webhook/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\webhook\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fwebhook%2Froute&page=%2Fapi%2Fwebhook%2Froute&pagePath=private-next-app-dir%2Fapi%2Fwebhook%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fapi%2Fwebhook%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/webhook/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/webhook/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/webhook/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { createDb } from \"./db\"\r\nimport { api<PERSON><PERSON>s } from \"./schema\"\r\nimport { eq, and, gt } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport type { User } from \"next-auth\"\r\nimport { auth } from \"./auth\"\r\nimport { headers } from \"next/headers\"\r\n\r\nasync function getUserByApiKey(key: string): Promise<User | null> {\r\n  const db = createDb()\r\n  const apiKey = await db.query.apiKeys.findFirst({\r\n    where: and(\r\n      eq(apiKeys.key, key),\r\n      eq(apiKeys.enabled, true),\r\n      gt(apiKeys.expiresAt, new Date())\r\n    ),\r\n    with: {\r\n      user: true\r\n    }\r\n  })\r\n\r\n  if (!apiKey) return null\r\n\r\n  return apiKey.user\r\n}\r\n\r\nexport async function handleApiKeyAuth(apiKey: string, pathname: string) {\r\n  if (!pathname.startsWith('/api/emails') && !pathname.startsWith('/api/config')) {\r\n    return NextResponse.json(\r\n      { error: \"无权限查看\" },\r\n      { status: 403 }\r\n    )\r\n  }\r\n\r\n  const user = await getUserByApiKey(apiKey)\r\n  if (!user?.id) {\r\n    return NextResponse.json(\r\n      { error: \"无效的 API Key\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  const response = NextResponse.next()\r\n  response.headers.set(\"X-User-Id\", user.id)\r\n  return response\r\n}\r\n\r\nexport const getUserId = async () => {\r\n  const headersList = await headers()\r\n  const userId = headersList.get(\"X-User-Id\")\r\n  \r\n  if (userId) return userId\r\n\r\n  const session = await auth()\r\n\r\n  return session?.user.id\r\n}\r\n", "const COLORS = [\r\n  '#2196F3', // 蓝色\r\n  '#009688', // 青色\r\n  '#9C27B0', // 紫色\r\n  '#F44336', // 红色\r\n  '#673AB7', // 深紫色\r\n  '#3F51B5', // 靛蓝\r\n  '#4CAF50', // 绿色\r\n  '#FF5722', // 深橙\r\n  '#795548', // 棕色\r\n  '#607D8B', // 蓝灰\r\n];\r\n\r\nexport function generateAvatarUrl(name: string): string {\r\n  const initial = name[0].toUpperCase();\r\n  \r\n  const colorIndex = Array.from(name).reduce(\r\n    (acc, char) => acc + char.charCodeAt(0), 0\r\n  ) % COLORS.length;\r\n  \r\n  const backgroundColor = COLORS[colorIndex];\r\n  \r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\" width=\"100\" height=\"100\">\r\n      <rect width=\"100\" height=\"100\" fill=\"${backgroundColor}\"/>\r\n      <text \r\n        x=\"50%\" \r\n        y=\"50%\" \r\n        fill=\"white\" \r\n        font-family=\"system-ui, -apple-system, sans-serif\" \r\n        font-size=\"45\" \r\n        font-weight=\"500\"\r\n        text-anchor=\"middle\"\r\n        alignment-baseline=\"central\"\r\n        dominant-baseline=\"central\"\r\n        style=\"text-transform: uppercase\"\r\n      >\r\n        ${initial}\r\n      </text>\r\n    </svg>\r\n  `.trim();\r\n\r\n  const encoder = new TextEncoder();\r\n  const bytes = encoder.encode(svg);\r\n  const base64 = Buffer.from(bytes).toString('base64');\r\n  \r\n  return `data:image/svg+xml;base64,${base64}`;\r\n} ", "import NextAuth from \"next-auth\"\r\nimport GitHub from \"next-auth/providers/github\"\r\nimport { Dr<PERSON>zleAdapter } from \"@auth/drizzle-adapter\"\r\nimport { createDb, Db } from \"./db\"\r\nimport { accounts, users, roles, userRoles } from \"./schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { Permission, hasPermission, ROLES, Role } from \"./permissions\"\r\nimport CredentialsProvider from \"next-auth/providers/credentials\"\r\nimport { hashPassword, comparePassword } from \"@/lib/utils\"\r\nimport { authSchema } from \"@/lib/validation\"\r\nimport { generateAvatarUrl } from \"./avatar\"\r\nimport { getUserId } from \"./apiKey\"\r\n\r\nconst ROLE_DESCRIPTIONS: Record<Role, string> = {\r\n  [ROLES.EMPEROR]: \"皇帝（网站所有者）\",\r\n  [ROLES.DUKE]: \"公爵（超级用户）\",\r\n  [ROLES.KNIGHT]: \"骑士（高级用户）\",\r\n  [ROLES.CIVILIAN]: \"平民（普通用户）\",\r\n}\r\n\r\nconst getDefaultRole = async (): Promise<Role> => {\r\n  const defaultRole = await getRequestContext().env.SITE_CONFIG.get(\"DEFAULT_ROLE\")\r\n\r\n  if (\r\n    defaultRole === ROLES.DUKE ||\r\n    defaultRole === ROLES.KNIGHT ||\r\n    defaultRole === ROLES.CIVILIAN\r\n  ) {\r\n    return defaultRole as Role\r\n  }\r\n  \r\n  return ROLES.CIVILIAN\r\n}\r\n\r\nasync function findOrCreateRole(db: Db, roleName: Role) {\r\n  let role = await db.query.roles.findFirst({\r\n    where: eq(roles.name, roleName),\r\n  })\r\n\r\n  if (!role) {\r\n    const [newRole] = await db.insert(roles)\r\n      .values({\r\n        name: roleName,\r\n        description: ROLE_DESCRIPTIONS[roleName],\r\n      })\r\n      .returning()\r\n    role = newRole\r\n  }\r\n\r\n  return role\r\n}\r\n\r\nexport async function assignRoleToUser(db: Db, userId: string, roleId: string) {\r\n  await db.delete(userRoles)\r\n    .where(eq(userRoles.userId, userId))\r\n\r\n  await db.insert(userRoles)\r\n    .values({\r\n      userId,\r\n      roleId,\r\n    })\r\n}\r\n\r\nexport async function getUserRole(userId: string) {\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n  return userRoleRecords[0].role.name\r\n}\r\n\r\nexport async function checkPermission(permission: Permission) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) return false\r\n\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n\r\n  const userRoleNames = userRoleRecords.map(ur => ur.role.name)\r\n  return hasPermission(userRoleNames as Role[], permission)\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  auth,\r\n  signIn,\r\n  signOut\r\n} = NextAuth(() => ({\r\n  secret: process.env.AUTH_SECRET,\r\n  adapter: DrizzleAdapter(createDb(), {\r\n    usersTable: users,\r\n    accountsTable: accounts,\r\n  }),\r\n  providers: [\r\n    GitHub({\r\n      clientId: process.env.AUTH_GITHUB_ID,\r\n      clientSecret: process.env.AUTH_GITHUB_SECRET,\r\n    }),\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        username: { label: \"用户名\", type: \"text\", placeholder: \"请输入用户名\" },\r\n        password: { label: \"密码\", type: \"password\", placeholder: \"请输入密码\" },\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials) {\r\n          throw new Error(\"请输入用户名和密码\")\r\n        }\r\n\r\n        const { username, password } = credentials\r\n\r\n        try {\r\n          authSchema.parse({ username, password })\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        } catch (error) {\r\n          throw new Error(\"输入格式不正确\")\r\n        }\r\n\r\n        const db = createDb()\r\n\r\n        const user = await db.query.users.findFirst({\r\n          where: eq(users.username, username as string),\r\n        })\r\n\r\n        if (!user) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        const isValid = await comparePassword(password as string, user.password as string)\r\n        if (!isValid) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        return {\r\n          ...user,\r\n          password: undefined,\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  events: {\r\n    async signIn({ user }) {\r\n      if (!user.id) return\r\n\r\n      try {\r\n        const db = createDb()\r\n        const existingRole = await db.query.userRoles.findFirst({\r\n          where: eq(userRoles.userId, user.id),\r\n        })\r\n\r\n        if (existingRole) return\r\n\r\n        const defaultRole = await getDefaultRole()\r\n        const role = await findOrCreateRole(db, defaultRole)\r\n        await assignRoleToUser(db, user.id, role.id)\r\n      } catch (error) {\r\n        console.error('Error assigning role:', error)\r\n      }\r\n    },\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id\r\n        token.name = user.name || user.username\r\n        token.username = user.username\r\n        token.image = user.image || generateAvatarUrl(token.name as string)\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token && session.user) {\r\n        session.user.id = token.id as string\r\n        session.user.name = token.name as string\r\n        session.user.username = token.username as string\r\n        session.user.image = token.image as string\r\n\r\n        const db = createDb()\r\n        let userRoleRecords = await db.query.userRoles.findMany({\r\n          where: eq(userRoles.userId, session.user.id),\r\n          with: { role: true },\r\n        })\r\n  \r\n        if (!userRoleRecords.length) {\r\n          const defaultRole = await getDefaultRole()\r\n          const role = await findOrCreateRole(db, defaultRole)\r\n          await assignRoleToUser(db, session.user.id, role.id)\r\n          userRoleRecords = [{\r\n            userId: session.user.id,\r\n            roleId: role.id,\r\n            createdAt: new Date(),\r\n            role: role\r\n          }]\r\n        }\r\n  \r\n        session.user.roles = userRoleRecords.map(ur => ({\r\n          name: ur.role.name,\r\n        }))\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: \"jwt\",\r\n  },\r\n}))\r\n\r\nexport async function register(username: string, password: string) {\r\n  const db = createDb()\r\n  \r\n  const existing = await db.query.users.findFirst({\r\n    where: eq(users.username, username)\r\n  })\r\n\r\n  if (existing) {\r\n    throw new Error(\"用户名已存在\")\r\n  }\r\n\r\n  const hashedPassword = await hashPassword(password)\r\n  \r\n  const [user] = await db.insert(users)\r\n    .values({\r\n      username,\r\n      password: hashedPassword,\r\n    })\r\n    .returning()\r\n\r\n  return user\r\n}\r\n", "import { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { drizzle } from \"drizzle-orm/d1\"\r\nimport * as schema from \"./schema\"\r\n\r\nexport const createDb = () => drizzle(getRequestContext().env.DB, { schema })\r\n\r\nexport type Db = ReturnType<typeof createDb>\r\n", "export const ROLES = {\r\n  EMPEROR: 'emperor',\r\n  <PERSON>U<PERSON>: 'duke',\r\n  <PERSON><PERSON><PERSON><PERSON>: 'knight',\r\n  CIVILIAN: 'civilian',\r\n} as const;\r\n\r\nexport type Role = typeof ROLES[keyof typeof ROLES];\r\n\r\nexport const PERMISSIONS = {\r\n  MANAGE_EMAIL: 'manage_email',\r\n  <PERSON><PERSON>GE_WEBHOOK: 'manage_webhook',\r\n  PROMOTE_USER: 'promote_user',\r\n  MANAGE_CONFIG: 'manage_config',\r\n  MANAGE_API_KEY: 'manage_api_key',\r\n} as const;\r\n\r\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\r\n\r\nexport const ROLE_PERMISSIONS: Record<Role, Permission[]> = {\r\n  [ROLES.EMPEROR]: Object.values(PERMISSIONS),\r\n  [ROLES.DUKE]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_API_KEY,\r\n  ],\r\n  [ROLES.KNIGHT]: [\r\n    PERMISSIONS.<PERSON>NAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n  ],\r\n  [ROLES.CIVILIAN]: [],\r\n} as const;\r\n\r\nexport function hasPermission(userRoles: Role[], permission: Permission): boolean {\r\n  return userRoles.some(role => ROLE_PERMISSIONS[role]?.includes(permission));\r\n} ", "import { integer, sqliteTable, text, primaryKey, uniqueIndex, index } from \"drizzle-orm/sqlite-core\"\r\nimport type { AdapterAccountType } from \"next-auth/adapters\"\r\nimport { relations } from 'drizzle-orm';\r\n\r\n// https://authjs.dev/getting-started/adapters/drizzle\r\nexport const users = sqliteTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: integer(\"emailVerified\", { mode: \"timestamp_ms\" }),\r\n  image: text(\"image\"),\r\n  username: text(\"username\").unique(),\r\n  password: text(\"password\"),\r\n})\r\nexport const accounts = sqliteTable(\r\n  \"account\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => users.id, { onDelete: \"cascade\" }),\r\n    type: text(\"type\").$type<AdapterAccountType>().notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n  },\r\n  (account) => ({\r\n    compoundKey: primaryKey({\r\n      columns: [account.provider, account.providerAccountId],\r\n    }),\r\n  })\r\n)\r\n\r\nexport const emails = sqliteTable(\"email\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  address: text(\"address\").notNull().unique(),\r\n  userId: text(\"userId\").references(() => users.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp_ms\" }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"email_expires_at_idx\").on(table.expiresAt),\r\n}))\r\n\r\nexport const messages = sqliteTable(\"message\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  emailId: text(\"emailId\")\r\n    .notNull()\r\n    .references(() => emails.id, { onDelete: \"cascade\" }),\r\n  fromAddress: text(\"from_address\").notNull(),\r\n  subject: text(\"subject\").notNull(),\r\n  content: text(\"content\").notNull(),\r\n  html: text(\"html\"),\r\n  receivedAt: integer(\"received_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  emailIdIdx: index(\"message_email_id_idx\").on(table.emailId),\r\n}))\r\n\r\nexport const webhooks = sqliteTable('webhook', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id')\r\n    .notNull()\r\n    .references(() => users.id, { onDelete: \"cascade\" }),\r\n  url: text('url').notNull(),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n})\r\n\r\nexport const roles = sqliteTable(\"role\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\").notNull(),\r\n  description: text(\"description\"),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n});\r\n\r\nexport const userRoles = sqliteTable(\"user_role\", {\r\n  userId: text(\"user_id\").notNull().references(() => users.id, { onDelete: \"cascade\" }),\r\n  roleId: text(\"role_id\").notNull().references(() => roles.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  pk: primaryKey({ columns: [table.userId, table.roleId] }),\r\n}));\r\n\r\nexport const apiKeys = sqliteTable('api_keys', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id),\r\n  name: text('name').notNull(),\r\n  key: text('key').notNull().unique(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n}, (table) => ({\r\n  nameUserIdUnique: uniqueIndex('name_user_id_unique').on(table.name, table.userId)\r\n}));\r\n\r\nexport const revoked_credentials = sqliteTable('revoked_credential', {\r\n  jti: text('jti').primaryKey(),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp_ms' }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"revoked_expires_at_idx\").on(table.expiresAt),\r\n}));\r\n\r\nexport const apiKeysRelations = relations(apiKeys, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [apiKeys.userId],\r\n    references: [users.id],\r\n  }),\r\n}));\r\n\r\nexport const userRolesRelations = relations(userRoles, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userRoles.userId],\r\n    references: [users.id],\r\n  }),\r\n  role: one(roles, {\r\n    fields: [userRoles.roleId],\r\n    references: [roles.id],\r\n  }),\r\n}));\r\n\r\nexport const usersRelations = relations(users, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n  apiKeys: many(apiKeys),\r\n}));\r\n\r\nexport const rolesRelations = relations(roles, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n}));", "import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport async function hashPassword(password: string): Promise<string> {\r\n  const encoder = new TextEncoder()\r\n  const salt = process.env.AUTH_SECRET || ''\r\n  const data = encoder.encode(password + salt)\r\n  const hash = await crypto.subtle.digest('SHA-256', data)\r\n  return btoa(String.fromCharCode(...new Uint8Array(hash)))\r\n}\r\n\r\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\r\n  const hash = await hashPassword(password)\r\n  return hash === hashedPassword\r\n}", "import { z } from \"zod\"\r\n\r\nexport const authSchema = z.object({\r\n  username: z.string()\r\n    .min(1, \"用户名不能为空\")\r\n    .max(20, \"用户名不能超过20个字符\")\r\n    .regex(/^[a-zA-Z0-9_-]+$/, \"用户名只能包含字母、数字、下划线和横杠\")\r\n    .refine(val => !val.includes('@'), \"用户名不能是邮箱格式\"),\r\n  password: z.string()\r\n    .min(8, \"密码长度必须大于等于8位\")\r\n})\r\n\r\nexport type AuthSchema = z.infer<typeof authSchema>"], "names": ["runtime", "z", "url", "enabled", "GET", "session", "auth", "db", "createDb", "webhook", "query", "webhooks", "<PERSON><PERSON><PERSON><PERSON>", "where", "eq", "userId", "user", "id", "Response", "json", "POST", "request", "error", "status", "body", "webhookSchema", "parse", "now", "Date", "update", "set", "updatedAt", "insert", "values", "success", "console", "getUserId", "headersList", "headers", "get", "COLORS", "ROLE_DESCRIPTIONS", "ROLES", "EMPEROR", "DUKE", "KNIGHT", "CIVILIAN", "getDefaultRole", "defaultRole", "getRequestContext", "env", "SITE_CONFIG", "findOrCreateRole", "<PERSON><PERSON><PERSON>", "role", "roles", "name", "newRole", "description", "returning", "assignRoleToUser", "roleId", "delete", "userRoles", "checkPermission", "permission", "userRoleNames", "userRoleRecords", "find<PERSON>any", "with", "map", "ur", "hasPermission", "handlers", "signIn", "signOut", "NextAuth", "secret", "process", "AUTH_SECRET", "adapter", "DrizzleAdapter", "usersTable", "users", "accountsTable", "accounts", "providers", "GitHub", "clientId", "AUTH_GITHUB_ID", "clientSecret", "AUTH_GITHUB_SECRET", "CredentialsProvider", "credentials", "username", "label", "type", "placeholder", "password", "authorize", "authSchema", "comparePassword", "undefined", "events", "existingRole", "callbacks", "jwt", "token", "image", "initial", "generateAvatarUrl", "toUpperCase", "colorIndex", "Array", "from", "reduce", "acc", "char", "charCodeAt", "length", "backgroundColor", "svg", "trim", "bytes", "TextEncoder", "encode", "base64", "<PERSON><PERSON><PERSON>", "toString", "createdAt", "strategy", "register", "existing", "hashedPassword", "hashPassword", "drizzle", "DB", "schema", "MANAGE_EMAIL", "MANAGE_WEBHOOK", "PROMOTE_USER", "MANAGE_CONFIG", "MANAGE_API_KEY", "Object", "PERMISSIONS", "some", "ROLE_PERMISSIONS", "includes", "sqliteTable", "text", "<PERSON><PERSON><PERSON>", "$defaultFn", "crypto", "randomUUID", "email", "unique", "emailVerified", "integer", "mode", "notNull", "references", "onDelete", "$type", "provider", "providerAccountId", "refresh_token", "access_token", "expires_at", "token_type", "scope", "id_token", "session_state", "<PERSON><PERSON><PERSON>", "columns", "account", "emails", "address", "expiresAt", "expiresAtIdx", "index", "on", "table", "messages", "emailId", "fromAddress", "subject", "content", "html", "receivedAt", "emailIdIdx", "default", "pk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "nameUserIdUnique", "uniqueIndex", "jti", "apiKeysRelations", "relations", "one", "fields", "userRolesRelations", "usersRelations", "many", "rolesRelations", "cn", "inputs", "twMerge", "clsx", "encoder", "salt", "data", "btoa", "String", "fromCharCode", "Uint8Array", "subtle", "digest", "min", "max", "regex", "refine", "val"], "sourceRoot": "", "ignoreList": []}