{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Toast/Toast.tsx"], "sourcesContent": ["import * as React from 'react'\n\nexport type ToastProps = React.HTMLProps<HTMLDivElement> & {\n  children?: React.ReactNode\n  onClick?: () => void\n  className?: string\n}\n\nexport const Toast: React.FC<ToastProps> = function Toast({\n  onClick,\n  children,\n  className,\n  ...props\n}) {\n  return (\n    <div\n      {...props}\n      onClick={(e) => {\n        e.preventDefault()\n        return onClick?.()\n      }}\n      className={`nextjs-toast${className ? ' ' + className : ''}`}\n    >\n      <div data-nextjs-toast-wrapper>{children}</div>\n    </div>\n  )\n}\n"], "names": ["Toast", "onClick", "children", "className", "props", "div", "e", "preventDefault", "data-nextjs-toast-wrapper"], "mappings": ";;;;+BAQaA;;;eAAAA;;;;;iEARU;AAQhB,MAAMA,QAA8B,SAASA,MAAM,KAKzD;IALyD,IAAA,EACxDC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACT,GAAGC,OACJ,GALyD;IAMxD,qBACE,qBAACC;QACE,GAAGD,KAAK;QACTH,SAAS,CAACK;YACRA,EAAEC,cAAc;YAChB,OAAON,2BAAAA;QACT;QACAE,WAAW,AAAC,iBAAcA,CAAAA,YAAY,MAAMA,YAAY,EAAC;kBAEzD,cAAA,qBAACE;YAAIG,2BAAyB;sBAAEN;;;AAGtC"}