{"version": 3, "file": "app/api/emails/route.js", "mappings": "qFAAA,6DCAA,mHGAA,kTFOO,IAAMA,EAAU,OAAM,eAIPC,EAAIC,CAAgB,EACxC,IAAMC,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAExB,cAAEC,CAAY,CAAE,CAAG,IAAIC,IAAIJ,EAAQK,GAAG,EACtCC,EAASH,EAAaI,GAAG,CAAC,UAE1BC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEnB,GAAI,CACF,IAAMC,EAAiBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACxBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACC,EAAAA,MAAMA,CAACZ,MAAM,CAAEA,GAClBa,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACD,EAAAA,MAAMA,CAACE,SAAS,CAAE,IAAIC,OAGrBC,EAAc,MAAMT,EAAGU,MAAM,CAAC,CAAEC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAW,CAAC,QAAQ,CAAC,GAC/DC,IAAI,CAACR,EAAAA,MAAMA,EACXS,KAAK,CAACZ,GACHa,EAAaC,OAAOP,CAAW,CAAC,EAAE,CAACE,KAAK,EAExCM,EAAa,CAACf,EAAe,CAEnC,GAAIJ,EAAQ,CACV,GAAM,WAAEoB,CAAS,IAAEC,CAAE,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACtB,GACvCmB,EAAWI,IAAI,CACbC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACAC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAClB,EAAAA,MAAMA,CAACmB,SAAS,CAAE,IAAIhB,KAAKU,IAC9Bf,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACC,EAAAA,MAAMA,CAACmB,SAAS,CAAE,IAAIhB,KAAKU,IAC9BK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAClB,EAAAA,MAAMA,CAACc,EAAE,CAAEA,KAItB,CAEA,IAAMM,EAAU,MAAMzB,EAAG0B,KAAK,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAC7Cb,MAAOX,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,IAAIc,GACdW,QAAS,CAACvB,EAAQ,MAAEwB,CAAI,CAAE,GAAK,CAC7BA,EAAKxB,EAAOmB,SAAS,EACrBK,EAAKxB,EAAOc,EAAE,EACf,CACDW,MAAOC,EACT,GAEMC,EAAUP,EAAQQ,GAHH,GAGS,GAAGF,CAC3BG,EAAaF,EACfG,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CACVV,CAAO,CAACM,GAAc,CAACP,QAAH,CAAY,CAACY,OAAO,GACxCX,CAAO,CAACM,GAAc,CAACZ,EAAE,EAE3B,IAFsB,CAGpBkB,EAAYL,EAAUP,EAAQa,KAAK,CAAC,EApD5B,CAoD+BP,GAAaN,EAE1D,OAAOc,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBnC,OAAQgC,aACRH,EACAO,MAAO1B,CACT,EACF,CAAE,MAAO2B,EAAO,CAEd,OADAC,QAAQD,KAAK,CAAC,+BAAgCA,GACvCH,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEE,MAAO,wBAAyB,EAClC,CAAEE,OAAQ,GAAI,EAElB,CACF,CCpEA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,yBACA,uBACA,iBACA,iCACA,CAAK,CACL,uFACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,kEACA,GAFA,2BAEA,4BACA,MACI,QAA8B,EAClC,yBACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,uIAiBnxJ,CAAC,CAAC,EAAC,kHCwBI,IAAMlD,EAAY,UAEvB,IAAMD,EAASoD,CADK,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,EAAAA,EACN/C,GAAG,CAAC,aAE/B,GAAIN,EAAQ,OAAOA,EAEnB,IAAMsD,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAE1B,OAAOD,GAASE,KAAK9B,EACvB,EAAC,oOCxDD,IAAM+B,EAAS,CACb,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACD,eCGD,IAAMC,EAA0C,CAC9C,CAACC,EAAAA,EAAKA,CAACC,OAAO,CAAC,CAAE,YACjB,CAACD,EAAAA,EAAKA,CAACE,IAAI,CAAC,CAAE,WACd,CAACF,EAAAA,EAAKA,CAACG,MAAM,CAAC,CAAE,WAChB,CAACH,EAAAA,EAAKA,CAACI,QAAQ,CAAC,CAAE,UACpB,EAEMC,EAAiB,UACrB,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACC,WAAW,CAAC9D,GAAG,CAAC,uBAElE,IACkBqD,EAAAA,EAAKA,CAACE,IAAI,EAC1BI,IAAgBN,EAAAA,EAAKA,CAACG,MAAM,EAC5BG,IAAgBN,EAAAA,EAAKA,CAACI,QAAQ,CAEvBE,CADP,CAIKN,EAAAA,EAAKA,CAACI,QACf,EAEA,eAAeM,EAAiB9D,CAAM,CAAE+D,CAAc,EACpD,IAAIC,EAAO,MAAMhE,EAAG0B,KAAK,CAACuC,KAAK,CAACC,SAAS,CAAC,CACxCpD,MAAOV,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC6D,EAAAA,KAAKA,CAACE,IAAI,CAAEJ,EACxB,GAEA,GAAI,CAACC,EAAM,CACT,GAAM,CAACI,EAAQ,CAAG,MAAMpE,EAAGqE,MAAM,CAACJ,EAAAA,KAAKA,EACpCK,MAAM,CAAC,CACNH,KAAMJ,EACNQ,YAAapB,CAAiB,CAACY,EAAS,GAEzCS,SAAS,GACZR,EAAOI,CACT,CAEA,OAAOJ,CACT,CAEO,eAAeS,EAAiBzE,CAAM,CAAEP,CAAc,CAAEiF,CAAc,EAC3E,MAAM1E,EAAG2E,MAAM,CAACC,EAAAA,SAASA,EACtB9D,KAAK,CAACV,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACwE,EAAAA,SAASA,CAACnF,MAAM,CAAEA,IAE9B,MAAMO,EAAGqE,MAAM,CAACO,EAAAA,SAASA,EACtBN,MAAM,CAAC,QACN7E,SACAiF,CACF,EACJ,CAEO,eAAeG,EAAYpF,CAAc,EAC9C,IAAMO,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,MAAO6E,CAJiB,MAAM9E,EAAG0B,KAAK,CAACkD,SAAS,CAACjD,QAAQ,CAAC,CACxDb,MAAOV,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACwE,EAAAA,SAASA,CAACnF,MAAM,CAAEA,GAC5BsF,KAAM,CAAEf,KAAM,EAAK,CACrB,GACsB,CAAC,EAAE,CAACA,IAAI,CAACG,IACjC,CAEO,eAAea,EAAgBC,CAAsB,EAC1D,IAAMxF,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE9B,GAAI,CAACD,EAAQ,OAAO,EAEpB,IAAMO,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMbiF,EAAgBJ,CALE,MAAM9E,EAAG0B,KAAK,CAACkD,SAAS,CAACjD,QAAQ,CAAC,CACxDb,MAAOV,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACwE,EAAAA,SAASA,CAACnF,MAAM,CAAEA,GAC5BsF,KAAM,CAAEf,MAAM,CAAK,CACrB,IAEsCmB,GAAG,CAACC,GAAMA,EAAGpB,IAAI,CAACG,IAAI,EAC5D,MAAOkB,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAACH,EAAyBD,EAChD,CAEO,GAAM,CACXK,SAAU,KAAE/F,CAAG,MAAEgG,CAAI,CAAE,CACvBvC,MAAI,QACJwC,CAAM,SACNC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,IAAO,EAClBC,OAAQC,QAAQhC,GAAG,CAACiC,WAAW,CAC/BC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAAC9F,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAAI,CAClC+F,WAAYC,EAAAA,KAAKA,CACjBC,cAAeC,EAAAA,QACjB,GACAC,UAAW,CACTC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC,CACLC,SAAUV,QAAQhC,GAAG,CAAC2C,cAAc,CACpCC,aAAcZ,QAAQhC,GAAG,CAAC6C,kBAAkB,GAE9CC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CAClBvC,KAAM,cACNwC,YAAa,CACXC,SAAU,CAAEC,MAAO,MAAOC,KAAM,OAAQC,YAAa,QAAS,EAC9DC,SAAU,CAAEH,MAAO,KAAMC,KAAM,WAAYC,YAAa,OAAQ,CAClE,EACA,MAAME,UAAUN,CAAW,EACzB,GAAI,CAACA,EACH,MAAM,KADU,CACA,aAGlB,GAAM,UAAEC,CAAQ,CAAEI,UAAQ,CAAE,CAAGL,EAE/B,GAAI,CACFO,EAAAA,CAAUA,CAACC,KAAK,CAAC,UAAEP,EAAUI,UAAS,EAExC,CAAE,MAAOtE,EAAO,CACd,MAAM,MAAU,UAClB,CAEA,IAAM1C,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEbgD,EAAO,MAAMjD,EAAG0B,KAAK,CAACuE,KAAK,CAAC/B,SAAS,CAAC,CAC1CpD,MAAOV,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC6F,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAEA,GAAI,CAAC3D,GAKD,CADY,EAJL,IAIWmE,CAAAA,EACR,EADQA,CAAAA,CAAeA,CAACJ,EAAoB/D,EAAK+D,QAAQ,EAHrE,MAAUK,MAAM,YAQlB,MAAO,CACL,GAAGpE,CAAI,CACP+D,SAAUM,MACZ,CACF,CACF,GACD,CACDC,OAAQ,CACN,MAAM/B,OAAO,MAAEvC,CAAI,CAAE,EACnB,GAAKA,CAAD,CAAM9B,EAAE,CAEZ,CAFc,EAEV,CACF,IAAMnB,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,GAJqB,CAIjBuH,KAJuBxH,EAAG0B,KAAK,CAACkD,SAAS,CAACV,SAAS,CAAC,CACtDpD,MAAOV,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACwE,EAAAA,SAASA,CAACnF,MAAM,CAAEwD,EAAK9B,EAAE,CACrC,GAEkB,OAElB,IAAMuC,EAAc,MAAMD,IACpBO,EAAO,MAAMF,EAAiB9D,EAAI0D,EACxC,OAAMe,EAAiBzE,EAAIiD,EAAK9B,EAAE,CAAE6C,EAAK7C,EAAE,CAC7C,CAAE,MAAOuB,EAAO,CACdC,QAAQD,KAAK,CAAC,wBAAyBA,EACzC,CACF,CACF,EACA+E,UAAW,CACHC,IAAN,MAAU,OAAEC,CAAK,CAAE1E,MAAI,CAAE,IACnBA,IACF0E,EADQ,EACA,CAAG1E,EAAK9B,EAAE,CAClBwG,EAAMxD,IAAI,CAAGlB,EAAKkB,IAAI,EAAIlB,EAAK2D,QAAQ,CACvCe,EAAMf,QAAQ,CAAG3D,EAAK2D,QAAQ,CAC9Be,EAAMC,KAAK,CAAG3E,EAAK2E,KAAK,ED/JzB,SAASC,CAA8B,EAC5C,IAAMC,CC8J6CD,CD9JnC1D,CAAI,CAAC,EAAE,CAAC4D,WAAW,GAE7BC,EAAaC,MAAMpH,IAAI,CAACsD,GAAM+D,MAAM,CACxC,CAACC,EAAKC,IAASD,EAAMC,EAAKC,UAAU,CAAC,GAAI,GACvCnF,EAAOjB,MAAM,CAEXqG,EAAkBpF,CAAM,CAAC8E,EAAW,CAEpCO,EAAM,CAAC;;2CAE4B,EAAED,EAAgB;;;;;;;;;;;;;QAarD,EAAER,QAAQ;;;EAGhB,CAAC,CAACU,IAAI,GAGAC,EADU,IAAIC,cACEC,MAAM,CAACJ,GACvBK,EAASC,EAAOhI,IAAI,CAAC4H,GAAOK,QAAQ,CAAC,UAE3C,MAAO,CAAC,0BAA0B,EAAEF,EAAAA,CAAQ,EC8HQjB,EAAMxD,IAAI,GAEnDwD,GAET,MAAM5E,QAAQ,SAAEA,CAAO,OAAE4E,CAAK,CAAE,EAC9B,GAAIA,GAAS5E,EAAQE,IAAI,CAAE,CACzBF,EAAQE,IAAI,CAAC9B,EAAE,CAAGwG,EAAMxG,EAAE,CAC1B4B,EAAQE,IAAI,CAACkB,IAAI,CAAGwD,EAAMxD,IAAI,CAC9BpB,EAAQE,IAAI,CAAC2D,QAAQ,CAAGe,EAAMf,QAAQ,CACtC7D,EAAQE,IAAI,CAAC2E,KAAK,CAAGD,EAAMC,KAAK,CAEhC,IAAM5H,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACf6E,EAAkB,MAAM9E,EAAG0B,KAAK,CAACkD,SAAS,CAACjD,QAAQ,CAAC,CACtDb,MAAOV,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACwE,EAAAA,SAASA,CAACnF,MAAM,CAAEsD,EAAQE,IAAI,CAAC9B,EAAE,EAC3C4D,KAAM,CAAEf,KAAM,EAAK,CACrB,GAEA,GAAI,CAACc,EAAgB7C,MAAM,CAAE,CAC3B,IAAMyB,EAAc,MAAMD,IACpBO,EAAO,MAAMF,EAAiB9D,EAAI0D,EACxC,OAAMe,EAAiBzE,EAAI+C,EAAQE,IAAI,CAAC9B,EAAE,CAAE6C,EAAK7C,EAAE,EACnD2D,EAAkB,CAAC,CACjBrF,OAAQsD,EAAQE,IAAI,CAAC9B,EAAE,CACvBuD,OAAQV,EAAK7C,EAAE,CACfK,UAAW,IAAIhB,KACfwD,KAAMA,CACR,EAAE,CAGJjB,EAAQE,IAAI,CAACgB,KAAK,CAAGa,EAAgBK,GAAG,CAACC,GAAO,EAC9CjB,KAAMiB,EAAGpB,IAAI,CAACG,IAAI,CACpB,EACF,CAEA,OAAOpB,CACT,CACF,EACAA,QAAS,CACPgG,SAAU,KACZ,EACF,GAEO,CAFJ,cAEmBC,EAASpC,CAAgB,CAAEI,CAAgB,EAC/D,IAAMhH,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,GAJiB,CAIbgJ,KAJmBjJ,EAAG0B,GAIZ,EAJiB,CAACuE,KAAK,CAAC/B,SAAS,CAAC,CAC9CpD,MAAOV,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC6F,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAGE,MAAM,MAAU,UAGlB,IAAMsC,EAAiB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACnC,GAEpC,CAAC/D,EAAK,CAAG,MAAMjD,EAAGqE,MAAM,CAAC4B,EAAAA,KAAKA,EACjC3B,MAAM,CAAC,UACNsC,EACAI,SAAUkC,CACZ,GACC1E,SAAS,GAEZ,OAAOvB,CACT,6ECtOO,SAASd,EAAajB,CAAiB,CAAEC,CAAU,EAExD,OAAO0H,EAAOhI,IAADgI,CAAMO,KAAKC,SAAS,CADR,WAAEnI,KAAWC,CAAG,IACA2H,QAAQ,CAAC,SACpD,CAEO,SAAS1H,EAAatB,CAAc,EAEzC,OADasJ,KAAKjC,KAAK,CAAC0B,EAAOhI,IAADgI,CAAM/I,EAAQ,UAAUgJ,QAAQ,GAEhE,gFCTO,IAAM7I,EAAW,IAAMqJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAAC3F,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAAC2F,EAAE,CAAE,CAAEC,MAAMA,CAAAA,CAAC,GAAE,gCAAHA,gCCJnE,IAAMpG,EAAQ,CACnBC,QAAS,UACTC,KAAM,OACNC,OAAQ,SACRC,SAAU,UACZ,EAAW,EAIgB,CACzBiG,aAAc,eACdC,eAAgB,iBAChBC,aAAc,eACdC,cAAe,gBACfC,eAAgB,gBAClB,EAAW,EAIiD,CAC1D,CAACzG,EAAMC,OAAO,CAAC,CAAEyG,OAAOxF,MAAM,CAACyF,GAC/B,CAAC3G,EAAME,IAAI,CAAC,CAAE,CACZyG,EAAYN,YAAY,CACxBM,EAAYL,cAAc,CAC1BK,EAAYF,cAAc,CAC3B,CACD,CAACzG,EAAMG,MAAM,CAAC,CAAE,CACdwG,EAAYN,YAAY,CACxBM,EAAYL,cAAc,CAC3B,CACD,CAACtG,EAAMI,QAAQ,CAAC,CAAE,EAAE,EACX,SAEK6B,EAAcT,CAAiB,CAAEK,CAAsB,EACrE,OAAOL,EAAUoF,IAAI,CAAChG,GAAQiG,CAAgB,CAACjG,EAAK,EAAEkG,SAASjF,GACjE,kVC9BO,IAAMgB,EAAQkE,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvChJ,GAAIiJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MACNC,UAAU,GACVC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IACrCrG,KAAMiG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXK,MAAOL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASM,MAAM,GAC3BC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,gBAAiB,CAAEC,KAAM,cAAe,GAC/DjD,MAAOwC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZxD,SAAUwD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYM,MAAM,GACjC1D,SAAUoD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACjB,GAAE,EACsBD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CACjC,UACA,CACE1K,OAAQ2K,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UACVU,OAAO,GACPC,UAAU,CAAC,IAAM9E,EAAM9E,EAAE,CAAE,CAAE6J,SAAU,SAAU,GACpDlE,KAAMsD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQa,KAAK,GAAuBH,OAAO,GACtDI,SAAUd,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYU,OAAO,GAClCK,kBAAmBf,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,qBAAqBU,OAAO,GACpDM,cAAehB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBACpBiB,aAAcjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACnBkB,WAAYV,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cACpBW,WAAYnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,cACjBoB,MAAOpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZqB,SAAUrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YACfsB,cAAetB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACtB,EACA,GAAc,EACZuB,KADY,OACCtB,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CACtBuB,QAAS,CAACC,EAAQX,QAAQ,CAAEW,EAAQV,iBAAiB,CAAC,GAE1D,GAGW9K,CAFZ,CAEqB8J,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,QAAS,CACzChJ,GAAIiJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DsB,QAAS1B,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGJ,MAAM,GACzCjL,OAAQ2K,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UAAUW,UAAU,CAAC,IAAM9E,EAAM9E,EAAE,CAAE,CAAE6J,SAAU,SAAU,GACxExJ,UAAWoJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAI9J,MACxBD,UAAWqK,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbiB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAM3L,SAAS,EAChE,GAEa4L,CAFV,CAEqBhC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7ChJ,GAAIiJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D4B,QAAShC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACXU,OAAO,GACPC,UAAU,CAAC,IAAM1K,EAAOc,EAAE,CAAE,CAAE6J,SAAU,SAAU,GACrDqB,YAAajC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBAAgBU,OAAO,GACzCwB,QAASlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChCyB,QAASnC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC0B,KAAMpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXqC,WAAY7B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cAAe,CAAEC,KAAM,cAAe,GACvDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAI9J,KAC1B,EAAG,GAAY,EACbkM,GADa,QACDV,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAME,OAAO,EAC5D,GAEaO,CAFV,CAEqBxC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7ChJ,GAAIiJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D/K,OAAQ2K,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACVU,OAAO,GACPC,UAAU,CAAC,IAAM9E,EAAM9E,EAAE,CAAE,CAAE6J,SAAU,SAAU,GACpDnL,IAAKuK,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GACxB8B,QAAShC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAG+B,OAAO,EAAC,GACnErL,UAAWoJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAI9J,MACxBsM,UAAWlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAI9J,KAC1B,GAAE,EAEmB2J,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvChJ,GAAIiJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DrG,KAAMiG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BvG,YAAa6F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,eAClB5I,UAAWoJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAI9J,MAC7EsM,UAAWlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAI9J,KAC/E,GAAG,EAEsB2J,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,YAAa,CAChD1K,OAAQ2K,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM9E,EAAM9E,EAAE,CAAE,CAAE6J,SAAU,SAAU,GACnFtG,OAAQ0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM9G,EAAM9C,EAAE,CAAE,CAAE6J,SAAU,SAAU,GACnFxJ,UAAWoJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAI9J,KAC/E,EAAG,GAAY,EACbuM,GADa,CACT1C,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CAAEuB,QAAS,CAACM,EAAMzM,MAAM,CAAEyM,EAAMxH,MAAM,CAAC,GACxD,GAEasI,CAFT,CAEmB7C,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,WAAY,CAC7ChJ,GAAIiJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D/K,OAAQ2K,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM9E,EAAM9E,EAAE,EAC3DgD,KAAMiG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BmC,IAAK7C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GAAGJ,MAAM,GACjClJ,UAAWoJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAI9J,MAC7ED,UAAWqK,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrD+B,QAAShC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAG+B,OAAO,EAAC,EACrE,EAAG,GAAY,EACbK,GADa,cACKC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,uBAAuBlB,EAAE,CAACC,EAAM/H,IAAI,CAAE+H,EAAMzM,MAAM,EAClF,GAEa2N,CAFT,CAE+BjD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,qBAAsB,CACnEkD,IAAKjD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOC,UAAU,GAC3B9J,UAAWqK,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAIoB,GAAW,EACbH,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,0BAA0BC,EAAE,CAACC,EAAM3L,SAAS,EAClE,GAEa+M,CAFT,CAE4BC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACP,EAAS,CAAC,CAAEQ,KAAG,CAAE,GAAM,EAC/DvK,KAAMuK,EAAIvH,EAAO,CACfwH,OAAQ,CAACT,EAAQvN,MAAM,CAAC,CACxBsL,WAAY,CAAC9E,EAAM9E,EAAE,CAAC,GAE1B,GAEauM,CAFT,CAE8BH,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAC3I,EAAW,CAAC,KAAE4I,CAAG,CAAE,GAAM,EACnEvK,KAAMuK,EAAIvH,EAAO,CACfwH,OAAQ,CAAC7I,EAAUnF,MAAM,CAAC,CAC1BsL,WAAY,CAAC9E,EAAM9E,EAAE,CAAC,GAExB6C,KAAMwJ,EAAIvJ,EAAO,CACfwJ,OAAQ,CAAC7I,EAAUF,MAAM,CAAC,CAC1BqG,WAAY,CAAC9G,EAAM9C,EAAE,CAAC,EAE1B,IAAI,EAE0BoM,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACtH,EAAO,CAAC,MAAE0H,CAAI,CAAE,GAAM,EAC5D/I,UAAW+I,EAAK/I,GAChBoI,QAASW,EAAKX,GAChB,GAEaY,CAFT,CAE0BL,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACtJ,EAAO,CAAC,MAAE0J,CAAI,CAAE,GAAM,EAC5D/I,UAAW+I,EAAK/I,GAClB,IAAI,sFC5IG,SAASiJ,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,CAEO,eAAe3E,EAAanC,CAAgB,EACjD,IAAMiH,EAAU,IAAIvF,YACdwF,EAAOtI,QAAQhC,GAAG,CAACiC,WAAW,EAAI,GAClCsI,EAAOF,EAAQtF,MAAM,CAAC3B,EAAWkH,GAEvC,OAAOE,KAAKC,OAAOC,YAAY,IAAI,IAAIC,WAD1B,MAAMhE,OAAOiE,MAAM,CAACC,MAAM,CAAC,UAAWN,KAErD,CAEO,eAAe/G,EAAgBJ,CAAgB,CAAEkC,CAAsB,EAE5E,OADa,MAAMC,EAAanC,KAChBkC,CAClB,8DChBO,IAAMhC,EAAawH,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACjC9H,SAAU8H,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,WACPC,GAAG,CAAC,GAAI,gBACRC,KAAK,CAAC,mBAAoB,uBAC1BC,MAAM,CAACC,GAAO,CAACA,EAAI7E,QAAQ,CAAC,KAAM,cACrClD,SAAU0H,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,eACZ,GAAE", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/emails/route.ts", "webpack://_N_E/./app/api/emails/route.ts?60cb", "webpack://_N_E/?bae9", "webpack://_N_E/./app/lib/apiKey.ts", "webpack://_N_E/./app/lib/avatar.ts", "webpack://_N_E/./app/lib/auth.ts", "webpack://_N_E/./app/lib/cursor.ts", "webpack://_N_E/./app/lib/db.ts", "webpack://_N_E/./app/lib/permissions.ts", "webpack://_N_E/./app/lib/schema.ts", "webpack://_N_E/./app/lib/utils.ts", "webpack://_N_E/./app/lib/validation.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { createDb } from \"@/lib/db\"\r\nimport { and, eq, gt, lt, or, sql } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport { emails } from \"@/lib/schema\"\r\nimport { encodeCursor, decodeCursor } from \"@/lib/cursor\"\r\nimport { getUserId } from \"@/lib/apiKey\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nconst PAGE_SIZE = 20\r\n\r\nexport async function GET(request: Request) {\r\n  const userId = await getUserId()\r\n\r\n  const { searchParams } = new URL(request.url)\r\n  const cursor = searchParams.get('cursor')\r\n  \r\n  const db = createDb()\r\n\r\n  try {\r\n    const baseConditions = and(\r\n      eq(emails.userId, userId!),\r\n      gt(emails.expiresAt, new Date())\r\n    )\r\n\r\n    const totalResult = await db.select({ count: sql<number>`count(*)` })\r\n      .from(emails)\r\n      .where(baseConditions)\r\n    const totalCount = Number(totalResult[0].count)\r\n\r\n    const conditions = [baseConditions]\r\n\r\n    if (cursor) {\r\n      const { timestamp, id } = decodeCursor(cursor)\r\n      conditions.push(\r\n        or(\r\n          lt(emails.createdAt, new Date(timestamp)),\r\n          and(\r\n            eq(emails.createdAt, new Date(timestamp)),\r\n            lt(emails.id, id)\r\n          )\r\n        )\r\n      )\r\n    }\r\n\r\n    const results = await db.query.emails.findMany({\r\n      where: and(...conditions),\r\n      orderBy: (emails, { desc }) => [\r\n        desc(emails.createdAt),\r\n        desc(emails.id)\r\n      ],\r\n      limit: PAGE_SIZE + 1\r\n    })\r\n    \r\n    const hasMore = results.length > PAGE_SIZE\r\n    const nextCursor = hasMore \r\n      ? encodeCursor(\r\n          results[PAGE_SIZE - 1].createdAt.getTime(),\r\n          results[PAGE_SIZE - 1].id\r\n        )\r\n      : null\r\n    const emailList = hasMore ? results.slice(0, PAGE_SIZE) : results\r\n\r\n    return NextResponse.json({ \r\n      emails: emailList,\r\n      nextCursor,\r\n      total: totalCount\r\n    })\r\n  } catch (error) {\r\n    console.error('Failed to fetch user emails:', error)\r\n    return NextResponse.json(\r\n      { error: \"Failed to fetch emails\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\emails\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/emails/route\",\n        pathname: \"/api/emails\",\n        filename: \"route\",\n        bundlePath: \"app/api/emails/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\emails\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Femails%2Froute&page=%2Fapi%2Femails%2Froute&pagePath=private-next-app-dir%2Fapi%2Femails%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fapi%2Femails%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/emails/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/emails/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/emails/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { createDb } from \"./db\"\r\nimport { api<PERSON><PERSON>s } from \"./schema\"\r\nimport { eq, and, gt } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport type { User } from \"next-auth\"\r\nimport { auth } from \"./auth\"\r\nimport { headers } from \"next/headers\"\r\n\r\nasync function getUserByApiKey(key: string): Promise<User | null> {\r\n  const db = createDb()\r\n  const apiKey = await db.query.apiKeys.findFirst({\r\n    where: and(\r\n      eq(apiKeys.key, key),\r\n      eq(apiKeys.enabled, true),\r\n      gt(apiKeys.expiresAt, new Date())\r\n    ),\r\n    with: {\r\n      user: true\r\n    }\r\n  })\r\n\r\n  if (!apiKey) return null\r\n\r\n  return apiKey.user\r\n}\r\n\r\nexport async function handleApiKeyAuth(apiKey: string, pathname: string) {\r\n  if (!pathname.startsWith('/api/emails') && !pathname.startsWith('/api/config')) {\r\n    return NextResponse.json(\r\n      { error: \"无权限查看\" },\r\n      { status: 403 }\r\n    )\r\n  }\r\n\r\n  const user = await getUserByApiKey(apiKey)\r\n  if (!user?.id) {\r\n    return NextResponse.json(\r\n      { error: \"无效的 API Key\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  const response = NextResponse.next()\r\n  response.headers.set(\"X-User-Id\", user.id)\r\n  return response\r\n}\r\n\r\nexport const getUserId = async () => {\r\n  const headersList = await headers()\r\n  const userId = headersList.get(\"X-User-Id\")\r\n  \r\n  if (userId) return userId\r\n\r\n  const session = await auth()\r\n\r\n  return session?.user.id\r\n}\r\n", "const COLORS = [\r\n  '#2196F3', // 蓝色\r\n  '#009688', // 青色\r\n  '#9C27B0', // 紫色\r\n  '#F44336', // 红色\r\n  '#673AB7', // 深紫色\r\n  '#3F51B5', // 靛蓝\r\n  '#4CAF50', // 绿色\r\n  '#FF5722', // 深橙\r\n  '#795548', // 棕色\r\n  '#607D8B', // 蓝灰\r\n];\r\n\r\nexport function generateAvatarUrl(name: string): string {\r\n  const initial = name[0].toUpperCase();\r\n  \r\n  const colorIndex = Array.from(name).reduce(\r\n    (acc, char) => acc + char.charCodeAt(0), 0\r\n  ) % COLORS.length;\r\n  \r\n  const backgroundColor = COLORS[colorIndex];\r\n  \r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\" width=\"100\" height=\"100\">\r\n      <rect width=\"100\" height=\"100\" fill=\"${backgroundColor}\"/>\r\n      <text \r\n        x=\"50%\" \r\n        y=\"50%\" \r\n        fill=\"white\" \r\n        font-family=\"system-ui, -apple-system, sans-serif\" \r\n        font-size=\"45\" \r\n        font-weight=\"500\"\r\n        text-anchor=\"middle\"\r\n        alignment-baseline=\"central\"\r\n        dominant-baseline=\"central\"\r\n        style=\"text-transform: uppercase\"\r\n      >\r\n        ${initial}\r\n      </text>\r\n    </svg>\r\n  `.trim();\r\n\r\n  const encoder = new TextEncoder();\r\n  const bytes = encoder.encode(svg);\r\n  const base64 = Buffer.from(bytes).toString('base64');\r\n  \r\n  return `data:image/svg+xml;base64,${base64}`;\r\n} ", "import NextAuth from \"next-auth\"\r\nimport GitHub from \"next-auth/providers/github\"\r\nimport { Dr<PERSON>zleAdapter } from \"@auth/drizzle-adapter\"\r\nimport { createDb, Db } from \"./db\"\r\nimport { accounts, users, roles, userRoles } from \"./schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { Permission, hasPermission, ROLES, Role } from \"./permissions\"\r\nimport CredentialsProvider from \"next-auth/providers/credentials\"\r\nimport { hashPassword, comparePassword } from \"@/lib/utils\"\r\nimport { authSchema } from \"@/lib/validation\"\r\nimport { generateAvatarUrl } from \"./avatar\"\r\nimport { getUserId } from \"./apiKey\"\r\n\r\nconst ROLE_DESCRIPTIONS: Record<Role, string> = {\r\n  [ROLES.EMPEROR]: \"皇帝（网站所有者）\",\r\n  [ROLES.DUKE]: \"公爵（超级用户）\",\r\n  [ROLES.KNIGHT]: \"骑士（高级用户）\",\r\n  [ROLES.CIVILIAN]: \"平民（普通用户）\",\r\n}\r\n\r\nconst getDefaultRole = async (): Promise<Role> => {\r\n  const defaultRole = await getRequestContext().env.SITE_CONFIG.get(\"DEFAULT_ROLE\")\r\n\r\n  if (\r\n    defaultRole === ROLES.DUKE ||\r\n    defaultRole === ROLES.KNIGHT ||\r\n    defaultRole === ROLES.CIVILIAN\r\n  ) {\r\n    return defaultRole as Role\r\n  }\r\n  \r\n  return ROLES.CIVILIAN\r\n}\r\n\r\nasync function findOrCreateRole(db: Db, roleName: Role) {\r\n  let role = await db.query.roles.findFirst({\r\n    where: eq(roles.name, roleName),\r\n  })\r\n\r\n  if (!role) {\r\n    const [newRole] = await db.insert(roles)\r\n      .values({\r\n        name: roleName,\r\n        description: ROLE_DESCRIPTIONS[roleName],\r\n      })\r\n      .returning()\r\n    role = newRole\r\n  }\r\n\r\n  return role\r\n}\r\n\r\nexport async function assignRoleToUser(db: Db, userId: string, roleId: string) {\r\n  await db.delete(userRoles)\r\n    .where(eq(userRoles.userId, userId))\r\n\r\n  await db.insert(userRoles)\r\n    .values({\r\n      userId,\r\n      roleId,\r\n    })\r\n}\r\n\r\nexport async function getUserRole(userId: string) {\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n  return userRoleRecords[0].role.name\r\n}\r\n\r\nexport async function checkPermission(permission: Permission) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) return false\r\n\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n\r\n  const userRoleNames = userRoleRecords.map(ur => ur.role.name)\r\n  return hasPermission(userRoleNames as Role[], permission)\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  auth,\r\n  signIn,\r\n  signOut\r\n} = NextAuth(() => ({\r\n  secret: process.env.AUTH_SECRET,\r\n  adapter: DrizzleAdapter(createDb(), {\r\n    usersTable: users,\r\n    accountsTable: accounts,\r\n  }),\r\n  providers: [\r\n    GitHub({\r\n      clientId: process.env.AUTH_GITHUB_ID,\r\n      clientSecret: process.env.AUTH_GITHUB_SECRET,\r\n    }),\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        username: { label: \"用户名\", type: \"text\", placeholder: \"请输入用户名\" },\r\n        password: { label: \"密码\", type: \"password\", placeholder: \"请输入密码\" },\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials) {\r\n          throw new Error(\"请输入用户名和密码\")\r\n        }\r\n\r\n        const { username, password } = credentials\r\n\r\n        try {\r\n          authSchema.parse({ username, password })\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        } catch (error) {\r\n          throw new Error(\"输入格式不正确\")\r\n        }\r\n\r\n        const db = createDb()\r\n\r\n        const user = await db.query.users.findFirst({\r\n          where: eq(users.username, username as string),\r\n        })\r\n\r\n        if (!user) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        const isValid = await comparePassword(password as string, user.password as string)\r\n        if (!isValid) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        return {\r\n          ...user,\r\n          password: undefined,\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  events: {\r\n    async signIn({ user }) {\r\n      if (!user.id) return\r\n\r\n      try {\r\n        const db = createDb()\r\n        const existingRole = await db.query.userRoles.findFirst({\r\n          where: eq(userRoles.userId, user.id),\r\n        })\r\n\r\n        if (existingRole) return\r\n\r\n        const defaultRole = await getDefaultRole()\r\n        const role = await findOrCreateRole(db, defaultRole)\r\n        await assignRoleToUser(db, user.id, role.id)\r\n      } catch (error) {\r\n        console.error('Error assigning role:', error)\r\n      }\r\n    },\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id\r\n        token.name = user.name || user.username\r\n        token.username = user.username\r\n        token.image = user.image || generateAvatarUrl(token.name as string)\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token && session.user) {\r\n        session.user.id = token.id as string\r\n        session.user.name = token.name as string\r\n        session.user.username = token.username as string\r\n        session.user.image = token.image as string\r\n\r\n        const db = createDb()\r\n        let userRoleRecords = await db.query.userRoles.findMany({\r\n          where: eq(userRoles.userId, session.user.id),\r\n          with: { role: true },\r\n        })\r\n  \r\n        if (!userRoleRecords.length) {\r\n          const defaultRole = await getDefaultRole()\r\n          const role = await findOrCreateRole(db, defaultRole)\r\n          await assignRoleToUser(db, session.user.id, role.id)\r\n          userRoleRecords = [{\r\n            userId: session.user.id,\r\n            roleId: role.id,\r\n            createdAt: new Date(),\r\n            role: role\r\n          }]\r\n        }\r\n  \r\n        session.user.roles = userRoleRecords.map(ur => ({\r\n          name: ur.role.name,\r\n        }))\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: \"jwt\",\r\n  },\r\n}))\r\n\r\nexport async function register(username: string, password: string) {\r\n  const db = createDb()\r\n  \r\n  const existing = await db.query.users.findFirst({\r\n    where: eq(users.username, username)\r\n  })\r\n\r\n  if (existing) {\r\n    throw new Error(\"用户名已存在\")\r\n  }\r\n\r\n  const hashedPassword = await hashPassword(password)\r\n  \r\n  const [user] = await db.insert(users)\r\n    .values({\r\n      username,\r\n      password: hashedPassword,\r\n    })\r\n    .returning()\r\n\r\n  return user\r\n}\r\n", "interface CursorData {\r\n  timestamp: number\r\n  id: string\r\n}\r\n\r\nexport function encodeCursor(timestamp: number, id: string): string {\r\n  const data: CursorData = { timestamp, id }\r\n  return Buffer.from(JSON.stringify(data)).toString('base64')\r\n}\r\n\r\nexport function decodeCursor(cursor: string): CursorData {\r\n  const data = JSON.parse(Buffer.from(cursor, 'base64').toString())\r\n  return data as CursorData\r\n} ", "import { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { drizzle } from \"drizzle-orm/d1\"\r\nimport * as schema from \"./schema\"\r\n\r\nexport const createDb = () => drizzle(getRequestContext().env.DB, { schema })\r\n\r\nexport type Db = ReturnType<typeof createDb>\r\n", "export const ROLES = {\r\n  EMPEROR: 'emperor',\r\n  <PERSON>U<PERSON>: 'duke',\r\n  <PERSON><PERSON><PERSON><PERSON>: 'knight',\r\n  CIVILIAN: 'civilian',\r\n} as const;\r\n\r\nexport type Role = typeof ROLES[keyof typeof ROLES];\r\n\r\nexport const PERMISSIONS = {\r\n  MANAGE_EMAIL: 'manage_email',\r\n  <PERSON><PERSON>GE_WEBHOOK: 'manage_webhook',\r\n  PROMOTE_USER: 'promote_user',\r\n  MANAGE_CONFIG: 'manage_config',\r\n  MANAGE_API_KEY: 'manage_api_key',\r\n} as const;\r\n\r\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\r\n\r\nexport const ROLE_PERMISSIONS: Record<Role, Permission[]> = {\r\n  [ROLES.EMPEROR]: Object.values(PERMISSIONS),\r\n  [ROLES.DUKE]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_API_KEY,\r\n  ],\r\n  [ROLES.KNIGHT]: [\r\n    PERMISSIONS.<PERSON>NAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n  ],\r\n  [ROLES.CIVILIAN]: [],\r\n} as const;\r\n\r\nexport function hasPermission(userRoles: Role[], permission: Permission): boolean {\r\n  return userRoles.some(role => ROLE_PERMISSIONS[role]?.includes(permission));\r\n} ", "import { integer, sqliteTable, text, primaryKey, uniqueIndex, index } from \"drizzle-orm/sqlite-core\"\r\nimport type { AdapterAccountType } from \"next-auth/adapters\"\r\nimport { relations } from 'drizzle-orm';\r\n\r\n// https://authjs.dev/getting-started/adapters/drizzle\r\nexport const users = sqliteTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: integer(\"emailVerified\", { mode: \"timestamp_ms\" }),\r\n  image: text(\"image\"),\r\n  username: text(\"username\").unique(),\r\n  password: text(\"password\"),\r\n})\r\nexport const accounts = sqliteTable(\r\n  \"account\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => users.id, { onDelete: \"cascade\" }),\r\n    type: text(\"type\").$type<AdapterAccountType>().notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n  },\r\n  (account) => ({\r\n    compoundKey: primaryKey({\r\n      columns: [account.provider, account.providerAccountId],\r\n    }),\r\n  })\r\n)\r\n\r\nexport const emails = sqliteTable(\"email\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  address: text(\"address\").notNull().unique(),\r\n  userId: text(\"userId\").references(() => users.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp_ms\" }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"email_expires_at_idx\").on(table.expiresAt),\r\n}))\r\n\r\nexport const messages = sqliteTable(\"message\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  emailId: text(\"emailId\")\r\n    .notNull()\r\n    .references(() => emails.id, { onDelete: \"cascade\" }),\r\n  fromAddress: text(\"from_address\").notNull(),\r\n  subject: text(\"subject\").notNull(),\r\n  content: text(\"content\").notNull(),\r\n  html: text(\"html\"),\r\n  receivedAt: integer(\"received_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  emailIdIdx: index(\"message_email_id_idx\").on(table.emailId),\r\n}))\r\n\r\nexport const webhooks = sqliteTable('webhook', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id')\r\n    .notNull()\r\n    .references(() => users.id, { onDelete: \"cascade\" }),\r\n  url: text('url').notNull(),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n})\r\n\r\nexport const roles = sqliteTable(\"role\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\").notNull(),\r\n  description: text(\"description\"),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n});\r\n\r\nexport const userRoles = sqliteTable(\"user_role\", {\r\n  userId: text(\"user_id\").notNull().references(() => users.id, { onDelete: \"cascade\" }),\r\n  roleId: text(\"role_id\").notNull().references(() => roles.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  pk: primaryKey({ columns: [table.userId, table.roleId] }),\r\n}));\r\n\r\nexport const apiKeys = sqliteTable('api_keys', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id),\r\n  name: text('name').notNull(),\r\n  key: text('key').notNull().unique(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n}, (table) => ({\r\n  nameUserIdUnique: uniqueIndex('name_user_id_unique').on(table.name, table.userId)\r\n}));\r\n\r\nexport const revoked_credentials = sqliteTable('revoked_credential', {\r\n  jti: text('jti').primaryKey(),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp_ms' }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"revoked_expires_at_idx\").on(table.expiresAt),\r\n}));\r\n\r\nexport const apiKeysRelations = relations(apiKeys, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [apiKeys.userId],\r\n    references: [users.id],\r\n  }),\r\n}));\r\n\r\nexport const userRolesRelations = relations(userRoles, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userRoles.userId],\r\n    references: [users.id],\r\n  }),\r\n  role: one(roles, {\r\n    fields: [userRoles.roleId],\r\n    references: [roles.id],\r\n  }),\r\n}));\r\n\r\nexport const usersRelations = relations(users, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n  apiKeys: many(apiKeys),\r\n}));\r\n\r\nexport const rolesRelations = relations(roles, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n}));", "import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport async function hashPassword(password: string): Promise<string> {\r\n  const encoder = new TextEncoder()\r\n  const salt = process.env.AUTH_SECRET || ''\r\n  const data = encoder.encode(password + salt)\r\n  const hash = await crypto.subtle.digest('SHA-256', data)\r\n  return btoa(String.fromCharCode(...new Uint8Array(hash)))\r\n}\r\n\r\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\r\n  const hash = await hashPassword(password)\r\n  return hash === hashedPassword\r\n}", "import { z } from \"zod\"\r\n\r\nexport const authSchema = z.object({\r\n  username: z.string()\r\n    .min(1, \"用户名不能为空\")\r\n    .max(20, \"用户名不能超过20个字符\")\r\n    .regex(/^[a-zA-Z0-9_-]+$/, \"用户名只能包含字母、数字、下划线和横杠\")\r\n    .refine(val => !val.includes('@'), \"用户名不能是邮箱格式\"),\r\n  password: z.string()\r\n    .min(8, \"密码长度必须大于等于8位\")\r\n})\r\n\r\nexport type AuthSchema = z.infer<typeof authSchema>"], "names": ["runtime", "GET", "request", "userId", "getUserId", "searchParams", "URL", "url", "cursor", "get", "db", "createDb", "baseConditions", "and", "eq", "emails", "gt", "expiresAt", "Date", "totalResult", "select", "count", "sql", "from", "where", "totalCount", "Number", "conditions", "timestamp", "id", "decodeCursor", "push", "or", "lt", "createdAt", "results", "query", "find<PERSON>any", "orderBy", "desc", "limit", "PAGE_SIZE", "hasMore", "length", "nextCursor", "encodeCursor", "getTime", "emailList", "slice", "NextResponse", "json", "total", "error", "console", "status", "headersList", "headers", "session", "auth", "user", "COLORS", "ROLE_DESCRIPTIONS", "ROLES", "EMPEROR", "DUKE", "KNIGHT", "CIVILIAN", "getDefaultRole", "defaultRole", "getRequestContext", "env", "SITE_CONFIG", "findOrCreateRole", "<PERSON><PERSON><PERSON>", "role", "roles", "<PERSON><PERSON><PERSON><PERSON>", "name", "newRole", "insert", "values", "description", "returning", "assignRoleToUser", "roleId", "delete", "userRoles", "getUserRole", "userRoleRecords", "with", "checkPermission", "permission", "userRoleNames", "map", "ur", "hasPermission", "handlers", "POST", "signIn", "signOut", "NextAuth", "secret", "process", "AUTH_SECRET", "adapter", "DrizzleAdapter", "usersTable", "users", "accountsTable", "accounts", "providers", "GitHub", "clientId", "AUTH_GITHUB_ID", "clientSecret", "AUTH_GITHUB_SECRET", "CredentialsProvider", "credentials", "username", "label", "type", "placeholder", "password", "authorize", "authSchema", "parse", "comparePassword", "Error", "undefined", "events", "existingRole", "callbacks", "jwt", "token", "image", "generateAvatarUrl", "initial", "toUpperCase", "colorIndex", "Array", "reduce", "acc", "char", "charCodeAt", "backgroundColor", "svg", "trim", "bytes", "TextEncoder", "encode", "base64", "<PERSON><PERSON><PERSON>", "toString", "strategy", "register", "existing", "hashedPassword", "hashPassword", "JSON", "stringify", "drizzle", "DB", "schema", "MANAGE_EMAIL", "MANAGE_WEBHOOK", "PROMOTE_USER", "MANAGE_CONFIG", "MANAGE_API_KEY", "Object", "PERMISSIONS", "some", "ROLE_PERMISSIONS", "includes", "sqliteTable", "text", "<PERSON><PERSON><PERSON>", "$defaultFn", "crypto", "randomUUID", "email", "unique", "emailVerified", "integer", "mode", "notNull", "references", "onDelete", "$type", "provider", "providerAccountId", "refresh_token", "access_token", "expires_at", "token_type", "scope", "id_token", "session_state", "<PERSON><PERSON><PERSON>", "columns", "account", "address", "expiresAtIdx", "index", "on", "table", "messages", "emailId", "fromAddress", "subject", "content", "html", "receivedAt", "emailIdIdx", "webhooks", "enabled", "default", "updatedAt", "pk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "nameUserIdUnique", "uniqueIndex", "revoked_credentials", "jti", "apiKeysRelations", "relations", "one", "fields", "userRolesRelations", "many", "rolesRelations", "cn", "inputs", "twMerge", "clsx", "encoder", "salt", "data", "btoa", "String", "fromCharCode", "Uint8Array", "subtle", "digest", "z", "min", "max", "regex", "refine", "val"], "sourceRoot": "", "ignoreList": []}