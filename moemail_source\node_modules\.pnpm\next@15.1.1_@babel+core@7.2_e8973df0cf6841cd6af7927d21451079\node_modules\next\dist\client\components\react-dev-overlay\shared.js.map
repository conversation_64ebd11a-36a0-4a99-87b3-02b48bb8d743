{"version": 3, "sources": ["../../../../src/client/components/react-dev-overlay/shared.ts"], "sourcesContent": ["import { useReducer } from 'react'\n\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport type { VersionInfo } from '../../../server/dev/parse-version-info'\nimport type { SupportedErrorEvent } from './internal/container/Errors'\nimport type { ComponentStackFrame } from './internal/helpers/parse-component-stack'\nimport type { DebugInfo } from './types'\n\ntype FastRefreshState =\n  /** No refresh in progress. */\n  | { type: 'idle' }\n  /** The refresh process has been triggered, but the new code has not been executed yet. */\n  | { type: 'pending'; errors: SupportedErrorEvent[] }\n\nexport interface OverlayState {\n  nextId: number\n  buildError: string | null\n  errors: SupportedErrorEvent[]\n  refreshState: FastRefreshState\n  rootLayoutMissingTags: typeof window.__next_root_layout_missing_tags\n  versionInfo: VersionInfo\n  notFound: boolean\n  staticIndicator: boolean\n  debugInfo: DebugInfo | undefined\n}\n\nexport const ACTION_STATIC_INDICATOR = 'static-indicator'\nexport const ACTION_BUILD_OK = 'build-ok'\nexport const ACTION_BUILD_ERROR = 'build-error'\nexport const ACTION_BEFORE_REFRESH = 'before-fast-refresh'\nexport const ACTION_REFRESH = 'fast-refresh'\nexport const ACTION_VERSION_INFO = 'version-info'\nexport const ACTION_UNHANDLED_ERROR = 'unhandled-error'\nexport const ACTION_UNHANDLED_REJECTION = 'unhandled-rejection'\nexport const ACTION_DEBUG_INFO = 'debug-info'\n\ninterface StaticIndicatorAction {\n  type: typeof ACTION_STATIC_INDICATOR\n  staticIndicator: boolean\n}\n\ninterface BuildOkAction {\n  type: typeof ACTION_BUILD_OK\n}\ninterface BuildErrorAction {\n  type: typeof ACTION_BUILD_ERROR\n  message: string\n}\ninterface BeforeFastRefreshAction {\n  type: typeof ACTION_BEFORE_REFRESH\n}\ninterface FastRefreshAction {\n  type: typeof ACTION_REFRESH\n}\n\nexport interface UnhandledErrorAction {\n  type: typeof ACTION_UNHANDLED_ERROR\n  reason: Error\n  frames: StackFrame[]\n  componentStackFrames?: ComponentStackFrame[]\n  warning?: [string, string, string]\n}\nexport interface UnhandledRejectionAction {\n  type: typeof ACTION_UNHANDLED_REJECTION\n  reason: Error\n  frames: StackFrame[]\n}\n\nexport interface DebugInfoAction {\n  type: typeof ACTION_DEBUG_INFO\n  debugInfo: any\n}\n\ninterface VersionInfoAction {\n  type: typeof ACTION_VERSION_INFO\n  versionInfo: VersionInfo\n}\n\nexport type BusEvent =\n  | BuildOkAction\n  | BuildErrorAction\n  | BeforeFastRefreshAction\n  | FastRefreshAction\n  | UnhandledErrorAction\n  | UnhandledRejectionAction\n  | VersionInfoAction\n  | StaticIndicatorAction\n  | DebugInfoAction\n\nfunction pushErrorFilterDuplicates(\n  errors: SupportedErrorEvent[],\n  err: SupportedErrorEvent\n): SupportedErrorEvent[] {\n  return [\n    ...errors.filter((e) => {\n      // Filter out duplicate errors\n      return e.event.reason !== err.event.reason\n    }),\n    err,\n  ]\n}\n\nexport const INITIAL_OVERLAY_STATE: OverlayState = {\n  nextId: 1,\n  buildError: null,\n  errors: [],\n  notFound: false,\n  staticIndicator: false,\n  refreshState: { type: 'idle' },\n  rootLayoutMissingTags: [],\n  versionInfo: { installed: '0.0.0', staleness: 'unknown' },\n  debugInfo: undefined,\n}\n\nexport function useErrorOverlayReducer() {\n  return useReducer((_state: OverlayState, action: BusEvent): OverlayState => {\n    switch (action.type) {\n      case ACTION_DEBUG_INFO: {\n        return { ..._state, debugInfo: action.debugInfo }\n      }\n      case ACTION_STATIC_INDICATOR: {\n        return { ..._state, staticIndicator: action.staticIndicator }\n      }\n      case ACTION_BUILD_OK: {\n        return { ..._state, buildError: null }\n      }\n      case ACTION_BUILD_ERROR: {\n        return { ..._state, buildError: action.message }\n      }\n      case ACTION_BEFORE_REFRESH: {\n        return { ..._state, refreshState: { type: 'pending', errors: [] } }\n      }\n      case ACTION_REFRESH: {\n        return {\n          ..._state,\n          buildError: null,\n          errors:\n            // Errors can come in during updates. In this case, UNHANDLED_ERROR\n            // and UNHANDLED_REJECTION events might be dispatched between the\n            // BEFORE_REFRESH and the REFRESH event. We want to keep those errors\n            // around until the next refresh. Otherwise we run into a race\n            // condition where those errors would be cleared on refresh completion\n            // before they can be displayed.\n            _state.refreshState.type === 'pending'\n              ? _state.refreshState.errors\n              : [],\n          refreshState: { type: 'idle' },\n        }\n      }\n      case ACTION_UNHANDLED_ERROR:\n      case ACTION_UNHANDLED_REJECTION: {\n        switch (_state.refreshState.type) {\n          case 'idle': {\n            return {\n              ..._state,\n              nextId: _state.nextId + 1,\n              errors: pushErrorFilterDuplicates(_state.errors, {\n                id: _state.nextId,\n                event: action,\n              }),\n            }\n          }\n          case 'pending': {\n            return {\n              ..._state,\n              nextId: _state.nextId + 1,\n              refreshState: {\n                ..._state.refreshState,\n                errors: pushErrorFilterDuplicates(_state.refreshState.errors, {\n                  id: _state.nextId,\n                  event: action,\n                }),\n              },\n            }\n          }\n          default:\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            const _: never = _state.refreshState\n            return _state\n        }\n      }\n      case ACTION_VERSION_INFO: {\n        return { ..._state, versionInfo: action.versionInfo }\n      }\n      default: {\n        return _state\n      }\n    }\n  }, INITIAL_OVERLAY_STATE)\n}\n\nexport const REACT_REFRESH_FULL_RELOAD_FROM_ERROR =\n  '[Fast Refresh] performing full reload because your application had an unrecoverable error'\n"], "names": ["ACTION_BEFORE_REFRESH", "ACTION_BUILD_ERROR", "ACTION_BUILD_OK", "ACTION_DEBUG_INFO", "ACTION_REFRESH", "ACTION_STATIC_INDICATOR", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "ACTION_VERSION_INFO", "INITIAL_OVERLAY_STATE", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "useErrorOverlayReducer", "pushErrorFilterDuplicates", "errors", "err", "filter", "e", "event", "reason", "nextId", "buildError", "notFound", "staticIndicator", "refreshState", "type", "rootLayoutMissingTags", "versionInfo", "installed", "staleness", "debugInfo", "undefined", "useReducer", "_state", "action", "message", "id", "_"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IA6BaA,qBAAqB;eAArBA;;IADAC,kBAAkB;eAAlBA;;IADAC,eAAe;eAAfA;;IAOAC,iBAAiB;eAAjBA;;IAJAC,cAAc;eAAdA;;IAJAC,uBAAuB;eAAvBA;;IAMAC,sBAAsB;eAAtBA;;IACAC,0BAA0B;eAA1BA;;IAFAC,mBAAmB;eAAnBA;;IAuEAC,qBAAqB;eAArBA;;IAyFAC,oCAAoC;eAApCA;;IA7EGC,sBAAsB;eAAtBA;;;uBAlHW;AA0BpB,MAAMN,0BAA0B;AAChC,MAAMH,kBAAkB;AACxB,MAAMD,qBAAqB;AAC3B,MAAMD,wBAAwB;AAC9B,MAAMI,iBAAiB;AACvB,MAAMI,sBAAsB;AAC5B,MAAMF,yBAAyB;AAC/B,MAAMC,6BAA6B;AACnC,MAAMJ,oBAAoB;AAuDjC,SAASS,0BACPC,MAA6B,EAC7BC,GAAwB;IAExB,OAAO;WACFD,OAAOE,MAAM,CAAC,CAACC;YAChB,8BAA8B;YAC9B,OAAOA,EAAEC,KAAK,CAACC,MAAM,KAAKJ,IAAIG,KAAK,CAACC,MAAM;QAC5C;QACAJ;KACD;AACH;AAEO,MAAML,wBAAsC;IACjDU,QAAQ;IACRC,YAAY;IACZP,QAAQ,EAAE;IACVQ,UAAU;IACVC,iBAAiB;IACjBC,cAAc;QAAEC,MAAM;IAAO;IAC7BC,uBAAuB,EAAE;IACzBC,aAAa;QAAEC,WAAW;QAASC,WAAW;IAAU;IACxDC,WAAWC;AACb;AAEO,SAASnB;IACd,OAAOoB,IAAAA,iBAAU,EAAC,CAACC,QAAsBC;QACvC,OAAQA,OAAOT,IAAI;YACjB,KAAKrB;gBAAmB;oBACtB,OAAO;wBAAE,GAAG6B,MAAM;wBAAEH,WAAWI,OAAOJ,SAAS;oBAAC;gBAClD;YACA,KAAKxB;gBAAyB;oBAC5B,OAAO;wBAAE,GAAG2B,MAAM;wBAAEV,iBAAiBW,OAAOX,eAAe;oBAAC;gBAC9D;YACA,KAAKpB;gBAAiB;oBACpB,OAAO;wBAAE,GAAG8B,MAAM;wBAAEZ,YAAY;oBAAK;gBACvC;YACA,KAAKnB;gBAAoB;oBACvB,OAAO;wBAAE,GAAG+B,MAAM;wBAAEZ,YAAYa,OAAOC,OAAO;oBAAC;gBACjD;YACA,KAAKlC;gBAAuB;oBAC1B,OAAO;wBAAE,GAAGgC,MAAM;wBAAET,cAAc;4BAAEC,MAAM;4BAAWX,QAAQ,EAAE;wBAAC;oBAAE;gBACpE;YACA,KAAKT;gBAAgB;oBACnB,OAAO;wBACL,GAAG4B,MAAM;wBACTZ,YAAY;wBACZP,QACE,mEAAmE;wBACnE,iEAAiE;wBACjE,qEAAqE;wBACrE,8DAA8D;wBAC9D,sEAAsE;wBACtE,gCAAgC;wBAChCmB,OAAOT,YAAY,CAACC,IAAI,KAAK,YACzBQ,OAAOT,YAAY,CAACV,MAAM,GAC1B,EAAE;wBACRU,cAAc;4BAAEC,MAAM;wBAAO;oBAC/B;gBACF;YACA,KAAKlB;YACL,KAAKC;gBAA4B;oBAC/B,OAAQyB,OAAOT,YAAY,CAACC,IAAI;wBAC9B,KAAK;4BAAQ;gCACX,OAAO;oCACL,GAAGQ,MAAM;oCACTb,QAAQa,OAAOb,MAAM,GAAG;oCACxBN,QAAQD,0BAA0BoB,OAAOnB,MAAM,EAAE;wCAC/CsB,IAAIH,OAAOb,MAAM;wCACjBF,OAAOgB;oCACT;gCACF;4BACF;wBACA,KAAK;4BAAW;gCACd,OAAO;oCACL,GAAGD,MAAM;oCACTb,QAAQa,OAAOb,MAAM,GAAG;oCACxBI,cAAc;wCACZ,GAAGS,OAAOT,YAAY;wCACtBV,QAAQD,0BAA0BoB,OAAOT,YAAY,CAACV,MAAM,EAAE;4CAC5DsB,IAAIH,OAAOb,MAAM;4CACjBF,OAAOgB;wCACT;oCACF;gCACF;4BACF;wBACA;4BACE,6DAA6D;4BAC7D,MAAMG,IAAWJ,OAAOT,YAAY;4BACpC,OAAOS;oBACX;gBACF;YACA,KAAKxB;gBAAqB;oBACxB,OAAO;wBAAE,GAAGwB,MAAM;wBAAEN,aAAaO,OAAOP,WAAW;oBAAC;gBACtD;YACA;gBAAS;oBACP,OAAOM;gBACT;QACF;IACF,GAAGvB;AACL;AAEO,MAAMC,uCACX"}