(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[254],{5521:e=>{"use strict";e.exports=require("node:async_hooks")},5356:e=>{"use strict";e.exports=require("node:buffer")},2728:(e,t,a)=>{"use strict";a.r(t),a.d(t,{ComponentMod:()=>P,default:()=>A});var s,r={};a.r(r),a.d(r,{ClientPageRoot:()=>u.Fy,ClientSegmentRoot:()=>u.pl,GlobalError:()=>m.default,HTTPAccessFallbackBoundary:()=>u.nQ,LayoutRouter:()=>u.C3,MetadataBoundary:()=>u.qB,OutletBoundary:()=>u.Cr,Postpone:()=>u.fK,RenderFromTemplateContext:()=>u.IY,ViewportBoundary:()=>u.PX,__next_app__:()=>x,actionAsyncStorage:()=>u.sc,collectSegmentData:()=>u.Uy,createMetadataComponents:()=>u.IB,createPrerenderParamsForClientSegment:()=>u.lu,createPrerenderSearchParamsForClientPage:()=>u.jO,createServerParamsForMetadata:()=>u.Kx,createServerParamsForServerSegment:()=>u.LV,createServerSearchParamsForMetadata:()=>u.mh,createServerSearchParamsForServerPage:()=>u.Vv,createTemporaryReferenceSet:()=>u.XI,decodeAction:()=>u.Jk,decodeFormState:()=>u.Am,decodeReply:()=>u.X$,pages:()=>f,patchFetch:()=>u.V5,preconnect:()=>u.kZ,preloadFont:()=>u.PY,preloadStyle:()=>u.vI,prerender:()=>u.CR,renderToReadableStream:()=>u.WK,routeModule:()=>h,serverHooks:()=>u.ge,taintObjectReference:()=>u.N2,tree:()=>p,workAsyncStorage:()=>u.J_,workUnitAsyncStorage:()=>u.FP}),a(9569);var i=a(981),n=a(9040),l=a(7598),o=a(5717),c=a(3307),d=a(6292),m=a(3094),u=a(6953);let p=["",{children:["moe",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,4730)),"F:\\CODE\\Project\\Mail\\moemail_source\\app\\moe\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4452))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,189)),"F:\\CODE\\Project\\Mail\\moemail_source\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,4962)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.bind(a,1648)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.bind(a,6408)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4452))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],f=["F:\\CODE\\Project\\Mail\\moemail_source\\app\\moe\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},h=new c.AppPageRouteModule({definition:{kind:d.A.APP_PAGE,page:"/moe/page",pathname:"/moe",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});var v=a(5711),g=a(4194),j=a(7285);globalThis.__nextCacheHandlers||(globalThis.__nextCacheHandlers={});let y=e=>e?JSON.parse(e):void 0,N=self.__BUILD_MANIFEST,b=y(self.__REACT_LOADABLE_MANIFEST),w=null==(s=self.__RSC_MANIFEST)?void 0:s["/moe/page"],C=y(self.__RSC_SERVER_MANIFEST),S=y(self.__NEXT_FONT_MANIFEST),R=y(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];w&&C&&(0,g.fQ)({page:"/moe/page",clientReferenceManifest:w,serverActionsManifest:C,serverModuleMap:(0,j.e)({serverActionsManifest:C})});let k=(0,n.R)({pagesType:v.g.APP,dev:!1,page:"/moe/page",appMod:null,pageMod:r,errorMod:null,error500Mod:null,Document:null,buildManifest:N,renderToHTML:o.W,reactLoadableManifest:b,clientReferenceManifest:w,serverActionsManifest:C,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\Mail\\moemail_source",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\Mail\\moemail_source"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\Mail\\moemail_source\\next.config.ts",configFileName:"next.config.ts"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:S,incrementalCacheHandler:null,interceptionRouteRewrites:R}),P=r;function A(e){return(0,i.O)({...e,IncrementalCache:l.N,handler:k})}},2179:(e,t,a)=>{Promise.resolve().then(a.bind(a,2483)),Promise.resolve().then(a.bind(a,7204)),Promise.resolve().then(a.bind(a,6628)),Promise.resolve().then(a.bind(a,38)),Promise.resolve().then(a.bind(a,9134))},6251:(e,t,a)=>{Promise.resolve().then(a.bind(a,2396)),Promise.resolve().then(a.bind(a,8435)),Promise.resolve().then(a.bind(a,1840)),Promise.resolve().then(a.bind(a,5354)),Promise.resolve().then(a.bind(a,4794))},8435:(e,t,a)=>{"use strict";a.d(t,{ThreeColumnLayout:()=>eH});var s=a(9796),r=a(2992),i=a(4123),n=a(1511),l=a(5935),o=a(9851),c=a(4383),d=a(297);let m=(0,d.A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var u=a(758),p=a(120);let f=(e=21)=>{let t="",a=crypto.getRandomValues(new Uint8Array(e|=0));for(;e--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&a[e]];return t};var x=a(173),h=a(3359),v=a(6442),g=a(7608),j=a(8743),y=a(3919),N=a(6794),b=a(5453),w=a(3263),C=a(8073),S="Radio",[R,k]=(0,v.A)(S),[P,A]=R(S),E=r.forwardRef((e,t)=>{let{__scopeRadio:a,name:i,checked:n=!1,required:l,disabled:o,value:c="on",onCheck:d,form:m,...u}=e,[p,f]=r.useState(null),v=(0,h.s)(t,e=>f(e)),j=r.useRef(!1),y=!p||m||!!p.closest("form");return(0,s.jsxs)(P,{scope:a,checked:n,disabled:o,children:[(0,s.jsx)(g.sG.button,{type:"button",role:"radio","aria-checked":n,"data-state":_(n),"data-disabled":o?"":void 0,disabled:o,value:c,...u,ref:v,onClick:(0,x.m)(e.onClick,e=>{n||d?.(),y&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),y&&(0,s.jsx)(I,{control:p,bubbles:!j.current,name:i,value:c,checked:n,required:l,disabled:o,form:m,style:{transform:"translateX(-100%)"}})]})});E.displayName=S;var D="RadioIndicator",T=r.forwardRef((e,t)=>{let{__scopeRadio:a,forceMount:r,...i}=e,n=A(D,a);return(0,s.jsx)(C.C,{present:r||n.checked,children:(0,s.jsx)(g.sG.span,{"data-state":_(n.checked),"data-disabled":n.disabled?"":void 0,...i,ref:t})})});T.displayName=D;var I=r.forwardRef(({__scopeRadio:e,control:t,checked:a,bubbles:i=!0,...n},l)=>{let o=r.useRef(null),c=(0,h.s)(o,l),d=(0,w.Z)(a),m=(0,b.X)(t);return r.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==a&&t){let s=new Event("click",{bubbles:i});t.call(e,a),e.dispatchEvent(s)}},[d,a,i]),(0,s.jsx)(g.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:a,...n,tabIndex:-1,ref:c,style:{...n.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function _(e){return e?"checked":"unchecked"}I.displayName="RadioBubbleInput";var M=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],F="RadioGroup",[L,O]=(0,v.A)(F,[j.RG,k]),q=(0,j.RG)(),z=k(),[G,$]=L(F),B=r.forwardRef((e,t)=>{let{__scopeRadioGroup:a,name:r,defaultValue:i,value:n,required:l=!1,disabled:o=!1,orientation:c,dir:d,loop:m=!0,onValueChange:u,...p}=e,f=q(a),x=(0,N.jH)(d),[h,v]=(0,y.i)({prop:n,defaultProp:i??null,onChange:u,caller:F});return(0,s.jsx)(G,{scope:a,name:r,required:l,disabled:o,value:h,onValueChange:v,children:(0,s.jsx)(j.bL,{asChild:!0,...f,orientation:c,dir:x,loop:m,children:(0,s.jsx)(g.sG.div,{role:"radiogroup","aria-required":l,"aria-orientation":c,"data-disabled":o?"":void 0,dir:x,...p,ref:t})})})});B.displayName=F;var H="RadioGroupItem",V=r.forwardRef((e,t)=>{let{__scopeRadioGroup:a,disabled:i,...n}=e,l=$(H,a),o=l.disabled||i,c=q(a),d=z(a),m=r.useRef(null),u=(0,h.s)(t,m),p=l.value===n.value,f=r.useRef(!1);return r.useEffect(()=>{let e=e=>{M.includes(e.key)&&(f.current=!0)},t=()=>f.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,s.jsx)(j.q7,{asChild:!0,...c,focusable:!o,active:p,children:(0,s.jsx)(E,{disabled:o,required:l.required,checked:p,...d,...n,name:l.name,ref:u,onCheck:()=>l.onValueChange(n.value),onKeyDown:(0,x.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,x.m)(n.onFocus,()=>{f.current&&m.current?.click()})})})});V.displayName=H;var U=r.forwardRef((e,t)=>{let{__scopeRadioGroup:a,...r}=e,i=z(a);return(0,s.jsx)(T,{...i,...r,ref:t})});U.displayName="RadioGroupIndicator";let K=(0,d.A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var J=a(2304);let Y=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(B,{className:(0,J.cn)("grid gap-2",e),...t,ref:a}));Y.displayName=B.displayName;let X=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(V,{ref:a,className:(0,J.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(U,{className:"flex items-center justify-center",children:(0,s.jsx)(K,{className:"h-2.5 w-2.5 fill-current text-current"})})}));X.displayName=V.displayName;var W=a(1673),Z=a(1043);let Q=[{label:"1小时",value:36e5},{label:"24小时",value:864e5},{label:"3天",value:2592e5},{label:"永久",value:0}];var ee=a(4399),et=a(5984);function ea({onEmailCreated:e}){let{config:t}=(0,et.U)(),[a,i]=(0,r.useState)(!1),[d,x]=(0,r.useState)(!1),[h,v]=(0,r.useState)(""),[g,j]=(0,r.useState)(""),[y,N]=(0,r.useState)(Q[1].value.toString()),{toast:b}=(0,p.dj)(),{copyToClipboard:w}=(0,ee.T)(),C=async()=>{if(!h.trim()){b({title:"错误",description:"请输入邮箱名",variant:"destructive"});return}x(!0);try{let t=await fetch("/api/emails/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:h,domain:g,expiryTime:parseInt(y)})});if(!t.ok){let e=await t.json();b({title:"错误",description:e.error,variant:"destructive"});return}b({title:"成功",description:"已创建新的临时邮箱"}),e(),i(!1),v("")}catch{b({title:"错误",description:"创建邮箱失败",variant:"destructive"})}finally{x(!1)}};return(0,s.jsxs)(o.lG,{open:a,onOpenChange:i,children:[(0,s.jsx)(o.zM,{asChild:!0,children:(0,s.jsxs)(n.$,{className:"gap-2",children:[(0,s.jsx)(c.A,{className:"w-4 h-4"}),"创建新邮箱"]})}),(0,s.jsxs)(o.Cf,{children:[(0,s.jsx)(o.c7,{children:(0,s.jsx)(o.L3,{children:"创建新的临时邮箱"})}),(0,s.jsxs)("div",{className:"space-y-4 py-4",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(l.p,{value:h,onChange:e=>v(e.target.value),placeholder:"输入邮箱名",className:"flex-1"}),(t?.emailDomainsArray?.length??0)>1&&(0,s.jsxs)(Z.l6,{value:g,onValueChange:j,children:[(0,s.jsx)(Z.bq,{className:"w-[180px]",children:(0,s.jsx)(Z.yv,{})}),(0,s.jsx)(Z.gC,{children:t?.emailDomainsArray?.map(e=>s.jsxs(Z.eb,{value:e,children:["@",e]},e))})]}),(0,s.jsx)(n.$,{variant:"outline",size:"icon",onClick:()=>v(f(8)),type:"button",children:(0,s.jsx)(m,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(W.J,{className:"shrink-0 text-muted-foreground",children:"过期时间"}),(0,s.jsx)(Y,{value:y,onValueChange:N,className:"flex gap-6",children:Q.map(e=>(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(X,{value:e.value.toString(),id:e.value.toString()}),(0,s.jsx)(W.J,{htmlFor:e.value.toString(),className:"cursor-pointer text-sm",children:e.label})]},e.value))})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,s.jsx)("span",{className:"shrink-0",children:"完整邮箱地址将为:"}),h?(0,s.jsxs)("div",{className:"flex items-center gap-2 min-w-0",children:[(0,s.jsx)("span",{className:"truncate",children:`${h}@${g}`}),(0,s.jsx)("div",{className:"shrink-0 cursor-pointer hover:text-primary transition-colors",onClick:()=>{w(`${h}@${g}`)},children:(0,s.jsx)(u.A,{className:"size-4"})})]}):(0,s.jsx)("span",{className:"text-gray-400",children:"..."})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:()=>i(!1),disabled:d,children:"取消"}),(0,s.jsx)(n.$,{onClick:C,disabled:d,children:d?"创建中...":"创建"})]})]})]})}var es=a(5139),er=a(1009);function ei(e,t){let a=(0,r.useRef)(Date.now());return(0,r.useCallback)((...s)=>{let r=Date.now();r-a.current>=t&&(e(...s),a.current=r)},[e,t])}var en=a(5746),el=a(5360),eo=a(6850),ec="AlertDialog",[ed,em]=(0,v.A)(ec,[el.Hs]),eu=(0,el.Hs)(),ep=e=>{let{__scopeAlertDialog:t,...a}=e,r=eu(t);return(0,s.jsx)(el.bL,{...r,...a,modal:!0})};ep.displayName=ec,r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,i=eu(a);return(0,s.jsx)(el.l9,{...i,...r,ref:t})}).displayName="AlertDialogTrigger";var ef=e=>{let{__scopeAlertDialog:t,...a}=e,r=eu(t);return(0,s.jsx)(el.ZL,{...r,...a})};ef.displayName="AlertDialogPortal";var ex=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,i=eu(a);return(0,s.jsx)(el.hJ,{...i,...r,ref:t})});ex.displayName="AlertDialogOverlay";var eh="AlertDialogContent",[ev,eg]=ed(eh),ej=(0,eo.Dc)("AlertDialogContent"),ey=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,children:i,...n}=e,l=eu(a),o=r.useRef(null),c=(0,h.s)(t,o),d=r.useRef(null);return(0,s.jsx)(el.G$,{contentName:eh,titleName:eN,docsSlug:"alert-dialog",children:(0,s.jsx)(ev,{scope:a,cancelRef:d,children:(0,s.jsxs)(el.UC,{role:"alertdialog",...l,...n,ref:c,onOpenAutoFocus:(0,x.m)(n.onOpenAutoFocus,e=>{e.preventDefault(),d.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(ej,{children:i}),(0,s.jsx)(eP,{contentRef:o})]})})})});ey.displayName=eh;var eN="AlertDialogTitle",eb=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,i=eu(a);return(0,s.jsx)(el.hE,{...i,...r,ref:t})});eb.displayName=eN;var ew="AlertDialogDescription",eC=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,i=eu(a);return(0,s.jsx)(el.VY,{...i,...r,ref:t})});eC.displayName=ew;var eS=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,i=eu(a);return(0,s.jsx)(el.bm,{...i,...r,ref:t})});eS.displayName="AlertDialogAction";var eR="AlertDialogCancel",ek=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,{cancelRef:i}=eg(eR,a),n=eu(a),l=(0,h.s)(t,i);return(0,s.jsx)(el.bm,{...n,...r,ref:l})});ek.displayName=eR;var eP=({contentRef:e})=>{let t=`\`${eh}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${eh}\` by passing a \`${ew}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${eh}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return r.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null};let eA=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(ex,{className:(0,J.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:a}));eA.displayName=ex.displayName;let eE=r.forwardRef(({className:e,...t},a)=>(0,s.jsxs)(ef,{children:[(0,s.jsx)(eA,{}),(0,s.jsx)(ey,{ref:a,className:(0,J.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));eE.displayName=ey.displayName;let eD=({className:e,...t})=>(0,s.jsx)("div",{className:(0,J.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});eD.displayName="AlertDialogHeader";let eT=({className:e,...t})=>(0,s.jsx)("div",{className:(0,J.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});eT.displayName="AlertDialogFooter";let eI=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(eb,{ref:a,className:(0,J.cn)("text-lg font-semibold",e),...t}));eI.displayName=eb.displayName;let e_=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(eC,{ref:a,className:(0,J.cn)("text-sm text-muted-foreground",e),...t}));e_.displayName=eC.displayName;let eM=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(eS,{ref:a,className:(0,J.cn)((0,n.r)(),e),...t}));eM.displayName=eS.displayName;let eF=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(ek,{ref:a,className:(0,J.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));eF.displayName=ek.displayName;var eL=a(9513);function eO({onEmailSelect:e,selectedEmailId:t}){let{data:a}=(0,i.wV)(),{config:l}=(0,et.U)(),{role:o}=function(){let{data:e}=(0,i.wV)(),[t,a]=(0,r.useState)(null);return{role:t,loading:!e}}(),[c,d]=(0,r.useState)([]),[u,f]=(0,r.useState)(!0),[x,h]=(0,r.useState)(!1),[v,g]=(0,r.useState)(null),[j,y]=(0,r.useState)(!1),[N,b]=(0,r.useState)(0),[w,C]=(0,r.useState)(null),{toast:S}=(0,p.dj)(),R=async e=>{try{let t=new URL("/api/emails",window.location.origin);e&&t.searchParams.set("cursor",e);let a=await fetch(t),s=await a.json();if(!e){let e=s.emails,t=e.findIndex(e=>c.some(t=>t.id===e.id));if(-1===t){d(e),g(s.nextCursor),b(s.total);return}let a=e.slice(0,t);d([...a,...c]),b(s.total);return}d(e=>[...e,...s.emails]),g(s.nextCursor),b(s.total)}catch(e){console.error("Failed to fetch emails:",e)}finally{f(!1),h(!1),y(!1)}},k=async()=>{h(!0),await R()},P=ei(e=>{if(j)return;let{scrollHeight:t,scrollTop:a,clientHeight:s}=e.currentTarget;t-a<=1.5*s&&v&&(y(!0),R(v))},200),A=async a=>{try{let s=await fetch(`/api/emails/${a.id}`,{method:"DELETE"});if(!s.ok){let e=await s.json();S({title:"错误",description:e.error,variant:"destructive"});return}d(e=>e.filter(e=>e.id!==a.id)),b(e=>e-1),S({title:"成功",description:"邮箱已删除"}),t===a.id&&e(null)}catch{S({title:"错误",description:"删除邮箱失败",variant:"destructive"})}finally{C(null)}};return a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsxs)("div",{className:"p-2 flex justify-between items-center border-b border-primary/20",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:k,disabled:x,className:(0,J.cn)("h-8 w-8",x&&"animate-spin"),children:(0,s.jsx)(m,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:o===eL.gg.EMPEROR?`${N}/∞ 个邮箱`:`${N}/${l?.maxEmails||en.q.MAX_ACTIVE_EMAILS} 个邮箱`})]}),(0,s.jsx)(ea,{onEmailCreated:k})]}),(0,s.jsx)("div",{className:"flex-1 overflow-auto p-2",onScroll:P,children:u?(0,s.jsx)("div",{className:"text-center text-sm text-gray-500",children:"加载中..."}):c.length>0?(0,s.jsxs)("div",{className:"space-y-1",children:[c.map(a=>(0,s.jsxs)("div",{className:(0,J.cn)("flex items-center gap-2 p-2 rounded cursor-pointer text-sm group","hover:bg-primary/5",t===a.id&&"bg-primary/10"),onClick:()=>e(a),children:[(0,s.jsx)(es.A,{className:"h-4 w-4 text-primary/60"}),(0,s.jsxs)("div",{className:"truncate flex-1",children:[(0,s.jsx)("div",{className:"font-medium truncate",children:a.address}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:9999===new Date(a.expiresAt).getFullYear()?"永久有效":`过期时间: ${new Date(a.expiresAt).toLocaleString()}`})]}),(0,s.jsx)(n.$,{variant:"ghost",size:"icon",className:"opacity-0 group-hover:opacity-100 h-8 w-8",onClick:e=>{e.stopPropagation(),C(a)},children:(0,s.jsx)(er.A,{className:"h-4 w-4 text-destructive"})})]},a.id)),j&&(0,s.jsx)("div",{className:"text-center text-sm text-gray-500 py-2",children:"加载更多..."})]}):(0,s.jsx)("div",{className:"text-center text-sm text-gray-500",children:"还没有邮箱，创建一个吧！"})})]}),(0,s.jsx)(ep,{open:!!w,onOpenChange:()=>C(null),children:(0,s.jsxs)(eE,{children:[(0,s.jsxs)(eD,{children:[(0,s.jsx)(eI,{children:"确认删除"}),(0,s.jsxs)(e_,{children:["确定要删除邮箱 ",w?.address," 吗？此操作将同时删除该邮箱中的所有邮件，且不可恢复。"]})]}),(0,s.jsxs)(eT,{children:[(0,s.jsx)(eF,{children:"取消"}),(0,s.jsx)(eM,{className:"bg-destructive hover:bg-destructive/90",onClick:()=>w&&A(w),children:"删除"})]})]})})]}):null}let eq=(0,d.A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);function ez({email:e,onMessageSelect:t,selectedMessageId:a}){let[i,l]=(0,r.useState)([]),[o,c]=(0,r.useState)(!0),[d,u]=(0,r.useState)(!1),[f,x]=(0,r.useState)(null),[h,v]=(0,r.useState)(!1),g=((0,r.useRef)(),(0,r.useRef)([])),[j,y]=(0,r.useState)(0),[N,b]=(0,r.useState)(null),{toast:w}=(0,p.dj)(),C=async t=>{try{let a=new URL(`/api/emails/${e.id}`,window.location.origin);t&&a.searchParams.set("cursor",t);let s=await fetch(a),r=await s.json();if(!t){let e=r.messages,t=g.current,a=e.findIndex(e=>t.some(t=>t.id===e.id));if(-1===a){l(e),x(r.nextCursor),y(r.total);return}let s=e.slice(0,a);l([...s,...t]),y(r.total);return}l(e=>[...e,...r.messages]),x(r.nextCursor),y(r.total)}catch(e){console.error("Failed to fetch messages:",e)}finally{c(!1),u(!1),v(!1)}},S=async()=>{u(!0),await C()},R=ei(e=>{if(h)return;let{scrollHeight:t,scrollTop:a,clientHeight:s}=e.currentTarget;t-a<=1.5*s&&f&&(v(!0),C(f))},200),k=async s=>{try{let r=await fetch(`/api/emails/${e.id}/${s.id}`,{method:"DELETE"});if(!r.ok){let e=await r.json();w({title:"错误",description:e.error,variant:"destructive"});return}l(e=>e.filter(e=>e.id!==s.id)),y(e=>e-1),w({title:"成功",description:"邮件已删除"}),a===s.id&&t(null)}catch{w({title:"错误",description:"删除邮件失败",variant:"destructive"})}finally{b(null)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsxs)("div",{className:"p-2 flex justify-between items-center border-b border-primary/20",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:S,disabled:d,className:(0,J.cn)("h-8 w-8",d&&"animate-spin"),children:(0,s.jsx)(m,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:j>0?`${j} 封邮件`:"暂无邮件"})]}),(0,s.jsx)("div",{className:"flex-1 overflow-auto",onScroll:R,children:o?(0,s.jsx)("div",{className:"p-4 text-center text-sm text-gray-500",children:"加载中..."}):i.length>0?(0,s.jsxs)("div",{className:"divide-y divide-primary/10",children:[i.map(e=>(0,s.jsx)("div",{onClick:()=>t(e.id),className:(0,J.cn)("p-3 hover:bg-primary/5 cursor-pointer group",a===e.id&&"bg-primary/10"),children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(es.A,{className:"w-4 h-4 text-primary/60 mt-1"}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("p",{className:"font-medium text-sm truncate",children:e.subject}),(0,s.jsxs)("div",{className:"mt-1 flex items-center gap-2 text-xs text-gray-500",children:[(0,s.jsx)("span",{className:"truncate",children:e.from_address}),(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(eq,{className:"w-3 h-3"}),new Date(e.received_at).toLocaleString()]})]})]}),(0,s.jsx)(n.$,{variant:"ghost",size:"icon",className:"opacity-0 group-hover:opacity-100 h-8 w-8",onClick:t=>{t.stopPropagation(),b(e)},children:(0,s.jsx)(er.A,{className:"h-4 w-4 text-destructive"})})]})},e.id)),h&&(0,s.jsx)("div",{className:"text-center text-sm text-gray-500 py-2",children:"加载更多..."})]}):(0,s.jsx)("div",{className:"p-4 text-center text-sm text-gray-500",children:"暂无邮件"})})]}),(0,s.jsx)(ep,{open:!!N,onOpenChange:()=>b(null),children:(0,s.jsxs)(eE,{children:[(0,s.jsxs)(eD,{children:[(0,s.jsx)(eI,{children:"确认删除"}),(0,s.jsxs)(e_,{children:["确定要删除邮件 ",N?.subject," 吗？"]})]}),(0,s.jsxs)(eT,{children:[(0,s.jsx)(eF,{children:"取消"}),(0,s.jsx)(eM,{className:"bg-destructive hover:bg-destructive/90",onClick:()=>N&&k(N),children:"删除"})]})]})})]})}var eG=a(4881),e$=a(9845);function eB({emailId:e,messageId:t}){let[a,i]=(0,r.useState)(null),[n,l]=(0,r.useState)(!0),[o,c]=(0,r.useState)("html"),d=(0,r.useRef)(null),{theme:m}=(0,e$.D)();return n?(0,s.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,s.jsx)(eG.A,{className:"w-5 h-5 animate-spin text-primary/60"})}):a?(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsxs)("div",{className:"p-4 space-y-3 border-b border-primary/20",children:[(0,s.jsx)("h3",{className:"text-base font-bold",children:a.subject}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,s.jsxs)("p",{children:["发件人：",a.from_address]}),(0,s.jsxs)("p",{children:["时间：",new Date(a.received_at).toLocaleString()]})]})]}),a.html&&(0,s.jsx)("div",{className:"border-b border-primary/20 p-2",children:(0,s.jsxs)(Y,{value:o,onValueChange:e=>c(e),className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(X,{value:"html",id:"html"}),(0,s.jsx)(W.J,{htmlFor:"html",className:"text-xs cursor-pointer",children:"HTML 格式"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(X,{value:"text",id:"text"}),(0,s.jsx)(W.J,{htmlFor:"text",className:"text-xs cursor-pointer",children:"纯文本格式"})]})]})}),(0,s.jsx)("div",{className:"flex-1 overflow-auto relative",children:"html"===o&&a.html?(0,s.jsx)("iframe",{ref:d,className:"absolute inset-0 w-full h-full border-0 bg-transparent",sandbox:"allow-same-origin allow-popups"}):(0,s.jsx)("div",{className:"p-4 text-sm whitespace-pre-wrap",children:a.content})})]}):null}function eH(){let[e,t]=(0,r.useState)(null),[a,i]=(0,r.useState)(null),{copyToClipboard:n}=(0,ee.T)(),l="border-2 border-primary/20 bg-background rounded-lg overflow-hidden flex flex-col",o="p-2 border-b-2 border-primary/20 flex items-center justify-between shrink-0",c="text-sm font-bold px-2 w-full overflow-hidden",d=a?"message":e?"emails":"list",m=()=>{n(e?.address||"")};return(0,s.jsxs)("div",{className:"pb-5 pt-20 h-full flex flex-col",children:[(0,s.jsxs)("div",{className:"hidden lg:grid grid-cols-12 gap-4 h-full min-h-0",children:[(0,s.jsxs)("div",{className:(0,J.cn)("col-span-3",l),children:[(0,s.jsx)("div",{className:o,children:(0,s.jsx)("h2",{className:c,children:"我的邮箱"})}),(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsx)(eO,{onEmailSelect:e=>{t(e),i(null)},selectedEmailId:e?.id})})]}),(0,s.jsxs)("div",{className:(0,J.cn)("col-span-4",l),children:[(0,s.jsx)("div",{className:o,children:(0,s.jsx)("h2",{className:c,children:e?(0,s.jsxs)("div",{className:"w-full flex items-center gap-2",children:[(0,s.jsx)("span",{className:"truncate min-w-0",children:e.address}),(0,s.jsx)("div",{className:"shrink-0 cursor-pointer text-primary",onClick:m,children:(0,s.jsx)(u.A,{className:"size-4"})})]}):"选择邮箱查看消息"})}),e&&(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsx)(ez,{email:e,onMessageSelect:i,selectedMessageId:a})})]}),(0,s.jsxs)("div",{className:(0,J.cn)("col-span-5",l),children:[(0,s.jsx)("div",{className:o,children:(0,s.jsx)("h2",{className:c,children:a?"邮件内容":"选择邮件查看详情"})}),e&&a&&(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsx)(eB,{emailId:e.id,messageId:a,onClose:()=>i(null)})})]})]}),(0,s.jsx)("div",{className:"lg:hidden h-full min-h-0",children:(0,s.jsxs)("div",{className:(0,J.cn)("h-full",l),children:["list"===d&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:o,children:(0,s.jsx)("h2",{className:c,children:"我的邮箱"})}),(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsx)(eO,{onEmailSelect:e=>{t(e)},selectedEmailId:e?.id})})]}),"emails"===d&&e&&(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsxs)("div",{className:(0,J.cn)(o,"gap-2"),children:[(0,s.jsx)("button",{onClick:()=>{t(null)},className:"text-sm text-primary shrink-0",children:"← 返回邮箱列表"}),(0,s.jsxs)("div",{className:"flex-1 flex items-center gap-2 min-w-0",children:[(0,s.jsx)("span",{className:"truncate min-w-0 flex-1 text-right",children:e.address}),(0,s.jsx)("div",{className:"shrink-0 cursor-pointer text-primary",onClick:m,children:(0,s.jsx)(u.A,{className:"size-4"})})]})]}),(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsx)(ez,{email:e,onMessageSelect:i,selectedMessageId:a})})]}),"message"===d&&e&&a&&(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsxs)("div",{className:o,children:[(0,s.jsx)("button",{onClick:()=>i(null),className:"text-sm text-primary",children:"← 返回消息列表"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"邮件内容"})]}),(0,s.jsx)("div",{className:"flex-1 overflow-auto",children:(0,s.jsx)(eB,{emailId:e.id,messageId:a,onClose:()=>i(null)})})]})]})})]})}},1840:(e,t,a)=>{"use strict";a.d(t,{NoPermissionDialog:()=>l});var s=a(9796),r=a(1511),i=a(6079),n=a(5984);function l(){let e=(0,i.rd)(),{config:t}=(0,n.U)();return(0,s.jsx)("div",{className:"fixed inset-0 bg-background/50 backdrop-blur-sm z-50",children:(0,s.jsx)("div",{className:"fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] w-[90%] max-w-md",children:(0,s.jsx)("div",{className:"bg-background border-2 border-primary/20 rounded-lg p-6 md:p-12 shadow-lg",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("h1",{className:"text-xl md:text-2xl font-bold",children:"权限不足"}),(0,s.jsx)("p",{className:"text-sm md:text-base text-muted-foreground",children:"你没有权限访问此页面，请联系网站管理员"}),t?.adminContact&&(0,s.jsxs)("p",{className:"text-sm md:text-base text-muted-foreground",children:["管理员联系方式：",t.adminContact]}),(0,s.jsx)(r.$,{onClick:()=>e.push("/"),className:"mt-4 w-full md:w-auto",children:"返回首页"})]})})})})}},7204:(e,t,a)=>{"use strict";a.d(t,{ThreeColumnLayout:()=>s});let s=(0,a(6853).YR)(function(){throw Error("Attempted to call ThreeColumnLayout() from the server but ThreeColumnLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\emails\\three-column-layout.tsx","ThreeColumnLayout")},6628:(e,t,a)=>{"use strict";a.d(t,{NoPermissionDialog:()=>s});let s=(0,a(6853).YR)(function(){throw Error("Attempted to call NoPermissionDialog() from the server but NoPermissionDialog is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\no-permission-dialog.tsx","NoPermissionDialog")},4730:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m,runtime:()=>d});var s=a(861),r=a(1024),i=a(7204),n=a(6628),l=a(1639),o=a(8149),c=a(789);let d="edge";async function m(){let e=await (0,l.j2)();e?.user||(0,o.V2)("/");let t=await (0,l.Yj)(c.Jj.MANAGE_EMAIL);return(0,s.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 h-screen",children:(0,s.jsxs)("div",{className:"container mx-auto h-full px-4 lg:px-8 max-w-[1600px]",children:[(0,s.jsx)(r.Y,{}),(0,s.jsxs)("main",{className:"h-full",children:[(0,s.jsx)(i.ThreeColumnLayout,{}),!t&&(0,s.jsx)(n.NoPermissionDialog,{})]})]})})}},8743:(e,t,a)=>{"use strict";a.d(t,{RG:()=>N,bL:()=>E,q7:()=>D});var s=a(2992),r=a(173),i=a(402),n=a(3359),l=a(6442),o=a(9297),c=a(7608),d=a(1968),m=a(3919),u=a(6794),p=a(9796),f="rovingFocusGroup.onEntryFocus",x={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[v,g,j]=(0,i.N)(h),[y,N]=(0,l.A)(h,[j]),[b,w]=y(h),C=s.forwardRef((e,t)=>(0,p.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(S,{...e,ref:t})})}));C.displayName=h;var S=s.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:a,orientation:i,loop:l=!1,dir:o,currentTabStopId:v,defaultCurrentTabStopId:j,onCurrentTabStopIdChange:y,onEntryFocus:N,preventScrollOnEntryFocus:w=!1,...C}=e,S=s.useRef(null),R=(0,n.s)(t,S),k=(0,u.jH)(o),[P,E]=(0,m.i)({prop:v,defaultProp:j??null,onChange:y,caller:h}),[D,T]=s.useState(!1),I=(0,d.c)(N),_=g(a),M=s.useRef(!1),[F,L]=s.useState(0);return s.useEffect(()=>{let e=S.current;if(e)return e.addEventListener(f,I),()=>e.removeEventListener(f,I)},[I]),(0,p.jsx)(b,{scope:a,orientation:i,dir:k,loop:l,currentTabStopId:P,onItemFocus:s.useCallback(e=>E(e),[E]),onItemShiftTab:s.useCallback(()=>T(!0),[]),onFocusableItemAdd:s.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:s.useCallback(()=>L(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:D||0===F?-1:0,"data-orientation":i,...C,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,r.m)(e.onMouseDown,()=>{M.current=!0}),onFocus:(0,r.m)(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(f,x);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);A([e.find(e=>e.active),e.find(e=>e.id===P),...e].filter(Boolean).map(e=>e.ref.current),w)}}M.current=!1}),onBlur:(0,r.m)(e.onBlur,()=>T(!1))})})}),R="RovingFocusGroupItem",k=s.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:a,focusable:i=!0,active:n=!1,tabStopId:l,children:d,...m}=e,u=(0,o.B)(),f=l||u,x=w(R,a),h=x.currentTabStopId===f,j=g(a),{onFocusableItemAdd:y,onFocusableItemRemove:N,currentTabStopId:b}=x;return s.useEffect(()=>{if(i)return y(),()=>N()},[i,y,N]),(0,p.jsx)(v.ItemSlot,{scope:a,id:f,focusable:i,active:n,children:(0,p.jsx)(c.sG.span,{tabIndex:h?0:-1,"data-orientation":x.orientation,...m,ref:t,onMouseDown:(0,r.m)(e.onMouseDown,e=>{i?x.onItemFocus(f):e.preventDefault()}),onFocus:(0,r.m)(e.onFocus,()=>x.onItemFocus(f)),onKeyDown:(0,r.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){x.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,a){var s;let r=(s=e.key,"rtl"!==a?s:"ArrowLeft"===s?"ArrowRight":"ArrowRight"===s?"ArrowLeft":s);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(r))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(r)))return P[r]}(e,x.orientation,x.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let a=j().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)a.reverse();else if("prev"===t||"next"===t){"prev"===t&&a.reverse();let s=a.indexOf(e.currentTarget);a=x.loop?function(e,t){return e.map((a,s)=>e[(t+s)%e.length])}(a,s+1):a.slice(s+1)}setTimeout(()=>A(a))}}),children:"function"==typeof d?d({isCurrentTabStop:h,hasTabStop:null!=b}):d})})});k.displayName=R;var P={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e,t=!1){let a=document.activeElement;for(let s of e)if(s===a||(s.focus({preventScroll:t}),document.activeElement!==a))return}var E=C,D=k}},e=>{var t=t=>e(e.s=t);e.O(0,[810,702,267,658,51,886,293,363,559,473],()=>t(2728));var a=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/moe/page"]=a}]);
//# sourceMappingURL=page.js.map