(()=>{var e={840:e=>{"use strict";function _interpolate(e,n,r){const t=e.match(/(.?\${*[\w]*(?::-)?[\w]*}*)/g)||[];return t.reduce((function(e,o,s){const c=/(.?)\${*([\w]*(?::-)?[\w]*)?}*/g.exec(o);if(!c||c.length===0){return e}const i=c[1];let a,_;if(i==="\\"){_=c[0];a=_.replace("\\$","$")}else{const o=c[2].split(":-");const l=o[0];_=c[0].substring(i.length);a=Object.prototype.hasOwnProperty.call(n,l)?n[l]:r.parsed[l]||o[1]||"";if(o.length>1&&a){const n=t[s+1];t[s+1]="";e=e.replace(n,"")}a=_interpolate(a,n,r)}return e.replace(_,a)}),e)}function expand(e){const n=e.ignoreProcessEnv?{}:process.env;for(const r in e.parsed){const t=Object.prototype.hasOwnProperty.call(n,r)?n[r]:e.parsed[r];e.parsed[r]=_interpolate(t,n,e)}for(const r in e.parsed){n[r]=e.parsed[r]}return e}e.exports.j=expand},358:(e,n,r)=>{var t;const o=r(147);const s=r(17);const c=r(37);function log(e){console.log(`[dotenv][DEBUG] ${e}`)}const i="\n";const a=/^\s*([\w.-]+)\s*=\s*(.*)?\s*$/;const _=/\\n/g;const l=/\r\n|\n|\r/;function parse(e,n){const r=Boolean(n&&n.debug);const t={};e.toString().split(l).forEach((function(e,n){const o=e.match(a);if(o!=null){const e=o[1];let n=o[2]||"";const r=n.length-1;const s=n[0]==='"'&&n[r]==='"';const c=n[0]==="'"&&n[r]==="'";if(c||s){n=n.substring(1,r);if(s){n=n.replace(_,i)}}else{n=n.trim()}t[e]=n}else if(r){log(`did not match key and value when parsing line ${n+1}: ${e}`)}}));return t}function resolveHome(e){return e[0]==="~"?s.join(c.homedir(),e.slice(1)):e}function config(e){let n=s.resolve(process.cwd(),".env");let r="utf8";let t=false;if(e){if(e.path!=null){n=resolveHome(e.path)}if(e.encoding!=null){r=e.encoding}if(e.debug!=null){t=true}}try{const e=parse(o.readFileSync(n,{encoding:r}),{debug:t});Object.keys(e).forEach((function(n){if(!Object.prototype.hasOwnProperty.call(process.env,n)){process.env[n]=e[n]}else if(t){log(`"${n}" is already defined in \`process.env\` and will not be overwritten`)}}));return{parsed:e}}catch(e){return{error:e}}}t=config;e.exports.Q=parse},147:e=>{"use strict";e.exports=require("fs")},37:e=>{"use strict";e.exports=require("os")},17:e=>{"use strict";e.exports=require("path")}};var n={};function __nccwpck_require__(r){var t=n[r];if(t!==undefined){return t.exports}var o=n[r]={exports:{}};var s=true;try{e[r](o,o.exports,__nccwpck_require__);s=false}finally{if(s)delete n[r]}return o.exports}(()=>{__nccwpck_require__.n=e=>{var n=e&&e.__esModule?()=>e["default"]:()=>e;__nccwpck_require__.d(n,{a:n});return n}})();(()=>{__nccwpck_require__.d=(e,n)=>{for(var r in n){if(__nccwpck_require__.o(n,r)&&!__nccwpck_require__.o(e,r)){Object.defineProperty(e,r,{enumerable:true,get:n[r]})}}}})();(()=>{__nccwpck_require__.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n)})();(()=>{__nccwpck_require__.r=e=>{if(typeof Symbol!=="undefined"&&Symbol.toStringTag){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}Object.defineProperty(e,"__esModule",{value:true})}})();if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var r={};(()=>{"use strict";__nccwpck_require__.r(r);__nccwpck_require__.d(r,{initialEnv:()=>i,updateInitialEnv:()=>updateInitialEnv,processEnv:()=>processEnv,resetEnv:()=>resetEnv,loadEnvConfig:()=>loadEnvConfig});var e=__nccwpck_require__(147);var n=__nccwpck_require__.n(e);var t=__nccwpck_require__(17);var o=__nccwpck_require__.n(t);var s=__nccwpck_require__(358);var c=__nccwpck_require__(840);let i=undefined;let a=undefined;let _=[];let l=[];function updateInitialEnv(e){Object.assign(i||{},e)}function replaceProcessEnv(e){Object.keys(process.env).forEach((n=>{if(!n.startsWith("__NEXT_PRIVATE")){if(e[n]===undefined||e[n]===""){delete process.env[n]}}}));Object.entries(e).forEach((([e,n])=>{process.env[e]=n}))}function processEnv(e,n,r=console,o=false,a){var _;if(!i){i=Object.assign({},process.env)}if(!o&&(process.env.__NEXT_PROCESSED_ENV||e.length===0)){return process.env}process.env.__NEXT_PROCESSED_ENV="true";const p=Object.assign({},i);const u={};for(const o of e){try{let e={};e.parsed=s.Q(o.contents);e=(0,c.j)(e);if(e.parsed&&!l.some((e=>e.contents===o.contents&&e.path===o.path))){a===null||a===void 0?void 0:a(o.path)}for(const n of Object.keys(e.parsed||{})){if(typeof u[n]==="undefined"&&typeof p[n]==="undefined"){u[n]=(_=e.parsed)===null||_===void 0?void 0:_[n]}}}catch(e){r.error(`Failed to load env from ${t.join(n||"",o.path)}`,e)}}return Object.assign(process.env,u)}function resetEnv(){if(i){replaceProcessEnv(i)}}function loadEnvConfig(n,r,o=console,s=false,c){if(!i){i=Object.assign({},process.env)}if(a&&!s){return{combinedEnv:a,loadedEnvFiles:_}}replaceProcessEnv(i);l=_;_=[];const p=process.env.NODE_ENV==="test";const u=p?"test":r?"development":"production";const f=[`.env.${u}.local`,u!=="test"&&`.env.local`,`.env.${u}`,".env"].filter(Boolean);for(const r of f){const s=t.join(n,r);try{const n=e.statSync(s);if(!n.isFile()){continue}const t=e.readFileSync(s,"utf8");_.push({path:r,contents:t})}catch(e){if(e.code!=="ENOENT"){o.error(`Failed to load env from ${r}`,e)}}}a=processEnv(_,n,o,s,c);return{combinedEnv:a,loadedEnvFiles:_}}})();module.exports=r})();