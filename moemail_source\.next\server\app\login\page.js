(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{5521:e=>{"use strict";e.exports=require("node:async_hooks")},5356:e=>{"use strict";e.exports=require("node:buffer")},4550:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>_,default:()=>F});var a,s={};r.r(s),r.d(s,{ClientPageRoot:()=>m.Fy,ClientSegmentRoot:()=>m.pl,GlobalError:()=>u.default,HTTPAccessFallbackBoundary:()=>m.nQ,LayoutRouter:()=>m.C3,MetadataBoundary:()=>m.qB,OutletBoundary:()=>m.Cr,Postpone:()=>m.fK,RenderFromTemplateContext:()=>m.IY,ViewportBoundary:()=>m.PX,__next_app__:()=>v,actionAsyncStorage:()=>m.sc,collectSegmentData:()=>m.Uy,createMetadataComponents:()=>m.IB,createPrerenderParamsForClientSegment:()=>m.lu,createPrerenderSearchParamsForClientPage:()=>m.jO,createServerParamsForMetadata:()=>m.Kx,createServerParamsForServerSegment:()=>m.LV,createServerSearchParamsForMetadata:()=>m.mh,createServerSearchParamsForServerPage:()=>m.Vv,createTemporaryReferenceSet:()=>m.XI,decodeAction:()=>m.Jk,decodeFormState:()=>m.Am,decodeReply:()=>m.X$,pages:()=>p,patchFetch:()=>m.V5,preconnect:()=>m.kZ,preloadFont:()=>m.PY,preloadStyle:()=>m.vI,prerender:()=>m.CR,renderToReadableStream:()=>m.WK,routeModule:()=>g,serverHooks:()=>m.ge,taintObjectReference:()=>m.N2,tree:()=>f,workAsyncStorage:()=>m.J_,workUnitAsyncStorage:()=>m.FP}),r(9569);var i=r(981),n=r(9040),o=r(7598),l=r(5717),c=r(3307),d=r(6292),u=r(3094),m=r(6953);let f=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1060)),"F:\\CODE\\Project\\Mail\\moemail_source\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4452))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,189)),"F:\\CODE\\Project\\Mail\\moemail_source\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4962)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.bind(r,1648)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.bind(r,6408)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4452))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["F:\\CODE\\Project\\Mail\\moemail_source\\app\\login\\page.tsx"],v={require:r,loadChunk:()=>Promise.resolve()},g=new c.AppPageRouteModule({definition:{kind:d.A.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:f}});var h=r(5711),x=r(4194),b=r(7285);globalThis.__nextCacheHandlers||(globalThis.__nextCacheHandlers={});let y=e=>e?JSON.parse(e):void 0,w=self.__BUILD_MANIFEST,j=y(self.__REACT_LOADABLE_MANIFEST),N=null==(a=self.__RSC_MANIFEST)?void 0:a["/login/page"],C=y(self.__RSC_SERVER_MANIFEST),P=y(self.__NEXT_FONT_MANIFEST),S=y(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];N&&C&&(0,x.fQ)({page:"/login/page",clientReferenceManifest:N,serverActionsManifest:C,serverModuleMap:(0,b.e)({serverActionsManifest:C})});let R=(0,n.R)({pagesType:h.g.APP,dev:!1,page:"/login/page",appMod:null,pageMod:s,errorMod:null,error500Mod:null,Document:null,buildManifest:w,renderToHTML:l.W,reactLoadableManifest:j,clientReferenceManifest:N,serverActionsManifest:C,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\Mail\\moemail_source",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\Mail\\moemail_source"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\Mail\\moemail_source\\next.config.ts",configFileName:"next.config.ts"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:P,incrementalCacheHandler:null,interceptionRouteRewrites:S}),_=s;function F(e){return(0,i.O)({...e,IncrementalCache:o.N,handler:R})}},7428:(e,t,r)=>{Promise.resolve().then(r.bind(r,9801))},3460:(e,t,r)=>{Promise.resolve().then(r.bind(r,3858))},3858:(e,t,r)=>{"use strict";r.d(t,{LoginForm:()=>V});var a=r(9796),s=r(2992),i=r(4123),n=r(120),o=r(1511),l=r(5935),c=r(2304);let d=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,c.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));d.displayName="Card";let u=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,c.cn)("flex flex-col space-y-1.5 p-6",e),...t}));u.displayName="CardHeader";let m=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("h3",{ref:r,className:(0,c.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));m.displayName="CardTitle";let f=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("p",{ref:r,className:(0,c.cn)("text-sm text-muted-foreground",e),...t}));f.displayName="CardDescription";let p=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,c.cn)("p-6 pt-0",e),...t}));p.displayName="CardContent",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,c.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter";var v=r(173),g=r(6442),h=r(8743),x=r(8073),b=r(7608),y=r(6794),w=r(3919),j=r(9297),N="Tabs",[C,P]=(0,g.A)(N,[h.RG]),S=(0,h.RG)(),[R,_]=C(N),F=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,onValueChange:i,defaultValue:n,orientation:o="horizontal",dir:l,activationMode:c="automatic",...d}=e,u=(0,y.jH)(l),[m,f]=(0,w.i)({prop:s,onChange:i,defaultProp:n??"",caller:N});return(0,a.jsx)(R,{scope:r,baseId:(0,j.B)(),value:m,onValueChange:f,orientation:o,dir:u,activationMode:c,children:(0,a.jsx)(b.sG.div,{dir:u,"data-orientation":o,...d,ref:t})})});F.displayName=N;var T="TabsList",A=s.forwardRef((e,t)=>{let{__scopeTabs:r,loop:s=!0,...i}=e,n=_(T,r),o=S(r);return(0,a.jsx)(h.bL,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:s,children:(0,a.jsx)(b.sG.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:t})})});A.displayName=T;var E="TabsTrigger",M=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,disabled:i=!1,...n}=e,o=_(E,r),l=S(r),c=D(o.baseId,s),d=O(o.baseId,s),u=s===o.value;return(0,a.jsx)(h.q7,{asChild:!0,...l,focusable:!i,active:u,children:(0,a.jsx)(b.sG.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":d,"data-state":u?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:c,...n,ref:t,onMouseDown:(0,v.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(s)}),onKeyDown:(0,v.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(s)}),onFocus:(0,v.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;u||i||!e||o.onValueChange(s)})})})});M.displayName=E;var k="TabsContent",I=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:i,forceMount:n,children:o,...l}=e,c=_(k,r),d=D(c.baseId,i),u=O(c.baseId,i),m=i===c.value,f=s.useRef(m);return s.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(x.C,{present:n||m,children:({present:r})=>(0,a.jsx)(b.sG.div,{"data-state":m?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:u,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:r&&o})})});function D(e,t){return`${e}-trigger-${t}`}function O(e,t){return`${e}-content-${t}`}I.displayName=k;let L=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(A,{ref:r,className:(0,c.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));L.displayName=A.displayName;let B=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(M,{ref:r,className:(0,c.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));B.displayName=M.displayName;let G=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(I,{ref:r,className:(0,c.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));G.displayName=I.displayName;var q=r(4145);let z=(0,r(297).A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]);var H=r(4881),K=r(4040);function V(){let[e,t]=(0,s.useState)(""),[r,v]=(0,s.useState)(""),[g,h]=(0,s.useState)(""),[x,b]=(0,s.useState)(!1),[y,w]=(0,s.useState)({}),{toast:j}=(0,n.dj)(),N=()=>{let t={};return e||(t.username="请输入用户名"),r||(t.password="请输入密码"),e.includes("@")&&(t.username="用户名不能包含 @ 符号"),r&&r.length<8&&(t.password="密码长度必须大于等于8位"),w(t),0===Object.keys(t).length},C=()=>{let t={};return e||(t.username="请输入用户名"),r||(t.password="请输入密码"),e.includes("@")&&(t.username="用户名不能包含 @ 符号"),r&&r.length<8&&(t.password="密码长度必须大于等于8位"),g||(t.confirmPassword="请确认密码"),r!==g&&(t.confirmPassword="两次输入的密码不一致"),w(t),0===Object.keys(t).length},P=async()=>{if(N()){b(!0);try{let t=await (0,i.Jv)("credentials",{username:e,password:r,redirect:!1});if(t?.error){j({title:"登录失败",description:"用户名或密码错误",variant:"destructive"}),b(!1);return}window.location.href="/"}catch(e){j({title:"登录失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"}),b(!1)}}},S=async()=>{if(C()){b(!0);try{let t=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:r})}),a=await t.json();if(!t.ok){j({title:"注册失败",description:a.error||"请稍后重试",variant:"destructive"}),b(!1);return}let s=await (0,i.Jv)("credentials",{username:e,password:r,redirect:!1});if(s?.error){j({title:"登录失败",description:"自动登录失败，请手动登录",variant:"destructive"}),b(!1);return}window.location.href="/"}catch(e){j({title:"注册失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"}),b(!1)}}};return(0,a.jsxs)(d,{className:"w-[95%] max-w-lg border-2 border-primary/20",children:[(0,a.jsxs)(u,{className:"space-y-2",children:[(0,a.jsx)(m,{className:"text-2xl text-center bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent",children:"欢迎使用 MoeMail"}),(0,a.jsx)(f,{className:"text-center",children:"萌萌哒临时邮箱服务 (。・∀・)ノ"})]}),(0,a.jsx)(p,{className:"px-6",children:(0,a.jsxs)(F,{defaultValue:"login",className:"w-full",onValueChange:()=>{t(""),v(""),h(""),w({})},children:[(0,a.jsxs)(L,{className:"grid w-full grid-cols-2 mb-6",children:[(0,a.jsx)(B,{value:"login",children:"登录"}),(0,a.jsx)(B,{value:"register",children:"注册"})]}),(0,a.jsxs)("div",{className:"min-h-[220px]",children:[(0,a.jsxs)(G,{value:"login",className:"space-y-4 mt-0",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,a.jsx)(q.A,{className:"h-5 w-5"})}),(0,a.jsx)(l.p,{className:(0,c.cn)("h-9 pl-9 pr-3",y.username&&"border-destructive focus-visible:ring-destructive"),placeholder:"用户名",value:e,onChange:e=>{t(e.target.value),w({})},disabled:x})]}),y.username&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:y.username})]}),(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,a.jsx)(z,{className:"h-5 w-5"})}),(0,a.jsx)(l.p,{className:(0,c.cn)("h-9 pl-9 pr-3",y.password&&"border-destructive focus-visible:ring-destructive"),type:"password",placeholder:"密码",value:r,onChange:e=>{v(e.target.value),w({})},disabled:x})]}),y.password&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:y.password})]})]}),(0,a.jsxs)("div",{className:"space-y-3 pt-1",children:[(0,a.jsxs)(o.$,{className:"w-full",onClick:P,disabled:x,children:[x&&(0,a.jsx)(H.A,{className:"mr-2 h-4 w-4 animate-spin"}),"登录"]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("span",{className:"w-full border-t"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,a.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"或者"})})]}),(0,a.jsxs)(o.$,{variant:"outline",className:"w-full",onClick:()=>{(0,i.Jv)("github",{callbackUrl:"/"})},children:[(0,a.jsx)(K.A,{className:"mr-2 h-4 w-4"}),"使用 GitHub 账号登录"]})]})]}),(0,a.jsxs)(G,{value:"register",className:"space-y-4 mt-0",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,a.jsx)(q.A,{className:"h-5 w-5"})}),(0,a.jsx)(l.p,{className:(0,c.cn)("h-9 pl-9 pr-3",y.username&&"border-destructive focus-visible:ring-destructive"),placeholder:"用户名",value:e,onChange:e=>{t(e.target.value),w({})},disabled:x})]}),y.username&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:y.username})]}),(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,a.jsx)(z,{className:"h-5 w-5"})}),(0,a.jsx)(l.p,{className:(0,c.cn)("h-9 pl-9 pr-3",y.password&&"border-destructive focus-visible:ring-destructive"),type:"password",placeholder:"密码",value:r,onChange:e=>{v(e.target.value),w({})},disabled:x})]}),y.password&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:y.password})]}),(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute left-2.5 top-2 text-muted-foreground",children:(0,a.jsx)(z,{className:"h-5 w-5"})}),(0,a.jsx)(l.p,{className:(0,c.cn)("h-9 pl-9 pr-3",y.confirmPassword&&"border-destructive focus-visible:ring-destructive"),type:"password",placeholder:"确认密码",value:g,onChange:e=>{h(e.target.value),w({})},disabled:x})]}),y.confirmPassword&&(0,a.jsx)("p",{className:"text-xs text-destructive",children:y.confirmPassword})]})]}),(0,a.jsx)("div",{className:"space-y-3 pt-1",children:(0,a.jsxs)(o.$,{className:"w-full",onClick:S,disabled:x,children:[x&&(0,a.jsx)(H.A,{className:"mr-2 h-4 w-4 animate-spin"}),"注册"]})})]})]})]})})]})}},5935:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(9796),s=r(2992),i=r(2304);let n=s.forwardRef(({className:e,type:t,...r},s)=>(0,a.jsx)("input",{type:t,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...r}));n.displayName="Input"},4881:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(297).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},4145:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(297).A)("UserRound",[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]])},9801:(e,t,r)=>{"use strict";r.d(t,{LoginForm:()=>a});let a=(0,r(6853).YR)(function(){throw Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\auth\\login-form.tsx","LoginForm")},1060:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,runtime:()=>o});var a=r(861),s=r(9801),i=r(1639),n=r(8149);let o="edge";async function l(){let e=await (0,i.j2)();return e?.user&&(0,n.V2)("/"),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800",children:(0,a.jsx)(s.LoginForm,{})})}},6794:(e,t,r)=>{"use strict";r.d(t,{jH:()=>i});var a=r(2992);r(9796);var s=a.createContext(void 0);function i(e){let t=a.useContext(s);return e||t||"ltr"}},8743:(e,t,r)=>{"use strict";r.d(t,{RG:()=>w,bL:()=>T,q7:()=>A});var a=r(2992),s=r(173),i=r(402),n=r(3359),o=r(6442),l=r(9297),c=r(7608),d=r(1968),u=r(3919),m=r(6794),f=r(9796),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[h,x,b]=(0,i.N)(g),[y,w]=(0,o.A)(g,[b]),[j,N]=y(g),C=a.forwardRef((e,t)=>(0,f.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(P,{...e,ref:t})})}));C.displayName=g;var P=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:o=!1,dir:l,currentTabStopId:h,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:y,onEntryFocus:w,preventScrollOnEntryFocus:N=!1,...C}=e,P=a.useRef(null),S=(0,n.s)(t,P),R=(0,m.jH)(l),[_,T]=(0,u.i)({prop:h,defaultProp:b??null,onChange:y,caller:g}),[A,E]=a.useState(!1),M=(0,d.c)(w),k=x(r),I=a.useRef(!1),[D,O]=a.useState(0);return a.useEffect(()=>{let e=P.current;if(e)return e.addEventListener(p,M),()=>e.removeEventListener(p,M)},[M]),(0,f.jsx)(j,{scope:r,orientation:i,dir:R,loop:o,currentTabStopId:_,onItemFocus:a.useCallback(e=>T(e),[T]),onItemShiftTab:a.useCallback(()=>E(!0),[]),onFocusableItemAdd:a.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>O(e=>e-1),[]),children:(0,f.jsx)(c.sG.div,{tabIndex:A||0===D?-1:0,"data-orientation":i,...C,ref:S,style:{outline:"none",...e.style},onMouseDown:(0,s.m)(e.onMouseDown,()=>{I.current=!0}),onFocus:(0,s.m)(e.onFocus,e=>{let t=!I.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===_),...e].filter(Boolean).map(e=>e.ref.current),N)}}I.current=!1}),onBlur:(0,s.m)(e.onBlur,()=>E(!1))})})}),S="RovingFocusGroupItem",R=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:n=!1,tabStopId:o,children:d,...u}=e,m=(0,l.B)(),p=o||m,v=N(S,r),g=v.currentTabStopId===p,b=x(r),{onFocusableItemAdd:y,onFocusableItemRemove:w,currentTabStopId:j}=v;return a.useEffect(()=>{if(i)return y(),()=>w()},[i,y,w]),(0,f.jsx)(h.ItemSlot,{scope:r,id:p,focusable:i,active:n,children:(0,f.jsx)(c.sG.span,{tabIndex:g?0:-1,"data-orientation":v.orientation,...u,ref:t,onMouseDown:(0,s.m)(e.onMouseDown,e=>{i?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,s.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,s.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let s=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(s))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(s)))return _[s]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>F(r))}}),children:"function"==typeof d?d({isCurrentTabStop:g,hasTabStop:null!=j}):d})})});R.displayName=S;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e,t=!1){let r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var T=C,A=R}},e=>{var t=t=>e(e.s=t);e.O(0,[810,702,267,658,51,886,559],()=>t(4550));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/login/page"]=r}]);
//# sourceMappingURL=page.js.map