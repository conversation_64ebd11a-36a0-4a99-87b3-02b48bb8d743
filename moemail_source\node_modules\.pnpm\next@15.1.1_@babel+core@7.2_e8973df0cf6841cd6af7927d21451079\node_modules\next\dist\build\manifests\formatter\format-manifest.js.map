{"version": 3, "sources": ["../../../../src/build/manifests/formatter/format-manifest.ts"], "sourcesContent": ["/**\n * Formats the manifest depending on the environment variable\n * `NODE_ENV`. If it's set to `development`, it will return a pretty printed\n * JSON string, otherwise it will return a minified JSON string.\n */\nexport function formatManifest<T extends object>(manifest: T): string {\n  if (process.env.NODE_ENV === 'development') {\n    return JSON.stringify(manifest, null, 2)\n  }\n\n  return JSON.stringify(manifest)\n}\n"], "names": ["formatManifest", "manifest", "process", "env", "NODE_ENV", "JSON", "stringify"], "mappings": "AAAA;;;;CAIC;;;;+BACeA;;;eAAAA;;;AAAT,SAASA,eAAiCC,QAAW;IAC1D,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,OAAOC,KAAKC,SAAS,CAACL,UAAU,MAAM;IACxC;IAEA,OAAOI,KAAKC,SAAS,CAACL;AACxB"}