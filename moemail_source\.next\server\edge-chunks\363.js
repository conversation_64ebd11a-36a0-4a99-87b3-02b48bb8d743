"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[363],{9240:(e,t,n)=>{n.d(t,{Eq:()=>u});var r=new WeakMap,o=new WeakMap,a={},l=0,i=function(e){return e&&(e.host||i(e.parentNode))},c=function(e,t,n,c){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=i(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(u),v=function(e){!e||f.has(e)||(f.add(e),v(e.parentNode))};u.forEach(v);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(c),a=null!==t&&"false"!==t,l=(r.get(e)||0)+1,i=(s.get(e)||0)+1;r.set(e,l),s.set(e,i),d.push(e),1===l&&a&&o.set(e,!0),1===i&&e.setAttribute(n,"true"),a||e.setAttribute(c,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),l++,function(){d.forEach(function(e){var t=r.get(e)-1,a=s.get(e)-1;r.set(e,t),s.set(e,a),t||(o.has(e)||e.removeAttribute(c),o.delete(e)),a||e.removeAttribute(n)}),--l||(r=new WeakMap,r=new WeakMap,o=new WeakMap,a={})}},u=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r,o=Array.from(Array.isArray(e)?e:[e]),a=t||(r=e,"undefined"==typeof document?null:(Array.isArray(r)?r[0]:r).ownerDocument.body);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),c(o,a,n,"aria-hidden")):function(){return null}}},231:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(297).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6457:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(297).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},758:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(297).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4881:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(297).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},4383:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(297).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},1009:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(297).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8608:(e,t,n)=>{n.d(t,{A:()=>q});var r,o,a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create,Object.create;var i=("function"==typeof SuppressedError&&SuppressedError,n(2992)),c="right-scroll-bar-position",u="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,f=new WeakMap;function p(e){return e}var v=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=p),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(a)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return o.options=a({async:!0,ssr:!1},e),o}(),h=function(){},m=i.forwardRef(function(e,t){var n,r,o,c,u=i.useRef(null),p=i.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),m=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,S=e.shards,C=e.sideCar,R=e.noRelative,k=e.noIsolation,j=e.inert,N=e.allowPinchZoom,T=e.as,M=e.gapMode,A=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=(n=[u,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,c=o.facade,d(function(){var e=f.get(c);if(e){var t=new Set(e),r=new Set(n),o=c.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(c,n)},[n]),c),I=a(a({},A),m);return i.createElement(i.Fragment,null,E&&i.createElement(C,{sideCar:v,removeScrollBar:x,shards:S,noRelative:R,noIsolation:k,inert:j,setCallbacks:g,allowPinchZoom:!!N,lockRef:u,gapMode:M}),y?i.cloneElement(i.Children.only(w),a(a({},I),{ref:D})):i.createElement(void 0===T?"div":T,a({},I,{className:b,ref:D}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:u,zeroRight:c};var g=function(e){var t=e.sideCar,n=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,a({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,l;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},R=b(),k="data-scroll-locked",j=function(e,t,n,r){var o=e.left,a=e.top,l=e.right,i=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(i,"px ").concat(r,";\n  }\n  body[").concat(k,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(c," {\n    right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(k,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},N=function(){var e=parseInt(document.body.getAttribute(k)||"0",10);return isFinite(e)?e:0},T=function(){i.useEffect(function(){return document.body.setAttribute(k,(N()+1).toString()),function(){var e=N()-1;e<=0?document.body.removeAttribute(k):document.body.setAttribute(k,e.toString())}},[])},M=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var a=i.useMemo(function(){return C(o)},[o]);return i.createElement(R,{styles:j(a,!t,o,n?"":"!important")})},A=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return A=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){A=!1}var I=!!A&&{passive:!1},P=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},L=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),O(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},O=function(e,t){return"v"===e?P(t,"overflowY"):P(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,n,r,o){var a,l=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),i=l*r,c=n.target,u=t.contains(c),s=!1,d=i>0,f=0,p=0;do{if(!c)break;var v=F(e,c),h=v[0],m=v[1]-v[2]-l*h;(h||m)&&O(e,c)&&(f+=m,p+=h);var g=c.parentNode;c=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return d&&(o&&1>Math.abs(f)||!o&&i>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-i>p)&&(s=!0),s},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},W=function(e){return[e.deltaX,e.deltaY]},H=function(e){return e&&"current"in e?e.current:e},G=0,K=[];let V=(r=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(G++)[0],a=i.useState(b)[0],l=i.useRef(e);i.useEffect(function(){l.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(H),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,a=B(e),i=n.current,c="deltaX"in e?e.deltaX:i[0]-a[0],u="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,d=Math.abs(c)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=L(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=L(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(c||u)&&(r.current=o),!o)return!0;var p=r.current||o;return _(p,t,e,"h"===p?c:u,!0)},[]),u=i.useCallback(function(e){if(K.length&&K[K.length-1]===a){var n="deltaY"in e?W(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(H).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=i.useCallback(function(e){n.current=B(e),r.current=void 0},[]),f=i.useCallback(function(t){s(t.type,W(t),t.target,c(t,e.lockRef.current))},[]),p=i.useCallback(function(t){s(t.type,B(t),t.target,c(t,e.lockRef.current))},[]);i.useEffect(function(){return K.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,I),document.addEventListener("touchmove",u,I),document.addEventListener("touchstart",d,I),function(){K=K.filter(function(e){return e!==a}),document.removeEventListener("wheel",u,I),document.removeEventListener("touchmove",u,I),document.removeEventListener("touchstart",d,I)}},[]);var v=e.removeScrollBar,h=e.inert;return i.createElement(i.Fragment,null,h?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?i.createElement(M,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},v.useMedium(r),g);var U=i.forwardRef(function(e,t){return i.createElement(m,a({},e,{ref:t,sideCar:V}))});U.classNames=m.classNames;let q=U},5360:(e,t,n)=>{n.d(t,{G$:()=>z,Hs:()=>x,UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>ea,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(2992),o=n(173),a=n(3359),l=n(6442),i=n(9297),c=n(3919),u=n(9492),s=n(9633),d=n(4464),f=n(8073),p=n(7608),v=n(6409),h=n(8608),m=n(9240),g=n(6850),y=n(9796),w="Dialog",[b,x]=(0,l.A)(w),[E,S]=b(w),C=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,c.i)({prop:o,defaultProp:a??!1,onChange:l,caller:w});return(0,y.jsx)(E,{scope:t,triggerRef:s,contentRef:d,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:u,children:n})};C.displayName=w;var R="DialogTrigger",k=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=S(R,n),i=(0,a.s)(t,l.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":q(l.open),...r,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});k.displayName=R;var j="DialogPortal",[N,T]=b(j,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,l=S(j,t);return(0,y.jsx)(N,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:n||l.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};M.displayName=j;var A="DialogOverlay",D=r.forwardRef((e,t)=>{let n=T(A,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=S(A,e.__scopeDialog);return a.modal?(0,y.jsx)(f.C,{present:r||a.open,children:(0,y.jsx)(P,{...o,ref:t})}):null});D.displayName=A;var I=(0,g.TL)("DialogOverlay.RemoveScroll"),P=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=S(A,n);return(0,y.jsx)(h.A,{as:I,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":q(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),L="DialogContent",O=r.forwardRef((e,t)=>{let n=T(L,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=S(L,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||a.open,children:a.modal?(0,y.jsx)(F,{...o,ref:t}):(0,y.jsx)(_,{...o,ref:t})})});O.displayName=L;var F=r.forwardRef((e,t)=>{let n=S(L,e.__scopeDialog),l=r.useRef(null),i=(0,a.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,m.Eq)(e)},[]),(0,y.jsx)(B,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),_=r.forwardRef((e,t)=>{let n=S(L,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,y.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),B=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...c}=e,d=S(L,n),f=r.useRef(null),p=(0,a.s)(t,f);return(0,v.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,y.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":q(d.open),...c,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(X,{titleId:d.titleId}),(0,y.jsx)($,{contentRef:f,descriptionId:d.descriptionId})]})]})}),W="DialogTitle",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=S(W,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});H.displayName=W;var G="DialogDescription",K=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=S(G,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});K.displayName=G;var V="DialogClose",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=S(V,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function q(e){return e?"open":"closed"}U.displayName=V;var Z="DialogTitleWarning",[z,Y]=(0,l.q)(Z,{contentName:L,titleName:W,docsSlug:"dialog"}),X=({titleId:e})=>{let t=Y(Z),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},$=({contentRef:e,descriptionId:t})=>{let n=Y("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},J=C,Q=k,ee=M,et=D,en=O,er=H,eo=K,ea=U},6794:(e,t,n)=>{n.d(t,{jH:()=>a});var r=n(2992);n(9796);var o=r.createContext(void 0);function a(e){let t=r.useContext(o);return e||t||"ltr"}},6409:(e,t,n)=>{n.d(t,{Oh:()=>a});var r=n(2992),o=0;function a(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},9633:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(2992),o=n(3359),a=n(7608),l=n(1968),i=n(9796),c="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,l.c)(m),E=(0,l.c)(g),S=r.useRef(null),C=(0,o.s)(t,e=>b(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?S.current=t:v(S.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||v(S.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,R.paused]),r.useEffect(()=>{if(w){h.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(c,s);w.addEventListener(c,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(v(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(w))}return()=>{w.removeEventListener(c,x),setTimeout(()=>{let t=new CustomEvent(u,s);w.addEventListener(u,E),w.dispatchEvent(t),t.defaultPrevented||v(e??document.body,{select:!0}),w.removeEventListener(u,E),h.remove(R)},0)}}},[w,x,E,R]);let k=r.useCallback(e=>{if(!n&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&v(a,{select:!0})):(e.preventDefault(),n&&v(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,R.paused]);return(0,i.jsx)(a.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:k})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function v(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=m(e,t)).unshift(t)},remove(t){e=m(e,t),e[0]?.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},1454:(e,t,n)=>{n.d(t,{b:()=>i});var r=n(2992),o=n(7608),a=n(9796),l=r.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},3382:(e,t,n)=>{n.d(t,{UC:()=>eM,In:()=>eN,q7:()=>eD,VF:()=>eP,p4:()=>eI,ZL:()=>eT,bL:()=>eR,l9:()=>ek,WT:()=>ej,LM:()=>eA});var r=n(2992),o=n(4405);function a(e,[t,n]){return Math.min(n,Math.max(t,e))}var l=n(173),i=n(402),c=n(3359),u=n(6442),s=n(6794),d=n(9492),f=n(6409),p=n(9633),v=n(9297),h=n(6936),m=n(4464),g=n(7608),y=n(6850),w=n(1968),b=n(3919),x=n(586),E=n(3263),S=n(581),C=n(9240),R=n(8608),k=n(9796),j=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],T="Select",[M,A,D]=(0,i.N)(T),[I,P]=(0,u.A)(T,[D,h.Bk]),L=(0,h.Bk)(),[O,F]=I(T),[_,B]=I(T),W=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:a,onOpenChange:l,value:i,defaultValue:c,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:m,required:g,form:y}=e,w=L(t),[x,E]=r.useState(null),[S,C]=r.useState(null),[R,j]=r.useState(!1),N=(0,s.jH)(d),[A,D]=(0,b.i)({prop:o,defaultProp:a??!1,onChange:l,caller:T}),[I,P]=(0,b.i)({prop:i,defaultProp:c,onChange:u,caller:T}),F=r.useRef(null),B=!x||y||!!x.closest("form"),[W,H]=r.useState(new Set),G=Array.from(W).map(e=>e.props.value).join(";");return(0,k.jsx)(h.bL,{...w,children:(0,k.jsxs)(O,{required:g,scope:t,trigger:x,onTriggerChange:E,valueNode:S,onValueNodeChange:C,valueNodeHasChildren:R,onValueNodeHasChildrenChange:j,contentId:(0,v.B)(),value:I,onValueChange:P,open:A,onOpenChange:D,dir:N,triggerPointerDownPosRef:F,disabled:m,children:[(0,k.jsx)(M.Provider,{scope:t,children:(0,k.jsx)(_,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{H(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{H(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),B?(0,k.jsxs)(ex,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:I,onChange:e=>P(e.target.value),disabled:m,form:y,children:[void 0===I?(0,k.jsx)("option",{value:""}):null,Array.from(W)]},G):null]})})};W.displayName=T;var H="SelectTrigger",G=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...a}=e,i=L(n),u=F(H,n),s=u.disabled||o,d=(0,c.s)(t,u.onTriggerChange),f=A(n),p=r.useRef("touch"),[v,m,y]=eS(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=eC(t,e,n);void 0!==r&&u.onValueChange(r.value)}),w=e=>{s||(u.onOpenChange(!0),y()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,k.jsx)(h.Mz,{asChild:!0,...i,children:(0,k.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":eE(u.value)?"":void 0,...a,ref:d,onClick:(0,l.m)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&w(e)}),onPointerDown:(0,l.m)(a.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,l.m)(a.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&j.includes(e.key)&&(w(),e.preventDefault())})})})});G.displayName=H;var K="SelectValue",V=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:a,placeholder:l="",...i}=e,u=F(K,n),{onValueNodeHasChildrenChange:s}=u,d=void 0!==a,f=(0,c.s)(t,u.onValueNodeChange);return(0,x.N)(()=>{s(d)},[s,d]),(0,k.jsx)(g.sG.span,{...i,ref:f,style:{pointerEvents:"none"},children:eE(u.value)?(0,k.jsx)(k.Fragment,{children:l}):a})});V.displayName=K;var U=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,k.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});U.displayName="SelectIcon";var q=e=>(0,k.jsx)(m.Z,{asChild:!0,...e});q.displayName="SelectPortal";var Z="SelectContent",z=r.forwardRef((e,t)=>{let n=F(Z,e.__scopeSelect),[a,l]=r.useState();return((0,x.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,k.jsx)(J,{...e,ref:t}):a?o.createPortal((0,k.jsx)(Y,{scope:e.__scopeSelect,children:(0,k.jsx)(M.Slot,{scope:e.__scopeSelect,children:(0,k.jsx)("div",{children:e.children})})}),a):null});z.displayName=Z;var[Y,X]=I(Z),$=(0,y.TL)("SelectContent.RemoveScroll"),J=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:i,onPointerDownOutside:u,side:s,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:E,...S}=e,j=F(Z,n),[N,T]=r.useState(null),[M,D]=r.useState(null),I=(0,c.s)(t,e=>T(e)),[P,L]=r.useState(null),[O,_]=r.useState(null),B=A(n),[W,H]=r.useState(!1),G=r.useRef(!1);r.useEffect(()=>{if(N)return(0,C.Eq)(N)},[N]),(0,f.Oh)();let K=r.useCallback(e=>{let[t,...n]=B().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&M&&(M.scrollTop=0),n===r&&M&&(M.scrollTop=M.scrollHeight),n?.focus(),document.activeElement!==o))return},[B,M]),V=r.useCallback(()=>K([P,N]),[K,P,N]);r.useEffect(()=>{W&&V()},[W,V]);let{onOpenChange:U,triggerPointerDownPosRef:q}=j;r.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(q.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(q.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():N.contains(n.target)||U(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[N,U,q]),r.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[z,X]=eS(e=>{let t=B().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eC(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),J=r.useCallback((e,t,n)=>{let r=!G.current&&!n;(void 0!==j.value&&j.value===t||r)&&(L(e),r&&(G.current=!0))},[j.value]),et=r.useCallback(()=>N?.focus(),[N]),en=r.useCallback((e,t,n)=>{let r=!G.current&&!n;(void 0!==j.value&&j.value===t||r)&&_(e)},[j.value]),er="popper"===o?ee:Q,eo=er===ee?{side:s,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:E}:{};return(0,k.jsx)(Y,{scope:n,content:N,viewport:M,onViewportChange:D,itemRefCallback:J,selectedItem:P,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:V,selectedItemText:O,position:o,isPositioned:W,searchRef:z,children:(0,k.jsx)(R.A,{as:$,allowPinchZoom:!0,children:(0,k.jsx)(p.n,{asChild:!0,trapped:j.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(a,e=>{j.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,k.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:(0,k.jsx)(er,{role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:e=>e.preventDefault(),...S,...eo,onPlaced:()=>H(!0),ref:I,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,l.m)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||X(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=B().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,i=F(Z,n),u=X(Z,n),[s,d]=r.useState(null),[f,p]=r.useState(null),v=(0,c.s)(t,e=>p(e)),h=A(n),m=r.useRef(!1),y=r.useRef(!0),{viewport:w,selectedItem:b,selectedItemText:E,focusSelectedItem:S}=u,C=r.useCallback(()=>{if(i.trigger&&i.valueNode&&s&&f&&w&&b&&E){let e=i.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),r=E.getBoundingClientRect();if("rtl"!==i.dir){let o=r.left-t.left,l=n.left-o,i=e.left-l,c=e.width+i,u=Math.max(c,t.width),d=a(l,[10,Math.max(10,window.innerWidth-10-u)]);s.style.minWidth=c+"px",s.style.left=d+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,i=window.innerWidth-e.right-l,c=e.width+i,u=Math.max(c,t.width),d=a(l,[10,Math.max(10,window.innerWidth-10-u)]);s.style.minWidth=c+"px",s.style.right=d+"px"}let l=h(),c=window.innerHeight-20,u=w.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),v=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),y=p+v+u+parseInt(d.paddingBottom,10)+g,x=Math.min(5*b.offsetHeight,y),S=window.getComputedStyle(w),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),k=e.top+e.height/2-10,j=b.offsetHeight/2,N=p+v+(b.offsetTop+j);if(N<=k){let e=l.length>0&&b===l[l.length-1].ref.current;s.style.bottom="0px";let t=Math.max(c-k,j+(e?R:0)+(f.clientHeight-w.offsetTop-w.offsetHeight)+g);s.style.height=N+t+"px"}else{let e=l.length>0&&b===l[0].ref.current;s.style.top="0px";let t=Math.max(k,p+w.offsetTop+(e?C:0)+j);s.style.height=t+(y-N)+"px",w.scrollTop=N-k+w.offsetTop}s.style.margin="10px 0",s.style.minHeight=x+"px",s.style.maxHeight=c+"px",o?.(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,s,f,w,b,E,i.dir,o]);(0,x.N)(()=>C(),[C]);let[R,j]=r.useState();(0,x.N)(()=>{f&&j(window.getComputedStyle(f).zIndex)},[f]);let N=r.useCallback(e=>{e&&!0===y.current&&(C(),S?.(),y.current=!1)},[C,S]);return(0,k.jsx)(et,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,k.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,k.jsx)(g.sG.div,{...l,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...a}=e,l=L(n);return(0,k.jsx)(h.UC,{...l,...a,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,en]=I(Z,{}),er="SelectViewport",eo=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...a}=e,i=X(er,n),u=en(er,n),s=(0,c.s)(t,i.onViewportChange),d=r.useRef(0);return(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,k.jsx)(M.Slot,{scope:n,children:(0,k.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,l.m)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if(r?.current&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let a=o+e,l=Math.min(r,a),i=a-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=i>0?i:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=er;var ea="SelectGroup",[el,ei]=I(ea);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,v.B)();return(0,k.jsx)(el,{scope:n,id:o,children:(0,k.jsx)(g.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=ea;var ec="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ei(ec,n);return(0,k.jsx)(g.sG.div,{id:o.id,...r,ref:t})}).displayName=ec;var eu="SelectItem",[es,ed]=I(eu),ef=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:a=!1,textValue:i,...u}=e,s=F(eu,n),d=X(eu,n),f=s.value===o,[p,h]=r.useState(i??""),[m,y]=r.useState(!1),w=(0,c.s)(t,e=>d.itemRefCallback?.(e,o,a)),b=(0,v.B)(),x=r.useRef("touch"),E=()=>{a||(s.onValueChange(o),s.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,k.jsx)(es,{scope:n,value:o,disabled:a,textId:b,isSelected:f,onItemTextChange:r.useCallback(e=>{h(t=>t||(e?.textContent??"").trim())},[]),children:(0,k.jsx)(M.ItemSlot,{scope:n,value:o,disabled:a,textValue:p,children:(0,k.jsx)(g.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":m?"":void 0,"aria-selected":f&&m,"data-state":f?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...u,ref:w,onFocus:(0,l.m)(u.onFocus,()=>y(!0)),onBlur:(0,l.m)(u.onBlur,()=>y(!1)),onClick:(0,l.m)(u.onClick,()=>{"mouse"!==x.current&&E()}),onPointerUp:(0,l.m)(u.onPointerUp,()=>{"mouse"===x.current&&E()}),onPointerDown:(0,l.m)(u.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,l.m)(u.onPointerMove,e=>{x.current=e.pointerType,a?d.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,l.m)(u.onKeyDown,e=>{d.searchRef?.current!==""&&" "===e.key||(N.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});ef.displayName=eu;var ep="SelectItemText",ev=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:a,style:l,...i}=e,u=F(ep,n),s=X(ep,n),d=ed(ep,n),f=B(ep,n),[p,v]=r.useState(null),h=(0,c.s)(t,e=>v(e),d.onItemTextChange,e=>s.itemTextRefCallback?.(e,d.value,d.disabled)),m=p?.textContent,y=r.useMemo(()=>(0,k.jsx)("option",{value:d.value,disabled:d.disabled,children:m},d.value),[d.disabled,d.value,m]),{onNativeOptionAdd:w,onNativeOptionRemove:b}=f;return(0,x.N)(()=>(w(y),()=>b(y)),[w,b,y]),(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(g.sG.span,{id:d.textId,...i,ref:h}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(i.children,u.valueNode):null]})});ev.displayName=ep;var eh="SelectItemIndicator",em=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ed(eh,n).isSelected?(0,k.jsx)(g.sG.span,{"aria-hidden":!0,...r,ref:t}):null});em.displayName=eh;var eg="SelectScrollUpButton";r.forwardRef((e,t)=>{let n=X(eg,e.__scopeSelect),o=en(eg,e.__scopeSelect),[a,l]=r.useState(!1),i=(0,c.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),a?(0,k.jsx)(ew,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}).displayName=eg;var ey="SelectScrollDownButton";r.forwardRef((e,t)=>{let n=X(ey,e.__scopeSelect),o=en(ey,e.__scopeSelect),[a,l]=r.useState(!1),i=(0,c.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),a?(0,k.jsx)(ew,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}).displayName=ey;var ew=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...a}=e,i=X("SelectScrollButton",n),c=r.useRef(null),u=A(n),s=r.useCallback(()=>{null!==c.current&&(window.clearInterval(c.current),c.current=null)},[]);return r.useEffect(()=>()=>s(),[s]),(0,x.N)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,k.jsx)(g.sG.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,l.m)(a.onPointerDown,()=>{null===c.current&&(c.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(a.onPointerMove,()=>{i.onItemLeave?.(),null===c.current&&(c.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(a.onPointerLeave,()=>{s()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,k.jsx)(g.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var eb="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=L(n),a=F(eb,n),l=X(eb,n);return a.open&&"popper"===l.position?(0,k.jsx)(h.i3,{...o,...r,ref:t}):null}).displayName=eb;var ex=r.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{let a=r.useRef(null),l=(0,c.s)(o,a),i=(0,E.Z)(t);return r.useEffect(()=>{let e=a.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[i,t]),(0,k.jsx)(g.sG.select,{...n,style:{...S.Qg,...n.style},ref:l,defaultValue:t})});function eE(e){return""===e||void 0===e}function eS(e){let t=(0,w.c)(e),n=r.useRef(""),o=r.useRef(0),a=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,a,l]}function eC(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}ex.displayName="SelectBubbleInput";var eR=W,ek=G,ej=V,eN=U,eT=q,eM=z,eA=eo,eD=ef,eI=ev,eP=em},3263:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(2992);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},3082:(e,t,n)=>{n.d(t,{v:()=>c});var r=n(2992);let o=e=>{let t;let n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,a={setState:r,getState:o,getInitialState:()=>l,subscribe:e=>(n.add(e),()=>n.delete(e))},l=t=e(r,o,a);return a},a=e=>e?o(e):o,l=e=>e,i=e=>{let t=a(e),n=e=>(function(e,t=l){let n=r.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return r.useDebugValue(n),n})(t,e);return Object.assign(n,t),n},c=e=>e?i(e):i}}]);
//# sourceMappingURL=363.js.map