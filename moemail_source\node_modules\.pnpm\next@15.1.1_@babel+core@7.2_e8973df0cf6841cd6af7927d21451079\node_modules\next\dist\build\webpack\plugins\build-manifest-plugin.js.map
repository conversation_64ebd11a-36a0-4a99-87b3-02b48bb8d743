{"version": 3, "sources": ["../../../../src/build/webpack/plugins/build-manifest-plugin.ts"], "sourcesContent": ["import type { <PERSON>Filter } from '../../../shared/lib/bloom-filter'\nimport type { Rewrite, CustomRoutes } from '../../../lib/load-custom-routes'\nimport devalue from 'next/dist/compiled/devalue'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  BUILD_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_PATH,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  SYSTEM_ENTRYPOINTS,\n} from '../../../shared/lib/constants'\nimport type { BuildManifest } from '../../../server/get-page-files'\nimport getRouteFromEntrypoint from '../../../server/get-route-from-entrypoint'\nimport { ampFirstEntryNamesMap } from './next-drop-client-page-plugin'\nimport { getSortedRoutes } from '../../../shared/lib/router/utils'\nimport { spans } from './profiling-plugin'\nimport { Span } from '../../../trace'\n\ntype DeepMutable<T> = { -readonly [P in keyof T]: DeepMutable<T[P]> }\n\nexport type ClientBuildManifest = {\n  [key: string]: string[]\n}\n\n// Add the runtime ssg manifest file as a lazy-loaded file dependency.\n// We also stub this file out for development mode (when it is not\n// generated).\nexport const srcEmptySsgManifest = `self.__SSG_MANIFEST=new Set;self.__SSG_MANIFEST_CB&&self.__SSG_MANIFEST_CB()`\n\n// nodejs: '/static/<build id>/low-priority.js'\nfunction buildNodejsLowPriorityPath(filename: string, buildId: string) {\n  return `${CLIENT_STATIC_FILES_PATH}/${buildId}/${filename}`\n}\n\nfunction createEdgeRuntimeManifest(originAssetMap: BuildManifest): string {\n  const manifestFilenames = ['_buildManifest.js', '_ssgManifest.js']\n\n  const assetMap: BuildManifest = {\n    ...originAssetMap,\n    lowPriorityFiles: [],\n  }\n\n  const manifestDefCode = `self.__BUILD_MANIFEST = ${JSON.stringify(\n    assetMap,\n    null,\n    2\n  )};\\n`\n  // edge lowPriorityFiles item: '\"/static/\" + process.env.__NEXT_BUILD_ID + \"/low-priority.js\"'.\n  // Since lowPriorityFiles is not fixed and relying on `process.env.__NEXT_BUILD_ID`, we'll produce code creating it dynamically.\n  const lowPriorityFilesCode =\n    `self.__BUILD_MANIFEST.lowPriorityFiles = [\\n` +\n    manifestFilenames\n      .map((filename) => {\n        return `\"/static/\" + process.env.__NEXT_BUILD_ID + \"/${filename}\",\\n`\n      })\n      .join(',') +\n    `\\n];`\n\n  return manifestDefCode + lowPriorityFilesCode\n}\n\nfunction normalizeRewrite(item: {\n  source: string\n  destination: string\n  has?: any\n}): CustomRoutes['rewrites']['beforeFiles'][0] {\n  return {\n    has: item.has,\n    source: item.source,\n    destination: item.destination,\n  }\n}\n\nexport function normalizeRewritesForBuildManifest(\n  rewrites: CustomRoutes['rewrites']\n): CustomRoutes['rewrites'] {\n  return {\n    afterFiles: rewrites.afterFiles\n      ?.map(processRoute)\n      ?.map((item) => normalizeRewrite(item)),\n    beforeFiles: rewrites.beforeFiles\n      ?.map(processRoute)\n      ?.map((item) => normalizeRewrite(item)),\n    fallback: rewrites.fallback\n      ?.map(processRoute)\n      ?.map((item) => normalizeRewrite(item)),\n  }\n}\n\n// This function takes the asset map generated in BuildManifestPlugin and creates a\n// reduced version to send to the client.\nexport function generateClientManifest(\n  assetMap: BuildManifest,\n  rewrites: CustomRoutes['rewrites'],\n  clientRouterFilters?: {\n    staticFilter: ReturnType<BloomFilter['export']>\n    dynamicFilter: ReturnType<BloomFilter['export']>\n  },\n  compiler?: any,\n  compilation?: any\n): string | undefined {\n  const compilationSpan = compilation\n    ? spans.get(compilation)\n    : compiler\n      ? spans.get(compiler)\n      : new Span({ name: 'client-manifest' })\n\n  const genClientManifestSpan = compilationSpan?.traceChild(\n    'NextJsBuildManifest-generateClientManifest'\n  )\n\n  return genClientManifestSpan?.traceFn(() => {\n    const clientManifest: ClientBuildManifest = {\n      __rewrites: normalizeRewritesForBuildManifest(rewrites) as any,\n      __routerFilterStatic: clientRouterFilters?.staticFilter as any,\n      __routerFilterDynamic: clientRouterFilters?.dynamicFilter as any,\n    }\n    const appDependencies = new Set(assetMap.pages['/_app'])\n    const sortedPageKeys = getSortedRoutes(Object.keys(assetMap.pages))\n\n    sortedPageKeys.forEach((page) => {\n      const dependencies = assetMap.pages[page]\n\n      if (page === '/_app') return\n      // Filter out dependencies in the _app entry, because those will have already\n      // been loaded by the client prior to a navigation event\n      const filteredDeps = dependencies.filter(\n        (dep) => !appDependencies.has(dep)\n      )\n\n      // The manifest can omit the page if it has no requirements\n      if (filteredDeps.length) {\n        clientManifest[page] = filteredDeps\n      }\n    })\n    // provide the sorted pages as an array so we don't rely on the object's keys\n    // being in order and we don't slow down look-up time for page assets\n    clientManifest.sortedPages = sortedPageKeys\n\n    return devalue(clientManifest)\n  })\n}\n\nexport function getEntrypointFiles(entrypoint: any): string[] {\n  return (\n    entrypoint\n      ?.getFiles()\n      .filter((file: string) => {\n        // We don't want to include `.hot-update.js` files into the initial page\n        return /(?<!\\.hot-update)\\.(js|css)($|\\?)/.test(file)\n      })\n      .map((file: string) => file.replace(/\\\\/g, '/')) ?? []\n  )\n}\n\nexport const processRoute = (r: Rewrite) => {\n  const rewrite = { ...r }\n\n  // omit external rewrite destinations since these aren't\n  // handled client-side\n  if (!rewrite?.destination?.startsWith('/')) {\n    delete (rewrite as any).destination\n  }\n  return rewrite\n}\n\n// This plugin creates a build-manifest.json for all assets that are being output\n// It has a mapping of \"entry\" filename to real filename. Because the real filename can be hashed in production\nexport default class BuildManifestPlugin {\n  private buildId: string\n  private rewrites: CustomRoutes['rewrites']\n  private isDevFallback: boolean\n  private appDirEnabled: boolean\n  private clientRouterFilters?: Parameters<typeof generateClientManifest>[2]\n\n  constructor(options: {\n    buildId: string\n    rewrites: CustomRoutes['rewrites']\n    isDevFallback?: boolean\n    appDirEnabled: boolean\n    clientRouterFilters?: Parameters<typeof generateClientManifest>[2]\n  }) {\n    this.buildId = options.buildId\n    this.isDevFallback = !!options.isDevFallback\n    this.rewrites = {\n      beforeFiles: [],\n      afterFiles: [],\n      fallback: [],\n    }\n    this.appDirEnabled = options.appDirEnabled\n    this.clientRouterFilters = options.clientRouterFilters\n    this.rewrites.beforeFiles = options.rewrites.beforeFiles.map(processRoute)\n    this.rewrites.afterFiles = options.rewrites.afterFiles.map(processRoute)\n    this.rewrites.fallback = options.rewrites.fallback.map(processRoute)\n  }\n\n  createAssets(compiler: any, compilation: any, assets: any) {\n    const compilationSpan = spans.get(compilation) || spans.get(compiler)\n    const createAssetsSpan = compilationSpan?.traceChild(\n      'NextJsBuildManifest-createassets'\n    )\n    return createAssetsSpan?.traceFn(() => {\n      const entrypoints: Map<string, any> = compilation.entrypoints\n      const assetMap: DeepMutable<BuildManifest> = {\n        polyfillFiles: [],\n        devFiles: [],\n        ampDevFiles: [],\n        lowPriorityFiles: [],\n        rootMainFiles: [],\n        rootMainFilesTree: {},\n        pages: { '/_app': [] },\n        ampFirstPages: [],\n      }\n\n      const ampFirstEntryNames = ampFirstEntryNamesMap.get(compilation)\n      if (ampFirstEntryNames) {\n        for (const entryName of ampFirstEntryNames) {\n          const pagePath = getRouteFromEntrypoint(entryName)\n          if (!pagePath) {\n            continue\n          }\n\n          assetMap.ampFirstPages.push(pagePath)\n        }\n      }\n\n      const mainFiles = new Set(\n        getEntrypointFiles(entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_MAIN))\n      )\n\n      if (this.appDirEnabled) {\n        assetMap.rootMainFiles = [\n          ...new Set(\n            getEntrypointFiles(\n              entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_MAIN_APP)\n            )\n          ),\n        ]\n      }\n\n      const compilationAssets: {\n        name: string\n        source: typeof sources.RawSource\n        info: object\n      }[] = compilation.getAssets()\n\n      assetMap.polyfillFiles = compilationAssets\n        .filter((p) => {\n          // Ensure only .js files are passed through\n          if (!p.name.endsWith('.js')) {\n            return false\n          }\n\n          return (\n            p.info && CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL in p.info\n          )\n        })\n        .map((v) => v.name)\n\n      assetMap.devFiles = getEntrypointFiles(\n        entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH)\n      ).filter((file) => !mainFiles.has(file))\n\n      assetMap.ampDevFiles = getEntrypointFiles(\n        entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_AMP)\n      )\n\n      for (const entrypoint of compilation.entrypoints.values()) {\n        if (SYSTEM_ENTRYPOINTS.has(entrypoint.name)) continue\n        const pagePath = getRouteFromEntrypoint(entrypoint.name)\n\n        if (!pagePath) {\n          continue\n        }\n\n        const filesForPage = getEntrypointFiles(entrypoint)\n\n        assetMap.pages[pagePath] = [...new Set([...mainFiles, ...filesForPage])]\n      }\n\n      if (!this.isDevFallback) {\n        // Add the runtime build manifest file (generated later in this file)\n        // as a dependency for the app. If the flag is false, the file won't be\n        // downloaded by the client.\n        const buildManifestPath = buildNodejsLowPriorityPath(\n          '_buildManifest.js',\n          this.buildId\n        )\n        const ssgManifestPath = buildNodejsLowPriorityPath(\n          '_ssgManifest.js',\n          this.buildId\n        )\n        assetMap.lowPriorityFiles.push(buildManifestPath, ssgManifestPath)\n        assets[ssgManifestPath] = new sources.RawSource(srcEmptySsgManifest)\n      }\n\n      assetMap.pages = Object.keys(assetMap.pages)\n        .sort()\n        .reduce(\n          // eslint-disable-next-line\n          (a, c) => ((a[c] = assetMap.pages[c]), a),\n          {} as typeof assetMap.pages\n        )\n\n      let buildManifestName = BUILD_MANIFEST\n\n      if (this.isDevFallback) {\n        buildManifestName = `fallback-${BUILD_MANIFEST}`\n      }\n\n      assets[buildManifestName] = new sources.RawSource(\n        JSON.stringify(assetMap, null, 2)\n      )\n\n      assets[`server/${MIDDLEWARE_BUILD_MANIFEST}.js`] = new sources.RawSource(\n        `${createEdgeRuntimeManifest(assetMap)}`\n      )\n\n      if (!this.isDevFallback) {\n        const clientManifestPath = `${CLIENT_STATIC_FILES_PATH}/${this.buildId}/_buildManifest.js`\n\n        assets[clientManifestPath] = new sources.RawSource(\n          `self.__BUILD_MANIFEST = ${generateClientManifest(\n            assetMap,\n            this.rewrites,\n            this.clientRouterFilters,\n            compiler,\n            compilation\n          )};self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()`\n        )\n      }\n\n      return assets\n    })\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.make.tap('NextJsBuildManifest', (compilation) => {\n      compilation.hooks.processAssets.tap(\n        {\n          name: 'NextJsBuildManifest',\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        (assets: any) => {\n          this.createAssets(compiler, compilation, assets)\n        }\n      )\n    })\n    return\n  }\n}\n"], "names": ["BuildManifestPlugin", "generateClientManifest", "getEntrypointFiles", "normalizeRewritesForBuildManifest", "processRoute", "srcEmptySsgManifest", "buildNodejsLowPriorityPath", "filename", "buildId", "CLIENT_STATIC_FILES_PATH", "createEdgeRuntimeManifest", "originAssetMap", "manifestFilenames", "assetMap", "lowPriorityFiles", "manifestDefCode", "JSON", "stringify", "lowPriorityFilesCode", "map", "join", "normalizeRewrite", "item", "has", "source", "destination", "rewrites", "afterFiles", "beforeFiles", "fallback", "clientRouterFilters", "compiler", "compilation", "compilationSpan", "spans", "get", "Span", "name", "genClientManifestSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "clientManifest", "__rewrites", "__routerFilterStatic", "staticFilter", "__routerFilterDynamic", "dynamicFilter", "appDependencies", "Set", "pages", "sortedPageKeys", "getSortedRoutes", "Object", "keys", "for<PERSON>ach", "page", "dependencies", "filteredDeps", "filter", "dep", "length", "sortedPages", "devalue", "entrypoint", "getFiles", "file", "test", "replace", "r", "rewrite", "startsWith", "constructor", "options", "isDev<PERSON><PERSON><PERSON>", "appDirEnabled", "createAssets", "assets", "createAssetsSpan", "entrypoints", "polyfillFiles", "devFiles", "ampDevFiles", "rootMainFiles", "rootMainFilesTree", "ampFirstPages", "ampFirstEntryNames", "ampFirstEntryNamesMap", "entryName", "pagePath", "getRouteFromEntrypoint", "push", "mainFiles", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "compilationAssets", "getAssets", "p", "endsWith", "info", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "v", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "values", "SYSTEM_ENTRYPOINTS", "filesForPage", "buildManifestPath", "ssgManifestPath", "sources", "RawSource", "sort", "reduce", "a", "c", "buildManifestName", "BUILD_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "clientManifestPath", "apply", "hooks", "make", "tap", "processAssets", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": ";;;;;;;;;;;;;;;;;;;IA0KA,iFAAiF;IACjF,+GAA+G;IAC/G,OAsLC;eAtLoBA;;IA7ELC,sBAAsB;eAAtBA;;IAoDAC,kBAAkB;eAAlBA;;IAtEAC,iCAAiC;eAAjCA;;IAkFHC,YAAY;eAAZA;;IAhIAC,mBAAmB;eAAnBA;;;gEA7BO;yBACa;2BAW1B;+EAE4B;0CACG;uBACN;iCACV;uBACD;;;;;;AAWd,MAAMA,sBAAsB,CAAC,4EAA4E,CAAC;AAEjH,+CAA+C;AAC/C,SAASC,2BAA2BC,QAAgB,EAAEC,OAAe;IACnE,OAAO,GAAGC,mCAAwB,CAAC,CAAC,EAAED,QAAQ,CAAC,EAAED,UAAU;AAC7D;AAEA,SAASG,0BAA0BC,cAA6B;IAC9D,MAAMC,oBAAoB;QAAC;QAAqB;KAAkB;IAElE,MAAMC,WAA0B;QAC9B,GAAGF,cAAc;QACjBG,kBAAkB,EAAE;IACtB;IAEA,MAAMC,kBAAkB,CAAC,wBAAwB,EAAEC,KAAKC,SAAS,CAC/DJ,UACA,MACA,GACA,GAAG,CAAC;IACN,+FAA+F;IAC/F,gIAAgI;IAChI,MAAMK,uBACJ,CAAC,4CAA4C,CAAC,GAC9CN,kBACGO,GAAG,CAAC,CAACZ;QACJ,OAAO,CAAC,6CAA6C,EAAEA,SAAS,IAAI,CAAC;IACvE,GACCa,IAAI,CAAC,OACR,CAAC,IAAI,CAAC;IAER,OAAOL,kBAAkBG;AAC3B;AAEA,SAASG,iBAAiBC,IAIzB;IACC,OAAO;QACLC,KAAKD,KAAKC,GAAG;QACbC,QAAQF,KAAKE,MAAM;QACnBC,aAAaH,KAAKG,WAAW;IAC/B;AACF;AAEO,SAAStB,kCACduB,QAAkC;QAGpBA,0BAAAA,sBAGCA,2BAAAA,uBAGHA,wBAAAA;IAPZ,OAAO;QACLC,UAAU,GAAED,uBAAAA,SAASC,UAAU,sBAAnBD,2BAAAA,qBACRP,GAAG,CAACf,kCADIsB,yBAERP,GAAG,CAAC,CAACG,OAASD,iBAAiBC;QACnCM,WAAW,GAAEF,wBAAAA,SAASE,WAAW,sBAApBF,4BAAAA,sBACTP,GAAG,CAACf,kCADKsB,0BAETP,GAAG,CAAC,CAACG,OAASD,iBAAiBC;QACnCO,QAAQ,GAAEH,qBAAAA,SAASG,QAAQ,sBAAjBH,yBAAAA,mBACNP,GAAG,CAACf,kCADEsB,uBAENP,GAAG,CAAC,CAACG,OAASD,iBAAiBC;IACrC;AACF;AAIO,SAASrB,uBACdY,QAAuB,EACvBa,QAAkC,EAClCI,mBAGC,EACDC,QAAc,EACdC,WAAiB;IAEjB,MAAMC,kBAAkBD,cACpBE,sBAAK,CAACC,GAAG,CAACH,eACVD,WACEG,sBAAK,CAACC,GAAG,CAACJ,YACV,IAAIK,WAAI,CAAC;QAAEC,MAAM;IAAkB;IAEzC,MAAMC,wBAAwBL,mCAAAA,gBAAiBM,UAAU,CACvD;IAGF,OAAOD,yCAAAA,sBAAuBE,OAAO,CAAC;QACpC,MAAMC,iBAAsC;YAC1CC,YAAYvC,kCAAkCuB;YAC9CiB,oBAAoB,EAAEb,uCAAAA,oBAAqBc,YAAY;YACvDC,qBAAqB,EAAEf,uCAAAA,oBAAqBgB,aAAa;QAC3D;QACA,MAAMC,kBAAkB,IAAIC,IAAInC,SAASoC,KAAK,CAAC,QAAQ;QACvD,MAAMC,iBAAiBC,IAAAA,sBAAe,EAACC,OAAOC,IAAI,CAACxC,SAASoC,KAAK;QAEjEC,eAAeI,OAAO,CAAC,CAACC;YACtB,MAAMC,eAAe3C,SAASoC,KAAK,CAACM,KAAK;YAEzC,IAAIA,SAAS,SAAS;YACtB,6EAA6E;YAC7E,wDAAwD;YACxD,MAAME,eAAeD,aAAaE,MAAM,CACtC,CAACC,MAAQ,CAACZ,gBAAgBxB,GAAG,CAACoC;YAGhC,2DAA2D;YAC3D,IAAIF,aAAaG,MAAM,EAAE;gBACvBnB,cAAc,CAACc,KAAK,GAAGE;YACzB;QACF;QACA,6EAA6E;QAC7E,qEAAqE;QACrEhB,eAAeoB,WAAW,GAAGX;QAE7B,OAAOY,IAAAA,gBAAO,EAACrB;IACjB;AACF;AAEO,SAASvC,mBAAmB6D,UAAe;IAChD,OACEA,CAAAA,8BAAAA,WACIC,QAAQ,GACTN,MAAM,CAAC,CAACO;QACP,wEAAwE;QACxE,OAAO,oCAAoCC,IAAI,CAACD;IAClD,GACC9C,GAAG,CAAC,CAAC8C,OAAiBA,KAAKE,OAAO,CAAC,OAAO,UAAS,EAAE;AAE5D;AAEO,MAAM/D,eAAe,CAACgE;QAKtBC;IAJL,MAAMA,UAAU;QAAE,GAAGD,CAAC;IAAC;IAEvB,wDAAwD;IACxD,sBAAsB;IACtB,IAAI,EAACC,4BAAAA,uBAAAA,QAAS5C,WAAW,qBAApB4C,qBAAsBC,UAAU,CAAC,OAAM;QAC1C,OAAO,AAACD,QAAgB5C,WAAW;IACrC;IACA,OAAO4C;AACT;AAIe,MAAMrE;IAOnBuE,YAAYC,OAMX,CAAE;QACD,IAAI,CAAChE,OAAO,GAAGgE,QAAQhE,OAAO;QAC9B,IAAI,CAACiE,aAAa,GAAG,CAAC,CAACD,QAAQC,aAAa;QAC5C,IAAI,CAAC/C,QAAQ,GAAG;YACdE,aAAa,EAAE;YACfD,YAAY,EAAE;YACdE,UAAU,EAAE;QACd;QACA,IAAI,CAAC6C,aAAa,GAAGF,QAAQE,aAAa;QAC1C,IAAI,CAAC5C,mBAAmB,GAAG0C,QAAQ1C,mBAAmB;QACtD,IAAI,CAACJ,QAAQ,CAACE,WAAW,GAAG4C,QAAQ9C,QAAQ,CAACE,WAAW,CAACT,GAAG,CAACf;QAC7D,IAAI,CAACsB,QAAQ,CAACC,UAAU,GAAG6C,QAAQ9C,QAAQ,CAACC,UAAU,CAACR,GAAG,CAACf;QAC3D,IAAI,CAACsB,QAAQ,CAACG,QAAQ,GAAG2C,QAAQ9C,QAAQ,CAACG,QAAQ,CAACV,GAAG,CAACf;IACzD;IAEAuE,aAAa5C,QAAa,EAAEC,WAAgB,EAAE4C,MAAW,EAAE;QACzD,MAAM3C,kBAAkBC,sBAAK,CAACC,GAAG,CAACH,gBAAgBE,sBAAK,CAACC,GAAG,CAACJ;QAC5D,MAAM8C,mBAAmB5C,mCAAAA,gBAAiBM,UAAU,CAClD;QAEF,OAAOsC,oCAAAA,iBAAkBrC,OAAO,CAAC;YAC/B,MAAMsC,cAAgC9C,YAAY8C,WAAW;YAC7D,MAAMjE,WAAuC;gBAC3CkE,eAAe,EAAE;gBACjBC,UAAU,EAAE;gBACZC,aAAa,EAAE;gBACfnE,kBAAkB,EAAE;gBACpBoE,eAAe,EAAE;gBACjBC,mBAAmB,CAAC;gBACpBlC,OAAO;oBAAE,SAAS,EAAE;gBAAC;gBACrBmC,eAAe,EAAE;YACnB;YAEA,MAAMC,qBAAqBC,+CAAqB,CAACnD,GAAG,CAACH;YACrD,IAAIqD,oBAAoB;gBACtB,KAAK,MAAME,aAAaF,mBAAoB;oBAC1C,MAAMG,WAAWC,IAAAA,+BAAsB,EAACF;oBACxC,IAAI,CAACC,UAAU;wBACb;oBACF;oBAEA3E,SAASuE,aAAa,CAACM,IAAI,CAACF;gBAC9B;YACF;YAEA,MAAMG,YAAY,IAAI3C,IACpB9C,mBAAmB4E,YAAY3C,GAAG,CAACyD,2CAAgC;YAGrE,IAAI,IAAI,CAAClB,aAAa,EAAE;gBACtB7D,SAASqE,aAAa,GAAG;uBACpB,IAAIlC,IACL9C,mBACE4E,YAAY3C,GAAG,CAAC0D,+CAAoC;iBAGzD;YACH;YAEA,MAAMC,oBAIA9D,YAAY+D,SAAS;YAE3BlF,SAASkE,aAAa,GAAGe,kBACtBpC,MAAM,CAAC,CAACsC;gBACP,2CAA2C;gBAC3C,IAAI,CAACA,EAAE3D,IAAI,CAAC4D,QAAQ,CAAC,QAAQ;oBAC3B,OAAO;gBACT;gBAEA,OACED,EAAEE,IAAI,IAAIC,uDAA4C,IAAIH,EAAEE,IAAI;YAEpE,GACC/E,GAAG,CAAC,CAACiF,IAAMA,EAAE/D,IAAI;YAEpBxB,SAASmE,QAAQ,GAAG9E,mBAClB4E,YAAY3C,GAAG,CAACkE,oDAAyC,GACzD3C,MAAM,CAAC,CAACO,OAAS,CAAC0B,UAAUpE,GAAG,CAAC0C;YAElCpD,SAASoE,WAAW,GAAG/E,mBACrB4E,YAAY3C,GAAG,CAACmE,0CAA+B;YAGjD,KAAK,MAAMvC,cAAc/B,YAAY8C,WAAW,CAACyB,MAAM,GAAI;gBACzD,IAAIC,6BAAkB,CAACjF,GAAG,CAACwC,WAAW1B,IAAI,GAAG;gBAC7C,MAAMmD,WAAWC,IAAAA,+BAAsB,EAAC1B,WAAW1B,IAAI;gBAEvD,IAAI,CAACmD,UAAU;oBACb;gBACF;gBAEA,MAAMiB,eAAevG,mBAAmB6D;gBAExClD,SAASoC,KAAK,CAACuC,SAAS,GAAG;uBAAI,IAAIxC,IAAI;2BAAI2C;2BAAcc;qBAAa;iBAAE;YAC1E;YAEA,IAAI,CAAC,IAAI,CAAChC,aAAa,EAAE;gBACvB,qEAAqE;gBACrE,uEAAuE;gBACvE,4BAA4B;gBAC5B,MAAMiC,oBAAoBpG,2BACxB,qBACA,IAAI,CAACE,OAAO;gBAEd,MAAMmG,kBAAkBrG,2BACtB,mBACA,IAAI,CAACE,OAAO;gBAEdK,SAASC,gBAAgB,CAAC4E,IAAI,CAACgB,mBAAmBC;gBAClD/B,MAAM,CAAC+B,gBAAgB,GAAG,IAAIC,gBAAO,CAACC,SAAS,CAACxG;YAClD;YAEAQ,SAASoC,KAAK,GAAGG,OAAOC,IAAI,CAACxC,SAASoC,KAAK,EACxC6D,IAAI,GACJC,MAAM,CACL,2BAA2B;YAC3B,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGpG,SAASoC,KAAK,CAACgE,EAAE,EAAGD,CAAAA,GACvC,CAAC;YAGL,IAAIE,oBAAoBC,yBAAc;YAEtC,IAAI,IAAI,CAAC1C,aAAa,EAAE;gBACtByC,oBAAoB,CAAC,SAAS,EAAEC,yBAAc,EAAE;YAClD;YAEAvC,MAAM,CAACsC,kBAAkB,GAAG,IAAIN,gBAAO,CAACC,SAAS,CAC/C7F,KAAKC,SAAS,CAACJ,UAAU,MAAM;YAGjC+D,MAAM,CAAC,CAAC,OAAO,EAAEwC,oCAAyB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAIR,gBAAO,CAACC,SAAS,CACtE,GAAGnG,0BAA0BG,WAAW;YAG1C,IAAI,CAAC,IAAI,CAAC4D,aAAa,EAAE;gBACvB,MAAM4C,qBAAqB,GAAG5G,mCAAwB,CAAC,CAAC,EAAE,IAAI,CAACD,OAAO,CAAC,kBAAkB,CAAC;gBAE1FoE,MAAM,CAACyC,mBAAmB,GAAG,IAAIT,gBAAO,CAACC,SAAS,CAChD,CAAC,wBAAwB,EAAE5G,uBACzBY,UACA,IAAI,CAACa,QAAQ,EACb,IAAI,CAACI,mBAAmB,EACxBC,UACAC,aACA,uDAAuD,CAAC;YAE9D;YAEA,OAAO4C;QACT;IACF;IAEA0C,MAAMvF,QAA0B,EAAE;QAChCA,SAASwF,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAACzF;YAC9CA,YAAYuF,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACEpF,MAAM;gBACNsF,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAAClD;gBACC,IAAI,CAACD,YAAY,CAAC5C,UAAUC,aAAa4C;YAC3C;QAEJ;QACA;IACF;AACF"}