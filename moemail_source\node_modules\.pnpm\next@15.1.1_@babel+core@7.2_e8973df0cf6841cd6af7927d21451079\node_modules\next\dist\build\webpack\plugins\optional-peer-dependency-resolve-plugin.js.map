{"version": 3, "sources": ["../../../../src/build/webpack/plugins/optional-peer-dependency-resolve-plugin.ts"], "sourcesContent": ["import type { Resolver } from 'webpack'\n\nconst pluginSymbol = Symbol('OptionalPeerDependencyResolverPlugin')\n\nexport class OptionalPeerDependencyResolverPlugin {\n  apply(resolver: Resolver) {\n    const target = resolver.ensureHook('raw-module')\n    target.tapAsync(\n      'OptionalPeerDependencyResolverPlugin',\n      (request, resolveContext, callback) => {\n        // if we've already recursed into this plugin, we want to skip it\n        if ((request as any)[pluginSymbol]) {\n          return callback()\n        }\n\n        // popping the stack to prevent the recursion check\n        resolveContext.stack?.delete(Array.from(resolveContext.stack).pop()!)\n\n        resolver.doResolve(\n          target,\n          // when we call doResolve again, we need to make sure we don't\n          // recurse into this plugin again\n          { ...request, [pluginSymbol]: true } as any,\n          null,\n          resolveContext,\n          (err, result) => {\n            if (\n              !result &&\n              request?.descriptionFileData?.peerDependenciesMeta &&\n              request.request\n            ) {\n              const peerDependenciesMeta = request.descriptionFileData\n                .peerDependenciesMeta as Record<string, { optional?: boolean }>\n\n              const isOptional =\n                peerDependenciesMeta &&\n                peerDependenciesMeta[request.request] &&\n                peerDependenciesMeta[request.request].optional\n\n              if (isOptional) {\n                return callback(null, {\n                  path: false,\n                })\n              }\n            }\n\n            return callback(err, result)\n          }\n        )\n      }\n    )\n  }\n}\n"], "names": ["OptionalPeerDependencyResolverPlugin", "pluginSymbol", "Symbol", "apply", "resolver", "target", "ensureH<PERSON>", "tapAsync", "request", "resolveContext", "callback", "stack", "delete", "Array", "from", "pop", "doResolve", "err", "result", "descriptionFileData", "peerDependenciesMeta", "isOptional", "optional", "path"], "mappings": ";;;;+BAIaA;;;eAAAA;;;AAFb,MAAMC,eAAeC,OAAO;AAErB,MAAMF;IACXG,MAAMC,QAAkB,EAAE;QACxB,MAAMC,SAASD,SAASE,UAAU,CAAC;QACnCD,OAAOE,QAAQ,CACb,wCACA,CAACC,SAASC,gBAAgBC;gBAMxB,mDAAmD;YACnDD;YANA,iEAAiE;YACjE,IAAI,AAACD,OAAe,CAACP,aAAa,EAAE;gBAClC,OAAOS;YACT;aAGAD,wBAAAA,eAAeE,KAAK,qBAApBF,sBAAsBG,MAAM,CAACC,MAAMC,IAAI,CAACL,eAAeE,KAAK,EAAEI,GAAG;YAEjEX,SAASY,SAAS,CAChBX,QACA,8DAA8D;YAC9D,iCAAiC;YACjC;gBAAE,GAAGG,OAAO;gBAAE,CAACP,aAAa,EAAE;YAAK,GACnC,MACAQ,gBACA,CAACQ,KAAKC;oBAGFV;gBAFF,IACE,CAACU,WACDV,4BAAAA,+BAAAA,QAASW,mBAAmB,qBAA5BX,6BAA8BY,oBAAoB,KAClDZ,QAAQA,OAAO,EACf;oBACA,MAAMY,uBAAuBZ,QAAQW,mBAAmB,CACrDC,oBAAoB;oBAEvB,MAAMC,aACJD,wBACAA,oBAAoB,CAACZ,QAAQA,OAAO,CAAC,IACrCY,oBAAoB,CAACZ,QAAQA,OAAO,CAAC,CAACc,QAAQ;oBAEhD,IAAID,YAAY;wBACd,OAAOX,SAAS,MAAM;4BACpBa,MAAM;wBACR;oBACF;gBACF;gBAEA,OAAOb,SAASO,KAAKC;YACvB;QAEJ;IAEJ;AACF"}