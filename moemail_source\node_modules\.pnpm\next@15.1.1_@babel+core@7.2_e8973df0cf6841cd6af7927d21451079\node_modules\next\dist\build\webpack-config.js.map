{"version": 3, "sources": ["../../src/build/webpack-config.ts"], "sourcesContent": ["import React from 'react'\nimport ReactRefreshWebpackPlugin from 'next/dist/compiled/@next/react-refresh-utils/dist/ReactRefreshWebpackPlugin'\nimport { yellow, bold } from '../lib/picocolors'\nimport crypto from 'crypto'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport path from 'path'\n\nimport { escapeStringRegexp } from '../shared/lib/escape-regexp'\nimport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES } from '../lib/constants'\nimport type { WebpackLayerName } from '../lib/constants'\nimport {\n  isWebpackBundledLayer,\n  isWebpackClientOnlyLayer,\n  isWebpackDefaultLayer,\n  isWebpackServerOnlyLayer,\n} from './utils'\nimport type { CustomRoutes } from '../lib/load-custom-routes.js'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_WEBPACK,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  SERVER_DIRECTORY,\n  COMPILER_NAMES,\n} from '../shared/lib/constants'\nimport type { CompilerNameValues } from '../shared/lib/constants'\nimport { execOnce } from '../shared/lib/utils'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport { finalizeEntrypoint } from './entries'\nimport * as Log from './output/log'\nimport { buildConfiguration } from './webpack/config'\nimport MiddlewarePlugin, {\n  getEdgePolyfilledModules,\n  handleWebpackExternalForEdgeRuntime,\n} from './webpack/plugins/middleware-plugin'\nimport BuildManifestPlugin from './webpack/plugins/build-manifest-plugin'\nimport { JsConfigPathsPlugin } from './webpack/plugins/jsconfig-paths-plugin'\nimport { DropClientPage } from './webpack/plugins/next-drop-client-page-plugin'\nimport PagesManifestPlugin from './webpack/plugins/pages-manifest-plugin'\nimport { ProfilingPlugin } from './webpack/plugins/profiling-plugin'\nimport { ReactLoadablePlugin } from './webpack/plugins/react-loadable-plugin'\nimport { WellKnownErrorsPlugin } from './webpack/plugins/wellknown-errors-plugin'\nimport { regexLikeCss } from './webpack/config/blocks/css'\nimport { CopyFilePlugin } from './webpack/plugins/copy-file-plugin'\nimport { ClientReferenceManifestPlugin } from './webpack/plugins/flight-manifest-plugin'\nimport { FlightClientEntryPlugin } from './webpack/plugins/flight-client-entry-plugin'\nimport { NextTypesPlugin } from './webpack/plugins/next-types-plugin'\nimport type {\n  Feature,\n  SWC_TARGET_TRIPLE,\n} from './webpack/plugins/telemetry-plugin'\nimport type { Span } from '../trace'\nimport type { MiddlewareMatcher } from './analysis/get-page-static-info'\nimport loadJsConfig, {\n  type JsConfig,\n  type ResolvedBaseUrl,\n} from './load-jsconfig'\nimport { loadBindings } from './swc'\nimport { AppBuildManifestPlugin } from './webpack/plugins/app-build-manifest-plugin'\nimport { SubresourceIntegrityPlugin } from './webpack/plugins/subresource-integrity-plugin'\nimport { NextFontManifestPlugin } from './webpack/plugins/next-font-manifest-plugin'\nimport { getSupportedBrowsers } from './utils'\nimport { MemoryWithGcCachePlugin } from './webpack/plugins/memory-with-gc-cache-plugin'\nimport { getBabelConfigFile } from './get-babel-config-file'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport { getDefineEnvPlugin } from './webpack/plugins/define-env-plugin'\nimport type { SWCLoaderOptions } from './webpack/loaders/next-swc-loader'\nimport { isResourceInPackages, makeExternalHandler } from './handle-externals'\nimport {\n  getMainField,\n  edgeConditionNames,\n} from './webpack-config-rules/resolve'\nimport { OptionalPeerDependencyResolverPlugin } from './webpack/plugins/optional-peer-dependency-resolve-plugin'\nimport {\n  createWebpackAliases,\n  createServerOnlyClientOnlyAliases,\n  createRSCAliases,\n  createNextApiEsmAliases,\n  createAppRouterApiAliases,\n} from './create-compiler-aliases'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { CssChunkingPlugin } from './webpack/plugins/css-chunking-plugin'\nimport {\n  getBabelLoader,\n  getReactCompilerLoader,\n} from './get-babel-loader-config'\nimport {\n  NEXT_PROJECT_ROOT,\n  NEXT_PROJECT_ROOT_DIST_CLIENT,\n} from './next-dir-paths'\n\ntype ExcludesFalse = <T>(x: T | false) => x is T\ntype ClientEntries = {\n  [key: string]: string | string[]\n}\n\nconst EXTERNAL_PACKAGES =\n  require('../lib/server-external-packages.json') as string[]\n\nconst DEFAULT_TRANSPILED_PACKAGES =\n  require('../lib/default-transpiled-packages.json') as string[]\n\nif (parseInt(React.version) < 18) {\n  throw new Error('Next.js requires react >= 18.2.0 to be installed.')\n}\n\nexport const babelIncludeRegexes: RegExp[] = [\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?shared[\\\\/]lib/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?client/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?pages/,\n  /[\\\\/](strip-ansi|ansi-regex|styled-jsx)[\\\\/]/,\n]\n\nconst browserNonTranspileModules = [\n  // Transpiling `process/browser` will trigger babel compilation error due to value replacement.\n  // TypeError: Property left of AssignmentExpression expected node to be of a type [\"LVal\"] but instead got \"BooleanLiteral\"\n  // e.g. `process.browser = true` will become `true = true`.\n  /[\\\\/]node_modules[\\\\/]process[\\\\/]browser/,\n  // Exclude precompiled react packages from browser compilation due to SWC helper insertion (#61791),\n  // We fixed the issue but it's safer to exclude them from compilation since they don't need to be re-compiled.\n  /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/](react|react-dom|react-server-dom-webpack)(-experimental)?($|[\\\\/])/,\n]\nconst precompileRegex = /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/]/\n\nconst asyncStoragesRegex =\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]app-render[\\\\/](work-async-storage|action-async-storage|work-unit-async-storage)/\n\n// Support for NODE_PATH\nconst nodePathList = (process.env.NODE_PATH || '')\n  .split(process.platform === 'win32' ? ';' : ':')\n  .filter((p) => !!p)\n\nconst baseWatchOptions: webpack.Configuration['watchOptions'] = Object.freeze({\n  aggregateTimeout: 5,\n  ignored:\n    // Matches **/node_modules/**, **/.git/** and **/.next/**\n    /^((?:[^/]*(?:\\/|$))*)(\\.(git|next)|node_modules)(\\/((?:[^/]*(?:\\/|$))*)(?:$|\\/))?/,\n})\n\nfunction isModuleCSS(module: { type: string }) {\n  return (\n    // mini-css-extract-plugin\n    module.type === `css/mini-extract` ||\n    // extract-css-chunks-webpack-plugin (old)\n    module.type === `css/extract-chunks` ||\n    // extract-css-chunks-webpack-plugin (new)\n    module.type === `css/extract-css-chunks`\n  )\n}\n\nconst devtoolRevertWarning = execOnce(\n  (devtool: webpack.Configuration['devtool']) => {\n    console.warn(\n      yellow(bold('Warning: ')) +\n        bold(`Reverting webpack devtool to '${devtool}'.\\n`) +\n        'Changing the webpack devtool in development mode will cause severe performance regressions.\\n' +\n        'Read more: https://nextjs.org/docs/messages/improper-devtool'\n    )\n  }\n)\n\nlet loggedSwcDisabled = false\nlet loggedIgnoredCompilerOptions = false\nconst reactRefreshLoaderName =\n  'next/dist/compiled/@next/react-refresh-utils/dist/loader'\n\nexport function attachReactRefresh(\n  webpackConfig: webpack.Configuration,\n  targetLoader: webpack.RuleSetUseItem\n) {\n  const reactRefreshLoader = require.resolve(reactRefreshLoaderName)\n  webpackConfig.module?.rules?.forEach((rule) => {\n    if (rule && typeof rule === 'object' && 'use' in rule) {\n      const curr = rule.use\n      // When the user has configured `defaultLoaders.babel` for a input file:\n      if (curr === targetLoader) {\n        rule.use = [reactRefreshLoader, curr as webpack.RuleSetUseItem]\n      } else if (\n        Array.isArray(curr) &&\n        curr.some((r) => r === targetLoader) &&\n        // Check if loader already exists:\n        !curr.some(\n          (r) => r === reactRefreshLoader || r === reactRefreshLoaderName\n        )\n      ) {\n        const idx = curr.findIndex((r) => r === targetLoader)\n        // Clone to not mutate user input\n        rule.use = [...curr]\n\n        // inject / input: [other, babel] output: [other, refresh, babel]:\n        rule.use.splice(idx, 0, reactRefreshLoader)\n      }\n    }\n  })\n}\n\nexport const NODE_RESOLVE_OPTIONS = {\n  dependencyType: 'commonjs',\n  modules: ['node_modules'],\n  fallback: false,\n  exportsFields: ['exports'],\n  importsFields: ['imports'],\n  conditionNames: ['node', 'require'],\n  descriptionFiles: ['package.json'],\n  extensions: ['.js', '.json', '.node'],\n  enforceExtensions: false,\n  symlinks: true,\n  mainFields: ['main'],\n  mainFiles: ['index'],\n  roots: [],\n  fullySpecified: false,\n  preferRelative: false,\n  preferAbsolute: false,\n  restrictions: [],\n}\n\nexport const NODE_BASE_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const NODE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n  dependencyType: 'esm',\n  conditionNames: ['node', 'import'],\n  fullySpecified: true,\n}\n\nexport const NODE_BASE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_ESM_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const nextImageLoaderRegex =\n  /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp|svg)$/i\n\nexport async function loadProjectInfo({\n  dir,\n  config,\n  dev,\n}: {\n  dir: string\n  config: NextConfigComplete\n  dev: boolean\n}): Promise<{\n  jsConfig: JsConfig\n  resolvedBaseUrl: ResolvedBaseUrl\n  supportedBrowsers: string[] | undefined\n}> {\n  const { jsConfig, resolvedBaseUrl } = await loadJsConfig(dir, config)\n  const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n  return {\n    jsConfig,\n    resolvedBaseUrl,\n    supportedBrowsers,\n  }\n}\n\nexport function hasExternalOtelApiPackage(): boolean {\n  try {\n    require('@opentelemetry/api')\n    return true\n  } catch {\n    return false\n  }\n}\n\nconst UNSAFE_CACHE_REGEX = /[\\\\/]pages[\\\\/][^\\\\/]+(?:$|\\?|#)/\n\nexport default async function getBaseWebpackConfig(\n  dir: string,\n  {\n    buildId,\n    encryptionKey,\n    config,\n    compilerType,\n    dev = false,\n    entrypoints,\n    isDevFallback = false,\n    pagesDir,\n    reactProductionProfiling = false,\n    rewrites,\n    originalRewrites,\n    originalRedirects,\n    runWebpackSpan,\n    appDir,\n    middlewareMatchers,\n    jsConfig,\n    resolvedBaseUrl,\n    supportedBrowsers,\n    clientRouterFilters,\n    fetchCacheKeyPrefix,\n    edgePreviewProps,\n  }: {\n    buildId: string\n    encryptionKey: string\n    config: NextConfigComplete\n    compilerType: CompilerNameValues\n    dev?: boolean\n    entrypoints: webpack.EntryObject\n    isDevFallback?: boolean\n    pagesDir?: string\n    reactProductionProfiling?: boolean\n    rewrites: CustomRoutes['rewrites']\n    originalRewrites: CustomRoutes['rewrites'] | undefined\n    originalRedirects: CustomRoutes['redirects'] | undefined\n    runWebpackSpan: Span\n    appDir?: string\n    middlewareMatchers?: MiddlewareMatcher[]\n    noMangling?: boolean\n    jsConfig: any\n    resolvedBaseUrl: ResolvedBaseUrl\n    supportedBrowsers: string[] | undefined\n    edgePreviewProps?: Record<string, string>\n    clientRouterFilters?: {\n      staticFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n      dynamicFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n    }\n    fetchCacheKeyPrefix?: string\n  }\n): Promise<webpack.Configuration> {\n  const isClient = compilerType === COMPILER_NAMES.client\n  const isEdgeServer = compilerType === COMPILER_NAMES.edgeServer\n  const isNodeServer = compilerType === COMPILER_NAMES.server\n\n  // If the current compilation is aimed at server-side code instead of client-side code.\n  const isNodeOrEdgeCompilation = isNodeServer || isEdgeServer\n\n  const hasRewrites =\n    rewrites.beforeFiles.length > 0 ||\n    rewrites.afterFiles.length > 0 ||\n    rewrites.fallback.length > 0\n\n  const hasAppDir = !!appDir\n  const disableOptimizedLoading = true\n  const enableTypedRoutes = !!config.experimental.typedRoutes && hasAppDir\n  const bundledReactChannel = needsExperimentalReact(config)\n    ? '-experimental'\n    : ''\n\n  const babelConfigFile = getBabelConfigFile(dir)\n\n  if (!dev && hasCustomExportOutput(config)) {\n    config.distDir = '.next'\n  }\n  const distDir = path.join(dir, config.distDir)\n\n  let useSWCLoader = !babelConfigFile || config.experimental.forceSwcTransforms\n  let SWCBinaryTarget: [Feature, boolean] | undefined = undefined\n  if (useSWCLoader) {\n    // TODO: we do not collect wasm target yet\n    const binaryTarget = require('./swc')?.getBinaryMetadata?.()\n      ?.target as SWC_TARGET_TRIPLE\n    SWCBinaryTarget = binaryTarget\n      ? [`swc/target/${binaryTarget}` as const, true]\n      : undefined\n  }\n\n  if (!loggedSwcDisabled && !useSWCLoader && babelConfigFile) {\n    Log.info(\n      `Disabled SWC as replacement for Babel because of custom Babel configuration \"${path.relative(\n        dir,\n        babelConfigFile\n      )}\" https://nextjs.org/docs/messages/swc-disabled`\n    )\n    loggedSwcDisabled = true\n  }\n\n  // eagerly load swc bindings instead of waiting for transform calls\n  if (!babelConfigFile && isClient) {\n    await loadBindings(config.experimental.useWasmBinary)\n  }\n\n  // since `pages` doesn't always bundle by default we need to\n  // auto-include optimizePackageImports in transpilePackages\n  const finalTranspilePackages: string[] = (\n    config.transpilePackages || []\n  ).concat(DEFAULT_TRANSPILED_PACKAGES)\n\n  for (const pkg of config.experimental.optimizePackageImports || []) {\n    if (!finalTranspilePackages.includes(pkg)) {\n      finalTranspilePackages.push(pkg)\n    }\n  }\n\n  if (!loggedIgnoredCompilerOptions && !useSWCLoader && config.compiler) {\n    Log.info(\n      '`compiler` options in `next.config.js` will be ignored while using Babel https://nextjs.org/docs/messages/ignored-compiler-options'\n    )\n    loggedIgnoredCompilerOptions = true\n  }\n\n  const shouldIncludeExternalDirs =\n    config.experimental.externalDir || !!config.transpilePackages\n  const codeCondition = {\n    test: { or: [/\\.(tsx|ts|js|cjs|mjs|jsx)$/, /__barrel_optimize__/] },\n    ...(shouldIncludeExternalDirs\n      ? // Allowing importing TS/TSX files from outside of the root dir.\n        {}\n      : { include: [dir, ...babelIncludeRegexes] }),\n    exclude: (excludePath: string) => {\n      if (babelIncludeRegexes.some((r) => r.test(excludePath))) {\n        return false\n      }\n\n      const shouldBeBundled = isResourceInPackages(\n        excludePath,\n        finalTranspilePackages\n      )\n      if (shouldBeBundled) return false\n\n      return excludePath.includes('node_modules')\n    },\n  }\n\n  const babelLoader = getBabelLoader(\n    useSWCLoader,\n    babelConfigFile,\n    isNodeOrEdgeCompilation,\n    distDir,\n    pagesDir,\n    dir,\n    (appDir || pagesDir)!,\n    dev,\n    isClient,\n    config.experimental?.reactCompiler,\n    codeCondition.exclude\n  )\n\n  const reactCompilerLoader = babelLoader\n    ? undefined\n    : getReactCompilerLoader(\n        config.experimental?.reactCompiler,\n        dir,\n        dev,\n        isNodeOrEdgeCompilation,\n        codeCondition.exclude\n      )\n\n  let swcTraceProfilingInitialized = false\n  const getSwcLoader = (extraOptions: Partial<SWCLoaderOptions>) => {\n    if (\n      config?.experimental?.swcTraceProfiling &&\n      !swcTraceProfilingInitialized\n    ) {\n      // This will init subscribers once only in a single process lifecycle,\n      // even though it can be called multiple times.\n      // Subscriber need to be initialized _before_ any actual swc's call (transform, etcs)\n      // to collect correct trace spans when they are called.\n      swcTraceProfilingInitialized = true\n      require('./swc')?.initCustomTraceSubscriber?.(\n        path.join(distDir, `swc-trace-profile-${Date.now()}.json`)\n      )\n    }\n\n    return {\n      loader: 'next-swc-loader',\n      options: {\n        isServer: isNodeOrEdgeCompilation,\n        rootDir: dir,\n        pagesDir,\n        appDir,\n        hasReactRefresh: dev && isClient,\n        nextConfig: config,\n        jsConfig,\n        transpilePackages: finalTranspilePackages,\n        supportedBrowsers,\n        swcCacheDir: path.join(dir, config?.distDir ?? '.next', 'cache', 'swc'),\n        serverReferenceHashSalt: encryptionKey,\n        ...extraOptions,\n      } satisfies SWCLoaderOptions,\n    }\n  }\n\n  // RSC loaders, prefer ESM, set `esm` to true\n  const swcServerLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.reactServerComponents,\n    esm: true,\n  })\n  const swcSSRLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.serverSideRendering,\n    esm: true,\n  })\n  const swcBrowserLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.appPagesBrowser,\n    esm: true,\n  })\n  // Default swc loaders for pages doesn't prefer ESM.\n  const swcDefaultLoader = getSwcLoader({\n    serverComponents: true,\n    esm: false,\n  })\n\n  const defaultLoaders = {\n    babel: useSWCLoader ? swcDefaultLoader : babelLoader!,\n  }\n\n  const appServerLayerLoaders = hasAppDir\n    ? [\n        // When using Babel, we will have to add the SWC loader\n        // as an additional pass to handle RSC correctly.\n        // This will cause some performance overhead but\n        // acceptable as Babel will not be recommended.\n        swcServerLayerLoader,\n        babelLoader,\n        reactCompilerLoader,\n      ].filter(Boolean)\n    : []\n\n  const instrumentLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to add the SWC loader\n    // as an additional pass to handle RSC correctly.\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    swcServerLayerLoader,\n    babelLoader,\n  ].filter(Boolean)\n\n  const middlewareLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to use SWC to do the optimization\n    // for middleware to tree shake the unused default optimized imports like \"next/server\".\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    getSwcLoader({\n      serverComponents: true,\n      bundleLayer: WEBPACK_LAYERS.middleware,\n    }),\n    babelLoader,\n  ].filter(Boolean)\n\n  const reactRefreshLoaders =\n    dev && isClient ? [require.resolve(reactRefreshLoaderName)] : []\n\n  // client components layers: SSR or browser\n  const createClientLayerLoader = ({\n    isBrowserLayer,\n    reactRefresh,\n  }: {\n    isBrowserLayer: boolean\n    reactRefresh: boolean\n  }) => [\n    ...(reactRefresh ? reactRefreshLoaders : []),\n    {\n      // This loader handles actions and client entries\n      // in the client layer.\n      loader: 'next-flight-client-module-loader',\n    },\n    ...(hasAppDir\n      ? [\n          // When using Babel, we will have to add the SWC loader\n          // as an additional pass to handle RSC correctly.\n          // This will cause some performance overhead but\n          // acceptable as Babel will not be recommended.\n          isBrowserLayer ? swcBrowserLayerLoader : swcSSRLayerLoader,\n          babelLoader,\n          reactCompilerLoader,\n        ].filter(Boolean)\n      : []),\n  ]\n\n  const appBrowserLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: true,\n    // reactRefresh for browser layer is applied conditionally to user-land source\n    reactRefresh: false,\n  })\n  const appSSRLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: false,\n    reactRefresh: true,\n  })\n\n  // Loader for API routes needs to be differently configured as it shouldn't\n  // have RSC transpiler enabled, so syntax checks such as invalid imports won't\n  // be performed.\n  const apiRoutesLayerLoaders = useSWCLoader\n    ? getSwcLoader({\n        serverComponents: false,\n        bundleLayer: WEBPACK_LAYERS.api,\n      })\n    : defaultLoaders.babel\n\n  const pageExtensions = config.pageExtensions\n\n  const outputPath = isNodeOrEdgeCompilation\n    ? path.join(distDir, SERVER_DIRECTORY)\n    : distDir\n\n  const reactServerCondition = [\n    'react-server',\n    ...(isEdgeServer ? edgeConditionNames : []),\n    // inherits the default conditions\n    '...',\n  ]\n\n  const clientEntries = isClient\n    ? ({\n        // Backwards compatibility\n        'main.js': [],\n        ...(dev\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]: require.resolve(\n                `next/dist/compiled/@next/react-refresh-utils/dist/runtime`\n              ),\n              [CLIENT_STATIC_FILES_RUNTIME_AMP]:\n                `./` +\n                path\n                  .relative(\n                    dir,\n                    path.join(NEXT_PROJECT_ROOT_DIST_CLIENT, 'dev', 'amp-dev')\n                  )\n                  .replace(/\\\\/g, '/'),\n            }\n          : {}),\n        [CLIENT_STATIC_FILES_RUNTIME_MAIN]:\n          `./` +\n          path\n            .relative(\n              dir,\n              path.join(\n                NEXT_PROJECT_ROOT_DIST_CLIENT,\n                dev ? `next-dev.js` : 'next.js'\n              )\n            )\n            .replace(/\\\\/g, '/'),\n        ...(hasAppDir\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_MAIN_APP]: dev\n                ? [\n                    require.resolve(\n                      `next/dist/compiled/@next/react-refresh-utils/dist/runtime`\n                    ),\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next-dev.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ]\n                : [\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ],\n            }\n          : {}),\n      } satisfies ClientEntries)\n    : undefined\n\n  const resolveConfig: webpack.Configuration['resolve'] = {\n    // Disable .mjs for node_modules bundling\n    extensions: ['.js', '.mjs', '.tsx', '.ts', '.jsx', '.json', '.wasm'],\n    extensionAlias: config.experimental.extensionAlias,\n    modules: [\n      'node_modules',\n      ...nodePathList, // Support for NODE_PATH environment variable\n    ],\n    alias: createWebpackAliases({\n      distDir,\n      isClient,\n      isEdgeServer,\n      isNodeServer,\n      dev,\n      config,\n      pagesDir,\n      appDir,\n      dir,\n      reactProductionProfiling,\n      hasRewrites,\n    }),\n    ...(isClient\n      ? {\n          fallback: {\n            process: require.resolve('./polyfills/process'),\n          },\n        }\n      : undefined),\n    // default main fields use pages dir ones, and customize app router ones in loaders.\n    mainFields: getMainField(compilerType, false),\n    ...(isEdgeServer && {\n      conditionNames: edgeConditionNames,\n    }),\n    plugins: [\n      isNodeServer ? new OptionalPeerDependencyResolverPlugin() : undefined,\n    ].filter(Boolean) as webpack.ResolvePluginInstance[],\n  }\n\n  // Packages which will be split into the 'framework' chunk.\n  // Only top-level packages are included, e.g. nested copies like\n  // 'node_modules/meow/node_modules/object-assign' are not included.\n  const nextFrameworkPaths: string[] = []\n  const topLevelFrameworkPaths: string[] = []\n  const visitedFrameworkPackages = new Set<string>()\n  // Adds package-paths of dependencies recursively\n  const addPackagePath = (\n    packageName: string,\n    relativeToPath: string,\n    paths: string[]\n  ) => {\n    try {\n      if (visitedFrameworkPackages.has(packageName)) {\n        return\n      }\n      visitedFrameworkPackages.add(packageName)\n\n      const packageJsonPath = require.resolve(`${packageName}/package.json`, {\n        paths: [relativeToPath],\n      })\n\n      // Include a trailing slash so that a `.startsWith(packagePath)` check avoids false positives\n      // when one package name starts with the full name of a different package.\n      // For example:\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react\")  // true\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react/\") // false\n      const directory = path.join(packageJsonPath, '../')\n\n      // Returning from the function in case the directory has already been added and traversed\n      if (paths.includes(directory)) return\n      paths.push(directory)\n      const dependencies = require(packageJsonPath).dependencies || {}\n      for (const name of Object.keys(dependencies)) {\n        addPackagePath(name, directory, paths)\n      }\n    } catch (_) {\n      // don't error on failing to resolve framework packages\n    }\n  }\n\n  for (const packageName of [\n    'react',\n    'react-dom',\n    ...(hasAppDir\n      ? [\n          `next/dist/compiled/react${bundledReactChannel}`,\n          `next/dist/compiled/react-dom${bundledReactChannel}`,\n        ]\n      : []),\n  ]) {\n    addPackagePath(packageName, dir, topLevelFrameworkPaths)\n  }\n  addPackagePath('next', dir, nextFrameworkPaths)\n\n  const crossOrigin = config.crossOrigin\n\n  // The `serverExternalPackages` should not conflict with\n  // the `transpilePackages`.\n  if (config.serverExternalPackages && finalTranspilePackages) {\n    const externalPackageConflicts = finalTranspilePackages.filter((pkg) =>\n      config.serverExternalPackages?.includes(pkg)\n    )\n    if (externalPackageConflicts.length > 0) {\n      throw new Error(\n        `The packages specified in the 'transpilePackages' conflict with the 'serverExternalPackages': ${externalPackageConflicts.join(\n          ', '\n        )}`\n      )\n    }\n  }\n\n  // For original request, such as `package name`\n  const optOutBundlingPackages = EXTERNAL_PACKAGES.concat(\n    ...(config.serverExternalPackages || [])\n  ).filter((pkg) => !finalTranspilePackages?.includes(pkg))\n  // For resolved request, such as `absolute path/package name/foo/bar.js`\n  const optOutBundlingPackageRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${optOutBundlingPackages\n      .map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const transpilePackagesRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${finalTranspilePackages\n      ?.map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const handleExternals = makeExternalHandler({\n    config,\n    optOutBundlingPackages,\n    optOutBundlingPackageRegex,\n    transpiledPackages: finalTranspilePackages,\n    dir,\n  })\n\n  const pageExtensionsRegex = new RegExp(`\\\\.(${pageExtensions.join('|')})$`)\n\n  const aliasCodeConditionTest = [codeCondition.test, pageExtensionsRegex]\n\n  const builtinModules = require('module').builtinModules\n\n  let webpackConfig: webpack.Configuration = {\n    parallelism: Number(process.env.NEXT_WEBPACK_PARALLELISM) || undefined,\n    ...(isNodeServer ? { externalsPresets: { node: true } } : {}),\n    // @ts-ignore\n    externals:\n      isClient || isEdgeServer\n        ? // make sure importing \"next\" is handled gracefully for client\n          // bundles in case a user imported types and it wasn't removed\n          // TODO: should we warn/error for this instead?\n          [\n            'next',\n            ...(isEdgeServer\n              ? [\n                  {\n                    '@builder.io/partytown': '{}',\n                    'next/dist/compiled/etag': '{}',\n                  },\n                  getEdgePolyfilledModules(),\n                  handleWebpackExternalForEdgeRuntime,\n                ]\n              : []),\n          ]\n        : [\n            ...builtinModules,\n            ({\n              context,\n              request,\n              dependencyType,\n              contextInfo,\n              getResolve,\n            }: {\n              context: string\n              request: string\n              dependencyType: string\n              contextInfo: {\n                issuer: string\n                issuerLayer: string | null\n                compiler: string\n              }\n              getResolve: (\n                options: any\n              ) => (\n                resolveContext: string,\n                resolveRequest: string,\n                callback: (\n                  err?: Error,\n                  result?: string,\n                  resolveData?: { descriptionFileData?: { type?: any } }\n                ) => void\n              ) => void\n            }) =>\n              handleExternals(\n                context,\n                request,\n                dependencyType,\n                contextInfo.issuerLayer as WebpackLayerName,\n                (options) => {\n                  const resolveFunction = getResolve(options)\n                  return (resolveContext: string, requestToResolve: string) =>\n                    new Promise((resolve, reject) => {\n                      resolveFunction(\n                        resolveContext,\n                        requestToResolve,\n                        (err, result, resolveData) => {\n                          if (err) return reject(err)\n                          if (!result) return resolve([null, false])\n                          const isEsm = /\\.js$/i.test(result)\n                            ? resolveData?.descriptionFileData?.type ===\n                              'module'\n                            : /\\.mjs$/i.test(result)\n                          resolve([result, isEsm])\n                        }\n                      )\n                    })\n                }\n              ),\n          ],\n\n    optimization: {\n      emitOnErrors: !dev,\n      checkWasmTypes: false,\n      nodeEnv: false,\n\n      splitChunks: (():\n        | Required<webpack.Configuration>['optimization']['splitChunks']\n        | false => {\n        // server chunking\n        if (dev) {\n          if (isNodeServer) {\n            /*\n              In development, we want to split code that comes from `node_modules` into their own chunks.\n              This is because in development, we often need to reload the user bundle due to changes in the code.\n              To work around this, we put all the vendor code into separate chunks so that we don't need to reload them.\n              This is safe because the vendor code doesn't change between reloads.\n            */\n            const extractRootNodeModule = (modulePath: string) => {\n              // This regex is used to extract the root node module name to be used as the chunk group name.\n              // example: ../../node_modules/.pnpm/next@10/foo/node_modules/bar -> next@10\n              const regex =\n                /node_modules(?:\\/|\\\\)\\.?(?:pnpm(?:\\/|\\\\))?([^/\\\\]+)/\n              const match = modulePath.match(regex)\n              return match ? match[1] : null\n            }\n            return {\n              cacheGroups: {\n                // this chunk configuration gives us a separate chunk for each top level module in node_modules\n                // or a hashed chunk if we can't extract the module name.\n                vendor: {\n                  chunks: 'all',\n                  reuseExistingChunk: true,\n                  test: /[\\\\/]node_modules[\\\\/]/,\n                  minSize: 0,\n                  minChunks: 1,\n                  maxAsyncRequests: 300,\n                  maxInitialRequests: 300,\n                  name: (module: webpack.Module) => {\n                    const moduleId = module.nameForCondition()!\n                    const rootModule = extractRootNodeModule(moduleId)\n                    if (rootModule) {\n                      return `vendor-chunks/${rootModule}`\n                    } else {\n                      const hash = crypto.createHash('sha1').update(moduleId)\n                      hash.update(moduleId)\n                      return `vendor-chunks/${hash.digest('hex')}`\n                    }\n                  },\n                },\n                // disable the default chunk groups\n                default: false,\n                defaultVendors: false,\n              },\n            }\n          }\n\n          return false\n        }\n\n        if (isNodeServer || isEdgeServer) {\n          return {\n            filename: `${isEdgeServer ? `edge-chunks/` : ''}[name].js`,\n            chunks: 'all',\n            minChunks: 2,\n          }\n        }\n\n        const frameworkCacheGroup = {\n          chunks: 'all' as const,\n          name: 'framework',\n          // Ensures the framework chunk is not created for App Router.\n          layer: isWebpackDefaultLayer,\n          test(module: any) {\n            const resource = module.nameForCondition?.()\n            return resource\n              ? topLevelFrameworkPaths.some((pkgPath) =>\n                  resource.startsWith(pkgPath)\n                )\n              : false\n          },\n          priority: 40,\n          // Don't let webpack eliminate this chunk (prevents this chunk from\n          // becoming a part of the commons chunk)\n          enforce: true,\n        }\n\n        const libCacheGroup = {\n          test(module: {\n            type: string\n            size: Function\n            nameForCondition: Function\n          }): boolean {\n            return (\n              !module.type?.startsWith('css') &&\n              module.size() > 160000 &&\n              /node_modules[/\\\\]/.test(module.nameForCondition() || '')\n            )\n          },\n          name(module: {\n            layer: string | null | undefined\n            type: string\n            libIdent?: Function\n            updateHash: (hash: crypto.Hash) => void\n          }): string {\n            const hash = crypto.createHash('sha1')\n            if (isModuleCSS(module)) {\n              module.updateHash(hash)\n            } else {\n              if (!module.libIdent) {\n                throw new Error(\n                  `Encountered unknown module type: ${module.type}. Please open an issue.`\n                )\n              }\n              hash.update(module.libIdent({ context: dir }))\n            }\n\n            // Ensures the name of the chunk is not the same between two modules in different layers\n            // E.g. if you import 'button-library' in App Router and Pages Router we don't want these to be bundled in the same chunk\n            // as they're never used on the same page.\n            if (module.layer) {\n              hash.update(module.layer)\n            }\n\n            return hash.digest('hex').substring(0, 8)\n          },\n          priority: 30,\n          minChunks: 1,\n          reuseExistingChunk: true,\n        }\n\n        // client chunking\n        return {\n          // Keep main and _app chunks unsplitted in webpack 5\n          // as we don't need a separate vendor chunk from that\n          // and all other chunk depend on them so there is no\n          // duplication that need to be pulled out.\n          chunks: (chunk: any) =>\n            !/^(polyfills|main|pages\\/_app)$/.test(chunk.name),\n          cacheGroups: {\n            framework: frameworkCacheGroup,\n            lib: libCacheGroup,\n          },\n          maxInitialRequests: 25,\n          minSize: 20000,\n        }\n      })(),\n      runtimeChunk: isClient\n        ? { name: CLIENT_STATIC_FILES_RUNTIME_WEBPACK }\n        : undefined,\n\n      minimize:\n        !dev &&\n        (isClient ||\n          isEdgeServer ||\n          (isNodeServer && config.experimental.serverMinification)),\n      minimizer: [\n        // Minify JavaScript\n        (compiler: webpack.Compiler) => {\n          // @ts-ignore No typings yet\n          const { MinifyPlugin } =\n            require('./webpack/plugins/minify-webpack-plugin/src/index.js') as typeof import('./webpack/plugins/minify-webpack-plugin/src')\n          new MinifyPlugin().apply(compiler)\n        },\n        // Minify CSS\n        (compiler: webpack.Compiler) => {\n          const {\n            CssMinimizerPlugin,\n          } = require('./webpack/plugins/css-minimizer-plugin')\n          new CssMinimizerPlugin({\n            postcssOptions: {\n              map: {\n                // `inline: false` generates the source map in a separate file.\n                // Otherwise, the CSS file is needlessly large.\n                inline: false,\n                // `annotation: false` skips appending the `sourceMappingURL`\n                // to the end of the CSS file. Webpack already handles this.\n                annotation: false,\n              },\n            },\n          }).apply(compiler)\n        },\n      ],\n    },\n    context: dir,\n    // Kept as function to be backwards compatible\n    entry: async () => {\n      return {\n        ...(clientEntries ? clientEntries : {}),\n        ...entrypoints,\n      }\n    },\n    watchOptions: Object.freeze({\n      ...baseWatchOptions,\n      poll: config.watchOptions?.pollIntervalMs,\n    }),\n    output: {\n      // we must set publicPath to an empty value to override the default of\n      // auto which doesn't work in IE11\n      publicPath: `${\n        config.assetPrefix\n          ? config.assetPrefix.endsWith('/')\n            ? config.assetPrefix.slice(0, -1)\n            : config.assetPrefix\n          : ''\n      }/_next/`,\n      path: !dev && isNodeServer ? path.join(outputPath, 'chunks') : outputPath,\n      // On the server we don't use hashes\n      filename: isNodeOrEdgeCompilation\n        ? dev || isEdgeServer\n          ? `[name].js`\n          : `../[name].js`\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}[name]${\n            dev ? '' : appDir ? '-[chunkhash]' : '-[contenthash]'\n          }.js`,\n      library: isClient || isEdgeServer ? '_N_E' : undefined,\n      libraryTarget: isClient || isEdgeServer ? 'assign' : 'commonjs2',\n      hotUpdateChunkFilename: 'static/webpack/[id].[fullhash].hot-update.js',\n      hotUpdateMainFilename:\n        'static/webpack/[fullhash].[runtime].hot-update.json',\n      // This saves chunks with the name given via `import()`\n      chunkFilename: isNodeOrEdgeCompilation\n        ? '[name].js'\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}${\n            dev ? '[name]' : '[name].[contenthash]'\n          }.js`,\n      strictModuleExceptionHandling: true,\n      crossOriginLoading: crossOrigin,\n      // if `sources[number]` is not an absolute path, it's is resolved\n      // relative to the location of the source map file (https://tc39.es/source-map/#resolving-sources).\n      // However, Webpack's `resource-path` is relative to the app dir.\n      // TODO: Either `sourceRoot` should be populated with the root and then we can use `[resource-path]`\n      // or we need a way to resolve return `path.relative(sourceMapLocation, info.resourcePath)`\n      devtoolModuleFilenameTemplate: dev\n        ? '[absolute-resource-path]'\n        : undefined,\n      webassemblyModuleFilename: 'static/wasm/[modulehash].wasm',\n      hashFunction: 'xxhash64',\n      hashDigestLength: 16,\n    },\n    performance: false,\n    resolve: resolveConfig,\n    resolveLoader: {\n      // The loaders Next.js provides\n      alias: [\n        'error-loader',\n        'next-swc-loader',\n        'next-client-pages-loader',\n        'next-image-loader',\n        'next-metadata-image-loader',\n        'next-style-loader',\n        'next-flight-loader',\n        'next-flight-client-entry-loader',\n        'next-flight-action-entry-loader',\n        'next-flight-client-module-loader',\n        'next-flight-server-reference-proxy-loader',\n        'empty-loader',\n        'next-middleware-loader',\n        'next-edge-function-loader',\n        'next-edge-app-route-loader',\n        'next-edge-ssr-loader',\n        'next-middleware-asset-loader',\n        'next-middleware-wasm-loader',\n        'next-app-loader',\n        'next-route-loader',\n        'next-font-loader',\n        'next-invalid-import-error-loader',\n        'next-metadata-route-loader',\n        'modularize-import-loader',\n        'next-barrel-loader',\n        'next-error-browser-binary-loader',\n      ].reduce(\n        (alias, loader) => {\n          // using multiple aliases to replace `resolveLoader.modules`\n          alias[loader] = path.join(__dirname, 'webpack', 'loaders', loader)\n\n          return alias\n        },\n        {} as Record<string, string>\n      ),\n      modules: [\n        'node_modules',\n        ...nodePathList, // Support for NODE_PATH environment variable\n      ],\n      plugins: [],\n    },\n    module: {\n      rules: [\n        // Alias server-only and client-only to proper exports based on bundling layers\n        {\n          issuerLayer: {\n            or: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on client-only but allow server-only\n            alias: createServerOnlyClientOnlyAliases(true),\n          },\n        },\n        {\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on server-only but allow client-only\n            alias: createServerOnlyClientOnlyAliases(false),\n          },\n        },\n        // Detect server-only / client-only imports and error in build time\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.serverOnly,\n          },\n          options: {\n            message:\n              \"'client-only' cannot be imported from a Server Component module. It should only be used from a Client Component.\",\n          },\n        },\n        {\n          test: [\n            /^server-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]server-only[\\\\/]index/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          options: {\n            message:\n              \"'server-only' cannot be imported from a Client Component module. It should only be used from a Server Component.\",\n          },\n        },\n        // Potential the bundle introduced into middleware and api can be poisoned by client-only\n        // but not being used, so we disabled the `client-only` erroring on these layers.\n        // `server-only` is still available.\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'empty-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.neutralTarget,\n          },\n        },\n        ...(isNodeServer\n          ? []\n          : [\n              {\n                test: /[\\\\/].*?\\.node$/,\n                loader: 'next-error-browser-binary-loader',\n              },\n            ]),\n        ...(hasAppDir\n          ? [\n              {\n                // Make sure that AsyncLocalStorage module instance is shared between server and client\n                // layers.\n                layer: WEBPACK_LAYERS.shared,\n                test: asyncStoragesRegex,\n              },\n              // Convert metadata routes to separate layer\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.metadataRoute\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n              {\n                // Ensure that the app page module is in the client layers, this\n                // enables React to work correctly for RSC.\n                layer: WEBPACK_LAYERS.serverSideRendering,\n                test: /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]route-modules[\\\\/]app-page[\\\\/]module/,\n              },\n              {\n                issuerLayer: isWebpackBundledLayer,\n                resolve: {\n                  alias: createNextApiEsmAliases(),\n                },\n              },\n              {\n                issuerLayer: isWebpackServerOnlyLayer,\n                resolve: {\n                  alias: createAppRouterApiAliases(true),\n                },\n              },\n              {\n                issuerLayer: isWebpackClientOnlyLayer,\n                resolve: {\n                  alias: createAppRouterApiAliases(false),\n                },\n              },\n            ]\n          : []),\n        ...(hasAppDir && !isClient\n          ? [\n              {\n                issuerLayer: isWebpackServerOnlyLayer,\n                test: {\n                  // Resolve it if it is a source code file, and it has NOT been\n                  // opted out of bundling.\n                  and: [\n                    aliasCodeConditionTest,\n                    {\n                      not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                    },\n                  ],\n                },\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                  conditionNames: reactServerCondition,\n                  // If missing the alias override here, the default alias will be used which aliases\n                  // react to the direct file path, not the package name. In that case the condition\n                  // will be ignored completely.\n                  alias: createRSCAliases(bundledReactChannel, {\n                    // No server components profiling\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.reactServerComponents,\n                    isEdgeServer,\n                  }),\n                },\n                use: 'next-flight-loader',\n              },\n            ]\n          : []),\n        // TODO: FIXME: do NOT webpack 5 support with this\n        // x-ref: https://github.com/webpack/webpack/issues/11467\n        ...(!config.experimental.fullySpecified\n          ? [\n              {\n                test: /\\.m?js/,\n                resolve: {\n                  fullySpecified: false,\n                },\n              } as any,\n            ]\n          : []),\n        ...(hasAppDir && isEdgeServer\n          ? [\n              // The Edge bundle includes the server in its entrypoint, so it has to\n              // be in the SSR layer — here we convert the actual page request to\n              // the RSC layer via a webpack rule.\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n            ]\n          : []),\n        ...(hasAppDir\n          ? [\n              {\n                // Alias react-dom for ReactDOM.preload usage.\n                // Alias react for switching between default set and share subset.\n                oneOf: [\n                  {\n                    issuerLayer: isWebpackServerOnlyLayer,\n                    test: {\n                      // Resolve it if it is a source code file, and it has NOT been\n                      // opted out of bundling.\n                      and: [\n                        aliasCodeConditionTest,\n                        {\n                          not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                        },\n                      ],\n                    },\n                    resolve: {\n                      // It needs `conditionNames` here to require the proper asset,\n                      // when react is acting as dependency of compiled/react-dom.\n                      alias: createRSCAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.reactServerComponents,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                  {\n                    test: aliasCodeConditionTest,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    resolve: {\n                      alias: createRSCAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.serverSideRendering,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                ],\n              },\n              {\n                test: aliasCodeConditionTest,\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                resolve: {\n                  alias: createRSCAliases(bundledReactChannel, {\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.appPagesBrowser,\n                    isEdgeServer,\n                  }),\n                },\n              },\n            ]\n          : []),\n        // Do not apply react-refresh-loader to node_modules for app router browser layer\n        ...(hasAppDir && dev && isClient\n          ? [\n              {\n                test: codeCondition.test,\n                exclude: [\n                  // exclude unchanged modules from react-refresh\n                  codeCondition.exclude,\n                  transpilePackagesRegex,\n                  precompileRegex,\n                ],\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                use: reactRefreshLoaders,\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                },\n              },\n            ]\n          : []),\n        {\n          oneOf: [\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.api,\n              parser: {\n                // In Node.js, switch back to normal URL handling.\n                // In Edge runtime, we should disable parser.url handling in webpack so URLDependency is not added.\n                // Then there's browser code won't be injected into the edge runtime chunk.\n                // x-ref: https://github.com/webpack/webpack/blob/d9ce3b1f87e63c809d8a19bbd92257d65922e81f/lib/web/JsonpChunkLoadingRuntimeModule.js#L69\n                url: !isEdgeServer,\n              },\n              use: apiRoutesLayerLoaders,\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.middleware,\n              use: middlewareLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createRSCAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.middleware,\n                  isEdgeServer,\n                }),\n              },\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.instrument,\n              use: instrumentLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createRSCAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.instrument,\n                  isEdgeServer,\n                }),\n              },\n            },\n            ...(hasAppDir\n              ? [\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: isWebpackServerOnlyLayer,\n                    exclude: asyncStoragesRegex,\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    resourceQuery: new RegExp(\n                      WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                    ),\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                    // Exclude the transpilation of the app layer due to compilation issues\n                    exclude: browserNonTranspileModules,\n                    use: appBrowserLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    exclude: asyncStoragesRegex,\n                    use: appSSRLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                ]\n              : []),\n            {\n              ...codeCondition,\n              use: [\n                ...reactRefreshLoaders,\n                defaultLoaders.babel,\n                reactCompilerLoader,\n              ].filter(Boolean),\n            },\n          ],\n        },\n\n        ...(!config.images.disableStaticImages\n          ? [\n              {\n                test: nextImageLoaderRegex,\n                loader: 'next-image-loader',\n                issuer: { not: regexLikeCss },\n                dependency: { not: ['url'] },\n                resourceQuery: {\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataRoute),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                options: {\n                  isDev: dev,\n                  compilerType,\n                  basePath: config.basePath,\n                  assetPrefix: config.assetPrefix,\n                },\n              },\n            ]\n          : []),\n        ...(isEdgeServer\n          ? [\n              {\n                resolve: {\n                  fallback: {\n                    process: require.resolve('./polyfills/process'),\n                  },\n                },\n              },\n            ]\n          : isClient\n            ? [\n                {\n                  resolve: {\n                    fallback:\n                      config.experimental.fallbackNodePolyfills === false\n                        ? {\n                            assert: false,\n                            buffer: false,\n                            constants: false,\n                            crypto: false,\n                            domain: false,\n                            http: false,\n                            https: false,\n                            os: false,\n                            path: false,\n                            punycode: false,\n                            process: false,\n                            querystring: false,\n                            stream: false,\n                            string_decoder: false,\n                            sys: false,\n                            timers: false,\n                            tty: false,\n                            util: false,\n                            vm: false,\n                            zlib: false,\n                            events: false,\n                            setImmediate: false,\n                          }\n                        : {\n                            assert: require.resolve(\n                              'next/dist/compiled/assert'\n                            ),\n                            buffer: require.resolve(\n                              'next/dist/compiled/buffer'\n                            ),\n                            constants: require.resolve(\n                              'next/dist/compiled/constants-browserify'\n                            ),\n                            crypto: require.resolve(\n                              'next/dist/compiled/crypto-browserify'\n                            ),\n                            domain: require.resolve(\n                              'next/dist/compiled/domain-browser'\n                            ),\n                            http: require.resolve(\n                              'next/dist/compiled/stream-http'\n                            ),\n                            https: require.resolve(\n                              'next/dist/compiled/https-browserify'\n                            ),\n                            os: require.resolve(\n                              'next/dist/compiled/os-browserify'\n                            ),\n                            path: require.resolve(\n                              'next/dist/compiled/path-browserify'\n                            ),\n                            punycode: require.resolve(\n                              'next/dist/compiled/punycode'\n                            ),\n                            process: require.resolve('./polyfills/process'),\n                            // Handled in separate alias\n                            querystring: require.resolve(\n                              'next/dist/compiled/querystring-es3'\n                            ),\n                            stream: require.resolve(\n                              'next/dist/compiled/stream-browserify'\n                            ),\n                            string_decoder: require.resolve(\n                              'next/dist/compiled/string_decoder'\n                            ),\n                            sys: require.resolve('next/dist/compiled/util'),\n                            timers: require.resolve(\n                              'next/dist/compiled/timers-browserify'\n                            ),\n                            tty: require.resolve(\n                              'next/dist/compiled/tty-browserify'\n                            ),\n                            // Handled in separate alias\n                            // url: require.resolve('url'),\n                            util: require.resolve('next/dist/compiled/util'),\n                            vm: require.resolve(\n                              'next/dist/compiled/vm-browserify'\n                            ),\n                            zlib: require.resolve(\n                              'next/dist/compiled/browserify-zlib'\n                            ),\n                            events: require.resolve(\n                              'next/dist/compiled/events'\n                            ),\n                            setImmediate: require.resolve(\n                              'next/dist/compiled/setimmediate'\n                            ),\n                          },\n                  },\n                },\n              ]\n            : []),\n        {\n          // Mark `image-response.js` as side-effects free to make sure we can\n          // tree-shake it if not used.\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]og[\\\\/]image-response\\.js/,\n          sideEffects: false,\n        },\n        // Mark the action-client-wrapper module as side-effects free to make sure\n        // the individual transformed module of client action can be tree-shaken.\n        // This will make modules processed by `next-flight-server-reference-proxy-loader` become side-effects free,\n        // then on client side the module ids will become tree-shakable.\n        // e.g. the output of client action module will look like:\n        // `export { a } from 'next-flight-server-reference-proxy-loader?id=idOfA&name=a!\n        // `export { b } from 'next-flight-server-reference-proxy-loader?id=idOfB&name=b!\n        {\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?build[\\\\/]webpack[\\\\/]loaders[\\\\/]next-flight-loader[\\\\/]action-client-wrapper\\.js/,\n          sideEffects: false,\n        },\n        {\n          // This loader rule should be before other rules, as it can output code\n          // that still contains `\"use client\"` or `\"use server\"` statements that\n          // needs to be re-transformed by the RSC compilers.\n          // This loader rule works like a bridge between user's import and\n          // the target module behind a package's barrel file. It reads SWC's\n          // analysis result from the previous loader, and directly returns the\n          // code that only exports values that are asked by the user.\n          test: /__barrel_optimize__/,\n          use: ({ resourceQuery }: { resourceQuery: string }) => {\n            const names = (\n              resourceQuery.match(/\\?names=([^&]+)/)?.[1] || ''\n            ).split(',')\n\n            return [\n              {\n                loader: 'next-barrel-loader',\n                options: {\n                  names,\n                  swcCacheDir: path.join(\n                    dir,\n                    config?.distDir ?? '.next',\n                    'cache',\n                    'swc'\n                  ),\n                },\n                // This is part of the request value to serve as the module key.\n                // The barrel loader are no-op re-exported modules keyed by\n                // export names.\n                ident: 'next-barrel-loader:' + resourceQuery,\n              },\n            ]\n          },\n        },\n        {\n          resolve: {\n            alias: {\n              next: NEXT_PROJECT_ROOT,\n            },\n          },\n        },\n      ],\n    },\n    plugins: [\n      isNodeServer &&\n        new webpack.NormalModuleReplacementPlugin(\n          /\\.\\/(.+)\\.shared-runtime$/,\n          function (resource) {\n            const moduleName = path.basename(\n              resource.request,\n              '.shared-runtime'\n            )\n            const layer = resource.contextInfo.issuerLayer\n            let runtime\n\n            switch (layer) {\n              case WEBPACK_LAYERS.serverSideRendering:\n              case WEBPACK_LAYERS.reactServerComponents:\n              case WEBPACK_LAYERS.appPagesBrowser:\n              case WEBPACK_LAYERS.actionBrowser:\n                runtime = 'app-page'\n                break\n              default:\n                runtime = 'pages'\n            }\n            resource.request = `next/dist/server/route-modules/${runtime}/vendored/contexts/${moduleName}`\n          }\n        ),\n      dev && new MemoryWithGcCachePlugin({ maxGenerations: 5 }),\n      dev && isClient && new ReactRefreshWebpackPlugin(webpack),\n      // Makes sure `Buffer` and `process` are polyfilled in client and flight bundles (same behavior as webpack 4)\n      (isClient || isEdgeServer) &&\n        new webpack.ProvidePlugin({\n          // Buffer is used by getInlineScriptSource\n          Buffer: [require.resolve('buffer'), 'Buffer'],\n          // Avoid process being overridden when in web run time\n          ...(isClient && { process: [require.resolve('process')] }),\n        }),\n      getDefineEnvPlugin({\n        isTurbopack: false,\n        config,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix,\n        hasRewrites,\n        isClient,\n        isEdgeServer,\n        isNodeOrEdgeCompilation,\n        isNodeServer,\n        middlewareMatchers,\n      }),\n      isClient &&\n        new ReactLoadablePlugin({\n          filename: REACT_LOADABLE_MANIFEST,\n          pagesDir,\n          appDir,\n          runtimeAsset: `server/${MIDDLEWARE_REACT_LOADABLE_MANIFEST}.js`,\n          dev,\n        }),\n      (isClient || isEdgeServer) && new DropClientPage(),\n      isNodeServer &&\n        !dev &&\n        new (require('./webpack/plugins/next-trace-entrypoints-plugin')\n          .TraceEntryPointsPlugin as typeof import('./webpack/plugins/next-trace-entrypoints-plugin').TraceEntryPointsPlugin)(\n          {\n            rootDir: dir,\n            appDir: appDir,\n            pagesDir: pagesDir,\n            esmExternals: config.experimental.esmExternals,\n            outputFileTracingRoot: config.outputFileTracingRoot,\n            appDirEnabled: hasAppDir,\n            optOutBundlingPackages,\n            traceIgnores: [],\n            compilerType,\n            swcLoaderConfig: swcDefaultLoader,\n          }\n        ),\n      // Moment.js is an extremely popular library that bundles large locale files\n      // by default due to how Webpack interprets its code. This is a practical\n      // solution that requires the user to opt into importing specific locales.\n      // https://github.com/jmblog/how-to-optimize-momentjs-with-webpack\n      config.excludeDefaultMomentLocales &&\n        new webpack.IgnorePlugin({\n          resourceRegExp: /^\\.\\/locale$/,\n          contextRegExp: /moment$/,\n        }),\n      ...(dev\n        ? (() => {\n            // Even though require.cache is server only we have to clear assets from both compilations\n            // This is because the client compilation generates the build manifest that's used on the server side\n            const { NextJsRequireCacheHotReloader } =\n              require('./webpack/plugins/nextjs-require-cache-hot-reloader') as typeof import('./webpack/plugins/nextjs-require-cache-hot-reloader')\n            const devPlugins: any[] = [\n              new NextJsRequireCacheHotReloader({\n                serverComponents: hasAppDir,\n              }),\n            ]\n\n            if (isClient || isEdgeServer) {\n              devPlugins.push(new webpack.HotModuleReplacementPlugin())\n            }\n\n            return devPlugins\n          })()\n        : []),\n      !dev &&\n        new webpack.IgnorePlugin({\n          resourceRegExp: /react-is/,\n          contextRegExp: /next[\\\\/]dist[\\\\/]/,\n        }),\n      isNodeOrEdgeCompilation &&\n        new PagesManifestPlugin({\n          dev,\n          appDirEnabled: hasAppDir,\n          isEdgeRuntime: isEdgeServer,\n          distDir: !dev ? distDir : undefined,\n        }),\n      // MiddlewarePlugin should be after DefinePlugin so NEXT_PUBLIC_*\n      // replacement is done before its process.env.* handling\n      isEdgeServer &&\n        new MiddlewarePlugin({\n          dev,\n          sriEnabled: !dev && !!config.experimental.sri?.algorithm,\n          rewrites,\n          edgeEnvironments: {\n            __NEXT_BUILD_ID: buildId,\n            NEXT_SERVER_ACTIONS_ENCRYPTION_KEY: encryptionKey,\n            ...edgePreviewProps,\n          },\n        }),\n      isClient &&\n        new BuildManifestPlugin({\n          buildId,\n          rewrites,\n          isDevFallback,\n          appDirEnabled: hasAppDir,\n          clientRouterFilters,\n        }),\n      new ProfilingPlugin({ runWebpackSpan, rootDir: dir }),\n      new WellKnownErrorsPlugin(),\n      isClient &&\n        new CopyFilePlugin({\n          // file path to build output of `@next/polyfill-nomodule`\n          filePath: require.resolve('./polyfills/polyfill-nomodule'),\n          cacheKey: process.env.__NEXT_VERSION as string,\n          name: `static/chunks/polyfills${dev ? '' : '-[hash]'}.js`,\n          minimize: false,\n          info: {\n            [CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL]: 1,\n            // This file is already minified\n            minimized: true,\n          },\n        }),\n      hasAppDir && isClient && new AppBuildManifestPlugin({ dev }),\n      hasAppDir &&\n        (isClient\n          ? new ClientReferenceManifestPlugin({\n              dev,\n              appDir,\n              experimentalInlineCss: !!config.experimental.inlineCss,\n            })\n          : new FlightClientEntryPlugin({\n              appDir,\n              dev,\n              isEdgeServer,\n              encryptionKey,\n            })),\n      hasAppDir &&\n        !isClient &&\n        new NextTypesPlugin({\n          dir,\n          distDir: config.distDir,\n          appDir,\n          dev,\n          isEdgeServer,\n          pageExtensions: config.pageExtensions,\n          typedRoutes: enableTypedRoutes,\n          cacheLifeConfig: config.experimental.cacheLife,\n          originalRewrites,\n          originalRedirects,\n        }),\n      !dev &&\n        isClient &&\n        !!config.experimental.sri?.algorithm &&\n        new SubresourceIntegrityPlugin(config.experimental.sri.algorithm),\n      isClient &&\n        new NextFontManifestPlugin({\n          appDir,\n        }),\n      !dev &&\n        isClient &&\n        config.experimental.cssChunking &&\n        new CssChunkingPlugin(config.experimental.cssChunking === 'strict'),\n      !dev &&\n        isClient &&\n        new (require('./webpack/plugins/telemetry-plugin').TelemetryPlugin)(\n          new Map(\n            [\n              ['swcLoader', useSWCLoader],\n              ['swcRelay', !!config.compiler?.relay],\n              ['swcStyledComponents', !!config.compiler?.styledComponents],\n              [\n                'swcReactRemoveProperties',\n                !!config.compiler?.reactRemoveProperties,\n              ],\n              [\n                'swcExperimentalDecorators',\n                !!jsConfig?.compilerOptions?.experimentalDecorators,\n              ],\n              ['swcRemoveConsole', !!config.compiler?.removeConsole],\n              ['swcImportSource', !!jsConfig?.compilerOptions?.jsxImportSource],\n              ['swcEmotion', !!config.compiler?.emotion],\n              ['transpilePackages', !!config.transpilePackages],\n              [\n                'skipMiddlewareUrlNormalize',\n                !!config.skipMiddlewareUrlNormalize,\n              ],\n              ['skipTrailingSlashRedirect', !!config.skipTrailingSlashRedirect],\n              ['modularizeImports', !!config.modularizeImports],\n              // If esmExternals is not same as default value, it represents customized usage\n              ['esmExternals', config.experimental.esmExternals !== true],\n              SWCBinaryTarget,\n            ].filter<[Feature, boolean]>(Boolean as any)\n          )\n        ),\n    ].filter(Boolean as any as ExcludesFalse),\n  }\n\n  // Support tsconfig and jsconfig baseUrl\n  // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n  if (resolvedBaseUrl && !resolvedBaseUrl.isImplicit) {\n    webpackConfig.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n  }\n\n  // always add JsConfigPathsPlugin to allow hot-reloading\n  // if the config is added/removed\n  webpackConfig.resolve?.plugins?.unshift(\n    new JsConfigPathsPlugin(\n      jsConfig?.compilerOptions?.paths || {},\n      resolvedBaseUrl\n    )\n  )\n\n  const webpack5Config = webpackConfig as webpack.Configuration\n\n  if (isEdgeServer) {\n    webpack5Config.module?.rules?.unshift({\n      test: /\\.wasm$/,\n      loader: 'next-middleware-wasm-loader',\n      type: 'javascript/auto',\n      resourceQuery: /module/i,\n    })\n    webpack5Config.module?.rules?.unshift({\n      dependency: 'url',\n      loader: 'next-middleware-asset-loader',\n      type: 'javascript/auto',\n      layer: WEBPACK_LAYERS.edgeAsset,\n    })\n    webpack5Config.module?.rules?.unshift({\n      issuerLayer: WEBPACK_LAYERS.edgeAsset,\n      type: 'asset/source',\n    })\n  }\n\n  webpack5Config.experiments = {\n    layers: true,\n    cacheUnaffected: true,\n    buildHttp: Array.isArray(config.experimental.urlImports)\n      ? {\n          allowedUris: config.experimental.urlImports,\n          cacheLocation: path.join(dir, 'next.lock/data'),\n          lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n        }\n      : config.experimental.urlImports\n        ? {\n            cacheLocation: path.join(dir, 'next.lock/data'),\n            lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n            ...config.experimental.urlImports,\n          }\n        : undefined,\n  }\n\n  webpack5Config.module!.parser = {\n    javascript: {\n      url: 'relative',\n    },\n  }\n  webpack5Config.module!.generator = {\n    asset: {\n      filename: 'static/media/[name].[hash:8][ext]',\n    },\n  }\n\n  if (!webpack5Config.output) {\n    webpack5Config.output = {}\n  }\n  if (isClient) {\n    webpack5Config.output.trustedTypes = 'nextjs#bundler'\n  }\n\n  if (isClient || isEdgeServer) {\n    webpack5Config.output.enabledLibraryTypes = ['assign']\n  }\n\n  // This enables managedPaths for all node_modules\n  // and also for the unplugged folder when using yarn pnp\n  // It also add the yarn cache to the immutable paths\n  webpack5Config.snapshot = {}\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.managedPaths = [\n      /^(.+?(?:[\\\\/]\\.yarn[\\\\/]unplugged[\\\\/][^\\\\/]+)?[\\\\/]node_modules[\\\\/])/,\n    ]\n  } else {\n    webpack5Config.snapshot.managedPaths = [/^(.+?[\\\\/]node_modules[\\\\/])/]\n  }\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.immutablePaths = [\n      /^(.+?[\\\\/]cache[\\\\/][^\\\\/]+\\.zip[\\\\/]node_modules[\\\\/])/,\n    ]\n  }\n\n  if (dev) {\n    if (!webpack5Config.optimization) {\n      webpack5Config.optimization = {}\n    }\n\n    // For Server Components, it's necessary to have provided exports collected\n    // to generate the correct flight manifest.\n    if (!hasAppDir) {\n      webpack5Config.optimization.providedExports = false\n    }\n    webpack5Config.optimization.usedExports = false\n  }\n\n  const configVars = JSON.stringify({\n    optimizePackageImports: config?.experimental?.optimizePackageImports,\n    crossOrigin: config.crossOrigin,\n    pageExtensions: pageExtensions,\n    trailingSlash: config.trailingSlash,\n    buildActivity: config.devIndicators.buildActivity,\n    buildActivityPosition: config.devIndicators.buildActivityPosition,\n    productionBrowserSourceMaps: !!config.productionBrowserSourceMaps,\n    reactStrictMode: config.reactStrictMode,\n    optimizeCss: config.experimental.optimizeCss,\n    nextScriptWorkers: config.experimental.nextScriptWorkers,\n    scrollRestoration: config.experimental.scrollRestoration,\n    typedRoutes: config.experimental.typedRoutes,\n    basePath: config.basePath,\n    excludeDefaultMomentLocales: config.excludeDefaultMomentLocales,\n    assetPrefix: config.assetPrefix,\n    disableOptimizedLoading,\n    isEdgeRuntime: isEdgeServer,\n    reactProductionProfiling,\n    webpack: !!config.webpack,\n    hasRewrites,\n    swcLoader: useSWCLoader,\n    removeConsole: config.compiler?.removeConsole,\n    reactRemoveProperties: config.compiler?.reactRemoveProperties,\n    styledComponents: config.compiler?.styledComponents,\n    relay: config.compiler?.relay,\n    emotion: config.compiler?.emotion,\n    modularizeImports: config.modularizeImports,\n    imageLoaderFile: config.images.loaderFile,\n    clientTraceMetadata: config.experimental.clientTraceMetadata,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n    serverReferenceHashSalt: encryptionKey,\n  })\n\n  const cache: any = {\n    type: 'filesystem',\n    // Disable memory cache in development in favor of our own MemoryWithGcCachePlugin.\n    maxMemoryGenerations: dev ? 0 : Infinity, // Infinity is default value for production in webpack currently.\n    // Includes:\n    //  - Next.js location on disk (some loaders use absolute paths and some resolve options depend on absolute paths)\n    //  - Next.js version\n    //  - next.config.js keys that affect compilation\n    version: `${__dirname}|${process.env.__NEXT_VERSION}|${configVars}`,\n    cacheDirectory: path.join(distDir, 'cache', 'webpack'),\n    // For production builds, it's more efficient to compress all cache files together instead of compression each one individually.\n    // So we disable compression here and allow the build runner to take care of compressing the cache as a whole.\n    // For local development, we still want to compress the cache files individually to avoid I/O bottlenecks\n    // as we are seeing 1~10 seconds of fs I/O time from user reports.\n    compression: dev ? 'gzip' : false,\n  }\n\n  // Adds `next.config.js` as a buildDependency when custom webpack config is provided\n  if (config.webpack && config.configFile) {\n    cache.buildDependencies = {\n      config: [config.configFile],\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  } else {\n    cache.buildDependencies = {\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  }\n  webpack5Config.plugins?.push((compiler) => {\n    compiler.hooks.done.tap('next-build-dependencies', (stats) => {\n      const buildDependencies = stats.compilation.buildDependencies\n      const nextPackage = path.dirname(require.resolve('next/package.json'))\n      // Remove all next.js build dependencies, they are already covered by the cacheVersion\n      // and next.js also imports the output files which leads to broken caching.\n      for (const dep of buildDependencies) {\n        if (dep.startsWith(nextPackage)) {\n          buildDependencies.delete(dep)\n        }\n      }\n    })\n  })\n\n  webpack5Config.cache = cache\n\n  if (process.env.NEXT_WEBPACK_LOGGING) {\n    const infra = process.env.NEXT_WEBPACK_LOGGING.includes('infrastructure')\n    const profileClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-client')\n    const profileServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-server')\n    const summaryClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-client')\n    const summaryServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-server')\n\n    const profile =\n      (profileClient && isClient) || (profileServer && isNodeOrEdgeCompilation)\n    const summary =\n      (summaryClient && isClient) || (summaryServer && isNodeOrEdgeCompilation)\n\n    const logDefault = !infra && !profile && !summary\n\n    if (logDefault || infra) {\n      webpack5Config.infrastructureLogging = {\n        level: 'verbose',\n        debug: /FileSystemInfo/,\n      }\n    }\n\n    if (logDefault || profile) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              colors: true,\n              logging: logDefault ? 'log' : 'verbose',\n            })\n          )\n        })\n      })\n    } else if (summary) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              preset: 'summary',\n              colors: true,\n              timings: true,\n            })\n          )\n        })\n      })\n    }\n\n    if (profile) {\n      const ProgressPlugin =\n        webpack.ProgressPlugin as unknown as typeof webpack.ProgressPlugin\n      webpack5Config.plugins!.push(\n        new ProgressPlugin({\n          profile: true,\n        })\n      )\n      webpack5Config.profile = true\n    }\n  }\n\n  webpackConfig = await buildConfiguration(webpackConfig, {\n    supportedBrowsers,\n    rootDirectory: dir,\n    customAppFile: pagesDir\n      ? new RegExp(escapeStringRegexp(path.join(pagesDir, `_app`)))\n      : undefined,\n    hasAppDir,\n    isDevelopment: dev,\n    isServer: isNodeOrEdgeCompilation,\n    isEdgeRuntime: isEdgeServer,\n    targetWeb: isClient || isEdgeServer,\n    assetPrefix: config.assetPrefix || '',\n    sassOptions: config.sassOptions,\n    productionBrowserSourceMaps: config.productionBrowserSourceMaps,\n    future: config.future,\n    experimental: config.experimental,\n    disableStaticImages: config.images.disableStaticImages,\n    transpilePackages: config.transpilePackages,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n  })\n\n  // @ts-ignore Cache exists\n  webpackConfig.cache.name = `${webpackConfig.name}-${webpackConfig.mode}${\n    isDevFallback ? '-fallback' : ''\n  }`\n\n  if (dev) {\n    if (webpackConfig.module) {\n      webpackConfig.module.unsafeCache = (module: any) =>\n        !UNSAFE_CACHE_REGEX.test(module.resource)\n    } else {\n      webpackConfig.module = {\n        unsafeCache: (module: any) => !UNSAFE_CACHE_REGEX.test(module.resource),\n      }\n    }\n  }\n\n  let originalDevtool = webpackConfig.devtool\n  if (typeof config.webpack === 'function') {\n    webpackConfig = config.webpack(webpackConfig, {\n      dir,\n      dev,\n      isServer: isNodeOrEdgeCompilation,\n      buildId,\n      config,\n      defaultLoaders,\n      totalPages: Object.keys(entrypoints).length,\n      webpack,\n      ...(isNodeOrEdgeCompilation\n        ? {\n            nextRuntime: isEdgeServer ? 'edge' : 'nodejs',\n          }\n        : {}),\n    })\n\n    if (!webpackConfig) {\n      throw new Error(\n        `Webpack config is undefined. You may have forgot to return properly from within the \"webpack\" method of your ${config.configFileName}.\\n` +\n          'See more info here https://nextjs.org/docs/messages/undefined-webpack-config'\n      )\n    }\n\n    if (dev && originalDevtool !== webpackConfig.devtool) {\n      webpackConfig.devtool = originalDevtool\n      devtoolRevertWarning(originalDevtool)\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    const webpack5Config = webpackConfig as webpack.Configuration\n\n    // disable lazy compilation of entries as next.js has it's own method here\n    if (webpack5Config.experiments?.lazyCompilation === true) {\n      webpack5Config.experiments.lazyCompilation = {\n        entries: false,\n      }\n    } else if (\n      typeof webpack5Config.experiments?.lazyCompilation === 'object' &&\n      webpack5Config.experiments.lazyCompilation.entries !== false\n    ) {\n      webpack5Config.experiments.lazyCompilation.entries = false\n    }\n\n    if (typeof (webpackConfig as any).then === 'function') {\n      console.warn(\n        '> Promise returned in next config. https://nextjs.org/docs/messages/promise-in-next-config'\n      )\n    }\n  }\n  const rules = webpackConfig.module?.rules || []\n\n  const customSvgRule = rules.find(\n    (rule): rule is webpack.RuleSetRule =>\n      (rule &&\n        typeof rule === 'object' &&\n        rule.loader !== 'next-image-loader' &&\n        'test' in rule &&\n        rule.test instanceof RegExp &&\n        rule.test.test('.svg')) ||\n      false\n  )\n\n  if (customSvgRule && hasAppDir) {\n    // Create React aliases for SVG components that were transformed using a\n    // custom webpack config with e.g. the `@svgr/webpack` loader, or the\n    // `babel-plugin-inline-react-svg` plugin.\n    rules.push({\n      test: customSvgRule.test,\n      oneOf: [\n        WEBPACK_LAYERS.reactServerComponents,\n        WEBPACK_LAYERS.serverSideRendering,\n        WEBPACK_LAYERS.appPagesBrowser,\n      ].map((layer) => ({\n        issuerLayer: layer,\n        resolve: {\n          alias: createRSCAliases(bundledReactChannel, {\n            reactProductionProfiling,\n            layer,\n            isEdgeServer,\n          }),\n        },\n      })),\n    })\n  }\n\n  if (!config.images.disableStaticImages) {\n    const nextImageRule = rules.find(\n      (rule) =>\n        rule && typeof rule === 'object' && rule.loader === 'next-image-loader'\n    )\n    if (customSvgRule && nextImageRule && typeof nextImageRule === 'object') {\n      // Exclude svg if the user already defined it in custom\n      // webpack config such as the `@svgr/webpack` loader, or\n      // the `babel-plugin-inline-react-svg` plugin.\n      nextImageRule.test = /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp)$/i\n    }\n  }\n\n  if (\n    config.experimental.craCompat &&\n    webpackConfig.module?.rules &&\n    webpackConfig.plugins\n  ) {\n    // CRA allows importing non-webpack handled files with file-loader\n    // these need to be the last rule to prevent catching other items\n    // https://github.com/facebook/create-react-app/blob/fddce8a9e21bf68f37054586deb0c8636a45f50b/packages/react-scripts/config/webpack.config.js#L594\n    const fileLoaderExclude = [/\\.(js|mjs|jsx|ts|tsx|json)$/]\n    const fileLoader = {\n      exclude: fileLoaderExclude,\n      issuer: fileLoaderExclude,\n      type: 'asset/resource',\n    }\n\n    const topRules = []\n    const innerRules = []\n\n    for (const rule of webpackConfig.module.rules) {\n      if (!rule || typeof rule !== 'object') continue\n      if (rule.resolve) {\n        topRules.push(rule)\n      } else {\n        if (\n          rule.oneOf &&\n          !(rule.test || rule.exclude || rule.resource || rule.issuer)\n        ) {\n          rule.oneOf.forEach((r) => innerRules.push(r))\n        } else {\n          innerRules.push(rule)\n        }\n      }\n    }\n\n    webpackConfig.module.rules = [\n      ...(topRules as any),\n      {\n        oneOf: [...innerRules, fileLoader],\n      },\n    ]\n  }\n\n  // Backwards compat with webpack-dev-middleware options object\n  if (typeof config.webpackDevMiddleware === 'function') {\n    const options = config.webpackDevMiddleware({\n      watchOptions: webpackConfig.watchOptions,\n    })\n    if (options.watchOptions) {\n      webpackConfig.watchOptions = options.watchOptions\n    }\n  }\n\n  function canMatchCss(rule: webpack.RuleSetCondition | undefined): boolean {\n    if (!rule) {\n      return false\n    }\n\n    const fileNames = [\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.css',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.scss',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.sass',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.less',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.styl',\n    ]\n\n    if (rule instanceof RegExp && fileNames.some((input) => rule.test(input))) {\n      return true\n    }\n\n    if (typeof rule === 'function') {\n      if (\n        fileNames.some((input) => {\n          try {\n            if (rule(input)) {\n              return true\n            }\n          } catch {}\n          return false\n        })\n      ) {\n        return true\n      }\n    }\n\n    if (Array.isArray(rule) && rule.some(canMatchCss)) {\n      return true\n    }\n\n    return false\n  }\n\n  const hasUserCssConfig =\n    webpackConfig.module?.rules?.some(\n      (rule: any) => canMatchCss(rule.test) || canMatchCss(rule.include)\n    ) ?? false\n\n  if (hasUserCssConfig) {\n    // only show warning for one build\n    if (isNodeOrEdgeCompilation) {\n      console.warn(\n        yellow(bold('Warning: ')) +\n          bold(\n            'Built-in CSS support is being disabled due to custom CSS configuration being detected.\\n'\n          ) +\n          'See here for more info: https://nextjs.org/docs/messages/built-in-css-disabled\\n'\n      )\n    }\n\n    if (webpackConfig.module?.rules?.length) {\n      // Remove default CSS Loaders\n      webpackConfig.module.rules.forEach((r) => {\n        if (!r || typeof r !== 'object') return\n        if (Array.isArray(r.oneOf)) {\n          r.oneOf = r.oneOf.filter(\n            (o) => (o as any)[Symbol.for('__next_css_remove')] !== true\n          )\n        }\n      })\n    }\n    if (webpackConfig.plugins?.length) {\n      // Disable CSS Extraction Plugin\n      webpackConfig.plugins = webpackConfig.plugins.filter(\n        (p) => (p as any).__next_css_remove !== true\n      )\n    }\n    if (webpackConfig.optimization?.minimizer?.length) {\n      // Disable CSS Minifier\n      webpackConfig.optimization.minimizer =\n        webpackConfig.optimization.minimizer.filter(\n          (e) => (e as any).__next_css_remove !== true\n        )\n    }\n  }\n\n  // Inject missing React Refresh loaders so that development mode is fast:\n  if (dev && isClient) {\n    attachReactRefresh(webpackConfig, defaultLoaders.babel)\n  }\n\n  // Backwards compat for `main.js` entry key\n  // and setup of dependencies between entries\n  // we can't do that in the initial entry for\n  // backward-compat reasons\n  const originalEntry: any = webpackConfig.entry\n  if (typeof originalEntry !== 'undefined') {\n    const updatedEntry = async () => {\n      const entry: webpack.EntryObject =\n        typeof originalEntry === 'function'\n          ? await originalEntry()\n          : originalEntry\n      // Server compilation doesn't have main.js\n      if (\n        clientEntries &&\n        Array.isArray(entry['main.js']) &&\n        entry['main.js'].length > 0\n      ) {\n        const originalFile = clientEntries[\n          CLIENT_STATIC_FILES_RUNTIME_MAIN\n        ] as string\n        entry[CLIENT_STATIC_FILES_RUNTIME_MAIN] = [\n          ...entry['main.js'],\n          originalFile,\n        ]\n      }\n      delete entry['main.js']\n\n      for (const name of Object.keys(entry)) {\n        entry[name] = finalizeEntrypoint({\n          value: entry[name],\n          compilerType,\n          name,\n          hasAppDir,\n        })\n      }\n\n      return entry\n    }\n    // @ts-ignore webpack 5 typings needed\n    webpackConfig.entry = updatedEntry\n  }\n\n  if (!dev && typeof webpackConfig.entry === 'function') {\n    // entry is always a function\n    webpackConfig.entry = await webpackConfig.entry()\n  }\n\n  return webpackConfig\n}\n"], "names": ["NODE_BASE_ESM_RESOLVE_OPTIONS", "NODE_BASE_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "attachReactRefresh", "babelIncludeRegexes", "getBaseWebpackConfig", "hasExternalOtelApiPackage", "loadProjectInfo", "nextImageLoaderRegex", "EXTERNAL_PACKAGES", "require", "DEFAULT_TRANSPILED_PACKAGES", "parseInt", "React", "version", "Error", "browserNonTranspileModules", "precompileRegex", "asyncStoragesRegex", "nodePathList", "process", "env", "NODE_PATH", "split", "platform", "filter", "p", "baseWatchOptions", "Object", "freeze", "aggregateTimeout", "ignored", "isModuleCSS", "module", "type", "devtoolRevertWarning", "execOnce", "devtool", "console", "warn", "yellow", "bold", "loggedSwcDisabled", "loggedIgnoredCompilerOptions", "reactRefreshLoaderName", "webpackConfig", "target<PERSON><PERSON><PERSON>", "reactRefreshLoader", "resolve", "rules", "for<PERSON>ach", "rule", "curr", "use", "Array", "isArray", "some", "r", "idx", "findIndex", "splice", "dependencyType", "modules", "fallback", "exportsFields", "importsFields", "conditionNames", "descriptionFiles", "extensions", "enforceExtensions", "symlinks", "mainFields", "mainFiles", "roots", "fullySpecified", "preferRelative", "preferAbsolute", "restrictions", "alias", "dir", "config", "dev", "jsConfig", "resolvedBaseUrl", "loadJsConfig", "supportedBrowsers", "getSupportedBrowsers", "UNSAFE_CACHE_REGEX", "buildId", "<PERSON><PERSON><PERSON>", "compilerType", "entrypoints", "isDev<PERSON><PERSON><PERSON>", "pagesDir", "reactProductionProfiling", "rewrites", "originalRewrites", "originalRedirects", "runWebpackSpan", "appDir", "middlewareMatchers", "clientRouterFilters", "fetchCacheKeyPrefix", "edgePreviewProps", "webpack5Config", "isClient", "COMPILER_NAMES", "client", "isEdgeServer", "edgeServer", "isNodeServer", "server", "isNodeOrEdgeCompilation", "hasRewrites", "beforeFiles", "length", "afterFiles", "hasAppDir", "disableOptimizedLoading", "enableTypedRoutes", "experimental", "typedRoutes", "bundledReactChannel", "needsExperimentalReact", "babelConfigFile", "getBabelConfigFile", "hasCustomExportOutput", "distDir", "path", "join", "useSWCLoader", "forceSwcTransforms", "SWCBinaryTarget", "undefined", "binaryTarget", "getBinaryMetadata", "target", "Log", "info", "relative", "loadBindings", "useWasmBinary", "finalTranspilePackages", "transpilePackages", "concat", "pkg", "optimizePackageImports", "includes", "push", "compiler", "shouldIncludeExternalDirs", "externalDir", "codeCondition", "test", "or", "include", "exclude", "excludePath", "shouldBeBundled", "isResourceInPackages", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactCompiler", "reactCompilerLoader", "getReactCompilerLoader", "swcTraceProfilingInitialized", "getSwcLoader", "extraOptions", "swcTraceProfiling", "initCustomTraceSubscriber", "Date", "now", "loader", "options", "isServer", "rootDir", "hasReactRefresh", "nextConfig", "swcCacheDir", "serverReferenceHashSalt", "swcServerLayerLoader", "serverComponents", "bundleLayer", "WEBPACK_LAYERS", "reactServerComponents", "esm", "swcSS<PERSON>ayer<PERSON><PERSON>der", "serverSideRendering", "swcBrowser<PERSON><PERSON><PERSON><PERSON><PERSON>der", "appPagesBrowser", "swcDefaultLoader", "defaultLoaders", "babel", "appServerLayerLoaders", "Boolean", "instrumentLayerLoaders", "middlewareLayerLoaders", "middleware", "reactRefreshLoaders", "createClientLayerLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactRefresh", "appBrowserLayerLoaders", "appSSRLayerLoaders", "apiRoutesLayerLoaders", "api", "pageExtensions", "outputPath", "SERVER_DIRECTORY", "reactServerCondition", "edgeConditionNames", "clientEntries", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "NEXT_PROJECT_ROOT_DIST_CLIENT", "replace", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "resolveConfig", "extensionAlias", "createWebpackAliases", "getMainField", "plugins", "OptionalPeerDependencyResolverPlugin", "<PERSON><PERSON><PERSON>eworkP<PERSON><PERSON>", "topLevelFrameworkPaths", "visitedFrameworkPackages", "Set", "addPackagePath", "packageName", "relativeToPath", "paths", "has", "add", "packageJsonPath", "directory", "dependencies", "name", "keys", "_", "crossOrigin", "serverExternalPackages", "externalPackageConflicts", "optOutBundlingPackages", "optOutBundlingPackageRegex", "RegExp", "map", "transpilePackagesRegex", "handleExternals", "makeExternalHandler", "transpiledPackages", "pageExtensionsRegex", "aliasCodeConditionTest", "builtinModules", "parallelism", "Number", "NEXT_WEBPACK_PARALLELISM", "externalsPresets", "node", "externals", "getEdgePolyfilledModules", "handleWebpackExternalForEdgeRuntime", "context", "request", "contextInfo", "getResolve", "issuer<PERSON><PERSON>er", "resolveFunction", "resolveContext", "requestToResolve", "Promise", "reject", "err", "result", "resolveData", "isEsm", "descriptionFileData", "optimization", "emitOnErrors", "checkWasmTypes", "nodeEnv", "splitChunks", "extractRootNodeModule", "modulePath", "regex", "match", "cacheGroups", "vendor", "chunks", "reuseExistingChunk", "minSize", "minChunks", "maxAsyncRequests", "maxInitialRequests", "moduleId", "nameForCondition", "rootModule", "hash", "crypto", "createHash", "update", "digest", "default", "defaultVendors", "filename", "frameworkCacheGroup", "layer", "isWebpackDefaultLayer", "resource", "pkgPath", "startsWith", "priority", "enforce", "libCacheGroup", "size", "updateHash", "libIdent", "substring", "chunk", "framework", "lib", "runtimeChunk", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "minimize", "serverMinification", "minimizer", "MinifyPlugin", "apply", "CssMinimizerPlugin", "postcssOptions", "inline", "annotation", "entry", "watchOptions", "poll", "pollIntervalMs", "output", "publicPath", "assetPrefix", "endsWith", "slice", "library", "libraryTarget", "hotUpdateChunkFilename", "hotUpdateMainFilename", "chunkFilename", "strictModuleExceptionHandling", "crossOriginLoading", "devtoolModuleFilenameTemplate", "webassemblyModuleFilename", "hashFunction", "hashDigestLength", "performance", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "__dirname", "GROUP", "serverOnly", "neutralTarget", "createServerOnlyClientOnlyAliases", "not", "message", "shared", "resourceQuery", "WEBPACK_RESOURCE_QUERIES", "metadataRoute", "isWebpackBundledLayer", "createNextApiEsmAliases", "isWebpackServerOnlyLayer", "createAppRouterApiAliases", "isWebpackClientOnlyLayer", "and", "createRSCAliases", "edgeSSREntry", "oneOf", "parser", "url", "instrument", "images", "disableStaticImages", "issuer", "regexLikeCss", "dependency", "metadata", "metadataImageMeta", "isDev", "basePath", "fallbackNodePolyfills", "assert", "buffer", "constants", "domain", "http", "https", "os", "punycode", "querystring", "stream", "string_decoder", "sys", "timers", "tty", "util", "vm", "zlib", "events", "setImmediate", "sideEffects", "names", "ident", "next", "NEXT_PROJECT_ROOT", "webpack", "NormalModuleReplacementPlugin", "moduleName", "basename", "runtime", "<PERSON><PERSON><PERSON><PERSON>", "MemoryWithGcCachePlugin", "maxGenerations", "ReactRefreshWebpackPlugin", "ProvidePlugin", "<PERSON><PERSON><PERSON>", "getDefineEnvPlugin", "isTurbopack", "ReactLoadablePlugin", "REACT_LOADABLE_MANIFEST", "runtimeAsset", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "DropClientPage", "TraceEntryPointsPlugin", "esmExternals", "outputFileTracingRoot", "appDirEnabled", "traceIgnores", "swcLoaderConfig", "excludeDefaultMomentLocales", "IgnorePlugin", "resourceRegExp", "contextRegExp", "NextJsRequireCacheHotReloader", "devP<PERSON><PERSON>", "HotModuleReplacementPlugin", "PagesManifestPlugin", "isEdgeRuntime", "MiddlewarePlugin", "sriEnabled", "sri", "algorithm", "edgeEnvironments", "__NEXT_BUILD_ID", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "BuildManifestPlugin", "Profiling<PERSON><PERSON><PERSON>", "WellKnownErrorsPlugin", "CopyFilePlugin", "filePath", "cache<PERSON>ey", "__NEXT_VERSION", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "minimized", "AppBuildManifestPlugin", "ClientReferenceManifestPlugin", "experimentalInlineCss", "inlineCss", "FlightClientEntryPlugin", "NextTypesPlugin", "cacheLifeConfig", "cacheLife", "SubresourceIntegrityPlugin", "NextFontManifestPlugin", "cssChunking", "CssChunkingPlugin", "TelemetryPlugin", "Map", "relay", "styledComponents", "reactRemoveProperties", "compilerOptions", "experimentalDecorators", "removeConsole", "jsxImportSource", "emotion", "skipMiddlewareUrlNormalize", "skipTrailingSlashRedirect", "modularizeImports", "isImplicit", "baseUrl", "unshift", "JsConfigPathsPlugin", "edgeAsset", "experiments", "layers", "cacheUnaffected", "buildHttp", "urlImports", "<PERSON><PERSON><PERSON>", "cacheLocation", "lockfileLocation", "javascript", "generator", "asset", "trustedTypes", "enabledLibraryTypes", "snapshot", "versions", "pnp", "managedPaths", "immutablePaths", "providedExports", "usedExports", "configVars", "JSON", "stringify", "trailingSlash", "buildActivity", "devIndicators", "buildActivityPosition", "productionBrowserSourceMaps", "reactStrictMode", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "sw<PERSON><PERSON><PERSON><PERSON>", "imageLoaderFile", "loaderFile", "clientTraceMetadata", "serverSourceMaps", "cache", "maxMemoryGenerations", "Infinity", "cacheDirectory", "compression", "configFile", "buildDependencies", "defaultWebpack", "hooks", "done", "tap", "stats", "compilation", "nextPackage", "dirname", "dep", "delete", "NEXT_WEBPACK_LOGGING", "infra", "profileClient", "profileServer", "summaryClient", "summaryServer", "profile", "summary", "logDefault", "infrastructureLogging", "level", "debug", "log", "toString", "colors", "logging", "preset", "timings", "ProgressPlugin", "buildConfiguration", "rootDirectory", "customAppFile", "escapeStringRegexp", "isDevelopment", "targetWeb", "sassOptions", "future", "mode", "unsafeCache", "originalDevtool", "totalPages", "nextRuntime", "configFileName", "lazyCompilation", "entries", "then", "customSvgRule", "find", "nextImageRule", "craCompat", "fileLoaderExclude", "fileLoader", "topRules", "innerRules", "webpackDevMiddleware", "canMatchCss", "fileNames", "input", "hasUserCssConfig", "o", "Symbol", "for", "__next_css_remove", "e", "originalEntry", "updatedEntry", "originalFile", "finalizeEntrypoint", "value"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAyOaA,6BAA6B;eAA7BA;;IAbAC,yBAAyB;eAAzBA;;IAKAC,wBAAwB;eAAxBA;;IAzBAC,oBAAoB;eAApBA;;IA9BGC,kBAAkB;eAAlBA;;IA5DHC,mBAAmB;eAAnBA;;IAoKb,OAsrEC;eAtrE6BC;;IAXdC,yBAAyB;eAAzBA;;IAtBMC,eAAe;eAAfA;;IAHTC,oBAAoB;eAApBA;;;8DA9OK;kFACoB;4BACT;+DACV;yBACK;6DACP;8BAEkB;2BACsB;uBAOlD;4BAaA;wBAEkB;yBAEU;6DACd;wBACc;0EAI5B;4EACyB;qCACI;0CACL;4EACC;iCACA;qCACI;uCACE;qBACT;gCACE;sCACe;yCACN;iCACR;qEAUzB;qBACsB;wCACU;4CACI;wCACJ;yCAEC;oCACL;wCACI;iCACJ;iCAEuB;yBAInD;qDAC8C;uCAO9C;wBAC+B;mCACJ;sCAI3B;8BAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOP,MAAMC,oBACJC,QAAQ;AAEV,MAAMC,8BACJD,QAAQ;AAEV,IAAIE,SAASC,cAAK,CAACC,OAAO,IAAI,IAAI;IAChC,MAAM,IAAIC,MAAM;AAClB;AAEO,MAAMX,sBAAgC;IAC3C;IACA;IACA;IACA;CACD;AAED,MAAMY,6BAA6B;IACjC,+FAA+F;IAC/F,2HAA2H;IAC3H,2DAA2D;IAC3D;IACA,oGAAoG;IACpG,8GAA8G;IAC9G;CACD;AACD,MAAMC,kBAAkB;AAExB,MAAMC,qBACJ;AAEF,wBAAwB;AACxB,MAAMC,eAAe,AAACC,CAAAA,QAAQC,GAAG,CAACC,SAAS,IAAI,EAAC,EAC7CC,KAAK,CAACH,QAAQI,QAAQ,KAAK,UAAU,MAAM,KAC3CC,MAAM,CAAC,CAACC,IAAM,CAAC,CAACA;AAEnB,MAAMC,mBAA0DC,OAAOC,MAAM,CAAC;IAC5EC,kBAAkB;IAClBC,SACE,yDAAyD;IACzD;AACJ;AAEA,SAASC,YAAYC,OAAwB;IAC3C,OACE,0BAA0B;IAC1BA,QAAOC,IAAI,KAAK,CAAC,gBAAgB,CAAC,IAClC,0CAA0C;IAC1CD,QAAOC,IAAI,KAAK,CAAC,kBAAkB,CAAC,IACpC,0CAA0C;IAC1CD,QAAOC,IAAI,KAAK,CAAC,sBAAsB,CAAC;AAE5C;AAEA,MAAMC,uBAAuBC,IAAAA,gBAAQ,EACnC,CAACC;IACCC,QAAQC,IAAI,CACVC,IAAAA,kBAAM,EAACC,IAAAA,gBAAI,EAAC,gBACVA,IAAAA,gBAAI,EAAC,CAAC,8BAA8B,EAAEJ,QAAQ,IAAI,CAAC,IACnD,kGACA;AAEN;AAGF,IAAIK,oBAAoB;AACxB,IAAIC,+BAA+B;AACnC,MAAMC,yBACJ;AAEK,SAASzC,mBACd0C,aAAoC,EACpCC,YAAoC;QAGpCD,6BAAAA;IADA,MAAME,qBAAqBrC,QAAQsC,OAAO,CAACJ;KAC3CC,wBAAAA,cAAcZ,MAAM,sBAApBY,8BAAAA,sBAAsBI,KAAK,qBAA3BJ,4BAA6BK,OAAO,CAAC,CAACC;QACpC,IAAIA,QAAQ,OAAOA,SAAS,YAAY,SAASA,MAAM;YACrD,MAAMC,OAAOD,KAAKE,GAAG;YACrB,wEAAwE;YACxE,IAAID,SAASN,cAAc;gBACzBK,KAAKE,GAAG,GAAG;oBAACN;oBAAoBK;iBAA+B;YACjE,OAAO,IACLE,MAAMC,OAAO,CAACH,SACdA,KAAKI,IAAI,CAAC,CAACC,IAAMA,MAAMX,iBACvB,kCAAkC;YAClC,CAACM,KAAKI,IAAI,CACR,CAACC,IAAMA,MAAMV,sBAAsBU,MAAMb,yBAE3C;gBACA,MAAMc,MAAMN,KAAKO,SAAS,CAAC,CAACF,IAAMA,MAAMX;gBACxC,iCAAiC;gBACjCK,KAAKE,GAAG,GAAG;uBAAID;iBAAK;gBAEpB,kEAAkE;gBAClED,KAAKE,GAAG,CAACO,MAAM,CAACF,KAAK,GAAGX;YAC1B;QACF;IACF;AACF;AAEO,MAAM7C,uBAAuB;IAClC2D,gBAAgB;IAChBC,SAAS;QAAC;KAAe;IACzBC,UAAU;IACVC,eAAe;QAAC;KAAU;IAC1BC,eAAe;QAAC;KAAU;IAC1BC,gBAAgB;QAAC;QAAQ;KAAU;IACnCC,kBAAkB;QAAC;KAAe;IAClCC,YAAY;QAAC;QAAO;QAAS;KAAQ;IACrCC,mBAAmB;IACnBC,UAAU;IACVC,YAAY;QAAC;KAAO;IACpBC,WAAW;QAAC;KAAQ;IACpBC,OAAO,EAAE;IACTC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,cAAc,EAAE;AAClB;AAEO,MAAM7E,4BAA4B;IACvC,GAAGE,oBAAoB;IACvB4E,OAAO;AACT;AAEO,MAAM7E,2BAA2B;IACtC,GAAGC,oBAAoB;IACvB4E,OAAO;IACPjB,gBAAgB;IAChBK,gBAAgB;QAAC;QAAQ;KAAS;IAClCQ,gBAAgB;AAClB;AAEO,MAAM3E,gCAAgC;IAC3C,GAAGE,wBAAwB;IAC3B6E,OAAO;AACT;AAEO,MAAMtE,uBACX;AAEK,eAAeD,gBAAgB,EACpCwE,GAAG,EACHC,MAAM,EACNC,GAAG,EAKJ;IAKC,MAAM,EAAEC,QAAQ,EAAEC,eAAe,EAAE,GAAG,MAAMC,IAAAA,qBAAY,EAACL,KAAKC;IAC9D,MAAMK,oBAAoB,MAAMC,IAAAA,2BAAoB,EAACP,KAAKE;IAC1D,OAAO;QACLC;QACAC;QACAE;IACF;AACF;AAEO,SAAS/E;IACd,IAAI;QACFI,QAAQ;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,MAAM6E,qBAAqB;AAEZ,eAAelF,qBAC5B0E,GAAW,EACX,EACES,OAAO,EACPC,aAAa,EACbT,MAAM,EACNU,YAAY,EACZT,MAAM,KAAK,EACXU,WAAW,EACXC,gBAAgB,KAAK,EACrBC,QAAQ,EACRC,2BAA2B,KAAK,EAChCC,QAAQ,EACRC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,EACdC,MAAM,EACNC,kBAAkB,EAClBlB,QAAQ,EACRC,eAAe,EACfE,iBAAiB,EACjBgB,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EA+BjB;QA0GCvB,sBAOIA,uBAmoBIA,sBAsuBoBA,0BA6DtBA,2BAgBmBA,kBACWA,mBAGtBA,mBAIAE,2BAEmBF,mBACDE,4BACLF,mBA2BzBE,4BAJJ,wDAAwD;IACxD,iCAAiC;IACjCrC,gCAAAA,wBAmG0BmC,uBAqBTA,mBACQA,mBACLA,mBACXA,mBACEA,mBAsCXwB,yBAsKc3D,uBAmDZA,wBA0FAA,6BAAAA;IA/hEF,MAAM4D,WAAWf,iBAAiBgB,0BAAc,CAACC,MAAM;IACvD,MAAMC,eAAelB,iBAAiBgB,0BAAc,CAACG,UAAU;IAC/D,MAAMC,eAAepB,iBAAiBgB,0BAAc,CAACK,MAAM;IAE3D,uFAAuF;IACvF,MAAMC,0BAA0BF,gBAAgBF;IAEhD,MAAMK,cACJlB,SAASmB,WAAW,CAACC,MAAM,GAAG,KAC9BpB,SAASqB,UAAU,CAACD,MAAM,GAAG,KAC7BpB,SAAShC,QAAQ,CAACoD,MAAM,GAAG;IAE7B,MAAME,YAAY,CAAC,CAAClB;IACpB,MAAMmB,0BAA0B;IAChC,MAAMC,oBAAoB,CAAC,CAACvC,OAAOwC,YAAY,CAACC,WAAW,IAAIJ;IAC/D,MAAMK,sBAAsBC,IAAAA,8CAAsB,EAAC3C,UAC/C,kBACA;IAEJ,MAAM4C,kBAAkBC,IAAAA,sCAAkB,EAAC9C;IAE3C,IAAI,CAACE,OAAO6C,IAAAA,6BAAqB,EAAC9C,SAAS;QACzCA,OAAO+C,OAAO,GAAG;IACnB;IACA,MAAMA,UAAUC,aAAI,CAACC,IAAI,CAAClD,KAAKC,OAAO+C,OAAO;IAE7C,IAAIG,eAAe,CAACN,mBAAmB5C,OAAOwC,YAAY,CAACW,kBAAkB;IAC7E,IAAIC,kBAAkDC;IACtD,IAAIH,cAAc;YAEKxH,4BAAAA,6BAAAA;QADrB,0CAA0C;QAC1C,MAAM4H,gBAAe5H,WAAAA,QAAQ,8BAARA,8BAAAA,SAAkB6H,iBAAiB,sBAAnC7H,6BAAAA,iCAAAA,8BAAAA,2BACjB8H,MAAM;QACVJ,kBAAkBE,eACd;YAAC,CAAC,WAAW,EAAEA,cAAc;YAAW;SAAK,GAC7CD;IACN;IAEA,IAAI,CAAC3F,qBAAqB,CAACwF,gBAAgBN,iBAAiB;QAC1Da,KAAIC,IAAI,CACN,CAAC,6EAA6E,EAAEV,aAAI,CAACW,QAAQ,CAC3F5D,KACA6C,iBACA,+CAA+C,CAAC;QAEpDlF,oBAAoB;IACtB;IAEA,mEAAmE;IACnE,IAAI,CAACkF,mBAAmBnB,UAAU;QAChC,MAAMmC,IAAAA,iBAAY,EAAC5D,OAAOwC,YAAY,CAACqB,aAAa;IACtD;IAEA,4DAA4D;IAC5D,2DAA2D;IAC3D,MAAMC,yBAAmC,AACvC9D,CAAAA,OAAO+D,iBAAiB,IAAI,EAAE,AAAD,EAC7BC,MAAM,CAACrI;IAET,KAAK,MAAMsI,OAAOjE,OAAOwC,YAAY,CAAC0B,sBAAsB,IAAI,EAAE,CAAE;QAClE,IAAI,CAACJ,uBAAuBK,QAAQ,CAACF,MAAM;YACzCH,uBAAuBM,IAAI,CAACH;QAC9B;IACF;IAEA,IAAI,CAACtG,gCAAgC,CAACuF,gBAAgBlD,OAAOqE,QAAQ,EAAE;QACrEZ,KAAIC,IAAI,CACN;QAEF/F,+BAA+B;IACjC;IAEA,MAAM2G,4BACJtE,OAAOwC,YAAY,CAAC+B,WAAW,IAAI,CAAC,CAACvE,OAAO+D,iBAAiB;IAC/D,MAAMS,gBAAgB;QACpBC,MAAM;YAAEC,IAAI;gBAAC;gBAA8B;aAAsB;QAAC;QAClE,GAAIJ,4BAEA,CAAC,IACD;YAAEK,SAAS;gBAAC5E;mBAAQ3E;aAAoB;QAAC,CAAC;QAC9CwJ,SAAS,CAACC;YACR,IAAIzJ,oBAAoBoD,IAAI,CAAC,CAACC,IAAMA,EAAEgG,IAAI,CAACI,eAAe;gBACxD,OAAO;YACT;YAEA,MAAMC,kBAAkBC,IAAAA,qCAAoB,EAC1CF,aACAf;YAEF,IAAIgB,iBAAiB,OAAO;YAE5B,OAAOD,YAAYV,QAAQ,CAAC;QAC9B;IACF;IAEA,MAAMa,cAAcC,IAAAA,oCAAc,EAChC/B,cACAN,iBACAZ,yBACAe,SACAlC,UACAd,KACCoB,UAAUN,UACXZ,KACAwB,WACAzB,uBAAAA,OAAOwC,YAAY,qBAAnBxC,qBAAqBkF,aAAa,EAClCV,cAAcI,OAAO;IAGvB,MAAMO,sBAAsBH,cACxB3B,YACA+B,IAAAA,4CAAsB,GACpBpF,wBAAAA,OAAOwC,YAAY,qBAAnBxC,sBAAqBkF,aAAa,EAClCnF,KACAE,KACA+B,yBACAwC,cAAcI,OAAO;IAG3B,IAAIS,+BAA+B;IACnC,MAAMC,eAAe,CAACC;YAElBvF;QADF,IACEA,CAAAA,2BAAAA,uBAAAA,OAAQwC,YAAY,qBAApBxC,qBAAsBwF,iBAAiB,KACvC,CAACH,8BACD;gBAMA3J,oCAAAA;YALA,sEAAsE;YACtE,+CAA+C;YAC/C,qFAAqF;YACrF,uDAAuD;YACvD2J,+BAA+B;aAC/B3J,WAAAA,QAAQ,8BAARA,qCAAAA,SAAkB+J,yBAAyB,qBAA3C/J,wCAAAA,UACEsH,aAAI,CAACC,IAAI,CAACF,SAAS,CAAC,kBAAkB,EAAE2C,KAAKC,GAAG,GAAG,KAAK,CAAC;QAE7D;QAEA,OAAO;YACLC,QAAQ;YACRC,SAAS;gBACPC,UAAU9D;gBACV+D,SAAShG;gBACTc;gBACAM;gBACA6E,iBAAiB/F,OAAOwB;gBACxBwE,YAAYjG;gBACZE;gBACA6D,mBAAmBD;gBACnBzD;gBACA6F,aAAalD,aAAI,CAACC,IAAI,CAAClD,KAAKC,CAAAA,0BAAAA,OAAQ+C,OAAO,KAAI,SAAS,SAAS;gBACjEoD,yBAAyB1F;gBACzB,GAAG8E,YAAY;YACjB;QACF;IACF;IAEA,6CAA6C;IAC7C,MAAMa,uBAAuBd,aAAa;QACxCe,kBAAkB;QAClBC,aAAaC,yBAAc,CAACC,qBAAqB;QACjDC,KAAK;IACP;IACA,MAAMC,oBAAoBpB,aAAa;QACrCe,kBAAkB;QAClBC,aAAaC,yBAAc,CAACI,mBAAmB;QAC/CF,KAAK;IACP;IACA,MAAMG,wBAAwBtB,aAAa;QACzCe,kBAAkB;QAClBC,aAAaC,yBAAc,CAACM,eAAe;QAC3CJ,KAAK;IACP;IACA,oDAAoD;IACpD,MAAMK,mBAAmBxB,aAAa;QACpCe,kBAAkB;QAClBI,KAAK;IACP;IAEA,MAAMM,iBAAiB;QACrBC,OAAO9D,eAAe4D,mBAAmB9B;IAC3C;IAEA,MAAMiC,wBAAwB5E,YAC1B;QACE,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/C+D;QACApB;QACAG;KACD,CAAC1I,MAAM,CAACyK,WACT,EAAE;IAEN,MAAMC,yBAAyB;QAC7B;QACA,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/Cf;QACApB;KACD,CAACvI,MAAM,CAACyK;IAET,MAAME,yBAAyB;QAC7B;QACA,mEAAmE;QACnE,wFAAwF;QACxF,gDAAgD;QAChD,+CAA+C;QAC/C9B,aAAa;YACXe,kBAAkB;YAClBC,aAAaC,yBAAc,CAACc,UAAU;QACxC;QACArC;KACD,CAACvI,MAAM,CAACyK;IAET,MAAMI,sBACJrH,OAAOwB,WAAW;QAAC/F,QAAQsC,OAAO,CAACJ;KAAwB,GAAG,EAAE;IAElE,2CAA2C;IAC3C,MAAM2J,0BAA0B,CAAC,EAC/BC,cAAc,EACdC,YAAY,EAIb,GAAK;eACAA,eAAeH,sBAAsB,EAAE;YAC3C;gBACE,iDAAiD;gBACjD,uBAAuB;gBACvB1B,QAAQ;YACV;eACIvD,YACA;gBACE,uDAAuD;gBACvD,iDAAiD;gBACjD,gDAAgD;gBAChD,+CAA+C;gBAC/CmF,iBAAiBZ,wBAAwBF;gBACzC1B;gBACAG;aACD,CAAC1I,MAAM,CAACyK,WACT,EAAE;SACP;IAED,MAAMQ,yBAAyBH,wBAAwB;QACrDC,gBAAgB;QAChB,8EAA8E;QAC9EC,cAAc;IAChB;IACA,MAAME,qBAAqBJ,wBAAwB;QACjDC,gBAAgB;QAChBC,cAAc;IAChB;IAEA,2EAA2E;IAC3E,8EAA8E;IAC9E,gBAAgB;IAChB,MAAMG,wBAAwB1E,eAC1BoC,aAAa;QACXe,kBAAkB;QAClBC,aAAaC,yBAAc,CAACsB,GAAG;IACjC,KACAd,eAAeC,KAAK;IAExB,MAAMc,iBAAiB9H,OAAO8H,cAAc;IAE5C,MAAMC,aAAa/F,0BACfgB,aAAI,CAACC,IAAI,CAACF,SAASiF,4BAAgB,IACnCjF;IAEJ,MAAMkF,uBAAuB;QAC3B;WACIrG,eAAesG,2BAAkB,GAAG,EAAE;QAC1C,kCAAkC;QAClC;KACD;IAED,MAAMC,gBAAgB1G,WACjB;QACC,0BAA0B;QAC1B,WAAW,EAAE;QACb,GAAIxB,MACA;YACE,CAACmI,qDAAyC,CAAC,EAAE1M,QAAQsC,OAAO,CAC1D,CAAC,yDAAyD,CAAC;YAE7D,CAACqK,2CAA+B,CAAC,EAC/B,CAAC,EAAE,CAAC,GACJrF,aAAI,CACDW,QAAQ,CACP5D,KACAiD,aAAI,CAACC,IAAI,CAACqF,2CAA6B,EAAE,OAAO,YAEjDC,OAAO,CAAC,OAAO;QACtB,IACA,CAAC,CAAC;QACN,CAACC,4CAAgC,CAAC,EAChC,CAAC,EAAE,CAAC,GACJxF,aAAI,CACDW,QAAQ,CACP5D,KACAiD,aAAI,CAACC,IAAI,CACPqF,2CAA6B,EAC7BrI,MAAM,CAAC,WAAW,CAAC,GAAG,YAGzBsI,OAAO,CAAC,OAAO;QACpB,GAAIlG,YACA;YACE,CAACoG,gDAAoC,CAAC,EAAExI,MACpC;gBACEvE,QAAQsC,OAAO,CACb,CAAC,yDAAyD,CAAC;gBAE7D,CAAC,EAAE,CAAC,GACFgF,aAAI,CACDW,QAAQ,CACP5D,KACAiD,aAAI,CAACC,IAAI,CACPqF,2CAA6B,EAC7B,oBAGHC,OAAO,CAAC,OAAO;aACrB,GACD;gBACE,CAAC,EAAE,CAAC,GACFvF,aAAI,CACDW,QAAQ,CACP5D,KACAiD,aAAI,CAACC,IAAI,CACPqF,2CAA6B,EAC7B,gBAGHC,OAAO,CAAC,OAAO;aACrB;QACP,IACA,CAAC,CAAC;IACR,IACAlF;IAEJ,MAAMqF,gBAAkD;QACtD,yCAAyC;QACzCtJ,YAAY;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAS;SAAQ;QACpEuJ,gBAAgB3I,OAAOwC,YAAY,CAACmG,cAAc;QAClD7J,SAAS;YACP;eACG3C;SACJ;QACD2D,OAAO8I,IAAAA,2CAAoB,EAAC;YAC1B7F;YACAtB;YACAG;YACAE;YACA7B;YACAD;YACAa;YACAM;YACApB;YACAe;YACAmB;QACF;QACA,GAAIR,WACA;YACE1C,UAAU;gBACR3C,SAASV,QAAQsC,OAAO,CAAC;YAC3B;QACF,IACAqF,SAAS;QACb,oFAAoF;QACpF9D,YAAYsJ,IAAAA,qBAAY,EAACnI,cAAc;QACvC,GAAIkB,gBAAgB;YAClB1C,gBAAgBgJ,2BAAkB;QACpC,CAAC;QACDY,SAAS;YACPhH,eAAe,IAAIiH,yEAAoC,KAAK1F;SAC7D,CAAC5G,MAAM,CAACyK;IACX;IAEA,2DAA2D;IAC3D,gEAAgE;IAChE,mEAAmE;IACnE,MAAM8B,qBAA+B,EAAE;IACvC,MAAMC,yBAAmC,EAAE;IAC3C,MAAMC,2BAA2B,IAAIC;IACrC,iDAAiD;IACjD,MAAMC,iBAAiB,CACrBC,aACAC,gBACAC;QAEA,IAAI;YACF,IAAIL,yBAAyBM,GAAG,CAACH,cAAc;gBAC7C;YACF;YACAH,yBAAyBO,GAAG,CAACJ;YAE7B,MAAMK,kBAAkBhO,QAAQsC,OAAO,CAAC,GAAGqL,YAAY,aAAa,CAAC,EAAE;gBACrEE,OAAO;oBAACD;iBAAe;YACzB;YAEA,6FAA6F;YAC7F,0EAA0E;YAC1E,eAAe;YACf,0EAA0E;YAC1E,2EAA2E;YAC3E,MAAMK,YAAY3G,aAAI,CAACC,IAAI,CAACyG,iBAAiB;YAE7C,yFAAyF;YACzF,IAAIH,MAAMpF,QAAQ,CAACwF,YAAY;YAC/BJ,MAAMnF,IAAI,CAACuF;YACX,MAAMC,eAAelO,QAAQgO,iBAAiBE,YAAY,IAAI,CAAC;YAC/D,KAAK,MAAMC,QAAQjN,OAAOkN,IAAI,CAACF,cAAe;gBAC5CR,eAAeS,MAAMF,WAAWJ;YAClC;QACF,EAAE,OAAOQ,GAAG;QACV,uDAAuD;QACzD;IACF;IAEA,KAAK,MAAMV,eAAe;QACxB;QACA;WACIhH,YACA;YACE,CAAC,wBAAwB,EAAEK,qBAAqB;YAChD,CAAC,4BAA4B,EAAEA,qBAAqB;SACrD,GACD,EAAE;KACP,CAAE;QACD0G,eAAeC,aAAatJ,KAAKkJ;IACnC;IACAG,eAAe,QAAQrJ,KAAKiJ;IAE5B,MAAMgB,cAAchK,OAAOgK,WAAW;IAEtC,wDAAwD;IACxD,2BAA2B;IAC3B,IAAIhK,OAAOiK,sBAAsB,IAAInG,wBAAwB;QAC3D,MAAMoG,2BAA2BpG,uBAAuBrH,MAAM,CAAC,CAACwH;gBAC9DjE;oBAAAA,iCAAAA,OAAOiK,sBAAsB,qBAA7BjK,+BAA+BmE,QAAQ,CAACF;;QAE1C,IAAIiG,yBAAyB/H,MAAM,GAAG,GAAG;YACvC,MAAM,IAAIpG,MACR,CAAC,8FAA8F,EAAEmO,yBAAyBjH,IAAI,CAC5H,OACC;QAEP;IACF;IAEA,+CAA+C;IAC/C,MAAMkH,yBAAyB1O,kBAAkBuI,MAAM,IACjDhE,OAAOiK,sBAAsB,IAAI,EAAE,EACvCxN,MAAM,CAAC,CAACwH,MAAQ,EAACH,0CAAAA,uBAAwBK,QAAQ,CAACF;IACpD,wEAAwE;IACxE,MAAMmG,6BAA6B,IAAIC,OACrC,CAAC,2BAA2B,EAAEF,uBAC3BG,GAAG,CAAC,CAAC5N,IAAMA,EAAE6L,OAAO,CAAC,OAAO,YAC5BtF,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMsH,yBAAyB,IAAIF,OACjC,CAAC,2BAA2B,EAAEvG,0CAAAA,uBAC1BwG,GAAG,CAAC,CAAC5N,IAAMA,EAAE6L,OAAO,CAAC,OAAO,YAC7BtF,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMuH,kBAAkBC,IAAAA,oCAAmB,EAAC;QAC1CzK;QACAmK;QACAC;QACAM,oBAAoB5G;QACpB/D;IACF;IAEA,MAAM4K,sBAAsB,IAAIN,OAAO,CAAC,IAAI,EAAEvC,eAAe7E,IAAI,CAAC,KAAK,EAAE,CAAC;IAE1E,MAAM2H,yBAAyB;QAACpG,cAAcC,IAAI;QAAEkG;KAAoB;IAExE,MAAME,iBAAiBnP,QAAQ,UAAUmP,cAAc;IAEvD,IAAIhN,gBAAuC;QACzCiN,aAAaC,OAAO3O,QAAQC,GAAG,CAAC2O,wBAAwB,KAAK3H;QAC7D,GAAIvB,eAAe;YAAEmJ,kBAAkB;gBAAEC,MAAM;YAAK;QAAE,IAAI,CAAC,CAAC;QAC5D,aAAa;QACbC,WACE1J,YAAYG,eAER,8DAA8D;QAC9D,+CAA+C;QAC/C;YACE;eACIA,eACA;gBACE;oBACE,yBAAyB;oBACzB,2BAA2B;gBAC7B;gBACAwJ,IAAAA,0CAAwB;gBACxBC,qDAAmC;aACpC,GACD,EAAE;SACP,GACD;eACKR;YACH,CAAC,EACCS,OAAO,EACPC,OAAO,EACP1M,cAAc,EACd2M,WAAW,EACXC,UAAU,EAqBX,GACCjB,gBACEc,SACAC,SACA1M,gBACA2M,YAAYE,WAAW,EACvB,CAAC7F;oBACC,MAAM8F,kBAAkBF,WAAW5F;oBACnC,OAAO,CAAC+F,gBAAwBC,mBAC9B,IAAIC,QAAQ,CAAC9N,SAAS+N;4BACpBJ,gBACEC,gBACAC,kBACA,CAACG,KAAKC,QAAQC;oCAIRA;gCAHJ,IAAIF,KAAK,OAAOD,OAAOC;gCACvB,IAAI,CAACC,QAAQ,OAAOjO,QAAQ;oCAAC;oCAAM;iCAAM;gCACzC,MAAMmO,QAAQ,SAAS1H,IAAI,CAACwH,UACxBC,CAAAA,gCAAAA,mCAAAA,YAAaE,mBAAmB,qBAAhCF,iCAAkChP,IAAI,MACtC,WACA,UAAUuH,IAAI,CAACwH;gCACnBjO,QAAQ;oCAACiO;oCAAQE;iCAAM;4BACzB;wBAEJ;gBACJ;SAEL;QAEPE,cAAc;YACZC,cAAc,CAACrM;YACfsM,gBAAgB;YAChBC,SAAS;YAETC,aAAa,AAAC,CAAA;gBAGZ,kBAAkB;gBAClB,IAAIxM,KAAK;oBACP,IAAI6B,cAAc;wBAChB;;;;;YAKA,GACA,MAAM4K,wBAAwB,CAACC;4BAC7B,8FAA8F;4BAC9F,4EAA4E;4BAC5E,MAAMC,QACJ;4BACF,MAAMC,QAAQF,WAAWE,KAAK,CAACD;4BAC/B,OAAOC,QAAQA,KAAK,CAAC,EAAE,GAAG;wBAC5B;wBACA,OAAO;4BACLC,aAAa;gCACX,+FAA+F;gCAC/F,yDAAyD;gCACzDC,QAAQ;oCACNC,QAAQ;oCACRC,oBAAoB;oCACpBxI,MAAM;oCACNyI,SAAS;oCACTC,WAAW;oCACXC,kBAAkB;oCAClBC,oBAAoB;oCACpBxD,MAAM,CAAC5M;wCACL,MAAMqQ,WAAWrQ,QAAOsQ,gBAAgB;wCACxC,MAAMC,aAAad,sBAAsBY;wCACzC,IAAIE,YAAY;4CACd,OAAO,CAAC,cAAc,EAAEA,YAAY;wCACtC,OAAO;4CACL,MAAMC,OAAOC,eAAM,CAACC,UAAU,CAAC,QAAQC,MAAM,CAACN;4CAC9CG,KAAKG,MAAM,CAACN;4CACZ,OAAO,CAAC,cAAc,EAAEG,KAAKI,MAAM,CAAC,QAAQ;wCAC9C;oCACF;gCACF;gCACA,mCAAmC;gCACnCC,SAAS;gCACTC,gBAAgB;4BAClB;wBACF;oBACF;oBAEA,OAAO;gBACT;gBAEA,IAAIjM,gBAAgBF,cAAc;oBAChC,OAAO;wBACLoM,UAAU,GAAGpM,eAAe,CAAC,YAAY,CAAC,GAAG,GAAG,SAAS,CAAC;wBAC1DoL,QAAQ;wBACRG,WAAW;oBACb;gBACF;gBAEA,MAAMc,sBAAsB;oBAC1BjB,QAAQ;oBACRnD,MAAM;oBACN,6DAA6D;oBAC7DqE,OAAOC,4BAAqB;oBAC5B1J,MAAKxH,OAAW;wBACd,MAAMmR,WAAWnR,QAAOsQ,gBAAgB,oBAAvBtQ,QAAOsQ,gBAAgB,MAAvBtQ;wBACjB,OAAOmR,WACHnF,uBAAuBzK,IAAI,CAAC,CAAC6P,UAC3BD,SAASE,UAAU,CAACD,YAEtB;oBACN;oBACAE,UAAU;oBACV,mEAAmE;oBACnE,wCAAwC;oBACxCC,SAAS;gBACX;gBAEA,MAAMC,gBAAgB;oBACpBhK,MAAKxH,OAIJ;4BAEIA;wBADH,OACE,GAACA,eAAAA,QAAOC,IAAI,qBAAXD,aAAaqR,UAAU,CAAC,WACzBrR,QAAOyR,IAAI,KAAK,UAChB,oBAAoBjK,IAAI,CAACxH,QAAOsQ,gBAAgB,MAAM;oBAE1D;oBACA1D,MAAK5M,OAKJ;wBACC,MAAMwQ,OAAOC,eAAM,CAACC,UAAU,CAAC;wBAC/B,IAAI3Q,YAAYC,UAAS;4BACvBA,QAAO0R,UAAU,CAAClB;wBACpB,OAAO;4BACL,IAAI,CAACxQ,QAAO2R,QAAQ,EAAE;gCACpB,MAAM,IAAI7S,MACR,CAAC,iCAAiC,EAAEkB,QAAOC,IAAI,CAAC,uBAAuB,CAAC;4BAE5E;4BACAuQ,KAAKG,MAAM,CAAC3Q,QAAO2R,QAAQ,CAAC;gCAAEtD,SAASvL;4BAAI;wBAC7C;wBAEA,wFAAwF;wBACxF,yHAAyH;wBACzH,0CAA0C;wBAC1C,IAAI9C,QAAOiR,KAAK,EAAE;4BAChBT,KAAKG,MAAM,CAAC3Q,QAAOiR,KAAK;wBAC1B;wBAEA,OAAOT,KAAKI,MAAM,CAAC,OAAOgB,SAAS,CAAC,GAAG;oBACzC;oBACAN,UAAU;oBACVpB,WAAW;oBACXF,oBAAoB;gBACtB;gBAEA,kBAAkB;gBAClB,OAAO;oBACL,oDAAoD;oBACpD,qDAAqD;oBACrD,oDAAoD;oBACpD,0CAA0C;oBAC1CD,QAAQ,CAAC8B,QACP,CAAC,iCAAiCrK,IAAI,CAACqK,MAAMjF,IAAI;oBACnDiD,aAAa;wBACXiC,WAAWd;wBACXe,KAAKP;oBACP;oBACApB,oBAAoB;oBACpBH,SAAS;gBACX;YACF,CAAA;YACA+B,cAAcxN,WACV;gBAAEoI,MAAMqF,+CAAmC;YAAC,IAC5C7L;YAEJ8L,UACE,CAAClP,OACAwB,CAAAA,YACCG,gBACCE,gBAAgB9B,OAAOwC,YAAY,CAAC4M,kBAAkB;YAC3DC,WAAW;gBACT,oBAAoB;gBACpB,CAAChL;oBACC,4BAA4B;oBAC5B,MAAM,EAAEiL,YAAY,EAAE,GACpB5T,QAAQ;oBACV,IAAI4T,eAAeC,KAAK,CAAClL;gBAC3B;gBACA,aAAa;gBACb,CAACA;oBACC,MAAM,EACJmL,kBAAkB,EACnB,GAAG9T,QAAQ;oBACZ,IAAI8T,mBAAmB;wBACrBC,gBAAgB;4BACdnF,KAAK;gCACH,+DAA+D;gCAC/D,+CAA+C;gCAC/CoF,QAAQ;gCACR,6DAA6D;gCAC7D,4DAA4D;gCAC5DC,YAAY;4BACd;wBACF;oBACF,GAAGJ,KAAK,CAAClL;gBACX;aACD;QACH;QACAiH,SAASvL;QACT,8CAA8C;QAC9C6P,OAAO;YACL,OAAO;gBACL,GAAIzH,gBAAgBA,gBAAgB,CAAC,CAAC;gBACtC,GAAGxH,WAAW;YAChB;QACF;QACAkP,cAAcjT,OAAOC,MAAM,CAAC;YAC1B,GAAGF,gBAAgB;YACnBmT,IAAI,GAAE9P,uBAAAA,OAAO6P,YAAY,qBAAnB7P,qBAAqB+P,cAAc;QAC3C;QACAC,QAAQ;YACN,sEAAsE;YACtE,kCAAkC;YAClCC,YAAY,GACVjQ,OAAOkQ,WAAW,GACdlQ,OAAOkQ,WAAW,CAACC,QAAQ,CAAC,OAC1BnQ,OAAOkQ,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,KAC7BpQ,OAAOkQ,WAAW,GACpB,GACL,OAAO,CAAC;YACTlN,MAAM,CAAC/C,OAAO6B,eAAekB,aAAI,CAACC,IAAI,CAAC8E,YAAY,YAAYA;YAC/D,oCAAoC;YACpCiG,UAAUhM,0BACN/B,OAAO2B,eACL,CAAC,SAAS,CAAC,GACX,CAAC,YAAY,CAAC,GAChB,CAAC,cAAc,EAAEhB,gBAAgB,cAAc,GAAG,MAAM,EACtDX,MAAM,KAAKkB,SAAS,iBAAiB,iBACtC,GAAG,CAAC;YACTkP,SAAS5O,YAAYG,eAAe,SAASyB;YAC7CiN,eAAe7O,YAAYG,eAAe,WAAW;YACrD2O,wBAAwB;YACxBC,uBACE;YACF,uDAAuD;YACvDC,eAAezO,0BACX,cACA,CAAC,cAAc,EAAEpB,gBAAgB,cAAc,KAC7CX,MAAM,WAAW,uBAClB,GAAG,CAAC;YACTyQ,+BAA+B;YAC/BC,oBAAoB3G;YACpB,iEAAiE;YACjE,mGAAmG;YACnG,iEAAiE;YACjE,oGAAoG;YACpG,2FAA2F;YAC3F4G,+BAA+B3Q,MAC3B,6BACAoD;YACJwN,2BAA2B;YAC3BC,cAAc;YACdC,kBAAkB;QACpB;QACAC,aAAa;QACbhT,SAAS0K;QACTuI,eAAe;YACb,+BAA+B;YAC/BnR,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,CAACoR,MAAM,CACN,CAACpR,OAAO8F;gBACN,4DAA4D;gBAC5D9F,KAAK,CAAC8F,OAAO,GAAG5C,aAAI,CAACC,IAAI,CAACkO,WAAW,WAAW,WAAWvL;gBAE3D,OAAO9F;YACT,GACA,CAAC;YAEHhB,SAAS;gBACP;mBACG3C;aACJ;YACD2M,SAAS,EAAE;QACb;QACA7L,QAAQ;YACNgB,OAAO;gBACL,+EAA+E;gBAC/E;oBACEyN,aAAa;wBACXhH,IAAI;+BACC6B,yBAAc,CAAC6K,KAAK,CAACC,UAAU;+BAC/B9K,yBAAc,CAAC6K,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACAtT,SAAS;wBACP,6CAA6C;wBAC7C8B,OAAOyR,IAAAA,wDAAiC,EAAC;oBAC3C;gBACF;gBACA;oBACE7F,aAAa;wBACX8F,KAAK;+BACAjL,yBAAc,CAAC6K,KAAK,CAACC,UAAU;+BAC/B9K,yBAAc,CAAC6K,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACAtT,SAAS;wBACP,6CAA6C;wBAC7C8B,OAAOyR,IAAAA,wDAAiC,EAAC;oBAC3C;gBACF;gBACA,mEAAmE;gBACnE;oBACE9M,MAAM;wBACJ;wBACA;qBACD;oBACDmB,QAAQ;oBACR8F,aAAa;wBACXhH,IAAI6B,yBAAc,CAAC6K,KAAK,CAACC,UAAU;oBACrC;oBACAxL,SAAS;wBACP4L,SACE;oBACJ;gBACF;gBACA;oBACEhN,MAAM;wBACJ;wBACA;qBACD;oBACDmB,QAAQ;oBACR8F,aAAa;wBACX8F,KAAK;+BACAjL,yBAAc,CAAC6K,KAAK,CAACC,UAAU;+BAC/B9K,yBAAc,CAAC6K,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACAzL,SAAS;wBACP4L,SACE;oBACJ;gBACF;gBACA,yFAAyF;gBACzF,iFAAiF;gBACjF,oCAAoC;gBACpC;oBACEhN,MAAM;wBACJ;wBACA;qBACD;oBACDmB,QAAQ;oBACR8F,aAAa;wBACXhH,IAAI6B,yBAAc,CAAC6K,KAAK,CAACE,aAAa;oBACxC;gBACF;mBACIxP,eACA,EAAE,GACF;oBACE;wBACE2C,MAAM;wBACNmB,QAAQ;oBACV;iBACD;mBACDvD,YACA;oBACE;wBACE,uFAAuF;wBACvF,UAAU;wBACV6L,OAAO3H,yBAAc,CAACmL,MAAM;wBAC5BjN,MAAMvI;oBACR;oBACA,4CAA4C;oBAC5C;wBACEyV,eAAe,IAAItH,OACjBuH,mCAAwB,CAACC,aAAa;wBAExC3D,OAAO3H,yBAAc,CAACC,qBAAqB;oBAC7C;oBACA;wBACE,gEAAgE;wBAChE,2CAA2C;wBAC3C0H,OAAO3H,yBAAc,CAACI,mBAAmB;wBACzClC,MAAM;oBACR;oBACA;wBACEiH,aAAaoG,4BAAqB;wBAClC9T,SAAS;4BACP8B,OAAOiS,IAAAA,8CAAuB;wBAChC;oBACF;oBACA;wBACErG,aAAasG,+BAAwB;wBACrChU,SAAS;4BACP8B,OAAOmS,IAAAA,gDAAyB,EAAC;wBACnC;oBACF;oBACA;wBACEvG,aAAawG,+BAAwB;wBACrClU,SAAS;4BACP8B,OAAOmS,IAAAA,gDAAyB,EAAC;wBACnC;oBACF;iBACD,GACD,EAAE;mBACF5P,aAAa,CAACZ,WACd;oBACE;wBACEiK,aAAasG,+BAAwB;wBACrCvN,MAAM;4BACJ,8DAA8D;4BAC9D,yBAAyB;4BACzB0N,KAAK;gCACHvH;gCACA;oCACE4G,KAAK;wCAACpH;wCAA4BlO;qCAAmB;gCACvD;6BACD;wBACH;wBACA8B,SAAS;4BACPuB,YAAYsJ,IAAAA,qBAAY,EAACnI,cAAc;4BACvCxB,gBAAgB+I;4BAChB,mFAAmF;4BACnF,kFAAkF;4BAClF,8BAA8B;4BAC9BnI,OAAOsS,IAAAA,uCAAgB,EAAC1P,qBAAqB;gCAC3C,iCAAiC;gCACjC5B;gCACAoN,OAAO3H,yBAAc,CAACC,qBAAqB;gCAC3C5E;4BACF;wBACF;wBACAvD,KAAK;oBACP;iBACD,GACD,EAAE;gBACN,kDAAkD;gBAClD,yDAAyD;mBACrD,CAAC2B,OAAOwC,YAAY,CAAC9C,cAAc,GACnC;oBACE;wBACE+E,MAAM;wBACNzG,SAAS;4BACP0B,gBAAgB;wBAClB;oBACF;iBACD,GACD,EAAE;mBACF2C,aAAaT,eACb;oBACE,sEAAsE;oBACtE,mEAAmE;oBACnE,oCAAoC;oBACpC;wBACE+P,eAAe,IAAItH,OACjBuH,mCAAwB,CAACS,YAAY;wBAEvCnE,OAAO3H,yBAAc,CAACC,qBAAqB;oBAC7C;iBACD,GACD,EAAE;mBACFnE,YACA;oBACE;wBACE,8CAA8C;wBAC9C,kEAAkE;wBAClEiQ,OAAO;4BACL;gCACE5G,aAAasG,+BAAwB;gCACrCvN,MAAM;oCACJ,8DAA8D;oCAC9D,yBAAyB;oCACzB0N,KAAK;wCACHvH;wCACA;4CACE4G,KAAK;gDAACpH;gDAA4BlO;6CAAmB;wCACvD;qCACD;gCACH;gCACA8B,SAAS;oCACP,8DAA8D;oCAC9D,4DAA4D;oCAC5D8B,OAAOsS,IAAAA,uCAAgB,EAAC1P,qBAAqB;wCAC3C5B;wCACAoN,OAAO3H,yBAAc,CAACC,qBAAqB;wCAC3C5E;oCACF;gCACF;4BACF;4BACA;gCACE6C,MAAMmG;gCACNc,aAAanF,yBAAc,CAACI,mBAAmB;gCAC/C3I,SAAS;oCACP8B,OAAOsS,IAAAA,uCAAgB,EAAC1P,qBAAqB;wCAC3C5B;wCACAoN,OAAO3H,yBAAc,CAACI,mBAAmB;wCACzC/E;oCACF;gCACF;4BACF;yBACD;oBACH;oBACA;wBACE6C,MAAMmG;wBACNc,aAAanF,yBAAc,CAACM,eAAe;wBAC3C7I,SAAS;4BACP8B,OAAOsS,IAAAA,uCAAgB,EAAC1P,qBAAqB;gCAC3C5B;gCACAoN,OAAO3H,yBAAc,CAACM,eAAe;gCACrCjF;4BACF;wBACF;oBACF;iBACD,GACD,EAAE;gBACN,iFAAiF;mBAC7ES,aAAapC,OAAOwB,WACpB;oBACE;wBACEgD,MAAMD,cAAcC,IAAI;wBACxBG,SAAS;4BACP,+CAA+C;4BAC/CJ,cAAcI,OAAO;4BACrB2F;4BACAtO;yBACD;wBACDyP,aAAanF,yBAAc,CAACM,eAAe;wBAC3CxI,KAAKiJ;wBACLtJ,SAAS;4BACPuB,YAAYsJ,IAAAA,qBAAY,EAACnI,cAAc;wBACzC;oBACF;iBACD,GACD,EAAE;gBACN;oBACE4R,OAAO;wBACL;4BACE,GAAG9N,aAAa;4BAChBkH,aAAanF,yBAAc,CAACsB,GAAG;4BAC/B0K,QAAQ;gCACN,kDAAkD;gCAClD,mGAAmG;gCACnG,2EAA2E;gCAC3E,wIAAwI;gCACxIC,KAAK,CAAC5Q;4BACR;4BACAvD,KAAKuJ;wBACP;wBACA;4BACEnD,MAAMD,cAAcC,IAAI;4BACxBiH,aAAanF,yBAAc,CAACc,UAAU;4BACtChJ,KAAK+I;4BACLpJ,SAAS;gCACPuB,YAAYsJ,IAAAA,qBAAY,EAACnI,cAAc;gCACvCxB,gBAAgB+I;gCAChBnI,OAAOsS,IAAAA,uCAAgB,EAAC1P,qBAAqB;oCAC3C5B;oCACAoN,OAAO3H,yBAAc,CAACc,UAAU;oCAChCzF;gCACF;4BACF;wBACF;wBACA;4BACE6C,MAAMD,cAAcC,IAAI;4BACxBiH,aAAanF,yBAAc,CAACkM,UAAU;4BACtCpU,KAAK8I;4BACLnJ,SAAS;gCACPuB,YAAYsJ,IAAAA,qBAAY,EAACnI,cAAc;gCACvCxB,gBAAgB+I;gCAChBnI,OAAOsS,IAAAA,uCAAgB,EAAC1P,qBAAqB;oCAC3C5B;oCACAoN,OAAO3H,yBAAc,CAACkM,UAAU;oCAChC7Q;gCACF;4BACF;wBACF;2BACIS,YACA;4BACE;gCACEoC,MAAMD,cAAcC,IAAI;gCACxBiH,aAAasG,+BAAwB;gCACrCpN,SAAS1I;gCACTmC,KAAK4I;4BACP;4BACA;gCACExC,MAAMD,cAAcC,IAAI;gCACxBkN,eAAe,IAAItH,OACjBuH,mCAAwB,CAACS,YAAY;gCAEvChU,KAAK4I;4BACP;4BACA;gCACExC,MAAMD,cAAcC,IAAI;gCACxBiH,aAAanF,yBAAc,CAACM,eAAe;gCAC3C,uEAAuE;gCACvEjC,SAAS5I;gCACTqC,KAAKqJ;gCACL1J,SAAS;oCACPuB,YAAYsJ,IAAAA,qBAAY,EAACnI,cAAc;gCACzC;4BACF;4BACA;gCACE+D,MAAMD,cAAcC,IAAI;gCACxBiH,aAAanF,yBAAc,CAACI,mBAAmB;gCAC/C/B,SAAS1I;gCACTmC,KAAKsJ;gCACL3J,SAAS;oCACPuB,YAAYsJ,IAAAA,qBAAY,EAACnI,cAAc;gCACzC;4BACF;yBACD,GACD,EAAE;wBACN;4BACE,GAAG8D,aAAa;4BAChBnG,KAAK;mCACAiJ;gCACHP,eAAeC,KAAK;gCACpB7B;6BACD,CAAC1I,MAAM,CAACyK;wBACX;qBACD;gBACH;mBAEI,CAAClH,OAAO0S,MAAM,CAACC,mBAAmB,GAClC;oBACE;wBACElO,MAAMjJ;wBACNoK,QAAQ;wBACRgN,QAAQ;4BAAEpB,KAAKqB,iBAAY;wBAAC;wBAC5BC,YAAY;4BAAEtB,KAAK;gCAAC;6BAAM;wBAAC;wBAC3BG,eAAe;4BACbH,KAAK;gCACH,IAAInH,OAAOuH,mCAAwB,CAACmB,QAAQ;gCAC5C,IAAI1I,OAAOuH,mCAAwB,CAACC,aAAa;gCACjD,IAAIxH,OAAOuH,mCAAwB,CAACoB,iBAAiB;6BACtD;wBACH;wBACAnN,SAAS;4BACPoN,OAAOhT;4BACPS;4BACAwS,UAAUlT,OAAOkT,QAAQ;4BACzBhD,aAAalQ,OAAOkQ,WAAW;wBACjC;oBACF;iBACD,GACD,EAAE;mBACFtO,eACA;oBACE;wBACE5D,SAAS;4BACPe,UAAU;gCACR3C,SAASV,QAAQsC,OAAO,CAAC;4BAC3B;wBACF;oBACF;iBACD,GACDyD,WACE;oBACE;wBACEzD,SAAS;4BACPe,UACEiB,OAAOwC,YAAY,CAAC2Q,qBAAqB,KAAK,QAC1C;gCACEC,QAAQ;gCACRC,QAAQ;gCACRC,WAAW;gCACX5F,QAAQ;gCACR6F,QAAQ;gCACRC,MAAM;gCACNC,OAAO;gCACPC,IAAI;gCACJ1Q,MAAM;gCACN2Q,UAAU;gCACVvX,SAAS;gCACTwX,aAAa;gCACbC,QAAQ;gCACRC,gBAAgB;gCAChBC,KAAK;gCACLC,QAAQ;gCACRC,KAAK;gCACLC,MAAM;gCACNC,IAAI;gCACJC,MAAM;gCACNC,QAAQ;gCACRC,cAAc;4BAChB,IACA;gCACElB,QAAQ1X,QAAQsC,OAAO,CACrB;gCAEFqV,QAAQ3X,QAAQsC,OAAO,CACrB;gCAEFsV,WAAW5X,QAAQsC,OAAO,CACxB;gCAEF0P,QAAQhS,QAAQsC,OAAO,CACrB;gCAEFuV,QAAQ7X,QAAQsC,OAAO,CACrB;gCAEFwV,MAAM9X,QAAQsC,OAAO,CACnB;gCAEFyV,OAAO/X,QAAQsC,OAAO,CACpB;gCAEF0V,IAAIhY,QAAQsC,OAAO,CACjB;gCAEFgF,MAAMtH,QAAQsC,OAAO,CACnB;gCAEF2V,UAAUjY,QAAQsC,OAAO,CACvB;gCAEF5B,SAASV,QAAQsC,OAAO,CAAC;gCACzB,4BAA4B;gCAC5B4V,aAAalY,QAAQsC,OAAO,CAC1B;gCAEF6V,QAAQnY,QAAQsC,OAAO,CACrB;gCAEF8V,gBAAgBpY,QAAQsC,OAAO,CAC7B;gCAEF+V,KAAKrY,QAAQsC,OAAO,CAAC;gCACrBgW,QAAQtY,QAAQsC,OAAO,CACrB;gCAEFiW,KAAKvY,QAAQsC,OAAO,CAClB;gCAEF,4BAA4B;gCAC5B,+BAA+B;gCAC/BkW,MAAMxY,QAAQsC,OAAO,CAAC;gCACtBmW,IAAIzY,QAAQsC,OAAO,CACjB;gCAEFoW,MAAM1Y,QAAQsC,OAAO,CACnB;gCAEFqW,QAAQ3Y,QAAQsC,OAAO,CACrB;gCAEFsW,cAAc5Y,QAAQsC,OAAO,CAC3B;4BAEJ;wBACR;oBACF;iBACD,GACD,EAAE;gBACR;oBACE,oEAAoE;oBACpE,6BAA6B;oBAC7ByG,MAAM;oBACN8P,aAAa;gBACf;gBACA,0EAA0E;gBAC1E,yEAAyE;gBACzE,4GAA4G;gBAC5G,gEAAgE;gBAChE,0DAA0D;gBAC1D,iFAAiF;gBACjF,iFAAiF;gBACjF;oBACE9P,MAAM;oBACN8P,aAAa;gBACf;gBACA;oBACE,uEAAuE;oBACvE,uEAAuE;oBACvE,mDAAmD;oBACnD,iEAAiE;oBACjE,mEAAmE;oBACnE,qEAAqE;oBACrE,4DAA4D;oBAC5D9P,MAAM;oBACNpG,KAAK,CAAC,EAAEsT,aAAa,EAA6B;4BAE9CA;wBADF,MAAM6C,QAAQ,AACZ7C,CAAAA,EAAAA,uBAAAA,cAAc9E,KAAK,CAAC,uCAApB8E,oBAAwC,CAAC,EAAE,KAAI,EAAC,EAChDpV,KAAK,CAAC;wBAER,OAAO;4BACL;gCACEqJ,QAAQ;gCACRC,SAAS;oCACP2O;oCACAtO,aAAalD,aAAI,CAACC,IAAI,CACpBlD,KACAC,CAAAA,0BAAAA,OAAQ+C,OAAO,KAAI,SACnB,SACA;gCAEJ;gCACA,gEAAgE;gCAChE,2DAA2D;gCAC3D,gBAAgB;gCAChB0R,OAAO,wBAAwB9C;4BACjC;yBACD;oBACH;gBACF;gBACA;oBACE3T,SAAS;wBACP8B,OAAO;4BACL4U,MAAMC,+BAAiB;wBACzB;oBACF;gBACF;aACD;QACH;QACA7L,SAAS;YACPhH,gBACE,IAAI8S,gBAAO,CAACC,6BAA6B,CACvC,6BACA,SAAUzG,QAAQ;gBAChB,MAAM0G,aAAa9R,aAAI,CAAC+R,QAAQ,CAC9B3G,SAAS7C,OAAO,EAChB;gBAEF,MAAM2C,QAAQE,SAAS5C,WAAW,CAACE,WAAW;gBAC9C,IAAIsJ;gBAEJ,OAAQ9G;oBACN,KAAK3H,yBAAc,CAACI,mBAAmB;oBACvC,KAAKJ,yBAAc,CAACC,qBAAqB;oBACzC,KAAKD,yBAAc,CAACM,eAAe;oBACnC,KAAKN,yBAAc,CAAC0O,aAAa;wBAC/BD,UAAU;wBACV;oBACF;wBACEA,UAAU;gBACd;gBACA5G,SAAS7C,OAAO,GAAG,CAAC,+BAA+B,EAAEyJ,QAAQ,mBAAmB,EAAEF,YAAY;YAChG;YAEJ7U,OAAO,IAAIiV,gDAAuB,CAAC;gBAAEC,gBAAgB;YAAE;YACvDlV,OAAOwB,YAAY,IAAI2T,kCAAyB,CAACR,gBAAO;YACxD,6GAA6G;YAC5GnT,CAAAA,YAAYG,YAAW,KACtB,IAAIgT,gBAAO,CAACS,aAAa,CAAC;gBACxB,0CAA0C;gBAC1CC,QAAQ;oBAAC5Z,QAAQsC,OAAO,CAAC;oBAAW;iBAAS;gBAC7C,sDAAsD;gBACtD,GAAIyD,YAAY;oBAAErF,SAAS;wBAACV,QAAQsC,OAAO,CAAC;qBAAW;gBAAC,CAAC;YAC3D;YACFuX,IAAAA,mCAAkB,EAAC;gBACjBC,aAAa;gBACbxV;gBACAC;gBACA8C;gBACAzB;gBACAW;gBACAR;gBACAG;gBACAI;gBACAF;gBACAV;YACF;YACAK,YACE,IAAIgU,wCAAmB,CAAC;gBACtBzH,UAAU0H,mCAAuB;gBACjC7U;gBACAM;gBACAwU,cAAc,CAAC,OAAO,EAAEC,8CAAkC,CAAC,GAAG,CAAC;gBAC/D3V;YACF;YACDwB,CAAAA,YAAYG,YAAW,KAAM,IAAIiU,wCAAc;YAChD/T,gBACE,CAAC7B,OACD,IAAKvE,CAAAA,QAAQ,kDAAiD,EAC3Doa,sBAAsB,CACvB;gBACE/P,SAAShG;gBACToB,QAAQA;gBACRN,UAAUA;gBACVkV,cAAc/V,OAAOwC,YAAY,CAACuT,YAAY;gBAC9CC,uBAAuBhW,OAAOgW,qBAAqB;gBACnDC,eAAe5T;gBACf8H;gBACA+L,cAAc,EAAE;gBAChBxV;gBACAyV,iBAAiBrP;YACnB;YAEJ,4EAA4E;YAC5E,yEAAyE;YACzE,0EAA0E;YAC1E,kEAAkE;YAClE9G,OAAOoW,2BAA2B,IAChC,IAAIxB,gBAAO,CAACyB,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;eACEtW,MACA,AAAC,CAAA;gBACC,0FAA0F;gBAC1F,qGAAqG;gBACrG,MAAM,EAAEuW,6BAA6B,EAAE,GACrC9a,QAAQ;gBACV,MAAM+a,aAAoB;oBACxB,IAAID,8BAA8B;wBAChCnQ,kBAAkBhE;oBACpB;iBACD;gBAED,IAAIZ,YAAYG,cAAc;oBAC5B6U,WAAWrS,IAAI,CAAC,IAAIwQ,gBAAO,CAAC8B,0BAA0B;gBACxD;gBAEA,OAAOD;YACT,CAAA,MACA,EAAE;YACN,CAACxW,OACC,IAAI2U,gBAAO,CAACyB,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;YACFvU,2BACE,IAAI2U,4BAAmB,CAAC;gBACtB1W;gBACAgW,eAAe5T;gBACfuU,eAAehV;gBACfmB,SAAS,CAAC9C,MAAM8C,UAAUM;YAC5B;YACF,iEAAiE;YACjE,wDAAwD;YACxDzB,gBACE,IAAIiV,yBAAgB,CAAC;gBACnB5W;gBACA6W,YAAY,CAAC7W,OAAO,CAAC,GAACD,2BAAAA,OAAOwC,YAAY,CAACuU,GAAG,qBAAvB/W,yBAAyBgX,SAAS;gBACxDjW;gBACAkW,kBAAkB;oBAChBC,iBAAiB1W;oBACjB2W,oCAAoC1W;oBACpC,GAAGc,gBAAgB;gBACrB;YACF;YACFE,YACE,IAAI2V,4BAAmB,CAAC;gBACtB5W;gBACAO;gBACAH;gBACAqV,eAAe5T;gBACfhB;YACF;YACF,IAAIgW,gCAAe,CAAC;gBAAEnW;gBAAgB6E,SAAShG;YAAI;YACnD,IAAIuX,4CAAqB;YACzB7V,YACE,IAAI8V,8BAAc,CAAC;gBACjB,yDAAyD;gBACzDC,UAAU9b,QAAQsC,OAAO,CAAC;gBAC1ByZ,UAAUrb,QAAQC,GAAG,CAACqb,cAAc;gBACpC7N,MAAM,CAAC,uBAAuB,EAAE5J,MAAM,KAAK,UAAU,GAAG,CAAC;gBACzDkP,UAAU;gBACVzL,MAAM;oBACJ,CAACiU,wDAA4C,CAAC,EAAE;oBAChD,gCAAgC;oBAChCC,WAAW;gBACb;YACF;YACFvV,aAAaZ,YAAY,IAAIoW,8CAAsB,CAAC;gBAAE5X;YAAI;YAC1DoC,aACGZ,CAAAA,WACG,IAAIqW,mDAA6B,CAAC;gBAChC7X;gBACAkB;gBACA4W,uBAAuB,CAAC,CAAC/X,OAAOwC,YAAY,CAACwV,SAAS;YACxD,KACA,IAAIC,gDAAuB,CAAC;gBAC1B9W;gBACAlB;gBACA2B;gBACAnB;YACF,EAAC;YACP4B,aACE,CAACZ,YACD,IAAIyW,gCAAe,CAAC;gBAClBnY;gBACAgD,SAAS/C,OAAO+C,OAAO;gBACvB5B;gBACAlB;gBACA2B;gBACAkG,gBAAgB9H,OAAO8H,cAAc;gBACrCrF,aAAaF;gBACb4V,iBAAiBnY,OAAOwC,YAAY,CAAC4V,SAAS;gBAC9CpX;gBACAC;YACF;YACF,CAAChB,OACCwB,YACA,CAAC,GAACzB,4BAAAA,OAAOwC,YAAY,CAACuU,GAAG,qBAAvB/W,0BAAyBgX,SAAS,KACpC,IAAIqB,sDAA0B,CAACrY,OAAOwC,YAAY,CAACuU,GAAG,CAACC,SAAS;YAClEvV,YACE,IAAI6W,8CAAsB,CAAC;gBACzBnX;YACF;YACF,CAAClB,OACCwB,YACAzB,OAAOwC,YAAY,CAAC+V,WAAW,IAC/B,IAAIC,oCAAiB,CAACxY,OAAOwC,YAAY,CAAC+V,WAAW,KAAK;YAC5D,CAACtY,OACCwB,YACA,IAAK/F,CAAAA,QAAQ,qCAAoC,EAAE+c,eAAe,CAChE,IAAIC,IACF;gBACE;oBAAC;oBAAaxV;iBAAa;gBAC3B;oBAAC;oBAAY,CAAC,GAAClD,mBAAAA,OAAOqE,QAAQ,qBAAfrE,iBAAiB2Y,KAAK;iBAAC;gBACtC;oBAAC;oBAAuB,CAAC,GAAC3Y,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiB4Y,gBAAgB;iBAAC;gBAC5D;oBACE;oBACA,CAAC,GAAC5Y,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiB6Y,qBAAqB;iBACzC;gBACD;oBACE;oBACA,CAAC,EAAC3Y,6BAAAA,4BAAAA,SAAU4Y,eAAe,qBAAzB5Y,0BAA2B6Y,sBAAsB;iBACpD;gBACD;oBAAC;oBAAoB,CAAC,GAAC/Y,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiBgZ,aAAa;iBAAC;gBACtD;oBAAC;oBAAmB,CAAC,EAAC9Y,6BAAAA,6BAAAA,SAAU4Y,eAAe,qBAAzB5Y,2BAA2B+Y,eAAe;iBAAC;gBACjE;oBAAC;oBAAc,CAAC,GAACjZ,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiBkZ,OAAO;iBAAC;gBAC1C;oBAAC;oBAAqB,CAAC,CAAClZ,OAAO+D,iBAAiB;iBAAC;gBACjD;oBACE;oBACA,CAAC,CAAC/D,OAAOmZ,0BAA0B;iBACpC;gBACD;oBAAC;oBAA6B,CAAC,CAACnZ,OAAOoZ,yBAAyB;iBAAC;gBACjE;oBAAC;oBAAqB,CAAC,CAACpZ,OAAOqZ,iBAAiB;iBAAC;gBACjD,+EAA+E;gBAC/E;oBAAC;oBAAgBrZ,OAAOwC,YAAY,CAACuT,YAAY,KAAK;iBAAK;gBAC3D3S;aACD,CAAC3G,MAAM,CAAqByK;SAGpC,CAACzK,MAAM,CAACyK;IACX;IAEA,wCAAwC;IACxC,mEAAmE;IACnE,IAAI/G,mBAAmB,CAACA,gBAAgBmZ,UAAU,EAAE;YAClDzb,gCAAAA;SAAAA,0BAAAA,cAAcG,OAAO,sBAArBH,iCAAAA,wBAAuBiB,OAAO,qBAA9BjB,+BAAgCuG,IAAI,CAACjE,gBAAgBoZ,OAAO;IAC9D;KAIA1b,yBAAAA,cAAcG,OAAO,sBAArBH,iCAAAA,uBAAuBiL,OAAO,qBAA9BjL,+BAAgC2b,OAAO,CACrC,IAAIC,wCAAmB,CACrBvZ,CAAAA,6BAAAA,6BAAAA,SAAU4Y,eAAe,qBAAzB5Y,2BAA2BqJ,KAAK,KAAI,CAAC,GACrCpJ;IAIJ,MAAMqB,iBAAiB3D;IAEvB,IAAI+D,cAAc;YAChBJ,8BAAAA,wBAMAA,+BAAAA,yBAMAA,+BAAAA;SAZAA,yBAAAA,eAAevE,MAAM,sBAArBuE,+BAAAA,uBAAuBvD,KAAK,qBAA5BuD,6BAA8BgY,OAAO,CAAC;YACpC/U,MAAM;YACNmB,QAAQ;YACR1I,MAAM;YACNyU,eAAe;QACjB;SACAnQ,0BAAAA,eAAevE,MAAM,sBAArBuE,gCAAAA,wBAAuBvD,KAAK,qBAA5BuD,8BAA8BgY,OAAO,CAAC;YACpC1G,YAAY;YACZlN,QAAQ;YACR1I,MAAM;YACNgR,OAAO3H,yBAAc,CAACmT,SAAS;QACjC;SACAlY,0BAAAA,eAAevE,MAAM,sBAArBuE,gCAAAA,wBAAuBvD,KAAK,qBAA5BuD,8BAA8BgY,OAAO,CAAC;YACpC9N,aAAanF,yBAAc,CAACmT,SAAS;YACrCxc,MAAM;QACR;IACF;IAEAsE,eAAemY,WAAW,GAAG;QAC3BC,QAAQ;QACRC,iBAAiB;QACjBC,WAAWxb,MAAMC,OAAO,CAACyB,OAAOwC,YAAY,CAACuX,UAAU,IACnD;YACEC,aAAaha,OAAOwC,YAAY,CAACuX,UAAU;YAC3CE,eAAejX,aAAI,CAACC,IAAI,CAAClD,KAAK;YAC9Bma,kBAAkBlX,aAAI,CAACC,IAAI,CAAClD,KAAK;QACnC,IACAC,OAAOwC,YAAY,CAACuX,UAAU,GAC5B;YACEE,eAAejX,aAAI,CAACC,IAAI,CAAClD,KAAK;YAC9Bma,kBAAkBlX,aAAI,CAACC,IAAI,CAAClD,KAAK;YACjC,GAAGC,OAAOwC,YAAY,CAACuX,UAAU;QACnC,IACA1W;IACR;IAEA7B,eAAevE,MAAM,CAAEsV,MAAM,GAAG;QAC9B4H,YAAY;YACV3H,KAAK;QACP;IACF;IACAhR,eAAevE,MAAM,CAAEmd,SAAS,GAAG;QACjCC,OAAO;YACLrM,UAAU;QACZ;IACF;IAEA,IAAI,CAACxM,eAAewO,MAAM,EAAE;QAC1BxO,eAAewO,MAAM,GAAG,CAAC;IAC3B;IACA,IAAIvO,UAAU;QACZD,eAAewO,MAAM,CAACsK,YAAY,GAAG;IACvC;IAEA,IAAI7Y,YAAYG,cAAc;QAC5BJ,eAAewO,MAAM,CAACuK,mBAAmB,GAAG;YAAC;SAAS;IACxD;IAEA,iDAAiD;IACjD,wDAAwD;IACxD,oDAAoD;IACpD/Y,eAAegZ,QAAQ,GAAG,CAAC;IAC3B,IAAIpe,QAAQqe,QAAQ,CAACC,GAAG,KAAK,KAAK;QAChClZ,eAAegZ,QAAQ,CAACG,YAAY,GAAG;YACrC;SACD;IACH,OAAO;QACLnZ,eAAegZ,QAAQ,CAACG,YAAY,GAAG;YAAC;SAA+B;IACzE;IACA,IAAIve,QAAQqe,QAAQ,CAACC,GAAG,KAAK,KAAK;QAChClZ,eAAegZ,QAAQ,CAACI,cAAc,GAAG;YACvC;SACD;IACH;IAEA,IAAI3a,KAAK;QACP,IAAI,CAACuB,eAAe6K,YAAY,EAAE;YAChC7K,eAAe6K,YAAY,GAAG,CAAC;QACjC;QAEA,2EAA2E;QAC3E,2CAA2C;QAC3C,IAAI,CAAChK,WAAW;YACdb,eAAe6K,YAAY,CAACwO,eAAe,GAAG;QAChD;QACArZ,eAAe6K,YAAY,CAACyO,WAAW,GAAG;IAC5C;IAEA,MAAMC,aAAaC,KAAKC,SAAS,CAAC;QAChC/W,sBAAsB,EAAElE,2BAAAA,wBAAAA,OAAQwC,YAAY,qBAApBxC,sBAAsBkE,sBAAsB;QACpE8F,aAAahK,OAAOgK,WAAW;QAC/BlC,gBAAgBA;QAChBoT,eAAelb,OAAOkb,aAAa;QACnCC,eAAenb,OAAOob,aAAa,CAACD,aAAa;QACjDE,uBAAuBrb,OAAOob,aAAa,CAACC,qBAAqB;QACjEC,6BAA6B,CAAC,CAACtb,OAAOsb,2BAA2B;QACjEC,iBAAiBvb,OAAOub,eAAe;QACvCC,aAAaxb,OAAOwC,YAAY,CAACgZ,WAAW;QAC5CC,mBAAmBzb,OAAOwC,YAAY,CAACiZ,iBAAiB;QACxDC,mBAAmB1b,OAAOwC,YAAY,CAACkZ,iBAAiB;QACxDjZ,aAAazC,OAAOwC,YAAY,CAACC,WAAW;QAC5CyQ,UAAUlT,OAAOkT,QAAQ;QACzBkD,6BAA6BpW,OAAOoW,2BAA2B;QAC/DlG,aAAalQ,OAAOkQ,WAAW;QAC/B5N;QACAsU,eAAehV;QACfd;QACA8T,SAAS,CAAC,CAAC5U,OAAO4U,OAAO;QACzB3S;QACA0Z,WAAWzY;QACX8V,aAAa,GAAEhZ,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiBgZ,aAAa;QAC7CH,qBAAqB,GAAE7Y,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiB6Y,qBAAqB;QAC7DD,gBAAgB,GAAE5Y,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiB4Y,gBAAgB;QACnDD,KAAK,GAAE3Y,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiB2Y,KAAK;QAC7BO,OAAO,GAAElZ,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiBkZ,OAAO;QACjCG,mBAAmBrZ,OAAOqZ,iBAAiB;QAC3CuC,iBAAiB5b,OAAO0S,MAAM,CAACmJ,UAAU;QACzCC,qBAAqB9b,OAAOwC,YAAY,CAACsZ,mBAAmB;QAC5DC,kBAAkB/b,OAAOwC,YAAY,CAACuZ,gBAAgB;QACtD5V,yBAAyB1F;IAC3B;IAEA,MAAMub,QAAa;QACjB9e,MAAM;QACN,mFAAmF;QACnF+e,sBAAsBhc,MAAM,IAAIic;QAChC,YAAY;QACZ,kHAAkH;QAClH,qBAAqB;QACrB,iDAAiD;QACjDpgB,SAAS,GAAGqV,UAAU,CAAC,EAAE/U,QAAQC,GAAG,CAACqb,cAAc,CAAC,CAAC,EAAEqD,YAAY;QACnEoB,gBAAgBnZ,aAAI,CAACC,IAAI,CAACF,SAAS,SAAS;QAC5C,gIAAgI;QAChI,8GAA8G;QAC9G,yGAAyG;QACzG,kEAAkE;QAClEqZ,aAAanc,MAAM,SAAS;IAC9B;IAEA,oFAAoF;IACpF,IAAID,OAAO4U,OAAO,IAAI5U,OAAOqc,UAAU,EAAE;QACvCL,MAAMM,iBAAiB,GAAG;YACxBtc,QAAQ;gBAACA,OAAOqc,UAAU;aAAC;YAC3B,uGAAuG;YACvGE,gBAAgB,EAAE;QACpB;IACF,OAAO;QACLP,MAAMM,iBAAiB,GAAG;YACxB,uGAAuG;YACvGC,gBAAgB,EAAE;QACpB;IACF;KACA/a,0BAAAA,eAAesH,OAAO,qBAAtBtH,wBAAwB4C,IAAI,CAAC,CAACC;QAC5BA,SAASmY,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,2BAA2B,CAACC;YAClD,MAAML,oBAAoBK,MAAMC,WAAW,CAACN,iBAAiB;YAC7D,MAAMO,cAAc7Z,aAAI,CAAC8Z,OAAO,CAACphB,QAAQsC,OAAO,CAAC;YACjD,sFAAsF;YACtF,2EAA2E;YAC3E,KAAK,MAAM+e,OAAOT,kBAAmB;gBACnC,IAAIS,IAAIzO,UAAU,CAACuO,cAAc;oBAC/BP,kBAAkBU,MAAM,CAACD;gBAC3B;YACF;QACF;IACF;IAEAvb,eAAewa,KAAK,GAAGA;IAEvB,IAAI5f,QAAQC,GAAG,CAAC4gB,oBAAoB,EAAE;QACpC,MAAMC,QAAQ9gB,QAAQC,GAAG,CAAC4gB,oBAAoB,CAAC9Y,QAAQ,CAAC;QACxD,MAAMgZ,gBACJ/gB,QAAQC,GAAG,CAAC4gB,oBAAoB,CAAC9Y,QAAQ,CAAC;QAC5C,MAAMiZ,gBACJhhB,QAAQC,GAAG,CAAC4gB,oBAAoB,CAAC9Y,QAAQ,CAAC;QAC5C,MAAMkZ,gBACJjhB,QAAQC,GAAG,CAAC4gB,oBAAoB,CAAC9Y,QAAQ,CAAC;QAC5C,MAAMmZ,gBACJlhB,QAAQC,GAAG,CAAC4gB,oBAAoB,CAAC9Y,QAAQ,CAAC;QAE5C,MAAMoZ,UACJ,AAACJ,iBAAiB1b,YAAc2b,iBAAiBpb;QACnD,MAAMwb,UACJ,AAACH,iBAAiB5b,YAAc6b,iBAAiBtb;QAEnD,MAAMyb,aAAa,CAACP,SAAS,CAACK,WAAW,CAACC;QAE1C,IAAIC,cAAcP,OAAO;YACvB1b,eAAekc,qBAAqB,GAAG;gBACrCC,OAAO;gBACPC,OAAO;YACT;QACF;QAEA,IAAIH,cAAcF,SAAS;YACzB/b,eAAesH,OAAO,CAAE1E,IAAI,CAAC,CAACC;gBAC5BA,SAASmY,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/Crf,QAAQugB,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbC,QAAQ;wBACRC,SAASP,aAAa,QAAQ;oBAChC;gBAEJ;YACF;QACF,OAAO,IAAID,SAAS;YAClBhc,eAAesH,OAAO,CAAE1E,IAAI,CAAC,CAACC;gBAC5BA,SAASmY,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/Crf,QAAQugB,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbG,QAAQ;wBACRF,QAAQ;wBACRG,SAAS;oBACX;gBAEJ;YACF;QACF;QAEA,IAAIX,SAAS;YACX,MAAMY,iBACJvJ,gBAAO,CAACuJ,cAAc;YACxB3c,eAAesH,OAAO,CAAE1E,IAAI,CAC1B,IAAI+Z,eAAe;gBACjBZ,SAAS;YACX;YAEF/b,eAAe+b,OAAO,GAAG;QAC3B;IACF;IAEA1f,gBAAgB,MAAMugB,IAAAA,0BAAkB,EAACvgB,eAAe;QACtDwC;QACAge,eAAete;QACfue,eAAezd,WACX,IAAIwJ,OAAOkU,IAAAA,gCAAkB,EAACvb,aAAI,CAACC,IAAI,CAACpC,UAAU,CAAC,IAAI,CAAC,MACxDwC;QACJhB;QACAmc,eAAeve;QACf6F,UAAU9D;QACV4U,eAAehV;QACf6c,WAAWhd,YAAYG;QACvBsO,aAAalQ,OAAOkQ,WAAW,IAAI;QACnCwO,aAAa1e,OAAO0e,WAAW;QAC/BpD,6BAA6Btb,OAAOsb,2BAA2B;QAC/DqD,QAAQ3e,OAAO2e,MAAM;QACrBnc,cAAcxC,OAAOwC,YAAY;QACjCmQ,qBAAqB3S,OAAO0S,MAAM,CAACC,mBAAmB;QACtD5O,mBAAmB/D,OAAO+D,iBAAiB;QAC3CgY,kBAAkB/b,OAAOwC,YAAY,CAACuZ,gBAAgB;IACxD;IAEA,0BAA0B;IAC1Ble,cAAcme,KAAK,CAACnS,IAAI,GAAG,GAAGhM,cAAcgM,IAAI,CAAC,CAAC,EAAEhM,cAAc+gB,IAAI,GACpEhe,gBAAgB,cAAc,IAC9B;IAEF,IAAIX,KAAK;QACP,IAAIpC,cAAcZ,MAAM,EAAE;YACxBY,cAAcZ,MAAM,CAAC4hB,WAAW,GAAG,CAAC5hB,UAClC,CAACsD,mBAAmBkE,IAAI,CAACxH,QAAOmR,QAAQ;QAC5C,OAAO;YACLvQ,cAAcZ,MAAM,GAAG;gBACrB4hB,aAAa,CAAC5hB,UAAgB,CAACsD,mBAAmBkE,IAAI,CAACxH,QAAOmR,QAAQ;YACxE;QACF;IACF;IAEA,IAAI0Q,kBAAkBjhB,cAAcR,OAAO;IAC3C,IAAI,OAAO2C,OAAO4U,OAAO,KAAK,YAAY;YAiCpCpT,6BAKKA;QArCT3D,gBAAgBmC,OAAO4U,OAAO,CAAC/W,eAAe;YAC5CkC;YACAE;YACA6F,UAAU9D;YACVxB;YACAR;YACA+G;YACAgY,YAAYniB,OAAOkN,IAAI,CAACnJ,aAAawB,MAAM;YAC3CyS,SAAAA,gBAAO;YACP,GAAI5S,0BACA;gBACEgd,aAAapd,eAAe,SAAS;YACvC,IACA,CAAC,CAAC;QACR;QAEA,IAAI,CAAC/D,eAAe;YAClB,MAAM,IAAI9B,MACR,CAAC,6GAA6G,EAAEiE,OAAOif,cAAc,CAAC,GAAG,CAAC,GACxI;QAEN;QAEA,IAAIhf,OAAO6e,oBAAoBjhB,cAAcR,OAAO,EAAE;YACpDQ,cAAcR,OAAO,GAAGyhB;YACxB3hB,qBAAqB2hB;QACvB;QAEA,wDAAwD;QACxD,MAAMtd,iBAAiB3D;QAEvB,0EAA0E;QAC1E,IAAI2D,EAAAA,8BAAAA,eAAemY,WAAW,qBAA1BnY,4BAA4B0d,eAAe,MAAK,MAAM;YACxD1d,eAAemY,WAAW,CAACuF,eAAe,GAAG;gBAC3CC,SAAS;YACX;QACF,OAAO,IACL,SAAO3d,+BAAAA,eAAemY,WAAW,qBAA1BnY,6BAA4B0d,eAAe,MAAK,YACvD1d,eAAemY,WAAW,CAACuF,eAAe,CAACC,OAAO,KAAK,OACvD;YACA3d,eAAemY,WAAW,CAACuF,eAAe,CAACC,OAAO,GAAG;QACvD;QAEA,IAAI,OAAO,AAACthB,cAAsBuhB,IAAI,KAAK,YAAY;YACrD9hB,QAAQC,IAAI,CACV;QAEJ;IACF;IACA,MAAMU,QAAQJ,EAAAA,wBAAAA,cAAcZ,MAAM,qBAApBY,sBAAsBI,KAAK,KAAI,EAAE;IAE/C,MAAMohB,gBAAgBphB,MAAMqhB,IAAI,CAC9B,CAACnhB,OACC,AAACA,QACC,OAAOA,SAAS,YAChBA,KAAKyH,MAAM,KAAK,uBAChB,UAAUzH,QACVA,KAAKsG,IAAI,YAAY4F,UACrBlM,KAAKsG,IAAI,CAACA,IAAI,CAAC,WACjB;IAGJ,IAAI4a,iBAAiBhd,WAAW;QAC9B,wEAAwE;QACxE,qEAAqE;QACrE,0CAA0C;QAC1CpE,MAAMmG,IAAI,CAAC;YACTK,MAAM4a,cAAc5a,IAAI;YACxB6N,OAAO;gBACL/L,yBAAc,CAACC,qBAAqB;gBACpCD,yBAAc,CAACI,mBAAmB;gBAClCJ,yBAAc,CAACM,eAAe;aAC/B,CAACyD,GAAG,CAAC,CAAC4D,QAAW,CAAA;oBAChBxC,aAAawC;oBACblQ,SAAS;wBACP8B,OAAOsS,IAAAA,uCAAgB,EAAC1P,qBAAqB;4BAC3C5B;4BACAoN;4BACAtM;wBACF;oBACF;gBACF,CAAA;QACF;IACF;IAEA,IAAI,CAAC5B,OAAO0S,MAAM,CAACC,mBAAmB,EAAE;QACtC,MAAM4M,gBAAgBthB,MAAMqhB,IAAI,CAC9B,CAACnhB,OACCA,QAAQ,OAAOA,SAAS,YAAYA,KAAKyH,MAAM,KAAK;QAExD,IAAIyZ,iBAAiBE,iBAAiB,OAAOA,kBAAkB,UAAU;YACvE,uDAAuD;YACvD,wDAAwD;YACxD,8CAA8C;YAC9CA,cAAc9a,IAAI,GAAG;QACvB;IACF;IAEA,IACEzE,OAAOwC,YAAY,CAACgd,SAAS,MAC7B3hB,yBAAAA,cAAcZ,MAAM,qBAApBY,uBAAsBI,KAAK,KAC3BJ,cAAciL,OAAO,EACrB;QACA,kEAAkE;QAClE,iEAAiE;QACjE,kJAAkJ;QAClJ,MAAM2W,oBAAoB;YAAC;SAA8B;QACzD,MAAMC,aAAa;YACjB9a,SAAS6a;YACT7M,QAAQ6M;YACRviB,MAAM;QACR;QAEA,MAAMyiB,WAAW,EAAE;QACnB,MAAMC,aAAa,EAAE;QAErB,KAAK,MAAMzhB,QAAQN,cAAcZ,MAAM,CAACgB,KAAK,CAAE;YAC7C,IAAI,CAACE,QAAQ,OAAOA,SAAS,UAAU;YACvC,IAAIA,KAAKH,OAAO,EAAE;gBAChB2hB,SAASvb,IAAI,CAACjG;YAChB,OAAO;gBACL,IACEA,KAAKmU,KAAK,IACV,CAAEnU,CAAAA,KAAKsG,IAAI,IAAItG,KAAKyG,OAAO,IAAIzG,KAAKiQ,QAAQ,IAAIjQ,KAAKyU,MAAM,AAAD,GAC1D;oBACAzU,KAAKmU,KAAK,CAACpU,OAAO,CAAC,CAACO,IAAMmhB,WAAWxb,IAAI,CAAC3F;gBAC5C,OAAO;oBACLmhB,WAAWxb,IAAI,CAACjG;gBAClB;YACF;QACF;QAEAN,cAAcZ,MAAM,CAACgB,KAAK,GAAG;eACvB0hB;YACJ;gBACErN,OAAO;uBAAIsN;oBAAYF;iBAAW;YACpC;SACD;IACH;IAEA,8DAA8D;IAC9D,IAAI,OAAO1f,OAAO6f,oBAAoB,KAAK,YAAY;QACrD,MAAMha,UAAU7F,OAAO6f,oBAAoB,CAAC;YAC1ChQ,cAAchS,cAAcgS,YAAY;QAC1C;QACA,IAAIhK,QAAQgK,YAAY,EAAE;YACxBhS,cAAcgS,YAAY,GAAGhK,QAAQgK,YAAY;QACnD;IACF;IAEA,SAASiQ,YAAY3hB,IAA0C;QAC7D,IAAI,CAACA,MAAM;YACT,OAAO;QACT;QAEA,MAAM4hB,YAAY;YAChB;YACA;YACA;YACA;YACA;SACD;QAED,IAAI5hB,gBAAgBkM,UAAU0V,UAAUvhB,IAAI,CAAC,CAACwhB,QAAU7hB,KAAKsG,IAAI,CAACub,SAAS;YACzE,OAAO;QACT;QAEA,IAAI,OAAO7hB,SAAS,YAAY;YAC9B,IACE4hB,UAAUvhB,IAAI,CAAC,CAACwhB;gBACd,IAAI;oBACF,IAAI7hB,KAAK6hB,QAAQ;wBACf,OAAO;oBACT;gBACF,EAAE,OAAM,CAAC;gBACT,OAAO;YACT,IACA;gBACA,OAAO;YACT;QACF;QAEA,IAAI1hB,MAAMC,OAAO,CAACJ,SAASA,KAAKK,IAAI,CAACshB,cAAc;YACjD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAMG,mBACJpiB,EAAAA,yBAAAA,cAAcZ,MAAM,sBAApBY,8BAAAA,uBAAsBI,KAAK,qBAA3BJ,4BAA6BW,IAAI,CAC/B,CAACL,OAAc2hB,YAAY3hB,KAAKsG,IAAI,KAAKqb,YAAY3hB,KAAKwG,OAAO,OAC9D;IAEP,IAAIsb,kBAAkB;YAYhBpiB,8BAAAA,wBAWAA,wBAMAA,uCAAAA;QA5BJ,kCAAkC;QAClC,IAAImE,yBAAyB;YAC3B1E,QAAQC,IAAI,CACVC,IAAAA,kBAAM,EAACC,IAAAA,gBAAI,EAAC,gBACVA,IAAAA,gBAAI,EACF,8FAEF;QAEN;QAEA,KAAII,yBAAAA,cAAcZ,MAAM,sBAApBY,+BAAAA,uBAAsBI,KAAK,qBAA3BJ,6BAA6BsE,MAAM,EAAE;YACvC,6BAA6B;YAC7BtE,cAAcZ,MAAM,CAACgB,KAAK,CAACC,OAAO,CAAC,CAACO;gBAClC,IAAI,CAACA,KAAK,OAAOA,MAAM,UAAU;gBACjC,IAAIH,MAAMC,OAAO,CAACE,EAAE6T,KAAK,GAAG;oBAC1B7T,EAAE6T,KAAK,GAAG7T,EAAE6T,KAAK,CAAC7V,MAAM,CACtB,CAACyjB,IAAM,AAACA,CAAS,CAACC,OAAOC,GAAG,CAAC,qBAAqB,KAAK;gBAE3D;YACF;QACF;QACA,KAAIviB,yBAAAA,cAAciL,OAAO,qBAArBjL,uBAAuBsE,MAAM,EAAE;YACjC,gCAAgC;YAChCtE,cAAciL,OAAO,GAAGjL,cAAciL,OAAO,CAACrM,MAAM,CAClD,CAACC,IAAM,AAACA,EAAU2jB,iBAAiB,KAAK;QAE5C;QACA,KAAIxiB,8BAAAA,cAAcwO,YAAY,sBAA1BxO,wCAAAA,4BAA4BwR,SAAS,qBAArCxR,sCAAuCsE,MAAM,EAAE;YACjD,uBAAuB;YACvBtE,cAAcwO,YAAY,CAACgD,SAAS,GAClCxR,cAAcwO,YAAY,CAACgD,SAAS,CAAC5S,MAAM,CACzC,CAAC6jB,IAAM,AAACA,EAAUD,iBAAiB,KAAK;QAE9C;IACF;IAEA,yEAAyE;IACzE,IAAIpgB,OAAOwB,UAAU;QACnBtG,mBAAmB0C,eAAekJ,eAAeC,KAAK;IACxD;IAEA,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAC5C,0BAA0B;IAC1B,MAAMuZ,gBAAqB1iB,cAAc+R,KAAK;IAC9C,IAAI,OAAO2Q,kBAAkB,aAAa;QACxC,MAAMC,eAAe;YACnB,MAAM5Q,QACJ,OAAO2Q,kBAAkB,aACrB,MAAMA,kBACNA;YACN,0CAA0C;YAC1C,IACEpY,iBACA7J,MAAMC,OAAO,CAACqR,KAAK,CAAC,UAAU,KAC9BA,KAAK,CAAC,UAAU,CAACzN,MAAM,GAAG,GAC1B;gBACA,MAAMse,eAAetY,aAAa,CAChCK,4CAAgC,CACjC;gBACDoH,KAAK,CAACpH,4CAAgC,CAAC,GAAG;uBACrCoH,KAAK,CAAC,UAAU;oBACnB6Q;iBACD;YACH;YACA,OAAO7Q,KAAK,CAAC,UAAU;YAEvB,KAAK,MAAM/F,QAAQjN,OAAOkN,IAAI,CAAC8F,OAAQ;gBACrCA,KAAK,CAAC/F,KAAK,GAAG6W,IAAAA,2BAAkB,EAAC;oBAC/BC,OAAO/Q,KAAK,CAAC/F,KAAK;oBAClBnJ;oBACAmJ;oBACAxH;gBACF;YACF;YAEA,OAAOuN;QACT;QACA,sCAAsC;QACtC/R,cAAc+R,KAAK,GAAG4Q;IACxB;IAEA,IAAI,CAACvgB,OAAO,OAAOpC,cAAc+R,KAAK,KAAK,YAAY;QACrD,6BAA6B;QAC7B/R,cAAc+R,KAAK,GAAG,MAAM/R,cAAc+R,KAAK;IACjD;IAEA,OAAO/R;AACT"}