{"version": 3, "file": "app/page.js", "mappings": "qFAAA,6DCAA,mHEAA,0nCDWA,MACA,CACA,GACA,CACA,uBAAiC,EACjC,MAZA,IAAoB,qCAA2F,CAY/G,0DACA,UACA,sBAAoC,uCAAqP,aACzR,SACA,aACA,WACA,eACA,CACA,CAAS,EACF,CACP,CACA,QA3BA,IAAsB,qCAA6F,CA2BnH,4DACA,aA3BA,IAAsB,sCAAgF,CA2BtG,+CACA,WA3BA,IAAsB,sCAAgF,CA2BtG,+CACA,cA3BA,IAAsB,sCAAmF,CA2BzG,kDACA,UACA,sBAAoC,uCAAqP,aACzR,SACA,aACA,WACA,eACA,CACA,EACA,CAEA,6DAKO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAGO,MAAwB,oBAAkB,EACjD,YACA,KAAc,GAAS,UACvB,aACA,aAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,mCCnDD,iCAEA,gCALA,CAEA,CAGA,EAWA,gCACA,wBACA,oCACA,kDACA,gCAEA,+BACA,oDACA,MACI,QAA8B,EAClC,aACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEL,IAAM,EAAS,OAAS,EACxB,UAAe,GAAU,KACzB,IAvBA,GAwBA,aACA,OA9BA,KA+BA,OAAW,GACX,SA/BA,KAgCA,YA/BA,KAgCA,SAnCA,KAoCA,gBACA,YAAgB,KAChB,wBACA,0BACA,wBACA,cAlCA,OAmCA,6BA5BA,OA6BA,OAnCA,CAAoB,MAAQ,SAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,wIAoCnxJ,oCACA,mBACA,wBAtDA,KAuDA,2BACA,CAAC,EACM,EAAqB,EACb,cACf,MAAW,OAAO,EAClB,KACA,IAJmC,YAIX,KACxB,QAAiB,CACjB,CAAK,CACL,iBC1EA,sCAAoJ,CAEpJ,sCAAwJ,CAExJ,oCAAuJ,CAEvJ,sCAAqI,iBCNrI,sCAAoJ,CAEpJ,sCAAwJ,CAExJ,sCAAuJ,CAEvJ,sCAAqI,2ICQ9H,SAASA,EAAW,CAAEC,OAAO,SAAS,CAAmB,EAC9D,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,GAClB,CAAEC,KAAMC,CAAO,QAAEC,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,SAG5C,IAAIC,QAFYF,CAEH,CACJ,UAACG,MAAAA,CAAIC,UAAU,QAGnBL,GAASM,KAUZ,WAACF,MAAAA,CAAIC,UAAU,oCACb,WAACE,EAAAA,CAAIA,CAAAA,CACHC,KAAK,WACLH,UAAU,wEAETL,EAAQM,IAAI,CAACG,KAAK,EACjB,UAACC,EAAAA,CAAKA,CAAAA,CACJC,IAAKX,EAAQM,IAAI,CAACG,KAAK,CACvBG,IAAKZ,EAAQM,IAAI,CAACO,IAAI,EAAI,OAC1BC,MAAO,GACPC,OAAQ,GACRV,UAAU,iBAGd,UAACW,OAAAA,CAAKX,UAAU,mBAAWL,EAAQM,IAAI,CAACO,IAAI,MAE9C,UAACI,EAAAA,CAAMA,CAAAA,CAACC,QAAS,IAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,CAAEC,YAAa,GAAI,GAAIC,QAAQ,UAAUhB,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gBAAiB1B,SAAgB,OAAS,IAAKA,KAAMA,WAAM,UAxBjJ,WAACqB,EAAAA,CAAMA,CAAAA,CAACC,QAAS,IAAMrB,EAAO0B,IAAI,CAAC,UAAWlB,UAAWiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,QAAkB,OAAT1B,EAAgB,OAAS,IAAKA,KAAMA,YACvG,UAAC4B,EAAAA,CAAKA,CAAAA,CAACnB,UAAoB,OAATT,EAAgB,UAAY,YAAa,UA4BnE,iHC3CO,SAAS6B,EAAa,YAAEC,CAAU,CAAqB,EAC5D,IAAM7B,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,UAExB,EAEI,UAFY,CAEXmB,EAAAA,CAAMA,CAAAA,CACLrB,KAAK,KACLsB,QAAS,IAAMrB,EAAO0B,IAAI,CAAC,QAC3BlB,UAAU,iEAEV,UAACsB,EAAAA,CAAIA,CAAAA,CAACtB,UAAU,YAAY,UAM3B,UAACV,EAAAA,UAAUA,CAAAA,CAACC,KAAK,MAC1B,gHCtBO,SAASgC,IACd,GAAM,OAAEC,CAAK,UAAEC,CAAQ,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEpC,MACE,WAACd,EAAAA,CAAMA,CAAAA,CACLI,QAAQ,QACRzB,KAAK,OACLsB,QAAS,IAAMY,EAAmB,UAAVD,EAAoB,OAAS,SACrDxB,UAAU,yBAEV,UAAC2B,EAAAA,CAAGA,CAAAA,CAAC3B,UAAU,2EACf,UAAC4B,EAAAA,CAAIA,CAAAA,CAAC5B,UAAU,mFAChB,UAACW,OAAAA,CAAKX,OL2BA,GK3BU,mBAAU,WAGhC,0ECjBO,SAAS6B,IACd,MACE,WAAC3B,EAAAA,CAAIA,CAAAA,CACHC,KAAK,IACLH,UAAU,wEAEV,UAACD,MAAAA,CAAIC,UAAU,4BACb,UAACD,MAAAA,CAAIC,UAAU,gEACb,WAAC8B,MAAAA,CACCrB,MAAM,KACNC,OAAO,KACPqB,QAAQ,YACRC,KAAK,OACLC,MAAM,6BACNjC,UAAU,yBAGV,UAACkC,OAAAA,CACCC,EAAE,kBACFnC,UAAU,oBAIZ,UAACkC,OAAAA,CACCC,EAAE,iCACFnC,UAAU,iBAIZ,UAACkC,OAAAA,CACCC,EAAE,uEACFnC,UAAU,iBAIZ,UAACkC,OAAAA,CACCC,EAAE,iBACFnC,UAAU,0BACVgC,KAAK,SAIP,UAACE,OAAAA,CACCC,EAAE,mCACFnC,UAAU,oBAIZ,UAACkC,OAAAA,CACCC,EAAE,mCACFnC,UAAU,2BAKlB,UAACW,OAAAA,CAAKX,UAAU,8GAAqG,cAK3H,i7BC5DO,SAASoC,IACd,MACE,UAACC,SAAAA,CAAOrC,UAAU,2FAChB,UAACD,MAAAA,CAAIC,UAAU,yCACb,WAACD,MAAAA,CAAIC,UAAU,qDACb,UAAC6B,EAAAA,IAAIA,CAAAA,CAAAA,GACL,WAAC9B,MAAAA,CAAIC,UAAU,oCACb,UAACuB,EAAAA,WAAWA,CAAAA,CAAAA,GACZ,UAACjC,EAAAA,UAAUA,CAAAA,CAAAA,YAMvB,s6BCVa,MAAc,GACzB,EAAO,CAAP,MAAO,CAAQ,oBAAsB,QAAO,EAAE,WAAY,GA+B/C,EAAe,EAA2C,MACrE,EACG,CAFuB,KAEvB,CAAO,CAAC,EAAW,EAAO,IAEvB,EAAQ,GACP,CACD,IADC,CADgB,CACK,CAArB,GAAqB,IACtB,EAAM,OAAQ,EAAS,IAAM,GAGhC,EAHgC,EAGhC,CAAK,CAAG,IACR,CAAK,MClDV,CAAe,MACb,KAAO,8BACP,KAAO,IACP,MAAQ,IACR,OAAS,aACT,IAAM,QACN,MAAQ,gBACR,WAAa,GACb,aAAe,SACf,cAAgB,QAClB,ECcA,CAAM,KAAO,kBAET,OACE,EAAQ,eACR,IAAO,kBACP,EAAc,sBACd,YACA,EAAY,YACZ,EACA,WACA,GAAG,GAEL,GACG,CACI,qBACL,KACA,KACE,EACA,GAAG,EACH,KAAO,GACP,MAAQ,GACR,MAAQ,GACR,YAAa,EAAuB,EAA4B,CAA5B,MAAO,EAAW,EAAU,KAA5B,EAAmC,CAAI,EAAjB,EAAqB,UACpE,EAAa,SAAU,CAAV,EACxB,GAAG,GAEL,IACK,CAAS,KAAI,CAAC,CAAC,CAAK,EAAK,CAAM,uBAAc,EAAK,KAAK,CAAC,CACvD,KAAM,SAAQ,GAAY,EAAW,CAAC,EAAhB,CAAI,GCzChC,EDyCoD,CCzChC,EAAkB,KAC1C,GADiE,CAC3D,CAAY,EADK,EACL,eAAwC,CAAE,CAAW,YAAG,IAAS,KACjF,mBAAa,CAAC,EAAM,KAClB,WACA,EACA,UAAW,EAAa,YAAsB,GAAS,EAAI,GAAL,CAAT,EAC1C,EACJ,CAFqE,EAOjE,OAFG,cAAc,EAAG,GAAQ,EAE5B,CACT,ECXM,CDQ+B,CCRtB,EAAiB,QAAU,EACxC,CACE,GAF2B,IAG3B,CACE,CAAG,sKACH,GAAK,SACP,EACF,CACD,ECRK,EAAO,EAAiB,MAAQ,EACpC,CAAC,KAD0B,EAClB,CAAE,MAAO,KAAM,CAAQ,WAAM,CAAG,KAAK,EAAG,CAAK,MAAI,CAAK,OAAK,SAAU,EAC9E,CAAC,MAAQ,EAAE,EAAG,CAA6C,+CAAK,SAAU,EAC3E,ECHK,EAAQ,EAAiB,OAAS,EACtC,CAAC,IAD2B,IACjB,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,MAAM,GAAK,UAAU,EACzD,CAAC,UAAY,EAAE,OAAQ,CAAoB,sBAAK,SAAU,EAC3D,gBCVM,SAASgD,EAAY,MAAEC,CAAI,OAAEC,CAAK,aAAEC,CAAW,CAAoB,EACxE,MACE,UAAC1C,MAAAA,CAAIC,UAAU,iFfgEI,oCe/DjB,WAACD,MAAAA,CAAIC,UAAU,oCACb,UAACD,MAAAA,CAAIC,UAAU,qDACZuC,IAEH,WAACxC,MAAAA,CAAIC,UAAU,sBACb,UAAC0C,KAAAA,CAAG1C,UAAU,qBAAawC,IAC3B,UAACG,IAAAA,CAAE3C,UAAU,oDAA4CyC,WAKnE,CCdO,IAAMG,EAAU,OAAM,eAECC,IAC5B,IAAMlD,EAAU,MAAMmD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAE1B,MACE,UAAC/C,MAAAA,CAAIC,UAAU,kGACb,WAACD,MAAAA,CAAIC,UAAU,iEACb,UAACoC,EAAAA,CAAMA,CAAAA,CAAAA,GACP,UAACW,OAAAA,CAAK/C,UAAU,kBACd,WAACD,MAAAA,CAAIC,UAAU,qGACb,UAACD,MAAAA,CAAIC,UAAU,6CAEf,WAACD,MAAAA,CAAIC,UAAU,qDACb,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACgD,KAAAA,CAAGhD,UAAU,qEACZ,UAACW,OAAAA,CAAKX,UAAU,qFAA4E,cAI9F,UAAC2C,IAAAA,CAAE3C,UAAU,6EAAoE,iBAKnF,WAACD,MAAAA,CAAIC,UAAU,8EACb,UAACsC,EAAWA,CACVC,KAAM,GADID,EACJ,KAACW,EAAMA,CAACjD,GAADiD,OAAW,YACxBT,MAAM,OACNC,YAAY,eAEd,UAACH,EAAWA,CACVC,KAAM,GADID,EACJ,KAAChB,EAAIA,CAACtB,CAADsB,SAAW,YACtBkB,MAAM,OACNC,YAAY,aAEd,UAACH,EAAWA,CACVC,KAAM,GADID,EACJ,KAACY,EAAKA,CAAClD,EAADkD,QAAW,YACvBV,MAAM,OACNC,YAAY,cAIhB,UAAC1C,MAAAA,CAAIC,UAAU,oFACb,UAACoB,EAAAA,YAAYA,CAAAA,CAACC,WAAY,CAAC,CAAC1B,kBAQ5C", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/page.tsx?73e4", "webpack://_N_E/|ssr", "webpack://_N_E/?33a1", "webpack://_N_E/?dcca", "webpack://_N_E/./app/components/auth/sign-button.tsx", "webpack://_N_E/./app/components/home/<USER>", "webpack://_N_E/./app/components/theme/theme-toggle.tsx", "webpack://_N_E/./app/components/ui/logo.tsx", "webpack://_N_E/./app/components/layout/header.tsx", "webpack://_N_E/../../../../shared/src/utils.ts", "webpack://_N_E/../../src/defaultAttributes.ts", "webpack://_N_E/../../src/Icon.ts", "webpack://_N_E/../../src/createLucideIcon.ts", "webpack://_N_E/../../../src/icons/shield.ts", "webpack://_N_E/../../../src/icons/mail.ts", "webpack://_N_E/../../../src/icons/clock.ts", "webpack://_N_E/./app/components/home/<USER>", "webpack://_N_E/./app/page.tsx"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/not-found-error\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page4 = () => import(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\";\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\";\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\";\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "var _self___RSC_MANIFEST;\nimport \"next/dist/server/web/globals\";\nimport { adapter } from \"next/dist/server/web/adapter\";\nimport { getRender } from \"next/dist/build/webpack/loaders/next-edge-ssr-loader/render\";\nimport { IncrementalCache } from \"next/dist/server/lib/incremental-cache\";\nimport { renderToHTMLOrFlight as renderToHTML } from \"next/dist/server/app-render/app-render\";\nimport * as pageMod from \"next-app-loader?name=app%2Fpage&page=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/page.tsx?__next_edge_ssr_entry__\";\nimport { PAGE_TYPES } from \"next/dist/lib/page-types\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nconst incrementalCacheHandler = null\nconst cacheHandlers = {\n\n};\nif (!globalThis.__nextCacheHandlers) {\n    ;\n    globalThis.__nextCacheHandlers = cacheHandlers;\n}\nconst Document = null;\nconst appMod = null;\nconst errorMod = null;\nconst error500Mod = null;\nconst sriEnabled = false\nconst isServerComponent = true\nconst dev = false\nconst serverActions = undefined\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst buildManifest = self.__BUILD_MANIFEST;\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST);\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/page\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nconst subresourceIntegrityManifest = sriEnabled ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST) : undefined;\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST);\nconst interceptionRouteRewrites = maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? [];\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/page\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nconst render = getRender({\n    pagesType: PAGE_TYPES.APP,\n    dev,\n    page: \"/page\",\n    appMod,\n    pageMod,\n    errorMod,\n    error500Mod,\n    Document,\n    buildManifest,\n    renderToHTML,\n    reactLoadableManifest,\n    clientReferenceManifest: isServerComponent ? rscManifest : null,\n    serverActionsManifest: isServerComponent ? rscServerManifest : null,\n    serverActions: isServerComponent ? serverActions : undefined,\n    subresourceIntegrityManifest,\n    config: nextConfig,\n    buildId: process.env.__NEXT_BUILD_ID,\n    nextFontManifest,\n    incrementalCacheHandler,\n    interceptionRouteRewrites\n});\nexport const ComponentMod = pageMod;\nexport default function nHandler(opts) {\n    return adapter({\n        ...opts,\n        IncrementalCache,\n        handler: render\n    });\n}\n\n//# sourceMappingURL=edge-ssr-app.js.map", "import(/* webpackMode: \"eager\", webpackExports: [\"SignButton\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\auth\\\\sign-button.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ActionButton\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\home\\\\action-button.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThemeToggle\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\theme\\\\theme-toggle.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Logo\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\ui\\\\logo.tsx\");\n", "import(/* webpackMode: \"eager\", webpackExports: [\"SignButton\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\auth\\\\sign-button.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ActionButton\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\home\\\\action-button.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThemeToggle\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\theme\\\\theme-toggle.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Logo\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\ui\\\\logo.tsx\");\n", "\"use client\"\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport Image from \"next/image\"\r\nimport { signOut, useSession } from \"next-auth/react\"\r\nimport { LogIn } from \"lucide-react\"\r\nimport { useRouter } from 'next/navigation'\r\nimport Link from \"next/link\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\ninterface SignButtonProps {\r\n  size?: \"default\" | \"lg\"\r\n}\r\n\r\nexport function SignButton({ size = \"default\" }: SignButtonProps) {\r\n  const router = useRouter()\r\n  const { data: session, status } = useSession()\r\n  const loading = status === \"loading\"\r\n\r\n  if (loading) {\r\n    return <div className=\"h-9\" />\r\n  }\r\n\r\n  if (!session?.user) {\r\n    return (\r\n      <Button onClick={() => router.push('/login')} className={cn(\"gap-2\", size === \"lg\" ? \"px-8\" : \"\")} size={size}>\r\n        <LogIn className={size === \"lg\" ? \"w-5 h-5\" : \"w-4 h-4\"} />\r\n        登录/注册\r\n      </Button>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-4\">\r\n      <Link \r\n        href=\"/profile\"\r\n        className=\"flex items-center gap-2 hover:opacity-80 transition-opacity\"\r\n      >\r\n        {session.user.image && (\r\n          <Image\r\n            src={session.user.image}\r\n            alt={session.user.name || \"用户头像\"}\r\n            width={24}\r\n            height={24}\r\n            className=\"rounded-full\"\r\n          />\r\n        )}\r\n        <span className=\"text-sm\">{session.user.name}</span>\r\n      </Link>\r\n      <Button onClick={() => signOut({ callbackUrl: \"/\" })} variant=\"outline\" className={cn(\"flex-shrink-0\", size === \"lg\" ? \"px-8\" : \"\")} size={size}>\r\n        登出\r\n      </Button>\r\n    </div>\r\n  )\r\n} ", "\"use client\"\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Mail } from \"lucide-react\"\r\nimport { useRouter } from \"next/navigation\"\r\nimport { SignButton } from \"../auth/sign-button\"\r\n\r\ninterface ActionButtonProps {\r\n  isLoggedIn?: boolean\r\n}\r\n\r\nexport function ActionButton({ isLoggedIn }: ActionButtonProps) {\r\n  const router = useRouter()\r\n\r\n  if (isLoggedIn) {\r\n    return (\r\n      <Button \r\n        size=\"lg\" \r\n        onClick={() => router.push(\"/moe\")}\r\n        className=\"gap-2 bg-primary hover:bg-primary/90 text-white px-8\"\r\n      >\r\n        <Mail className=\"w-5 h-5\" />\r\n        进入邮箱\r\n      </Button>\r\n    )\r\n  }\r\n\r\n  return <SignButton size=\"lg\" />\r\n} ", "\"use client\"\r\n\r\nimport { Moon, Sun } from \"lucide-react\"\r\nimport { useTheme } from \"next-themes\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\n\r\nexport function ThemeToggle() {\r\n  const { theme, setTheme } = useTheme()\r\n\r\n  return (\r\n    <Button\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\r\n      className=\"rounded-full\"\r\n    >\r\n      <Sun className=\"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n      <Moon className=\"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n      <span className=\"sr-only\">切换主题</span>\r\n    </Button>\r\n  )\r\n} ", "\"use client\"\r\n\r\nimport Link from \"next/link\"\r\n\r\nexport function Logo() {\r\n  return (\r\n    <Link \r\n      href=\"/\"\r\n      className=\"flex items-center gap-2 hover:opacity-80 transition-opacity\"\r\n    >\r\n      <div className=\"relative w-8 h-8\">\r\n        <div className=\"absolute inset-0 grid grid-cols-8 grid-rows-8 gap-px\">\r\n          <svg\r\n            width=\"32\"\r\n            height=\"32\"\r\n            viewBox=\"0 0 32 32\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"text-primary\"\r\n          >\r\n            {/* 信封主体 */}\r\n            <path\r\n              d=\"M4 8h24v16H4V8z\"\r\n              className=\"fill-primary/20\"\r\n            />\r\n            \r\n            {/* 信封边框 */}\r\n            <path\r\n              d=\"M4 8h24v2H4V8zM4 22h24v2H4v-2z\"\r\n              className=\"fill-primary\"\r\n            />\r\n            \r\n            {/* @ 符号 */}\r\n            <path\r\n              d=\"M14 12h4v4h-4v-4zM12 14h2v4h-2v-4zM18 14h2v4h-2v-4zM14 18h4v2h-4v-2z\"\r\n              className=\"fill-primary\"\r\n            />\r\n            \r\n            {/* 折线装饰 */}\r\n            <path\r\n              d=\"M4 8l12 8 12-8\"\r\n              className=\"stroke-primary stroke-2\"\r\n              fill=\"none\"\r\n            />\r\n            \r\n            {/* 装饰点 */}\r\n            <path\r\n              d=\"M8 18h2v2H8v-2zM22 18h2v2h-2v-2z\"\r\n              className=\"fill-primary/60\"\r\n            />\r\n            \r\n            {/* 底部装饰线 */}\r\n            <path\r\n              d=\"M8 14h2v2H8v-2zM22 14h2v2h-2v-2z\"\r\n              className=\"fill-primary/40\"\r\n            />\r\n          </svg>\r\n        </div>\r\n      </div>\r\n      <span className=\"font-bold tracking-wider bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600\">\r\n        MoeMail\r\n      </span>\r\n    </Link>\r\n  )\r\n}", "import { SignButton } from \"@/components/auth/sign-button\"\r\nimport { ThemeToggle } from \"@/components/theme/theme-toggle\"\r\nimport { Logo } from \"@/components/ui/logo\"\r\n\r\nexport function Header() {\r\n  return (\r\n    <header className=\"fixed top-0 left-0 right-0 z-50 h-16 bg-background/80 backdrop-blur-sm border-b\">\r\n      <div className=\"container mx-auto h-full px-4\">\r\n        <div className=\"h-full flex items-center justify-between\">\r\n          <Logo />\r\n          <div className=\"flex items-center gap-4\">\r\n            <ThemeToggle />\r\n            <SignButton />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  )\r\n} ", "import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) => {\n    return createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    );\n  },\n);\n\nexport default Icon;\n", "import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(`lucide-${toKebabCase(iconName)}`, className),\n      ...props,\n    }),\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTNjMCA1LTMuNSA3LjUtNy42NiA4Ljk1YTEgMSAwIDAgMS0uNjctLjAxQzcuNSAyMC41IDQgMTggNCAxM1Y2YTEgMSAwIDAgMSAxLTFjMiAwIDQuNS0xLjIgNi4yNC0yLjcyYTEuMTcgMS4xNyAwIDAgMSAxLjUyIDBDMTQuNTEgMy44MSAxNyA1IDE5IDVhMSAxIDAgMCAxIDEgMXoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('Shield', [\n  [\n    'path',\n    {\n      d: 'M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z',\n      key: 'oel41y',\n    },\n  ],\n]);\n\nexport default Shield;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Im0yMiA3LTguOTcgNS43YTEuOTQgMS45NCAwIDAgMS0yLjA2IDBMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('Mail', [\n  ['rect', { width: '20', height: '16', x: '2', y: '4', rx: '2', key: '18n3k1' }],\n  ['path', { d: 'm22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7', key: '1ocrg3' }],\n]);\n\nexport default Mail;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('Clock', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n]);\n\nexport default Clock;\n", "interface FeatureCardProps { \r\n  icon: React.ReactNode\r\n  title: string\r\n  description: string \r\n}\r\n\r\nexport function FeatureCard({ icon, title, description }: FeatureCardProps) {\r\n  return (\r\n    <div className=\"p-4 rounded border-2 border-primary/20 hover:border-primary/40 transition-colors bg-white/5 backdrop-blur\">\r\n      <div className=\"flex items-center gap-3\">\r\n        <div className=\"rounded-lg bg-primary/10 text-primary p-2\">\r\n          {icon}\r\n        </div>\r\n        <div className=\"text-left\">\r\n          <h3 className=\"font-bold\">{title}</h3>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">{description}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n} ", "import { Header } from \"@/components/layout/header\"\r\nimport { auth } from \"@/lib/auth\"\r\nimport { Shield, Mail, Clock } from \"lucide-react\"\r\nimport { ActionButton } from \"@/components/home/<USER>\"\r\nimport { FeatureCard } from \"@/components/home/<USER>\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nexport default async function Home() {\r\n  const session = await auth()\r\n\r\n  return (\r\n    <div className=\"bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 h-screen\">\r\n      <div className=\"container mx-auto h-full px-4 lg:px-8 max-w-[1600px]\">\r\n        <Header />\r\n        <main className=\"h-full\">\r\n          <div className=\"h-[calc(100vh-4rem)] flex flex-col items-center justify-center text-center px-4 relative\">\r\n            <div className=\"absolute inset-0 -z-10 bg-grid-primary/5\" />\r\n            \r\n            <div className=\"w-full max-w-3xl mx-auto space-y-12 py-8\">\r\n              <div className=\"space-y-4\">\r\n                <h1 className=\"text-3xl sm:text-4xl md:text-5xl font-bold tracking-wider\">\r\n                  <span className=\"bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600\">\r\n                    MoeMail\r\n                  </span>\r\n                </h1>\r\n                <p className=\"text-lg sm:text-xl text-gray-600 dark:text-gray-300 tracking-wide\">\r\n                  萌萌哒临时邮箱服务\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 px-4 sm:px-0\">\r\n                <FeatureCard\r\n                  icon={<Shield className=\"w-5 h-5\" />}\r\n                  title=\"隐私保护\"\r\n                  description=\"保护您的真实邮箱地址\"\r\n                />\r\n                <FeatureCard\r\n                  icon={<Mail className=\"w-5 h-5\" />}\r\n                  title=\"即时收件\"\r\n                  description=\"实时接收邮件通知\"\r\n                />\r\n                <FeatureCard\r\n                  icon={<Clock className=\"w-5 h-5\" />}\r\n                  title=\"自动过期\"\r\n                  description=\"到期自动失效\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4 px-4 sm:px-0\">\r\n                <ActionButton isLoggedIn={!!session} />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": ["SignButton", "size", "router", "useRouter", "data", "session", "status", "useSession", "loading", "div", "className", "user", "Link", "href", "image", "Image", "src", "alt", "name", "width", "height", "span", "<PERSON><PERSON>", "onClick", "signOut", "callbackUrl", "variant", "cn", "push", "LogIn", "ActionButton", "isLoggedIn", "Mail", "ThemeToggle", "theme", "setTheme", "useTheme", "Sun", "Moon", "Logo", "svg", "viewBox", "fill", "xmlns", "path", "d", "Header", "header", "FeatureCard", "icon", "title", "description", "h3", "p", "runtime", "Home", "auth", "main", "h1", "Shield", "Clock"], "sourceRoot": "", "ignoreList": []}