# 初始化/更新 D1 数据库

## 创建数据库

打开 cloudflare 控制台，选择 `Workers & Pages` -> `D1` -> `Create Database`，点击创建数据库

![d1](/ui_install/d1.png)

创建完成后，我们在 cloudflare 的控制台可以看到 D1 数据库

## 初始化数据库

你也可以跳过初始化数据库，在部署完成后，在 admin 页面的 `快速设置` -> `数据库` 中，点击 `初始化数据库` 按钮来初始化数据库

::: warning 注意
下面输入的是 `db/schema.sql` 的内容
:::

打开 `Console` 标签页，输入 `db/schema.sql` 的内容，点击 `Execute` 执行

![d1](/ui_install/d1-exec.png)

## 更新数据库 schema

`schema` 更新，请确认你之前部署的版本,

查看 [更新日志](https://github.com/dreamhunter2333/cloudflare_temp_email/blob/main/CHANGELOG.md)

找到需要执行的 `patch` 文件, 执行, 例如:  `db/2024-01-13-patch.sql`

打开 `Console` 标签页，输入 `patch` 文件的内容，点击 `Execute` 执行

![d1](/ui_install/d1-exec.png)
