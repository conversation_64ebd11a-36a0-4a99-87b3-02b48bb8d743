{"version": 3, "file": "app/api/config/route.js", "mappings": "qFAAA,6DCAA,mHGAA,8RFKO,IAAMA,EAAU,OAAM,eAEPC,IACpB,IAAMC,EAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGD,GAAG,CAC7B,CAACE,EAAaC,EAAcC,EAAcC,EAAU,CAAG,MAAMC,QAAQC,GAAG,CAAC,CAC7EP,EAAIQ,WAAW,CAACC,GAAG,CAAC,gBACpBT,EAAIQ,WAAW,CAACC,GAAG,CAAC,iBACpBT,EAAIQ,WAAW,CAACC,GAAG,CAAC,iBACpBT,EAAIQ,WAAW,CAACC,GAAG,CAAC,cACrB,EAED,OAAOC,SAASC,IAAI,CAAC,CACnBT,YAAaA,GAAeU,EAAAA,EAAKA,CAACC,QAAQ,CAC1CV,aAAcA,GAAgB,cAC9BC,aAAcA,GAAgB,GAC9BC,UAAWA,GAAaS,EAAAA,CAAYA,CAACC,iBAAiB,CAACC,QAAQ,EACjE,EACF,CAEO,eAAeC,EAAKC,CAAgB,EAGzC,GAAI,CAFc,MAAMC,CAAAA,EAAAA,EAER,EAFQA,CAAeA,CAACC,EAAAA,EAAWA,CAACC,aAAa,EAG/D,OAAOX,SAASC,IAAI,CAAC,CACnBW,MAAO,MACT,EAAG,CAAEC,OAAQ,GAAI,GAGnB,GAAM,aAAErB,CAAW,cAAEC,CAAY,cAAEC,CAAY,WAAEC,CAAS,CAAE,CAAG,MAAMa,EAAQP,IAAI,GAOjF,GAAI,CAAC,CAACC,EAAAA,EAAKA,CAACY,IAAI,CAAEZ,EAAAA,EAAKA,CAACa,MAAM,CAAEb,EAAAA,EAAKA,CAACC,QAAQ,CAAC,CAACa,QAAQ,CAACxB,GACvD,OAAOQ,IAD8D,KACrDC,IAAI,CAAC,CAAEW,MAAO,OAAQ,EAAG,CAAEC,OAAQ,GAAI,GAGzD,IAAMvB,EAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGD,GAAG,CAQnC,OAPA,MAAMM,QAAQC,GAAG,CAAC,CAChBP,EAAIQ,WAAW,CAACmB,GAAG,CAAC,eAAgBzB,GACpCF,EAAIQ,WAAW,CAACmB,GAAG,CAAC,gBAAiBxB,GACrCH,EAAIQ,WAAW,CAACmB,GAAG,CAAC,gBAAiBvB,GACrCJ,EAAIQ,WAAW,CAACmB,GAAG,CAAC,aAActB,GACnC,EAEMK,SAASC,IAAI,CAAC,CAAEiB,SAAS,CAAK,EACvC,CC9CA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,yBACA,uBACA,iBACA,iCACA,CAAK,CACL,uFACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,kEACA,GAFA,2BAEA,4BACA,MACI,QAA8B,EAClC,yBACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,uIAiBnxJ,CAAC,CAAC,EAAC,4ECvBI,IAAMd,EAAe,CAC1BC,kBAAmB,GACnBc,cAAe,GACjB,EAAU,ECHoB,CAC5BC,YAAa,EACbC,QAAS,IACTC,YAAa,IACbC,OAAQ,CACNC,YAAa,aACf,CACF,EAAU,2FEwCH,IAAMC,EAAY,UAEvB,IAAMC,EAASC,CADK,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,EAAAA,EACN7B,GAAG,CAAC,aAE/B,GAAI2B,EAAQ,OAAOA,EAEnB,IAAMG,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAE1B,OAAOD,GAASE,KAAKC,EACvB,EAAC,oOCxDD,IAAMC,EAAS,CACb,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACD,eCGD,IAAMC,EAA0C,CAC9C,CAAChC,EAAAA,EAAKA,CAACiC,OAAO,CAAC,CAAE,YACjB,CAACjC,EAAAA,EAAKA,CAACY,IAAI,CAAC,CAAE,WACd,CAACZ,EAAAA,EAAKA,CAACa,MAAM,CAAC,CAAE,WAChB,CAACb,EAAAA,EAAKA,CAACC,QAAQ,CAAC,CAAE,UACpB,EAEMiC,EAAiB,UACrB,IAAM5C,EAAc,MAAMD,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGD,GAAG,CAACQ,WAAW,CAACC,GAAG,CAAC,uBAElE,IACkBG,EAAAA,EAAKA,CAACY,IAAI,EAC1BtB,IAAgBU,EAAAA,EAAKA,CAACa,MAAM,EAC5BvB,IAAgBU,EAAAA,EAAKA,CAACC,QAAQ,CAEvBX,CADP,CAIKU,EAAAA,EAAKA,CAACC,QAAQ,EAGvB,eAAekC,EAAiBC,CAAM,CAAEC,CAAc,EACpD,IAAIC,EAAO,MAAMF,EAAGG,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC,CACxCC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,KAAKA,CAACI,IAAI,CAAEP,EACxB,GAEA,GAAI,CAACC,EAAM,CACT,GAAM,CAACO,EAAQ,CAAG,MAAMT,EAAGU,MAAM,CAACN,EAAAA,KAAKA,EACpCO,MAAM,CAAC,CACNH,KAAMP,EACNW,YAAahB,CAAiB,CAACK,EAAS,GAEzCY,SAAS,GACZX,EAAOO,CACT,CAEA,OAAOP,CACT,CAEO,eAAeY,EAAiBd,CAAM,CAAEZ,CAAc,CAAE2B,CAAc,EAC3E,MAAMf,EAAGgB,MAAM,CAACC,EAAAA,SAASA,EACtBX,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAAC7B,MAAM,CAAEA,IAE9B,MAAMY,EAAGU,MAAM,CAACO,EAAAA,SAASA,EACtBN,MAAM,CAAC,QACNvB,SACA2B,CACF,EACJ,CAEO,eAAeG,EAAY9B,CAAc,EAC9C,IAAMY,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,MAAOC,CAJiB,MAAMpB,EAAGG,KAAK,CAACc,SAAS,CAACI,QAAQ,CAAC,CACxDf,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAAC7B,MAAM,CAAEA,GAC5BkC,KAAM,CAAEpB,MAAM,CAAK,CACrB,GACsB,CAAC,EAAE,CAACA,IAAI,CAACM,IAAI,CAG9B,eAAerC,EAAgBoD,CAAsB,EAC1D,IAAMnC,EAAS,MAAMD,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE9B,GAAI,CAACC,EAAQ,OAAO,EAEpB,IAAMY,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMbK,EAAgBJ,CALE,MAAMpB,EAAGG,KAAK,CAACc,SAAS,CAACI,QAAQ,CAAC,CACxDf,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAAC7B,MAAM,CAAEA,GAC5BkC,KAAM,CAAEpB,MAAM,CAAK,CACrB,IAEsCuB,GAAG,CAACC,GAAMA,EAAGxB,IAAI,CAACM,IAAI,EAC5D,MAAOmB,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAACH,EAAyBD,EAChD,CAEO,GAAM,CACXK,SAAU,KAAE7E,CAAG,CAAEkB,MAAI,CAAE,MACvBuB,CAAI,QACJqC,CAAM,SACNC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,IAAO,EAClBC,OAAQC,QAAQjF,GAAG,CAACkF,WAAW,CAC/BC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACjB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAAI,CAClCkB,WAAYC,EAAAA,KAAKA,CACjBC,cAAeC,EAAAA,QACjB,GACAC,UAAW,CACTC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC,CACLC,SAAUV,QAAQjF,GAAG,CAAC4F,cAAc,CACpCC,aAAcZ,QAAQjF,GAAG,CAAC8F,kBAAkB,GAE9CC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CAClBvC,KAAM,cACNwC,YAAa,CACXC,SAAU,CAAEC,MAAO,MAAOC,KAAM,OAAQC,YAAa,QAAS,EAC9DC,SAAU,CAAEH,MAAO,KAAMC,KAAM,WAAYC,YAAa,OAAQ,CAClE,EACA,MAAME,UAAUN,CAAW,EACzB,GAAI,CAACA,EACH,MAAM,KADU,CACA,aAGlB,GAAM,UAAEC,CAAQ,UAAEI,CAAQ,CAAE,CAAGL,EAE/B,GAAI,CACFO,EAAAA,CAAUA,CAACC,KAAK,CAAC,CAAEP,oBAAUI,CAAS,EAExC,CAAE,MAAO/E,EAAO,CACd,MAAM,MAAU,UAClB,CAEA,IAAM0B,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEb1B,EAAO,MAAMO,EAAGG,KAAK,CAACmC,KAAK,CAACjC,SAAS,CAAC,CAC1CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC+B,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAEA,GAAI,CAACxD,GAKD,CADY,EAJL,IAIWgE,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAACJ,EAAoB5D,EAAK4D,QAAQ,EAHrE,MAAM,MAAU,YAQlB,MAAO,CACL,GAAG5D,CAAI,CACP4D,SAAUK,MACZ,CACF,CACF,GACD,CACDC,OAAQ,CACN,MAAM9B,OAAO,MAAEpC,CAAI,CAAE,EACnB,GAAKA,CAAD,CAAMC,EAAE,CAEZ,CAFc,EAEV,CACF,IAAMM,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,GAJqB,CAIjByC,KAJuB5D,EAAGG,KAAK,CAACc,SAAS,CAACZ,SAAS,CAAC,CACtDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAAC7B,MAAM,CAAEK,EAAKC,EAAE,CACrC,GAEkB,OAElB,IAAMxC,EAAc,MAAM4C,IACpBI,EAAO,MAAMH,EAAiBC,EAAI9C,EACxC,OAAM4D,EAAiBd,EAAIP,EAAKC,EAAE,CAAEQ,EAAKR,EAAE,CAC7C,CAAE,MAAOpB,EAAO,CACduF,QAAQvF,KAAK,CAAC,wBAAyBA,EACzC,CACF,CACF,EACAwF,UAAW,CACHC,IAAN,MAAU,CAAEC,OAAK,MAAEvE,CAAI,CAAE,IACnBA,IACFuE,EADQ,EACA,CAAGvE,EAAKC,EAAE,CAClBsE,EAAMxD,IAAI,CAAGf,EAAKe,IAAI,EAAIf,EAAKwD,QAAQ,CACvCe,EAAMf,QAAQ,CAAGxD,EAAKwD,QAAQ,CAC9Be,EAAMC,KAAK,CAAGxE,EAAKwE,KAAK,ED/JzB,SAASC,CAA8B,EAC5C,IAAMC,CC8J6CD,CD9JnC1D,CAAI,CAAC,EAAE,CAAC4D,WAAW,GAE7BC,EAAaC,MAAMC,IAAI,CAAC/D,GAAMgE,MAAM,CACxC,CAACC,EAAKC,IAASD,EAAMC,EAAKC,UAAU,CAAC,GAAI,GACvChF,EAAOiF,MAAM,CAEXC,EAAkBlF,CAAM,CAAC0E,EAAW,CAEpCS,EAAM,CAAC;;2CAE4B,EAAED,EAAgB;;;;;;;;;;;;;QAarD,EAAEV,QAAQ;;;EAGhB,CAAC,CAACY,IAAI,GAGAC,EADU,IAAIC,cACEC,MAAM,CAACJ,GACvBK,EAASC,EAAOb,IAADa,CAAMJ,GAAOhH,QAAQ,CAAC,UAE3C,MAAO,CAAC,0BAA0B,EAAEmH,EAAAA,CACtC,EC6HsDnB,EAAMxD,IAAI,GAEnDwD,GAET,MAAMzE,QAAQ,SAAEA,CAAO,OAAEyE,CAAK,CAAE,EAC9B,GAAIA,GAASzE,EAAQE,IAAI,CAAE,CACzBF,EAAQE,IAAI,CAACC,EAAE,CAAGsE,EAAMtE,EAAE,CAC1BH,EAAQE,IAAI,CAACe,IAAI,CAAGwD,EAAMxD,IAAI,CAC9BjB,EAAQE,IAAI,CAACwD,QAAQ,CAAGe,EAAMf,QAAQ,CACtC1D,EAAQE,IAAI,CAACwE,KAAK,CAAGD,EAAMC,KAAK,CAEhC,IAAMjE,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACfC,EAAkB,MAAMpB,EAAGG,KAAK,CAACc,SAAS,CAACI,QAAQ,CAAC,CACtDf,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAAC7B,MAAM,CAAEG,EAAQE,IAAI,CAACC,EAAE,EAC3C4B,KAAM,CAAEpB,MAAM,CAAK,CACrB,GAEA,GAAI,CAACkB,EAAgBwD,MAAM,CAAE,CAC3B,IAAM1H,EAAc,MAAM4C,IACpBI,EAAO,MAAMH,EAAiBC,EAAI9C,EACxC,OAAM4D,EAAiBd,EAAIT,EAAQE,IAAI,CAACC,EAAE,CAAEQ,EAAKR,EAAE,EACnD0B,EAAkB,CAAC,CACjBhC,OAAQG,EAAQE,IAAI,CAACC,EAAE,CACvBqB,OAAQb,EAAKR,EAAE,CACf2F,UAAW,IAAIC,KACfpF,KAAMA,CACR,EAAE,CAGJX,EAAQE,IAAI,CAACW,KAAK,CAAGgB,EAAgBK,GAAG,CAACC,GAAO,OACxCA,EAAGxB,IAAI,CAACM,IAAI,CACpB,EACF,CAEA,OAAOjB,CACT,CACF,EACAA,QAAS,CACPgG,SAAU,KACZ,EACF,GAEO,CAFJ,cAEmBC,EAASvC,CAAgB,CAAEI,CAAgB,EAC/D,IAAMrD,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,GAJiB,CAIbsE,KAJmBzF,EAAGG,GAIZ,EAJiB,CAACmC,KAAK,CAACjC,SAAS,CAAC,CAC9CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC+B,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAGE,MAAM,MAAU,UAGlB,IAAMyC,EAAiB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACtC,GAEpC,CAAC5D,EAAK,CAAG,MAAMO,EAAGU,MAAM,CAAC4B,EAAAA,KAAKA,EACjC3B,MAAM,CAAC,UACNsC,EACAI,SAAUqC,CACZ,GACC7E,SAAS,GAEZ,OAAOpB,CACT,gFCvOO,IAAM0B,EAAW,IAAMyE,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAAC3I,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGD,GAAG,CAAC6I,EAAE,CAAE,CAAEC,MAAMA,CAAAA,CAAC,GAAE,gCAAHA,gCCJnE,IAAMlI,EAAQ,CACnBiC,QAAS,UACTrB,KAAM,OACNC,OAAQ,SACRZ,SAAU,UACZ,EAAW,EAIgB,CACzBkI,aAAc,eACdC,eAAgB,iBAChBC,aAAc,eACd5H,cAAe,gBACf6H,eAAgB,gBAClB,EAAW,EAIiD,CAC1D,CAACtI,EAAMiC,OAAO,CAAC,CAAEsG,OAAOxF,MAAM,CAACvC,GAC/B,CAACR,EAAMY,IAAI,CAAC,CAAE,CACZJ,EAAY2H,YAAY,CACxB3H,EAAY4H,cAAc,CAC1B5H,EAAY8H,cAAc,CAC3B,CACD,CAACtI,EAAMa,MAAM,CAAC,CAAE,CACdL,EAAY2H,YAAY,CACxB3H,EAAY4H,cAAc,CAC3B,CACD,CAACpI,EAAMC,QAAQ,CAAC,CAAE,EAAE,EACX,SAEK8D,EAAcV,CAAiB,CAAEM,CAAsB,EACrE,OAAON,EAAUmF,IAAI,CAAClG,GAAQmG,CAAgB,CAACnG,EAAK,EAAExB,SAAS6C,GACjE,kVC9BO,IAAMe,EAAQgE,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvC5G,GAAI6G,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MACNC,UAAU,GACVC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IACrCnG,KAAM+F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXK,MAAOL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASM,MAAM,GAC3BC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,gBAAiB,CAAEC,KAAM,cAAe,GAC/D/C,MAAOsC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZtD,SAAUsD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYM,MAAM,GACjCxD,SAAUkD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACjB,GAAE,EACsBD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CACjC,UACA,CACElH,OAAQmH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UACVU,OAAO,GACPC,UAAU,CAAC,IAAM5E,EAAM5C,EAAE,CAAE,CAAEyH,SAAU,SAAU,GACpDhE,KAAMoD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQa,KAAK,GAAuBH,OAAO,GACtDI,SAAUd,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYU,OAAO,GAClCK,kBAAmBf,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,qBAAqBU,OAAO,GACpDM,cAAehB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBACpBiB,aAAcjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACnBkB,WAAYV,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cACpBW,WAAYnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,cACjBoB,MAAOpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZqB,SAAUrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YACfsB,cAAetB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACtB,EACA,GAAc,EACZuB,KADY,OACCtB,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CACtBuB,QAAS,CAACC,EAAQX,QAAQ,CAAEW,EAAQV,iBAAiB,CAAC,GAE1D,GAGWW,CAFZ,CAEqB3B,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,QAAS,CACzC5G,GAAI6G,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DuB,QAAS3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGJ,MAAM,GACzCzH,OAAQmH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UAAUW,UAAU,CAAC,IAAM5E,EAAM5C,EAAE,CAAE,CAAEyH,SAAU,SAAU,GACxE9B,UAAW0B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAInB,MACxB6C,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbmB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAMJ,SAAS,EAChE,GAEaK,CAFV,CAEqBlC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7C5G,GAAI6G,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D8B,QAASlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACXU,OAAO,GACPC,UAAU,CAAC,IAAMe,EAAOvI,EAAE,CAAE,CAAEyH,SAAU,SAAU,GACrDuB,YAAanC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBAAgBU,OAAO,GACzC0B,QAASpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC2B,QAASrC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC4B,KAAMtC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXuC,WAAY/B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cAAe,CAAEC,KAAM,cAAe,GACvDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAInB,KAC1B,EAAG,GAAY,EACbyD,GADa,QACDV,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAME,OAAO,EAC5D,GAEaO,CAFV,CAEqB1C,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7C5G,GAAI6G,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DvH,OAAQmH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACVU,OAAO,GACPC,UAAU,CAAC,IAAM5E,EAAM5C,EAAE,CAAE,CAAEyH,SAAU,SAAU,GACpD8B,IAAK1C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GACxBiC,QAASnC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGkC,OAAO,EAAC,GACnE9D,UAAW0B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAInB,MACxB8D,UAAWrC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAInB,KAC1B,GAAE,EAEmBgB,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvC5G,GAAI6G,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DnG,KAAM+F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BrG,YAAa2F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,eAClBlB,UAAW0B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAInB,MAC7E8D,UAAWrC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAInB,KAC/E,GAAG,EAEsBgB,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,YAAa,CAChDlH,OAAQmH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM5E,EAAM5C,EAAE,CAAE,CAAEyH,SAAU,SAAU,GACnFpG,OAAQwF,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM9G,EAAMV,EAAE,CAAE,CAAEyH,SAAU,SAAU,GACnF9B,UAAW0B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAInB,KAC/E,EAAG,GAAY,EACb+D,GADa,CACT7C,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CAAEuB,QAAS,CAACQ,EAAMnJ,MAAM,CAAEmJ,EAAMxH,MAAM,CAAC,EACxD,IAAI,EAEmBuF,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,WAAY,CAC7C5G,GAAI6G,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DvH,OAAQmH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM5E,EAAM5C,EAAE,EAC3Dc,KAAM+F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BqC,IAAK/C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GAAGJ,MAAM,GACjCxB,UAAW0B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAInB,MAC7E6C,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrDkC,QAASnC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGkC,OAAO,EAAC,EACrE,EAAG,GAAY,EACbI,GADa,cACKC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,uBAAuBlB,EAAE,CAACC,EAAM/H,IAAI,CAAE+H,EAAMnJ,MAAM,EAClF,GAEaqK,CAFT,CAE+BnD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,qBAAsB,CACnEoD,IAAKnD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOC,UAAU,GAC3B2B,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbmB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,0BAA0BC,EAAE,CAACC,EAAMJ,SAAS,EAClE,GAEawB,CAFT,CAE4BC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACC,EAAS,CAAC,KAAEC,CAAG,CAAE,GAAM,EAC/DrK,KAAMqK,EAAIxH,EAAO,CACfyH,OAAQ,CAACF,EAAQzK,MAAM,CAAC,CACxB8H,WAAY,CAAC5E,EAAM5C,EAAE,CAAC,GAE1B,GAEasK,CAFT,CAE8BJ,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAC3I,EAAW,CAAC,CAAE6I,KAAG,CAAE,GAAM,EACnErK,KAAMqK,EAAIxH,EAAO,CACfyH,OAAQ,CAAC9I,EAAU7B,MAAM,CAAC,CAC1B8H,WAAY,CAAC5E,EAAM5C,EAAE,CAAC,GAExBQ,KAAM4J,EAAI1J,EAAO,CACf2J,OAAQ,CAAC9I,EAAUF,MAAM,CAAC,CAC1BmG,WAAY,CAAC9G,EAAMV,EAAE,CAAC,GAE1B,GAEauK,CAFT,CAE0BL,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACtH,EAAO,CAAC,CAAE4H,MAAI,CAAE,GAAM,EAC5DjJ,UAAWiJ,EAAKjJ,GAChB4I,QAASK,EAAKL,GAChB,GAEaM,CAFT,CAE0BP,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACxJ,EAAO,CAAC,MAAE8J,CAAI,CAAE,GAAM,EAC5DjJ,UAAWiJ,EAAKjJ,GAClB,IAAI,sFC5IG,SAASmJ,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,CAEO,eAAe1E,EAAatC,CAAgB,EACjD,IAAMmH,EAAU,IAAIvF,YACdwF,EAAOxI,QAAQjF,GAAG,CAACkF,WAAW,EAAI,GAClCwI,EAAOF,EAAQtF,MAAM,CAAC7B,EAAWoH,GAEvC,OAAOE,KAAKC,OAAOC,YAAY,IAAI,IAAIC,WAAWC,MAD/BrE,OAAOsE,MAAM,CAACC,MAAM,CAAC,UAAWP,KAErD,CAEO,eAAejH,EAAgBJ,CAAgB,CAAEqC,CAAsB,EAE5E,OADa,MAAMC,EAAatC,KAChBqC,CAClB,8DChBO,IAAMnC,EAAa2H,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACjCjI,SAAUiI,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,WACPC,GAAG,CAAC,GAAI,gBACRC,KAAK,CAAC,mBAAoB,uBAC1BC,MAAM,CAACC,GAAO,CAACA,EAAI7M,QAAQ,CAAC,KAAM,cACrC2E,SAAU6H,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,eACZ,GAAE", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/config/route.ts", "webpack://_N_E/./app/api/config/route.ts?d3a6", "webpack://_N_E/?45e6", "webpack://_N_E/./app/config/email.ts", "webpack://_N_E/./app/config/webhook.ts", "webpack://_N_E/./app/config/index.ts", "webpack://_N_E/./app/lib/apiKey.ts", "webpack://_N_E/./app/lib/avatar.ts", "webpack://_N_E/./app/lib/auth.ts", "webpack://_N_E/./app/lib/db.ts", "webpack://_N_E/./app/lib/permissions.ts", "webpack://_N_E/./app/lib/schema.ts", "webpack://_N_E/./app/lib/utils.ts", "webpack://_N_E/./app/lib/validation.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { PERMISSIONS, Role, ROLES } from \"@/lib/permissions\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { EMAIL_CONFIG } from \"@/config\"\r\nimport { checkPermission } from \"@/lib/auth\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nexport async function GET() {\r\n  const env = getRequestContext().env\r\n  const [defaultRole, emailDomains, adminContact, maxEmails] = await Promise.all([\r\n    env.SITE_CONFIG.get(\"DEFAULT_ROLE\"),\r\n    env.SITE_CONFIG.get(\"EMAIL_DOMAINS\"),\r\n    env.SITE_CONFIG.get(\"ADMIN_CONTACT\"),\r\n    env.SITE_CONFIG.get(\"MAX_EMAILS\")\r\n  ])\r\n\r\n  return Response.json({\r\n    defaultRole: defaultRole || ROLES.CIVILIAN,\r\n    emailDomains: emailDomains || \"moemail.app\",\r\n    adminContact: adminContact || \"\",\r\n    maxEmails: maxEmails || EMAIL_CONFIG.MAX_ACTIVE_EMAILS.toString()\r\n  })\r\n}\r\n\r\nexport async function POST(request: Request) {\r\n  const canAccess = await checkPermission(PERMISSIONS.MANAGE_CONFIG)\r\n\r\n  if (!canAccess) {\r\n    return Response.json({\r\n      error: \"权限不足\"\r\n    }, { status: 403 })\r\n  }\r\n\r\n  const { defaultRole, emailDomains, adminContact, maxEmails } = await request.json() as { \r\n    defaultRole: Exclude<Role, typeof ROLES.EMPEROR>,\r\n    emailDomains: string,\r\n    adminContact: string,\r\n    maxEmails: string\r\n  }\r\n  \r\n  if (![ROLES.DUKE, ROLES.KNIGHT, ROLES.CIVILIAN].includes(defaultRole)) {\r\n    return Response.json({ error: \"无效的角色\" }, { status: 400 })\r\n  }\r\n\r\n  const env = getRequestContext().env\r\n  await Promise.all([\r\n    env.SITE_CONFIG.put(\"DEFAULT_ROLE\", defaultRole),\r\n    env.SITE_CONFIG.put(\"EMAIL_DOMAINS\", emailDomains),\r\n    env.SITE_CONFIG.put(\"ADMIN_CONTACT\", adminContact),\r\n    env.SITE_CONFIG.put(\"MAX_EMAILS\", maxEmails)\r\n  ])\r\n\r\n  return Response.json({ success: true })\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\config\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/config/route\",\n        pathname: \"/api/config\",\n        filename: \"route\",\n        bundlePath: \"app/api/config/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\config\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fconfig%2Froute&page=%2Fapi%2Fconfig%2Froute&pagePath=private-next-app-dir%2Fapi%2Fconfig%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fapi%2Fconfig%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/config/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/config/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/config/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "export const EMAIL_CONFIG = {\r\n  MAX_ACTIVE_EMAILS: 30, // Maximum number of active emails\r\n  POLL_INTERVAL: 10_000, // Polling interval in milliseconds\r\n} as const\r\n\r\nexport type EmailConfig = typeof EMAIL_CONFIG ", "export const WEBHOOK_CONFIG = {\r\n  MAX_RETRIES: 3, // Maximum retry count\r\n  TIMEOUT: 10_000, // Timeout time (milliseconds)\r\n  RETRY_DELAY: 1000, // Retry delay (milliseconds)\r\n  EVENTS: {\r\n    NEW_MESSAGE: 'new_message',\r\n  }\r\n} as const\r\n\r\nexport type WebhookConfig = typeof WEBHOOK_CONFIG ", "export * from './email'\r\nexport * from './webhook'", "import { createDb } from \"./db\"\r\nimport { api<PERSON><PERSON>s } from \"./schema\"\r\nimport { eq, and, gt } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport type { User } from \"next-auth\"\r\nimport { auth } from \"./auth\"\r\nimport { headers } from \"next/headers\"\r\n\r\nasync function getUserByApiKey(key: string): Promise<User | null> {\r\n  const db = createDb()\r\n  const apiKey = await db.query.apiKeys.findFirst({\r\n    where: and(\r\n      eq(apiKeys.key, key),\r\n      eq(apiKeys.enabled, true),\r\n      gt(apiKeys.expiresAt, new Date())\r\n    ),\r\n    with: {\r\n      user: true\r\n    }\r\n  })\r\n\r\n  if (!apiKey) return null\r\n\r\n  return apiKey.user\r\n}\r\n\r\nexport async function handleApiKeyAuth(apiKey: string, pathname: string) {\r\n  if (!pathname.startsWith('/api/emails') && !pathname.startsWith('/api/config')) {\r\n    return NextResponse.json(\r\n      { error: \"无权限查看\" },\r\n      { status: 403 }\r\n    )\r\n  }\r\n\r\n  const user = await getUserByApiKey(apiKey)\r\n  if (!user?.id) {\r\n    return NextResponse.json(\r\n      { error: \"无效的 API Key\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  const response = NextResponse.next()\r\n  response.headers.set(\"X-User-Id\", user.id)\r\n  return response\r\n}\r\n\r\nexport const getUserId = async () => {\r\n  const headersList = await headers()\r\n  const userId = headersList.get(\"X-User-Id\")\r\n  \r\n  if (userId) return userId\r\n\r\n  const session = await auth()\r\n\r\n  return session?.user.id\r\n}\r\n", "const COLORS = [\r\n  '#2196F3', // 蓝色\r\n  '#009688', // 青色\r\n  '#9C27B0', // 紫色\r\n  '#F44336', // 红色\r\n  '#673AB7', // 深紫色\r\n  '#3F51B5', // 靛蓝\r\n  '#4CAF50', // 绿色\r\n  '#FF5722', // 深橙\r\n  '#795548', // 棕色\r\n  '#607D8B', // 蓝灰\r\n];\r\n\r\nexport function generateAvatarUrl(name: string): string {\r\n  const initial = name[0].toUpperCase();\r\n  \r\n  const colorIndex = Array.from(name).reduce(\r\n    (acc, char) => acc + char.charCodeAt(0), 0\r\n  ) % COLORS.length;\r\n  \r\n  const backgroundColor = COLORS[colorIndex];\r\n  \r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\" width=\"100\" height=\"100\">\r\n      <rect width=\"100\" height=\"100\" fill=\"${backgroundColor}\"/>\r\n      <text \r\n        x=\"50%\" \r\n        y=\"50%\" \r\n        fill=\"white\" \r\n        font-family=\"system-ui, -apple-system, sans-serif\" \r\n        font-size=\"45\" \r\n        font-weight=\"500\"\r\n        text-anchor=\"middle\"\r\n        alignment-baseline=\"central\"\r\n        dominant-baseline=\"central\"\r\n        style=\"text-transform: uppercase\"\r\n      >\r\n        ${initial}\r\n      </text>\r\n    </svg>\r\n  `.trim();\r\n\r\n  const encoder = new TextEncoder();\r\n  const bytes = encoder.encode(svg);\r\n  const base64 = Buffer.from(bytes).toString('base64');\r\n  \r\n  return `data:image/svg+xml;base64,${base64}`;\r\n} ", "import NextAuth from \"next-auth\"\r\nimport GitHub from \"next-auth/providers/github\"\r\nimport { Dr<PERSON>zleAdapter } from \"@auth/drizzle-adapter\"\r\nimport { createDb, Db } from \"./db\"\r\nimport { accounts, users, roles, userRoles } from \"./schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { Permission, hasPermission, ROLES, Role } from \"./permissions\"\r\nimport CredentialsProvider from \"next-auth/providers/credentials\"\r\nimport { hashPassword, comparePassword } from \"@/lib/utils\"\r\nimport { authSchema } from \"@/lib/validation\"\r\nimport { generateAvatarUrl } from \"./avatar\"\r\nimport { getUserId } from \"./apiKey\"\r\n\r\nconst ROLE_DESCRIPTIONS: Record<Role, string> = {\r\n  [ROLES.EMPEROR]: \"皇帝（网站所有者）\",\r\n  [ROLES.DUKE]: \"公爵（超级用户）\",\r\n  [ROLES.KNIGHT]: \"骑士（高级用户）\",\r\n  [ROLES.CIVILIAN]: \"平民（普通用户）\",\r\n}\r\n\r\nconst getDefaultRole = async (): Promise<Role> => {\r\n  const defaultRole = await getRequestContext().env.SITE_CONFIG.get(\"DEFAULT_ROLE\")\r\n\r\n  if (\r\n    defaultRole === ROLES.DUKE ||\r\n    defaultRole === ROLES.KNIGHT ||\r\n    defaultRole === ROLES.CIVILIAN\r\n  ) {\r\n    return defaultRole as Role\r\n  }\r\n  \r\n  return ROLES.CIVILIAN\r\n}\r\n\r\nasync function findOrCreateRole(db: Db, roleName: Role) {\r\n  let role = await db.query.roles.findFirst({\r\n    where: eq(roles.name, roleName),\r\n  })\r\n\r\n  if (!role) {\r\n    const [newRole] = await db.insert(roles)\r\n      .values({\r\n        name: roleName,\r\n        description: ROLE_DESCRIPTIONS[roleName],\r\n      })\r\n      .returning()\r\n    role = newRole\r\n  }\r\n\r\n  return role\r\n}\r\n\r\nexport async function assignRoleToUser(db: Db, userId: string, roleId: string) {\r\n  await db.delete(userRoles)\r\n    .where(eq(userRoles.userId, userId))\r\n\r\n  await db.insert(userRoles)\r\n    .values({\r\n      userId,\r\n      roleId,\r\n    })\r\n}\r\n\r\nexport async function getUserRole(userId: string) {\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n  return userRoleRecords[0].role.name\r\n}\r\n\r\nexport async function checkPermission(permission: Permission) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) return false\r\n\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n\r\n  const userRoleNames = userRoleRecords.map(ur => ur.role.name)\r\n  return hasPermission(userRoleNames as Role[], permission)\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  auth,\r\n  signIn,\r\n  signOut\r\n} = NextAuth(() => ({\r\n  secret: process.env.AUTH_SECRET,\r\n  adapter: DrizzleAdapter(createDb(), {\r\n    usersTable: users,\r\n    accountsTable: accounts,\r\n  }),\r\n  providers: [\r\n    GitHub({\r\n      clientId: process.env.AUTH_GITHUB_ID,\r\n      clientSecret: process.env.AUTH_GITHUB_SECRET,\r\n    }),\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        username: { label: \"用户名\", type: \"text\", placeholder: \"请输入用户名\" },\r\n        password: { label: \"密码\", type: \"password\", placeholder: \"请输入密码\" },\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials) {\r\n          throw new Error(\"请输入用户名和密码\")\r\n        }\r\n\r\n        const { username, password } = credentials\r\n\r\n        try {\r\n          authSchema.parse({ username, password })\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        } catch (error) {\r\n          throw new Error(\"输入格式不正确\")\r\n        }\r\n\r\n        const db = createDb()\r\n\r\n        const user = await db.query.users.findFirst({\r\n          where: eq(users.username, username as string),\r\n        })\r\n\r\n        if (!user) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        const isValid = await comparePassword(password as string, user.password as string)\r\n        if (!isValid) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        return {\r\n          ...user,\r\n          password: undefined,\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  events: {\r\n    async signIn({ user }) {\r\n      if (!user.id) return\r\n\r\n      try {\r\n        const db = createDb()\r\n        const existingRole = await db.query.userRoles.findFirst({\r\n          where: eq(userRoles.userId, user.id),\r\n        })\r\n\r\n        if (existingRole) return\r\n\r\n        const defaultRole = await getDefaultRole()\r\n        const role = await findOrCreateRole(db, defaultRole)\r\n        await assignRoleToUser(db, user.id, role.id)\r\n      } catch (error) {\r\n        console.error('Error assigning role:', error)\r\n      }\r\n    },\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id\r\n        token.name = user.name || user.username\r\n        token.username = user.username\r\n        token.image = user.image || generateAvatarUrl(token.name as string)\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token && session.user) {\r\n        session.user.id = token.id as string\r\n        session.user.name = token.name as string\r\n        session.user.username = token.username as string\r\n        session.user.image = token.image as string\r\n\r\n        const db = createDb()\r\n        let userRoleRecords = await db.query.userRoles.findMany({\r\n          where: eq(userRoles.userId, session.user.id),\r\n          with: { role: true },\r\n        })\r\n  \r\n        if (!userRoleRecords.length) {\r\n          const defaultRole = await getDefaultRole()\r\n          const role = await findOrCreateRole(db, defaultRole)\r\n          await assignRoleToUser(db, session.user.id, role.id)\r\n          userRoleRecords = [{\r\n            userId: session.user.id,\r\n            roleId: role.id,\r\n            createdAt: new Date(),\r\n            role: role\r\n          }]\r\n        }\r\n  \r\n        session.user.roles = userRoleRecords.map(ur => ({\r\n          name: ur.role.name,\r\n        }))\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: \"jwt\",\r\n  },\r\n}))\r\n\r\nexport async function register(username: string, password: string) {\r\n  const db = createDb()\r\n  \r\n  const existing = await db.query.users.findFirst({\r\n    where: eq(users.username, username)\r\n  })\r\n\r\n  if (existing) {\r\n    throw new Error(\"用户名已存在\")\r\n  }\r\n\r\n  const hashedPassword = await hashPassword(password)\r\n  \r\n  const [user] = await db.insert(users)\r\n    .values({\r\n      username,\r\n      password: hashedPassword,\r\n    })\r\n    .returning()\r\n\r\n  return user\r\n}\r\n", "import { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { drizzle } from \"drizzle-orm/d1\"\r\nimport * as schema from \"./schema\"\r\n\r\nexport const createDb = () => drizzle(getRequestContext().env.DB, { schema })\r\n\r\nexport type Db = ReturnType<typeof createDb>\r\n", "export const ROLES = {\r\n  EMPEROR: 'emperor',\r\n  <PERSON>U<PERSON>: 'duke',\r\n  <PERSON><PERSON><PERSON><PERSON>: 'knight',\r\n  CIVILIAN: 'civilian',\r\n} as const;\r\n\r\nexport type Role = typeof ROLES[keyof typeof ROLES];\r\n\r\nexport const PERMISSIONS = {\r\n  MANAGE_EMAIL: 'manage_email',\r\n  <PERSON><PERSON>GE_WEBHOOK: 'manage_webhook',\r\n  PROMOTE_USER: 'promote_user',\r\n  MANAGE_CONFIG: 'manage_config',\r\n  MANAGE_API_KEY: 'manage_api_key',\r\n} as const;\r\n\r\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\r\n\r\nexport const ROLE_PERMISSIONS: Record<Role, Permission[]> = {\r\n  [ROLES.EMPEROR]: Object.values(PERMISSIONS),\r\n  [ROLES.DUKE]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_API_KEY,\r\n  ],\r\n  [ROLES.KNIGHT]: [\r\n    PERMISSIONS.<PERSON>NAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n  ],\r\n  [ROLES.CIVILIAN]: [],\r\n} as const;\r\n\r\nexport function hasPermission(userRoles: Role[], permission: Permission): boolean {\r\n  return userRoles.some(role => ROLE_PERMISSIONS[role]?.includes(permission));\r\n} ", "import { integer, sqliteTable, text, primaryKey, uniqueIndex, index } from \"drizzle-orm/sqlite-core\"\r\nimport type { AdapterAccountType } from \"next-auth/adapters\"\r\nimport { relations } from 'drizzle-orm';\r\n\r\n// https://authjs.dev/getting-started/adapters/drizzle\r\nexport const users = sqliteTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: integer(\"emailVerified\", { mode: \"timestamp_ms\" }),\r\n  image: text(\"image\"),\r\n  username: text(\"username\").unique(),\r\n  password: text(\"password\"),\r\n})\r\nexport const accounts = sqliteTable(\r\n  \"account\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => users.id, { onDelete: \"cascade\" }),\r\n    type: text(\"type\").$type<AdapterAccountType>().notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n  },\r\n  (account) => ({\r\n    compoundKey: primaryKey({\r\n      columns: [account.provider, account.providerAccountId],\r\n    }),\r\n  })\r\n)\r\n\r\nexport const emails = sqliteTable(\"email\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  address: text(\"address\").notNull().unique(),\r\n  userId: text(\"userId\").references(() => users.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp_ms\" }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"email_expires_at_idx\").on(table.expiresAt),\r\n}))\r\n\r\nexport const messages = sqliteTable(\"message\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  emailId: text(\"emailId\")\r\n    .notNull()\r\n    .references(() => emails.id, { onDelete: \"cascade\" }),\r\n  fromAddress: text(\"from_address\").notNull(),\r\n  subject: text(\"subject\").notNull(),\r\n  content: text(\"content\").notNull(),\r\n  html: text(\"html\"),\r\n  receivedAt: integer(\"received_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  emailIdIdx: index(\"message_email_id_idx\").on(table.emailId),\r\n}))\r\n\r\nexport const webhooks = sqliteTable('webhook', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id')\r\n    .notNull()\r\n    .references(() => users.id, { onDelete: \"cascade\" }),\r\n  url: text('url').notNull(),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n})\r\n\r\nexport const roles = sqliteTable(\"role\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\").notNull(),\r\n  description: text(\"description\"),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n});\r\n\r\nexport const userRoles = sqliteTable(\"user_role\", {\r\n  userId: text(\"user_id\").notNull().references(() => users.id, { onDelete: \"cascade\" }),\r\n  roleId: text(\"role_id\").notNull().references(() => roles.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  pk: primaryKey({ columns: [table.userId, table.roleId] }),\r\n}));\r\n\r\nexport const apiKeys = sqliteTable('api_keys', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id),\r\n  name: text('name').notNull(),\r\n  key: text('key').notNull().unique(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n}, (table) => ({\r\n  nameUserIdUnique: uniqueIndex('name_user_id_unique').on(table.name, table.userId)\r\n}));\r\n\r\nexport const revoked_credentials = sqliteTable('revoked_credential', {\r\n  jti: text('jti').primaryKey(),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp_ms' }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"revoked_expires_at_idx\").on(table.expiresAt),\r\n}));\r\n\r\nexport const apiKeysRelations = relations(apiKeys, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [apiKeys.userId],\r\n    references: [users.id],\r\n  }),\r\n}));\r\n\r\nexport const userRolesRelations = relations(userRoles, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userRoles.userId],\r\n    references: [users.id],\r\n  }),\r\n  role: one(roles, {\r\n    fields: [userRoles.roleId],\r\n    references: [roles.id],\r\n  }),\r\n}));\r\n\r\nexport const usersRelations = relations(users, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n  apiKeys: many(apiKeys),\r\n}));\r\n\r\nexport const rolesRelations = relations(roles, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n}));", "import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport async function hashPassword(password: string): Promise<string> {\r\n  const encoder = new TextEncoder()\r\n  const salt = process.env.AUTH_SECRET || ''\r\n  const data = encoder.encode(password + salt)\r\n  const hash = await crypto.subtle.digest('SHA-256', data)\r\n  return btoa(String.fromCharCode(...new Uint8Array(hash)))\r\n}\r\n\r\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\r\n  const hash = await hashPassword(password)\r\n  return hash === hashedPassword\r\n}", "import { z } from \"zod\"\r\n\r\nexport const authSchema = z.object({\r\n  username: z.string()\r\n    .min(1, \"用户名不能为空\")\r\n    .max(20, \"用户名不能超过20个字符\")\r\n    .regex(/^[a-zA-Z0-9_-]+$/, \"用户名只能包含字母、数字、下划线和横杠\")\r\n    .refine(val => !val.includes('@'), \"用户名不能是邮箱格式\"),\r\n  password: z.string()\r\n    .min(8, \"密码长度必须大于等于8位\")\r\n})\r\n\r\nexport type AuthSchema = z.infer<typeof authSchema>"], "names": ["runtime", "GET", "env", "getRequestContext", "defaultRole", "emailDomains", "adminContact", "maxEmails", "Promise", "all", "SITE_CONFIG", "get", "Response", "json", "ROLES", "CIVILIAN", "EMAIL_CONFIG", "MAX_ACTIVE_EMAILS", "toString", "POST", "request", "checkPermission", "PERMISSIONS", "MANAGE_CONFIG", "error", "status", "DUKE", "KNIGHT", "includes", "put", "success", "POLL_INTERVAL", "MAX_RETRIES", "TIMEOUT", "RETRY_DELAY", "EVENTS", "NEW_MESSAGE", "getUserId", "userId", "headersList", "headers", "session", "auth", "user", "id", "COLORS", "ROLE_DESCRIPTIONS", "EMPEROR", "getDefaultRole", "findOrCreateRole", "db", "<PERSON><PERSON><PERSON>", "role", "query", "roles", "<PERSON><PERSON><PERSON><PERSON>", "where", "eq", "name", "newRole", "insert", "values", "description", "returning", "assignRoleToUser", "roleId", "delete", "userRoles", "getUserRole", "createDb", "userRoleRecords", "find<PERSON>any", "with", "permission", "userRoleNames", "map", "ur", "hasPermission", "handlers", "signIn", "signOut", "NextAuth", "secret", "process", "AUTH_SECRET", "adapter", "DrizzleAdapter", "usersTable", "users", "accountsTable", "accounts", "providers", "GitHub", "clientId", "AUTH_GITHUB_ID", "clientSecret", "AUTH_GITHUB_SECRET", "CredentialsProvider", "credentials", "username", "label", "type", "placeholder", "password", "authorize", "authSchema", "parse", "comparePassword", "undefined", "events", "existingRole", "console", "callbacks", "jwt", "token", "image", "generateAvatarUrl", "initial", "toUpperCase", "colorIndex", "Array", "from", "reduce", "acc", "char", "charCodeAt", "length", "backgroundColor", "svg", "trim", "bytes", "TextEncoder", "encode", "base64", "<PERSON><PERSON><PERSON>", "createdAt", "Date", "strategy", "register", "existing", "hashedPassword", "hashPassword", "drizzle", "DB", "schema", "MANAGE_EMAIL", "MANAGE_WEBHOOK", "PROMOTE_USER", "MANAGE_API_KEY", "Object", "some", "ROLE_PERMISSIONS", "sqliteTable", "text", "<PERSON><PERSON><PERSON>", "$defaultFn", "crypto", "randomUUID", "email", "unique", "emailVerified", "integer", "mode", "notNull", "references", "onDelete", "$type", "provider", "providerAccountId", "refresh_token", "access_token", "expires_at", "token_type", "scope", "id_token", "session_state", "<PERSON><PERSON><PERSON>", "columns", "account", "emails", "address", "expiresAt", "expiresAtIdx", "index", "on", "table", "messages", "emailId", "fromAddress", "subject", "content", "html", "receivedAt", "emailIdIdx", "webhooks", "url", "enabled", "default", "updatedAt", "pk", "key", "nameUserIdUnique", "uniqueIndex", "revoked_credentials", "jti", "apiKeysRelations", "relations", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "one", "fields", "userRolesRelations", "usersRelations", "many", "rolesRelations", "cn", "inputs", "twMerge", "clsx", "encoder", "salt", "data", "btoa", "String", "fromCharCode", "Uint8Array", "hash", "subtle", "digest", "z", "min", "max", "regex", "refine", "val"], "sourceRoot": "", "ignoreList": []}