{"version": 3, "sources": ["../../../../src/client/components/router-reducer/ppr-navigations.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../../../server/app-render/types'\nimport type {\n  CacheNode,\n  ChildSegmentMap,\n  ReadyCacheNode,\n} from '../../../shared/lib/app-router-context.shared-runtime'\nimport { DEFAULT_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport { matchSegment } from '../match-segments'\nimport { createRouter<PERSON>ache<PERSON>ey } from './create-router-cache-key'\nimport type { FetchServerResponseResult } from './fetch-server-response'\n\n// This is yet another tree type that is used to track pending promises that\n// need to be fulfilled once the dynamic data is received. The terminal nodes of\n// this tree represent the new Cache Node trees that were created during this\n// request. We can't use the Cache Node tree or Route State tree directly\n// because those include reused nodes, too. This tree is discarded as soon as\n// the navigation response is received.\nexport type Task = {\n  // The router state that corresponds to the tree that this Task represents.\n  route: FlightRouterState\n  // The CacheNode that corresponds to the tree that this Task represents. If\n  // `children` is null (i.e. if this is a terminal task node), then `node`\n  // represents a brand new Cache Node tree, which way or may not need to be\n  // filled with dynamic data from the server.\n  node: CacheNode | null\n  // Whether anything in this tree contains dynamic holes that need to be filled\n  // by the server.\n  needsDynamicRequest: boolean\n  children: Map<string, Task> | null\n}\n\n// Creates a new Cache Node tree (i.e. copy-on-write) that represents the\n// optimistic result of a navigation, using both the current Cache Node tree and\n// data that was prefetched prior to navigation.\n//\n// At the moment we call this function, we haven't yet received the navigation\n// response from the server. It could send back something completely different\n// from the tree that was prefetched — due to rewrites, default routes, parallel\n// routes, etc.\n//\n// But in most cases, it will return the same tree that we prefetched, just with\n// the dynamic holes filled in. So we optimistically assume this will happen,\n// and accept that the real result could be arbitrarily different.\n//\n// We'll reuse anything that was already in the previous tree, since that's what\n// the server does.\n//\n// New segments (ones that don't appear in the old tree) are assigned an\n// unresolved promise. The data for these promises will be fulfilled later, when\n// the navigation response is received.\n//\n// The tree can be rendered immediately after it is created (that's why this is\n// a synchronous function). Any new trees that do not have prefetch data will\n// suspend during rendering, until the dynamic data streams in.\n//\n// Returns a Task object, which contains both the updated Cache Node and a path\n// to the pending subtrees that need to be resolved by the navigation response.\n//\n// A return value of `null` means there were no changes, and the previous tree\n// can be reused without initiating a server request.\nexport function updateCacheNodeOnNavigation(\n  oldCacheNode: CacheNode,\n  oldRouterState: FlightRouterState,\n  newRouterState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: React.ReactNode | null,\n  isPrefetchHeadPartial: boolean\n): Task | null {\n  // Diff the old and new trees to reuse the shared layouts.\n  const oldRouterStateChildren = oldRouterState[1]\n  const newRouterStateChildren = newRouterState[1]\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n\n  const oldParallelRoutes = oldCacheNode.parallelRoutes\n\n  // Clone the current set of segment children, even if they aren't active in\n  // the new tree.\n  // TODO: We currently retain all the inactive segments indefinitely, until\n  // there's an explicit refresh, or a parent layout is lazily refreshed. We\n  // rely on this for popstate navigations, which update the Router State Tree\n  // but do not eagerly perform a data fetch, because they expect the segment\n  // data to already be in the Cache Node tree. For highly static sites that\n  // are mostly read-only, this may happen only rarely, causing memory to\n  // leak. We should figure out a better model for the lifetime of inactive\n  // segments, so we can maintain instant back/forward navigations without\n  // leaking memory indefinitely.\n  const prefetchParallelRoutes = new Map(oldParallelRoutes)\n\n  // As we diff the trees, we may sometimes modify (copy-on-write, not mutate)\n  // the Route Tree that was returned by the server — for example, in the case\n  // of default parallel routes, we preserve the currently active segment. To\n  // avoid mutating the original tree, we clone the router state children along\n  // the return path.\n  let patchedRouterStateChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n  let taskChildren = null\n\n  // Most navigations require a request to fetch additional data from the\n  // server, either because the data was not already prefetched, or because the\n  // target route contains dynamic data that cannot be prefetched.\n  //\n  // However, if the target route is fully static, and it's already completely\n  // loaded into the segment cache, then we can skip the server request.\n  //\n  // This starts off as `false`, and is set to `true` if any of the child\n  // routes requires a dynamic request.\n  let needsDynamicRequest = false\n\n  for (let parallelRouteKey in newRouterStateChildren) {\n    const newRouterStateChild: FlightRouterState =\n      newRouterStateChildren[parallelRouteKey]\n    const oldRouterStateChild: FlightRouterState | void =\n      oldRouterStateChildren[parallelRouteKey]\n    const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey)\n    const prefetchDataChild: CacheNodeSeedData | void | null =\n      prefetchDataChildren !== null\n        ? prefetchDataChildren[parallelRouteKey]\n        : null\n\n    const newSegmentChild = newRouterStateChild[0]\n    const newSegmentKeyChild = createRouterCacheKey(newSegmentChild)\n\n    const oldSegmentChild =\n      oldRouterStateChild !== undefined ? oldRouterStateChild[0] : undefined\n\n    const oldCacheNodeChild =\n      oldSegmentMapChild !== undefined\n        ? oldSegmentMapChild.get(newSegmentKeyChild)\n        : undefined\n\n    let taskChild: Task | null\n    if (newSegmentChild === DEFAULT_SEGMENT_KEY) {\n      // This is another kind of leaf segment — a default route.\n      //\n      // Default routes have special behavior. When there's no matching segment\n      // for a parallel route, Next.js preserves the currently active segment\n      // during a client navigation — but not for initial render. The server\n      // leaves it to the client to account for this. So we need to handle\n      // it here.\n      if (oldRouterStateChild !== undefined) {\n        // Reuse the existing Router State for this segment. We spawn a \"task\"\n        // just to keep track of the updated router state; unlike most, it's\n        // already fulfilled and won't be affected by the dynamic response.\n        taskChild = spawnReusedTask(oldRouterStateChild)\n      } else {\n        // There's no currently active segment. Switch to the \"create\" path.\n        taskChild = createCacheNodeOnNavigation(\n          newRouterStateChild,\n          prefetchDataChild !== undefined ? prefetchDataChild : null,\n          prefetchHead,\n          isPrefetchHeadPartial\n        )\n      }\n    } else if (\n      oldSegmentChild !== undefined &&\n      matchSegment(newSegmentChild, oldSegmentChild)\n    ) {\n      if (\n        oldCacheNodeChild !== undefined &&\n        oldRouterStateChild !== undefined\n      ) {\n        // This segment exists in both the old and new trees. Recursively update\n        // the children.\n        taskChild = updateCacheNodeOnNavigation(\n          oldCacheNodeChild,\n          oldRouterStateChild,\n          newRouterStateChild,\n          prefetchDataChild,\n          prefetchHead,\n          isPrefetchHeadPartial\n        )\n      } else {\n        // Either there's no existing Cache Node for this segment, or this\n        // segment doesn't exist in the old Router State tree. Switch to the\n        // \"create\" path.\n        taskChild = createCacheNodeOnNavigation(\n          newRouterStateChild,\n          prefetchDataChild !== undefined ? prefetchDataChild : null,\n          prefetchHead,\n          isPrefetchHeadPartial\n        )\n      }\n    } else {\n      // This is a new tree. Switch to the \"create\" path.\n      taskChild = createCacheNodeOnNavigation(\n        newRouterStateChild,\n        prefetchDataChild !== undefined ? prefetchDataChild : null,\n        prefetchHead,\n        isPrefetchHeadPartial\n      )\n    }\n\n    if (taskChild !== null) {\n      // Something changed in the child tree. Keep track of the child task.\n      if (taskChildren === null) {\n        taskChildren = new Map()\n      }\n      taskChildren.set(parallelRouteKey, taskChild)\n      const newCacheNodeChild = taskChild.node\n      if (newCacheNodeChild !== null) {\n        const newSegmentMapChild: ChildSegmentMap = new Map(oldSegmentMapChild)\n        newSegmentMapChild.set(newSegmentKeyChild, newCacheNodeChild)\n        prefetchParallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n      }\n\n      if (taskChild.needsDynamicRequest) {\n        needsDynamicRequest = true\n      }\n\n      // The child tree's route state may be different from the prefetched\n      // route sent by the server. We need to clone it as we traverse back up\n      // the tree.\n      patchedRouterStateChildren[parallelRouteKey] = taskChild.route\n    } else {\n      // The child didn't change. We can use the prefetched router state.\n      patchedRouterStateChildren[parallelRouteKey] = newRouterStateChild\n    }\n  }\n\n  if (taskChildren === null) {\n    // No new tasks were spawned.\n    return null\n  }\n\n  const newCacheNode: ReadyCacheNode = {\n    lazyData: null,\n    rsc: oldCacheNode.rsc,\n    // We intentionally aren't updating the prefetchRsc field, since this node\n    // is already part of the current tree, because it would be weird for\n    // prefetch data to be newer than the final data. It probably won't ever be\n    // observable anyway, but it could happen if the segment is unmounted then\n    // mounted again, because LayoutRouter will momentarily switch to rendering\n    // prefetchRsc, via useDeferredValue.\n    prefetchRsc: oldCacheNode.prefetchRsc,\n    head: oldCacheNode.head,\n    prefetchHead: oldCacheNode.prefetchHead,\n    loading: oldCacheNode.loading,\n\n    // Everything is cloned except for the children, which we computed above.\n    parallelRoutes: prefetchParallelRoutes,\n  }\n\n  return {\n    // Return a cloned copy of the router state with updated children.\n    route: patchRouterStateWithNewChildren(\n      newRouterState,\n      patchedRouterStateChildren\n    ),\n    node: newCacheNode,\n    needsDynamicRequest,\n    children: taskChildren,\n  }\n}\n\nfunction createCacheNodeOnNavigation(\n  routerState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  possiblyPartialPrefetchHead: React.ReactNode | null,\n  isPrefetchHeadPartial: boolean\n): Task {\n  // Same traversal as updateCacheNodeNavigation, but we switch to this path\n  // once we reach the part of the tree that was not in the previous route. We\n  // don't need to diff against the old tree, we just need to create a new one.\n  if (prefetchData === null) {\n    // There's no prefetch for this segment. Everything from this point will be\n    // requested from the server, even if there are static children below it.\n    // Create a terminal task node that will later be fulfilled by\n    // server response.\n    return spawnPendingTask(\n      routerState,\n      null,\n      possiblyPartialPrefetchHead,\n      isPrefetchHeadPartial\n    )\n  }\n\n  const routerStateChildren = routerState[1]\n  const isPrefetchRscPartial = prefetchData[4]\n\n  // The head is assigned to every leaf segment delivered by the server. Based\n  // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n  const isLeafSegment = Object.keys(routerStateChildren).length === 0\n\n  // If prefetch data is available for a segment, and it's fully static (i.e.\n  // does not contain any dynamic holes), we don't need to request it from\n  // the server.\n  if (\n    // Check if the segment data is partial\n    isPrefetchRscPartial ||\n    // Check if the head is partial (only relevant if this is a leaf segment)\n    (isPrefetchHeadPartial && isLeafSegment)\n  ) {\n    // We only have partial data from this segment. Like missing segments, we\n    // must request the full data from the server.\n    return spawnPendingTask(\n      routerState,\n      prefetchData,\n      possiblyPartialPrefetchHead,\n      isPrefetchHeadPartial\n    )\n  }\n\n  // The prefetched segment is fully static, so we don't need to request a new\n  // one from the server. Keep traversing down the tree until we reach something\n  // that requires a dynamic request.\n  const prefetchDataChildren = prefetchData[2]\n  const taskChildren = new Map()\n  const cacheNodeChildren = new Map()\n  let needsDynamicRequest = false\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const prefetchDataChild: CacheNodeSeedData | void | null =\n      prefetchDataChildren !== null\n        ? prefetchDataChildren[parallelRouteKey]\n        : null\n    const segmentChild = routerStateChild[0]\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n    const taskChild = createCacheNodeOnNavigation(\n      routerStateChild,\n      prefetchDataChild,\n      possiblyPartialPrefetchHead,\n      isPrefetchHeadPartial\n    )\n    taskChildren.set(parallelRouteKey, taskChild)\n    if (taskChild.needsDynamicRequest) {\n      needsDynamicRequest = true\n    }\n    const newCacheNodeChild = taskChild.node\n    if (newCacheNodeChild !== null) {\n      const newSegmentMapChild: ChildSegmentMap = new Map()\n      newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n      cacheNodeChildren.set(parallelRouteKey, newSegmentMapChild)\n    }\n  }\n\n  const rsc = prefetchData[1]\n  const loading = prefetchData[3]\n  return {\n    route: routerState,\n    node: {\n      lazyData: null,\n      // Since this is a fully static segment, we don't need to use the\n      // `prefetchRsc` field.\n      rsc,\n      prefetchRsc: null,\n      head: isLeafSegment ? possiblyPartialPrefetchHead : null,\n      prefetchHead: null,\n      loading,\n      parallelRoutes: cacheNodeChildren,\n    },\n    needsDynamicRequest,\n    children: taskChildren,\n  }\n}\n\nfunction patchRouterStateWithNewChildren(\n  baseRouterState: FlightRouterState,\n  newChildren: { [parallelRouteKey: string]: FlightRouterState }\n): FlightRouterState {\n  const clone: FlightRouterState = [baseRouterState[0], newChildren]\n  // Based on equivalent logic in apply-router-state-patch-to-tree, but should\n  // confirm whether we need to copy all of these fields. Not sure the server\n  // ever sends, e.g. the refetch marker.\n  if (2 in baseRouterState) {\n    clone[2] = baseRouterState[2]\n  }\n  if (3 in baseRouterState) {\n    clone[3] = baseRouterState[3]\n  }\n  if (4 in baseRouterState) {\n    clone[4] = baseRouterState[4]\n  }\n  return clone\n}\n\nfunction spawnPendingTask(\n  routerState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: React.ReactNode | null,\n  isPrefetchHeadPartial: boolean\n): Task {\n  // Create a task that will later be fulfilled by data from the server.\n  const newTask: Task = {\n    route: routerState,\n\n    // Corresponds to the part of the route that will be rendered on the server.\n    node: createPendingCacheNode(\n      routerState,\n      prefetchData,\n      prefetchHead,\n      isPrefetchHeadPartial\n    ),\n    // Set this to true to indicate that this tree is missing data. This will\n    // be propagated to all the parent tasks.\n    needsDynamicRequest: true,\n    children: null,\n  }\n  return newTask\n}\n\nfunction spawnReusedTask(reusedRouterState: FlightRouterState): Task {\n  // Create a task that reuses an existing segment, e.g. when reusing\n  // the current active segment in place of a default route.\n  return {\n    route: reusedRouterState,\n    node: null,\n    needsDynamicRequest: false,\n    children: null,\n  }\n}\n\n// Writes a dynamic server response into the tree created by\n// updateCacheNodeOnNavigation. All pending promises that were spawned by the\n// navigation will be resolved, either with dynamic data from the server, or\n// `null` to indicate that the data is missing.\n//\n// A `null` value will trigger a lazy fetch during render, which will then patch\n// up the tree using the same mechanism as the non-PPR implementation\n// (serverPatchReducer).\n//\n// Usually, the server will respond with exactly the subset of data that we're\n// waiting for — everything below the nearest shared layout. But technically,\n// the server can return anything it wants.\n//\n// This does _not_ create a new tree; it modifies the existing one in place.\n// Which means it must follow the Suspense rules of cache safety.\nexport function listenForDynamicRequest(\n  task: Task,\n  responsePromise: Promise<FetchServerResponseResult>\n) {\n  responsePromise.then(\n    ({ flightData }: FetchServerResponseResult) => {\n      if (typeof flightData === 'string') {\n        // Happens when navigating to page in `pages` from `app`. We shouldn't\n        // get here because should have already handled this during\n        // the prefetch.\n        return\n      }\n      for (const normalizedFlightData of flightData) {\n        const {\n          segmentPath,\n          tree: serverRouterState,\n          seedData: dynamicData,\n          head: dynamicHead,\n        } = normalizedFlightData\n\n        if (!dynamicData) {\n          // This shouldn't happen. PPR should always send back a response.\n          // However, `FlightDataPath` is a shared type and the pre-PPR handling of\n          // this might return null.\n          continue\n        }\n\n        writeDynamicDataIntoPendingTask(\n          task,\n          segmentPath,\n          serverRouterState,\n          dynamicData,\n          dynamicHead\n        )\n      }\n\n      // Now that we've exhausted all the data we received from the server, if\n      // there are any remaining pending tasks in the tree, abort them now.\n      // If there's any missing data, it will trigger a lazy fetch.\n      abortTask(task, null)\n    },\n    (error: any) => {\n      // This will trigger an error during render\n      abortTask(task, error)\n    }\n  )\n}\n\nfunction writeDynamicDataIntoPendingTask(\n  rootTask: Task,\n  segmentPath: FlightSegmentPath,\n  serverRouterState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: React.ReactNode\n) {\n  // The data sent by the server represents only a subtree of the app. We need\n  // to find the part of the task tree that matches the server response, and\n  // fulfill it using the dynamic data.\n  //\n  // segmentPath represents the parent path of subtree. It's a repeating pattern\n  // of parallel route key and segment:\n  //\n  //   [string, Segment, string, Segment, string, Segment, ...]\n  //\n  // Iterate through the path and finish any tasks that match this payload.\n  let task = rootTask\n  for (let i = 0; i < segmentPath.length; i += 2) {\n    const parallelRouteKey: string = segmentPath[i]\n    const segment: Segment = segmentPath[i + 1]\n    const taskChildren = task.children\n    if (taskChildren !== null) {\n      const taskChild = taskChildren.get(parallelRouteKey)\n      if (taskChild !== undefined) {\n        const taskSegment = taskChild.route[0]\n        if (matchSegment(segment, taskSegment)) {\n          // Found a match for this task. Keep traversing down the task tree.\n          task = taskChild\n          continue\n        }\n      }\n    }\n    // We didn't find a child task that matches the server data. Exit. We won't\n    // abort the task, though, because a different FlightDataPath may be able to\n    // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n    // once we've run out of data.\n    return\n  }\n\n  finishTaskUsingDynamicDataPayload(\n    task,\n    serverRouterState,\n    dynamicData,\n    dynamicHead\n  )\n}\n\nfunction finishTaskUsingDynamicDataPayload(\n  task: Task,\n  serverRouterState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: React.ReactNode\n) {\n  if (!task.needsDynamicRequest) {\n    // Everything in this subtree is already complete. Bail out.\n    return\n  }\n\n  // dynamicData may represent a larger subtree than the task. Before we can\n  // finish the task, we need to line them up.\n  const taskChildren = task.children\n  const taskNode = task.node\n  if (taskChildren === null) {\n    // We've reached the leaf node of the pending task. The server data tree\n    // lines up the pending Cache Node tree. We can now switch to the\n    // normal algorithm.\n    if (taskNode !== null) {\n      finishPendingCacheNode(\n        taskNode,\n        task.route,\n        serverRouterState,\n        dynamicData,\n        dynamicHead\n      )\n      // Set this to false to indicate that this task is now complete.\n      task.needsDynamicRequest = false\n    }\n    return\n  }\n  // The server returned more data than we need to finish the task. Skip over\n  // the extra segments until we reach the leaf task node.\n  const serverChildren = serverRouterState[1]\n  const dynamicDataChildren = dynamicData[2]\n\n  for (const parallelRouteKey in serverRouterState) {\n    const serverRouterStateChild: FlightRouterState =\n      serverChildren[parallelRouteKey]\n    const dynamicDataChild: CacheNodeSeedData | null | void =\n      dynamicDataChildren[parallelRouteKey]\n\n    const taskChild = taskChildren.get(parallelRouteKey)\n    if (taskChild !== undefined) {\n      const taskSegment = taskChild.route[0]\n      if (\n        matchSegment(serverRouterStateChild[0], taskSegment) &&\n        dynamicDataChild !== null &&\n        dynamicDataChild !== undefined\n      ) {\n        // Found a match for this task. Keep traversing down the task tree.\n        return finishTaskUsingDynamicDataPayload(\n          taskChild,\n          serverRouterStateChild,\n          dynamicDataChild,\n          dynamicHead\n        )\n      }\n    }\n    // We didn't find a child task that matches the server data. We won't abort\n    // the task, though, because a different FlightDataPath may be able to\n    // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n    // once we've run out of data.\n  }\n}\n\nfunction createPendingCacheNode(\n  routerState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: React.ReactNode | null,\n  isPrefetchHeadPartial: boolean\n): ReadyCacheNode {\n  const routerStateChildren = routerState[1]\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n\n  const parallelRoutes = new Map()\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const prefetchDataChild: CacheNodeSeedData | null | void =\n      prefetchDataChildren !== null\n        ? prefetchDataChildren[parallelRouteKey]\n        : null\n\n    const segmentChild = routerStateChild[0]\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n\n    const newCacheNodeChild = createPendingCacheNode(\n      routerStateChild,\n      prefetchDataChild === undefined ? null : prefetchDataChild,\n      prefetchHead,\n      isPrefetchHeadPartial\n    )\n\n    const newSegmentMapChild: ChildSegmentMap = new Map()\n    newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n    parallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n  }\n\n  // The head is assigned to every leaf segment delivered by the server. Based\n  // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n  const isLeafSegment = parallelRoutes.size === 0\n  const maybePrefetchRsc = prefetchData !== null ? prefetchData[1] : null\n  const maybePrefetchLoading = prefetchData !== null ? prefetchData[3] : null\n  return {\n    lazyData: null,\n    parallelRoutes: parallelRoutes,\n\n    prefetchRsc: maybePrefetchRsc !== undefined ? maybePrefetchRsc : null,\n    prefetchHead: isLeafSegment ? prefetchHead : null,\n\n    // TODO: Technically, a loading boundary could contain dynamic data. We must\n    // have separate `loading` and `prefetchLoading` fields to handle this, like\n    // we do for the segment data and head.\n    loading: maybePrefetchLoading !== undefined ? maybePrefetchLoading : null,\n\n    // Create a deferred promise. This will be fulfilled once the dynamic\n    // response is received from the server.\n    rsc: createDeferredRsc() as React.ReactNode,\n    head: isLeafSegment ? (createDeferredRsc() as React.ReactNode) : null,\n  }\n}\n\nfunction finishPendingCacheNode(\n  cacheNode: CacheNode,\n  taskState: FlightRouterState,\n  serverState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: React.ReactNode\n): void {\n  // Writes a dynamic response into an existing Cache Node tree. This does _not_\n  // create a new tree, it updates the existing tree in-place. So it must follow\n  // the Suspense rules of cache safety — it can resolve pending promises, but\n  // it cannot overwrite existing data. It can add segments to the tree (because\n  // a missing segment will cause the layout router to suspend).\n  // but it cannot delete them.\n  //\n  // We must resolve every promise in the tree, or else it will suspend\n  // indefinitely. If we did not receive data for a segment, we will resolve its\n  // data promise to `null` to trigger a lazy fetch during render.\n  const taskStateChildren = taskState[1]\n  const serverStateChildren = serverState[1]\n  const dataChildren = dynamicData[2]\n\n  // The router state that we traverse the tree with (taskState) is the same one\n  // that we used to construct the pending Cache Node tree. That way we're sure\n  // to resolve all the pending promises.\n  const parallelRoutes = cacheNode.parallelRoutes\n  for (let parallelRouteKey in taskStateChildren) {\n    const taskStateChild: FlightRouterState =\n      taskStateChildren[parallelRouteKey]\n    const serverStateChild: FlightRouterState | void =\n      serverStateChildren[parallelRouteKey]\n    const dataChild: CacheNodeSeedData | null | void =\n      dataChildren[parallelRouteKey]\n\n    const segmentMapChild = parallelRoutes.get(parallelRouteKey)\n    const taskSegmentChild = taskStateChild[0]\n    const taskSegmentKeyChild = createRouterCacheKey(taskSegmentChild)\n\n    const cacheNodeChild =\n      segmentMapChild !== undefined\n        ? segmentMapChild.get(taskSegmentKeyChild)\n        : undefined\n\n    if (cacheNodeChild !== undefined) {\n      if (\n        serverStateChild !== undefined &&\n        matchSegment(taskSegmentChild, serverStateChild[0])\n      ) {\n        if (dataChild !== undefined && dataChild !== null) {\n          // This is the happy path. Recursively update all the children.\n          finishPendingCacheNode(\n            cacheNodeChild,\n            taskStateChild,\n            serverStateChild,\n            dataChild,\n            dynamicHead\n          )\n        } else {\n          // The server never returned data for this segment. Trigger a lazy\n          // fetch during render. This shouldn't happen because the Route Tree\n          // and the Seed Data tree sent by the server should always be the same\n          // shape when part of the same server response.\n          abortPendingCacheNode(taskStateChild, cacheNodeChild, null)\n        }\n      } else {\n        // The server never returned data for this segment. Trigger a lazy\n        // fetch during render.\n        abortPendingCacheNode(taskStateChild, cacheNodeChild, null)\n      }\n    } else {\n      // The server response matches what was expected to receive, but there's\n      // no matching Cache Node in the task tree. This is a bug in the\n      // implementation because we should have created a node for every\n      // segment in the tree that's associated with this task.\n    }\n  }\n\n  // Use the dynamic data from the server to fulfill the deferred RSC promise\n  // on the Cache Node.\n  const rsc = cacheNode.rsc\n  const dynamicSegmentData = dynamicData[1]\n  if (rsc === null) {\n    // This is a lazy cache node. We can overwrite it. This is only safe\n    // because we know that the LayoutRouter suspends if `rsc` is `null`.\n    cacheNode.rsc = dynamicSegmentData\n  } else if (isDeferredRsc(rsc)) {\n    // This is a deferred RSC promise. We can fulfill it with the data we just\n    // received from the server. If it was already resolved by a different\n    // navigation, then this does nothing because we can't overwrite data.\n    rsc.resolve(dynamicSegmentData)\n  } else {\n    // This is not a deferred RSC promise, nor is it empty, so it must have\n    // been populated by a different navigation. We must not overwrite it.\n  }\n\n  // Check if this is a leaf segment. If so, it will have a `head` property with\n  // a pending promise that needs to be resolved with the dynamic head from\n  // the server.\n  const head = cacheNode.head\n  if (isDeferredRsc(head)) {\n    head.resolve(dynamicHead)\n  }\n}\n\nexport function abortTask(task: Task, error: any): void {\n  const cacheNode = task.node\n  if (cacheNode === null) {\n    // This indicates the task is already complete.\n    return\n  }\n\n  const taskChildren = task.children\n  if (taskChildren === null) {\n    // Reached the leaf task node. This is the root of a pending cache\n    // node tree.\n    abortPendingCacheNode(task.route, cacheNode, error)\n  } else {\n    // This is an intermediate task node. Keep traversing until we reach a\n    // task node with no children. That will be the root of the cache node tree\n    // that needs to be resolved.\n    for (const taskChild of taskChildren.values()) {\n      abortTask(taskChild, error)\n    }\n  }\n\n  // Set this to false to indicate that this task is now complete.\n  task.needsDynamicRequest = false\n}\n\nfunction abortPendingCacheNode(\n  routerState: FlightRouterState,\n  cacheNode: CacheNode,\n  error: any\n): void {\n  // For every pending segment in the tree, resolve its `rsc` promise to `null`\n  // to trigger a lazy fetch during render.\n  //\n  // Or, if an error object is provided, it will error instead.\n  const routerStateChildren = routerState[1]\n  const parallelRoutes = cacheNode.parallelRoutes\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const segmentMapChild = parallelRoutes.get(parallelRouteKey)\n    if (segmentMapChild === undefined) {\n      // This shouldn't happen because we're traversing the same tree that was\n      // used to construct the cache nodes in the first place.\n      continue\n    }\n    const segmentChild = routerStateChild[0]\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n    const cacheNodeChild = segmentMapChild.get(segmentKeyChild)\n    if (cacheNodeChild !== undefined) {\n      abortPendingCacheNode(routerStateChild, cacheNodeChild, error)\n    } else {\n      // This shouldn't happen because we're traversing the same tree that was\n      // used to construct the cache nodes in the first place.\n    }\n  }\n  const rsc = cacheNode.rsc\n  if (isDeferredRsc(rsc)) {\n    if (error === null) {\n      // This will trigger a lazy fetch during render.\n      rsc.resolve(null)\n    } else {\n      // This will trigger an error during rendering.\n      rsc.reject(error)\n    }\n  }\n\n  // Check if this is a leaf segment. If so, it will have a `head` property with\n  // a pending promise that needs to be resolved. If an error was provided, we\n  // will not resolve it with an error, since this is rendered at the root of\n  // the app. We want the segment to error, not the entire app.\n  const head = cacheNode.head\n  if (isDeferredRsc(head)) {\n    head.resolve(null)\n  }\n}\n\nexport function updateCacheNodeOnPopstateRestoration(\n  oldCacheNode: CacheNode,\n  routerState: FlightRouterState\n) {\n  // A popstate navigation reads data from the local cache. It does not issue\n  // new network requests (unless the cache entries have been evicted). So, we\n  // update the cache to drop the prefetch data for any segment whose dynamic\n  // data was already received. This prevents an unnecessary flash back to PPR\n  // state during a back/forward navigation.\n  //\n  // This function clones the entire cache node tree and sets the `prefetchRsc`\n  // field to `null` to prevent it from being rendered. We can't mutate the node\n  // in place because this is a concurrent data structure.\n\n  const routerStateChildren = routerState[1]\n  const oldParallelRoutes = oldCacheNode.parallelRoutes\n  const newParallelRoutes = new Map(oldParallelRoutes)\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const segmentChild = routerStateChild[0]\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n    const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey)\n    if (oldSegmentMapChild !== undefined) {\n      const oldCacheNodeChild = oldSegmentMapChild.get(segmentKeyChild)\n      if (oldCacheNodeChild !== undefined) {\n        const newCacheNodeChild = updateCacheNodeOnPopstateRestoration(\n          oldCacheNodeChild,\n          routerStateChild\n        )\n        const newSegmentMapChild = new Map(oldSegmentMapChild)\n        newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n        newParallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n      }\n    }\n  }\n\n  // Only show prefetched data if the dynamic data is still pending.\n  //\n  // Tehnically, what we're actually checking is whether the dynamic network\n  // response was received. But since it's a streaming response, this does not\n  // mean that all the dynamic data has fully streamed in. It just means that\n  // _some_ of the dynamic data was received. But as a heuristic, we assume that\n  // the rest dynamic data will stream in quickly, so it's still better to skip\n  // the prefetch state.\n  const rsc = oldCacheNode.rsc\n  const shouldUsePrefetch = isDeferredRsc(rsc) && rsc.status === 'pending'\n\n  return {\n    lazyData: null,\n    rsc,\n    head: oldCacheNode.head,\n\n    prefetchHead: shouldUsePrefetch ? oldCacheNode.prefetchHead : null,\n    prefetchRsc: shouldUsePrefetch ? oldCacheNode.prefetchRsc : null,\n    loading: oldCacheNode.loading,\n\n    // These are the cloned children we computed above\n    parallelRoutes: newParallelRoutes,\n  }\n}\n\nconst DEFERRED = Symbol()\n\ntype PendingDeferredRsc = Promise<React.ReactNode> & {\n  status: 'pending'\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype FulfilledDeferredRsc = Promise<React.ReactNode> & {\n  status: 'fulfilled'\n  value: React.ReactNode\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype RejectedDeferredRsc = Promise<React.ReactNode> & {\n  status: 'rejected'\n  reason: any\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype DeferredRsc =\n  | PendingDeferredRsc\n  | FulfilledDeferredRsc\n  | RejectedDeferredRsc\n\n// This type exists to distinguish a DeferredRsc from a Flight promise. It's a\n// compromise to avoid adding an extra field on every Cache Node, which would be\n// awkward because the pre-PPR parts of codebase would need to account for it,\n// too. We can remove it once type Cache Node type is more settled.\nfunction isDeferredRsc(value: any): value is DeferredRsc {\n  return value && value.tag === DEFERRED\n}\n\nfunction createDeferredRsc(): PendingDeferredRsc {\n  let resolve: any\n  let reject: any\n  const pendingRsc = new Promise<React.ReactNode>((res, rej) => {\n    resolve = res\n    reject = rej\n  }) as PendingDeferredRsc\n  pendingRsc.status = 'pending'\n  pendingRsc.resolve = (value: React.ReactNode) => {\n    if (pendingRsc.status === 'pending') {\n      const fulfilledRsc: FulfilledDeferredRsc = pendingRsc as any\n      fulfilledRsc.status = 'fulfilled'\n      fulfilledRsc.value = value\n      resolve(value)\n    }\n  }\n  pendingRsc.reject = (error: any) => {\n    if (pendingRsc.status === 'pending') {\n      const rejectedRsc: RejectedDeferredRsc = pendingRsc as any\n      rejectedRsc.status = 'rejected'\n      rejectedRsc.reason = error\n      reject(error)\n    }\n  }\n  pendingRsc.tag = DEFERRED\n  return pendingRsc\n}\n"], "names": ["abortTask", "listenForDynamicRequest", "updateCacheNodeOnNavigation", "updateCacheNodeOnPopstateRestoration", "oldCacheNode", "oldRouterState", "newRouterState", "prefetchData", "prefetchHead", "isPrefetchHeadPartial", "oldRouterStateChildren", "newRouterStateChildren", "prefetchDataChildren", "oldParallelRoutes", "parallelRoutes", "prefetchParallelRoutes", "Map", "patchedRouterStateChildren", "task<PERSON><PERSON><PERSON><PERSON>", "needsDynamicRequest", "parallelRouteKey", "newRouterStateChild", "oldRouterStateChild", "oldSegmentMapChild", "get", "prefetchDataChild", "newSegmentChild", "newSegmentKeyChild", "createRouterCache<PERSON>ey", "oldSegment<PERSON>hild", "undefined", "oldCacheNodeChild", "task<PERSON><PERSON><PERSON>", "DEFAULT_SEGMENT_KEY", "spawnReusedTask", "createCacheNodeOnNavigation", "matchSegment", "set", "newCacheNodeChild", "node", "newSegmentMapChild", "route", "newCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "loading", "patchRouterStateWithNewChildren", "children", "routerState", "possiblyPartialPrefetchHead", "spawnPendingTask", "routerStateChildren", "isPrefetchRscPartial", "isLeafSegment", "Object", "keys", "length", "cacheNodeChildren", "routerStateChild", "segmentChild", "segmentKeyChild", "baseRouterState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clone", "newTask", "createPendingCacheNode", "reusedRouterState", "task", "responsePromise", "then", "flightData", "normalizedFlightData", "segmentPath", "tree", "serverRouterState", "seedData", "dynamicData", "dynamicHead", "writeDynamicDataIntoPendingTask", "error", "rootTask", "i", "segment", "taskSegment", "finishTaskUsingDynamicDataPayload", "taskNode", "finishPendingCacheNode", "serverChildren", "dynamicDataChildren", "serverRouterStateChild", "dynamicDataChild", "size", "maybePrefetchRsc", "maybePrefetchLoading", "createDeferredRsc", "cacheNode", "taskState", "serverState", "taskStateChildren", "serverStateChildren", "dataChildren", "taskStateChild", "serverStateChild", "dataChild", "segmentMapChild", "taskSegmentChild", "taskSegmentKeyChild", "cacheNodeChild", "abortPendingCacheNode", "dynamicSegmentData", "isDeferredRsc", "resolve", "values", "reject", "newParallelRoutes", "shouldUsePrefetch", "status", "DEFERRED", "Symbol", "value", "tag", "pendingRsc", "Promise", "res", "rej", "fulfilledRsc", "rejectedRsc", "reason"], "mappings": ";;;;;;;;;;;;;;;;;IAovBgBA,SAAS;eAATA;;IAnUAC,uBAAuB;eAAvBA;;IAhXAC,2BAA2B;eAA3BA;;IA+vBAC,oCAAoC;eAApCA;;;yBArzBoB;+BACP;sCACQ;AAoD9B,SAASD,4BACdE,YAAuB,EACvBC,cAAiC,EACjCC,cAAiC,EACjCC,YAAsC,EACtCC,YAAoC,EACpCC,qBAA8B;IAE9B,0DAA0D;IAC1D,MAAMC,yBAAyBL,cAAc,CAAC,EAAE;IAChD,MAAMM,yBAAyBL,cAAc,CAAC,EAAE;IAChD,MAAMM,uBAAuBL,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IAEvE,MAAMM,oBAAoBT,aAAaU,cAAc;IAErD,2EAA2E;IAC3E,gBAAgB;IAChB,0EAA0E;IAC1E,0EAA0E;IAC1E,4EAA4E;IAC5E,2EAA2E;IAC3E,0EAA0E;IAC1E,uEAAuE;IACvE,yEAAyE;IACzE,wEAAwE;IACxE,+BAA+B;IAC/B,MAAMC,yBAAyB,IAAIC,IAAIH;IAEvC,4EAA4E;IAC5E,4EAA4E;IAC5E,2EAA2E;IAC3E,6EAA6E;IAC7E,mBAAmB;IACnB,IAAII,6BAEA,CAAC;IACL,IAAIC,eAAe;IAEnB,uEAAuE;IACvE,6EAA6E;IAC7E,gEAAgE;IAChE,EAAE;IACF,4EAA4E;IAC5E,sEAAsE;IACtE,EAAE;IACF,uEAAuE;IACvE,qCAAqC;IACrC,IAAIC,sBAAsB;IAE1B,IAAK,IAAIC,oBAAoBT,uBAAwB;QACnD,MAAMU,sBACJV,sBAAsB,CAACS,iBAAiB;QAC1C,MAAME,sBACJZ,sBAAsB,CAACU,iBAAiB;QAC1C,MAAMG,qBAAqBV,kBAAkBW,GAAG,CAACJ;QACjD,MAAMK,oBACJb,yBAAyB,OACrBA,oBAAoB,CAACQ,iBAAiB,GACtC;QAEN,MAAMM,kBAAkBL,mBAAmB,CAAC,EAAE;QAC9C,MAAMM,qBAAqBC,IAAAA,0CAAoB,EAACF;QAEhD,MAAMG,kBACJP,wBAAwBQ,YAAYR,mBAAmB,CAAC,EAAE,GAAGQ;QAE/D,MAAMC,oBACJR,uBAAuBO,YACnBP,mBAAmBC,GAAG,CAACG,sBACvBG;QAEN,IAAIE;QACJ,IAAIN,oBAAoBO,4BAAmB,EAAE;YAC3C,0DAA0D;YAC1D,EAAE;YACF,yEAAyE;YACzE,uEAAuE;YACvE,sEAAsE;YACtE,oEAAoE;YACpE,WAAW;YACX,IAAIX,wBAAwBQ,WAAW;gBACrC,sEAAsE;gBACtE,oEAAoE;gBACpE,mEAAmE;gBACnEE,YAAYE,gBAAgBZ;YAC9B,OAAO;gBACL,oEAAoE;gBACpEU,YAAYG,4BACVd,qBACAI,sBAAsBK,YAAYL,oBAAoB,MACtDjB,cACAC;YAEJ;QACF,OAAO,IACLoB,oBAAoBC,aACpBM,IAAAA,2BAAY,EAACV,iBAAiBG,kBAC9B;YACA,IACEE,sBAAsBD,aACtBR,wBAAwBQ,WACxB;gBACA,wEAAwE;gBACxE,gBAAgB;gBAChBE,YAAY9B,4BACV6B,mBACAT,qBACAD,qBACAI,mBACAjB,cACAC;YAEJ,OAAO;gBACL,kEAAkE;gBAClE,oEAAoE;gBACpE,iBAAiB;gBACjBuB,YAAYG,4BACVd,qBACAI,sBAAsBK,YAAYL,oBAAoB,MACtDjB,cACAC;YAEJ;QACF,OAAO;YACL,mDAAmD;YACnDuB,YAAYG,4BACVd,qBACAI,sBAAsBK,YAAYL,oBAAoB,MACtDjB,cACAC;QAEJ;QAEA,IAAIuB,cAAc,MAAM;YACtB,qEAAqE;YACrE,IAAId,iBAAiB,MAAM;gBACzBA,eAAe,IAAIF;YACrB;YACAE,aAAamB,GAAG,CAACjB,kBAAkBY;YACnC,MAAMM,oBAAoBN,UAAUO,IAAI;YACxC,IAAID,sBAAsB,MAAM;gBAC9B,MAAME,qBAAsC,IAAIxB,IAAIO;gBACpDiB,mBAAmBH,GAAG,CAACV,oBAAoBW;gBAC3CvB,uBAAuBsB,GAAG,CAACjB,kBAAkBoB;YAC/C;YAEA,IAAIR,UAAUb,mBAAmB,EAAE;gBACjCA,sBAAsB;YACxB;YAEA,oEAAoE;YACpE,uEAAuE;YACvE,YAAY;YACZF,0BAA0B,CAACG,iBAAiB,GAAGY,UAAUS,KAAK;QAChE,OAAO;YACL,mEAAmE;YACnExB,0BAA0B,CAACG,iBAAiB,GAAGC;QACjD;IACF;IAEA,IAAIH,iBAAiB,MAAM;QACzB,6BAA6B;QAC7B,OAAO;IACT;IAEA,MAAMwB,eAA+B;QACnCC,UAAU;QACVC,KAAKxC,aAAawC,GAAG;QACrB,0EAA0E;QAC1E,qEAAqE;QACrE,2EAA2E;QAC3E,0EAA0E;QAC1E,2EAA2E;QAC3E,qCAAqC;QACrCC,aAAazC,aAAayC,WAAW;QACrCC,MAAM1C,aAAa0C,IAAI;QACvBtC,cAAcJ,aAAaI,YAAY;QACvCuC,SAAS3C,aAAa2C,OAAO;QAE7B,yEAAyE;QACzEjC,gBAAgBC;IAClB;IAEA,OAAO;QACL,kEAAkE;QAClE0B,OAAOO,gCACL1C,gBACAW;QAEFsB,MAAMG;QACNvB;QACA8B,UAAU/B;IACZ;AACF;AAEA,SAASiB,4BACPe,WAA8B,EAC9B3C,YAAsC,EACtC4C,2BAAmD,EACnD1C,qBAA8B;IAE9B,0EAA0E;IAC1E,4EAA4E;IAC5E,6EAA6E;IAC7E,IAAIF,iBAAiB,MAAM;QACzB,2EAA2E;QAC3E,yEAAyE;QACzE,8DAA8D;QAC9D,mBAAmB;QACnB,OAAO6C,iBACLF,aACA,MACAC,6BACA1C;IAEJ;IAEA,MAAM4C,sBAAsBH,WAAW,CAAC,EAAE;IAC1C,MAAMI,uBAAuB/C,YAAY,CAAC,EAAE;IAE5C,4EAA4E;IAC5E,mEAAmE;IACnE,MAAMgD,gBAAgBC,OAAOC,IAAI,CAACJ,qBAAqBK,MAAM,KAAK;IAElE,2EAA2E;IAC3E,wEAAwE;IACxE,cAAc;IACd,IACE,uCAAuC;IACvCJ,wBACA,yEAAyE;IACxE7C,yBAAyB8C,eAC1B;QACA,yEAAyE;QACzE,8CAA8C;QAC9C,OAAOH,iBACLF,aACA3C,cACA4C,6BACA1C;IAEJ;IAEA,4EAA4E;IAC5E,8EAA8E;IAC9E,mCAAmC;IACnC,MAAMG,uBAAuBL,YAAY,CAAC,EAAE;IAC5C,MAAMW,eAAe,IAAIF;IACzB,MAAM2C,oBAAoB,IAAI3C;IAC9B,IAAIG,sBAAsB;IAC1B,IAAK,IAAIC,oBAAoBiC,oBAAqB;QAChD,MAAMO,mBACJP,mBAAmB,CAACjC,iBAAiB;QACvC,MAAMK,oBACJb,yBAAyB,OACrBA,oBAAoB,CAACQ,iBAAiB,GACtC;QACN,MAAMyC,eAAeD,gBAAgB,CAAC,EAAE;QACxC,MAAME,kBAAkBlC,IAAAA,0CAAoB,EAACiC;QAC7C,MAAM7B,YAAYG,4BAChByB,kBACAnC,mBACA0B,6BACA1C;QAEFS,aAAamB,GAAG,CAACjB,kBAAkBY;QACnC,IAAIA,UAAUb,mBAAmB,EAAE;YACjCA,sBAAsB;QACxB;QACA,MAAMmB,oBAAoBN,UAAUO,IAAI;QACxC,IAAID,sBAAsB,MAAM;YAC9B,MAAME,qBAAsC,IAAIxB;YAChDwB,mBAAmBH,GAAG,CAACyB,iBAAiBxB;YACxCqB,kBAAkBtB,GAAG,CAACjB,kBAAkBoB;QAC1C;IACF;IAEA,MAAMI,MAAMrC,YAAY,CAAC,EAAE;IAC3B,MAAMwC,UAAUxC,YAAY,CAAC,EAAE;IAC/B,OAAO;QACLkC,OAAOS;QACPX,MAAM;YACJI,UAAU;YACV,iEAAiE;YACjE,uBAAuB;YACvBC;YACAC,aAAa;YACbC,MAAMS,gBAAgBJ,8BAA8B;YACpD3C,cAAc;YACduC;YACAjC,gBAAgB6C;QAClB;QACAxC;QACA8B,UAAU/B;IACZ;AACF;AAEA,SAAS8B,gCACPe,eAAkC,EAClCC,WAA8D;IAE9D,MAAMC,QAA2B;QAACF,eAAe,CAAC,EAAE;QAAEC;KAAY;IAClE,4EAA4E;IAC5E,2EAA2E;IAC3E,uCAAuC;IACvC,IAAI,KAAKD,iBAAiB;QACxBE,KAAK,CAAC,EAAE,GAAGF,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBE,KAAK,CAAC,EAAE,GAAGF,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBE,KAAK,CAAC,EAAE,GAAGF,eAAe,CAAC,EAAE;IAC/B;IACA,OAAOE;AACT;AAEA,SAASb,iBACPF,WAA8B,EAC9B3C,YAAsC,EACtCC,YAAoC,EACpCC,qBAA8B;IAE9B,sEAAsE;IACtE,MAAMyD,UAAgB;QACpBzB,OAAOS;QAEP,4EAA4E;QAC5EX,MAAM4B,uBACJjB,aACA3C,cACAC,cACAC;QAEF,yEAAyE;QACzE,yCAAyC;QACzCU,qBAAqB;QACrB8B,UAAU;IACZ;IACA,OAAOiB;AACT;AAEA,SAAShC,gBAAgBkC,iBAAoC;IAC3D,mEAAmE;IACnE,0DAA0D;IAC1D,OAAO;QACL3B,OAAO2B;QACP7B,MAAM;QACNpB,qBAAqB;QACrB8B,UAAU;IACZ;AACF;AAiBO,SAAShD,wBACdoE,IAAU,EACVC,eAAmD;IAEnDA,gBAAgBC,IAAI,CAClB;YAAC,EAAEC,UAAU,EAA6B;QACxC,IAAI,OAAOA,eAAe,UAAU;YAClC,sEAAsE;YACtE,2DAA2D;YAC3D,gBAAgB;YAChB;QACF;QACA,KAAK,MAAMC,wBAAwBD,WAAY;YAC7C,MAAM,EACJE,WAAW,EACXC,MAAMC,iBAAiB,EACvBC,UAAUC,WAAW,EACrBhC,MAAMiC,WAAW,EAClB,GAAGN;YAEJ,IAAI,CAACK,aAAa;gBAIhB;YACF;YAEAE,gCACEX,MACAK,aACAE,mBACAE,aACAC;QAEJ;QAEA,wEAAwE;QACxE,qEAAqE;QACrE,6DAA6D;QAC7D/E,UAAUqE,MAAM;IAClB,GACA,CAACY;QACC,2CAA2C;QAC3CjF,UAAUqE,MAAMY;IAClB;AAEJ;AAEA,SAASD,gCACPE,QAAc,EACdR,WAA8B,EAC9BE,iBAAoC,EACpCE,WAA8B,EAC9BC,WAA4B;IAE5B,4EAA4E;IAC5E,0EAA0E;IAC1E,qCAAqC;IACrC,EAAE;IACF,8EAA8E;IAC9E,qCAAqC;IACrC,EAAE;IACF,6DAA6D;IAC7D,EAAE;IACF,yEAAyE;IACzE,IAAIV,OAAOa;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIT,YAAYhB,MAAM,EAAEyB,KAAK,EAAG;QAC9C,MAAM/D,mBAA2BsD,WAAW,CAACS,EAAE;QAC/C,MAAMC,UAAmBV,WAAW,CAACS,IAAI,EAAE;QAC3C,MAAMjE,eAAemD,KAAKpB,QAAQ;QAClC,IAAI/B,iBAAiB,MAAM;YACzB,MAAMc,YAAYd,aAAaM,GAAG,CAACJ;YACnC,IAAIY,cAAcF,WAAW;gBAC3B,MAAMuD,cAAcrD,UAAUS,KAAK,CAAC,EAAE;gBACtC,IAAIL,IAAAA,2BAAY,EAACgD,SAASC,cAAc;oBACtC,mEAAmE;oBACnEhB,OAAOrC;oBACP;gBACF;YACF;QACF;QACA,2EAA2E;QAC3E,4EAA4E;QAC5E,wEAAwE;QACxE,8BAA8B;QAC9B;IACF;IAEAsD,kCACEjB,MACAO,mBACAE,aACAC;AAEJ;AAEA,SAASO,kCACPjB,IAAU,EACVO,iBAAoC,EACpCE,WAA8B,EAC9BC,WAA4B;IAE5B,IAAI,CAACV,KAAKlD,mBAAmB,EAAE;QAC7B,4DAA4D;QAC5D;IACF;IAEA,0EAA0E;IAC1E,4CAA4C;IAC5C,MAAMD,eAAemD,KAAKpB,QAAQ;IAClC,MAAMsC,WAAWlB,KAAK9B,IAAI;IAC1B,IAAIrB,iBAAiB,MAAM;QACzB,wEAAwE;QACxE,iEAAiE;QACjE,oBAAoB;QACpB,IAAIqE,aAAa,MAAM;YACrBC,uBACED,UACAlB,KAAK5B,KAAK,EACVmC,mBACAE,aACAC;YAEF,gEAAgE;YAChEV,KAAKlD,mBAAmB,GAAG;QAC7B;QACA;IACF;IACA,2EAA2E;IAC3E,wDAAwD;IACxD,MAAMsE,iBAAiBb,iBAAiB,CAAC,EAAE;IAC3C,MAAMc,sBAAsBZ,WAAW,CAAC,EAAE;IAE1C,IAAK,MAAM1D,oBAAoBwD,kBAAmB;QAChD,MAAMe,yBACJF,cAAc,CAACrE,iBAAiB;QAClC,MAAMwE,mBACJF,mBAAmB,CAACtE,iBAAiB;QAEvC,MAAMY,YAAYd,aAAaM,GAAG,CAACJ;QACnC,IAAIY,cAAcF,WAAW;YAC3B,MAAMuD,cAAcrD,UAAUS,KAAK,CAAC,EAAE;YACtC,IACEL,IAAAA,2BAAY,EAACuD,sBAAsB,CAAC,EAAE,EAAEN,gBACxCO,qBAAqB,QACrBA,qBAAqB9D,WACrB;gBACA,mEAAmE;gBACnE,OAAOwD,kCACLtD,WACA2D,wBACAC,kBACAb;YAEJ;QACF;IACA,2EAA2E;IAC3E,sEAAsE;IACtE,wEAAwE;IACxE,8BAA8B;IAChC;AACF;AAEA,SAASZ,uBACPjB,WAA8B,EAC9B3C,YAAsC,EACtCC,YAAoC,EACpCC,qBAA8B;IAE9B,MAAM4C,sBAAsBH,WAAW,CAAC,EAAE;IAC1C,MAAMtC,uBAAuBL,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IAEvE,MAAMO,iBAAiB,IAAIE;IAC3B,IAAK,IAAII,oBAAoBiC,oBAAqB;QAChD,MAAMO,mBACJP,mBAAmB,CAACjC,iBAAiB;QACvC,MAAMK,oBACJb,yBAAyB,OACrBA,oBAAoB,CAACQ,iBAAiB,GACtC;QAEN,MAAMyC,eAAeD,gBAAgB,CAAC,EAAE;QACxC,MAAME,kBAAkBlC,IAAAA,0CAAoB,EAACiC;QAE7C,MAAMvB,oBAAoB6B,uBACxBP,kBACAnC,sBAAsBK,YAAY,OAAOL,mBACzCjB,cACAC;QAGF,MAAM+B,qBAAsC,IAAIxB;QAChDwB,mBAAmBH,GAAG,CAACyB,iBAAiBxB;QACxCxB,eAAeuB,GAAG,CAACjB,kBAAkBoB;IACvC;IAEA,4EAA4E;IAC5E,mEAAmE;IACnE,MAAMe,gBAAgBzC,eAAe+E,IAAI,KAAK;IAC9C,MAAMC,mBAAmBvF,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IACnE,MAAMwF,uBAAuBxF,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IACvE,OAAO;QACLoC,UAAU;QACV7B,gBAAgBA;QAEhB+B,aAAaiD,qBAAqBhE,YAAYgE,mBAAmB;QACjEtF,cAAc+C,gBAAgB/C,eAAe;QAE7C,4EAA4E;QAC5E,4EAA4E;QAC5E,uCAAuC;QACvCuC,SAASgD,yBAAyBjE,YAAYiE,uBAAuB;QAErE,qEAAqE;QACrE,wCAAwC;QACxCnD,KAAKoD;QACLlD,MAAMS,gBAAiByC,sBAA0C;IACnE;AACF;AAEA,SAASR,uBACPS,SAAoB,EACpBC,SAA4B,EAC5BC,WAA8B,EAC9BrB,WAA8B,EAC9BC,WAA4B;IAE5B,8EAA8E;IAC9E,8EAA8E;IAC9E,4EAA4E;IAC5E,8EAA8E;IAC9E,8DAA8D;IAC9D,6BAA6B;IAC7B,EAAE;IACF,qEAAqE;IACrE,8EAA8E;IAC9E,gEAAgE;IAChE,MAAMqB,oBAAoBF,SAAS,CAAC,EAAE;IACtC,MAAMG,sBAAsBF,WAAW,CAAC,EAAE;IAC1C,MAAMG,eAAexB,WAAW,CAAC,EAAE;IAEnC,8EAA8E;IAC9E,6EAA6E;IAC7E,uCAAuC;IACvC,MAAMhE,iBAAiBmF,UAAUnF,cAAc;IAC/C,IAAK,IAAIM,oBAAoBgF,kBAAmB;QAC9C,MAAMG,iBACJH,iBAAiB,CAAChF,iBAAiB;QACrC,MAAMoF,mBACJH,mBAAmB,CAACjF,iBAAiB;QACvC,MAAMqF,YACJH,YAAY,CAAClF,iBAAiB;QAEhC,MAAMsF,kBAAkB5F,eAAeU,GAAG,CAACJ;QAC3C,MAAMuF,mBAAmBJ,cAAc,CAAC,EAAE;QAC1C,MAAMK,sBAAsBhF,IAAAA,0CAAoB,EAAC+E;QAEjD,MAAME,iBACJH,oBAAoB5E,YAChB4E,gBAAgBlF,GAAG,CAACoF,uBACpB9E;QAEN,IAAI+E,mBAAmB/E,WAAW;YAChC,IACE0E,qBAAqB1E,aACrBM,IAAAA,2BAAY,EAACuE,kBAAkBH,gBAAgB,CAAC,EAAE,GAClD;gBACA,IAAIC,cAAc3E,aAAa2E,cAAc,MAAM;oBACjD,+DAA+D;oBAC/DjB,uBACEqB,gBACAN,gBACAC,kBACAC,WACA1B;gBAEJ,OAAO;oBACL,kEAAkE;oBAClE,oEAAoE;oBACpE,sEAAsE;oBACtE,+CAA+C;oBAC/C+B,sBAAsBP,gBAAgBM,gBAAgB;gBACxD;YACF,OAAO;gBACL,kEAAkE;gBAClE,uBAAuB;gBACvBC,sBAAsBP,gBAAgBM,gBAAgB;YACxD;QACF,OAAO;QACL,wEAAwE;QACxE,gEAAgE;QAChE,iEAAiE;QACjE,wDAAwD;QAC1D;IACF;IAEA,2EAA2E;IAC3E,qBAAqB;IACrB,MAAMjE,MAAMqD,UAAUrD,GAAG;IACzB,MAAMmE,qBAAqBjC,WAAW,CAAC,EAAE;IACzC,IAAIlC,QAAQ,MAAM;QAChB,oEAAoE;QACpE,qEAAqE;QACrEqD,UAAUrD,GAAG,GAAGmE;IAClB,OAAO,IAAIC,cAAcpE,MAAM;QAC7B,0EAA0E;QAC1E,sEAAsE;QACtE,sEAAsE;QACtEA,IAAIqE,OAAO,CAACF;IACd,OAAO;IACL,uEAAuE;IACvE,sEAAsE;IACxE;IAEA,8EAA8E;IAC9E,yEAAyE;IACzE,cAAc;IACd,MAAMjE,OAAOmD,UAAUnD,IAAI;IAC3B,IAAIkE,cAAclE,OAAO;QACvBA,KAAKmE,OAAO,CAAClC;IACf;AACF;AAEO,SAAS/E,UAAUqE,IAAU,EAAEY,KAAU;IAC9C,MAAMgB,YAAY5B,KAAK9B,IAAI;IAC3B,IAAI0D,cAAc,MAAM;QACtB,+CAA+C;QAC/C;IACF;IAEA,MAAM/E,eAAemD,KAAKpB,QAAQ;IAClC,IAAI/B,iBAAiB,MAAM;QACzB,kEAAkE;QAClE,aAAa;QACb4F,sBAAsBzC,KAAK5B,KAAK,EAAEwD,WAAWhB;IAC/C,OAAO;QACL,sEAAsE;QACtE,2EAA2E;QAC3E,6BAA6B;QAC7B,KAAK,MAAMjD,aAAad,aAAagG,MAAM,GAAI;YAC7ClH,UAAUgC,WAAWiD;QACvB;IACF;IAEA,gEAAgE;IAChEZ,KAAKlD,mBAAmB,GAAG;AAC7B;AAEA,SAAS2F,sBACP5D,WAA8B,EAC9B+C,SAAoB,EACpBhB,KAAU;IAEV,6EAA6E;IAC7E,yCAAyC;IACzC,EAAE;IACF,6DAA6D;IAC7D,MAAM5B,sBAAsBH,WAAW,CAAC,EAAE;IAC1C,MAAMpC,iBAAiBmF,UAAUnF,cAAc;IAC/C,IAAK,IAAIM,oBAAoBiC,oBAAqB;QAChD,MAAMO,mBACJP,mBAAmB,CAACjC,iBAAiB;QACvC,MAAMsF,kBAAkB5F,eAAeU,GAAG,CAACJ;QAC3C,IAAIsF,oBAAoB5E,WAAW;YAGjC;QACF;QACA,MAAM+B,eAAeD,gBAAgB,CAAC,EAAE;QACxC,MAAME,kBAAkBlC,IAAAA,0CAAoB,EAACiC;QAC7C,MAAMgD,iBAAiBH,gBAAgBlF,GAAG,CAACsC;QAC3C,IAAI+C,mBAAmB/E,WAAW;YAChCgF,sBAAsBlD,kBAAkBiD,gBAAgB5B;QAC1D,OAAO;QACL,wEAAwE;QACxE,wDAAwD;QAC1D;IACF;IACA,MAAMrC,MAAMqD,UAAUrD,GAAG;IACzB,IAAIoE,cAAcpE,MAAM;QACtB,IAAIqC,UAAU,MAAM;YAClB,gDAAgD;YAChDrC,IAAIqE,OAAO,CAAC;QACd,OAAO;YACL,+CAA+C;YAC/CrE,IAAIuE,MAAM,CAAClC;QACb;IACF;IAEA,8EAA8E;IAC9E,4EAA4E;IAC5E,2EAA2E;IAC3E,6DAA6D;IAC7D,MAAMnC,OAAOmD,UAAUnD,IAAI;IAC3B,IAAIkE,cAAclE,OAAO;QACvBA,KAAKmE,OAAO,CAAC;IACf;AACF;AAEO,SAAS9G,qCACdC,YAAuB,EACvB8C,WAA8B;IAE9B,2EAA2E;IAC3E,4EAA4E;IAC5E,2EAA2E;IAC3E,4EAA4E;IAC5E,0CAA0C;IAC1C,EAAE;IACF,6EAA6E;IAC7E,8EAA8E;IAC9E,wDAAwD;IAExD,MAAMG,sBAAsBH,WAAW,CAAC,EAAE;IAC1C,MAAMrC,oBAAoBT,aAAaU,cAAc;IACrD,MAAMsG,oBAAoB,IAAIpG,IAAIH;IAClC,IAAK,IAAIO,oBAAoBiC,oBAAqB;QAChD,MAAMO,mBACJP,mBAAmB,CAACjC,iBAAiB;QACvC,MAAMyC,eAAeD,gBAAgB,CAAC,EAAE;QACxC,MAAME,kBAAkBlC,IAAAA,0CAAoB,EAACiC;QAC7C,MAAMtC,qBAAqBV,kBAAkBW,GAAG,CAACJ;QACjD,IAAIG,uBAAuBO,WAAW;YACpC,MAAMC,oBAAoBR,mBAAmBC,GAAG,CAACsC;YACjD,IAAI/B,sBAAsBD,WAAW;gBACnC,MAAMQ,oBAAoBnC,qCACxB4B,mBACA6B;gBAEF,MAAMpB,qBAAqB,IAAIxB,IAAIO;gBACnCiB,mBAAmBH,GAAG,CAACyB,iBAAiBxB;gBACxC8E,kBAAkB/E,GAAG,CAACjB,kBAAkBoB;YAC1C;QACF;IACF;IAEA,kEAAkE;IAClE,EAAE;IACF,0EAA0E;IAC1E,4EAA4E;IAC5E,2EAA2E;IAC3E,8EAA8E;IAC9E,6EAA6E;IAC7E,sBAAsB;IACtB,MAAMI,MAAMxC,aAAawC,GAAG;IAC5B,MAAMyE,oBAAoBL,cAAcpE,QAAQA,IAAI0E,MAAM,KAAK;IAE/D,OAAO;QACL3E,UAAU;QACVC;QACAE,MAAM1C,aAAa0C,IAAI;QAEvBtC,cAAc6G,oBAAoBjH,aAAaI,YAAY,GAAG;QAC9DqC,aAAawE,oBAAoBjH,aAAayC,WAAW,GAAG;QAC5DE,SAAS3C,aAAa2C,OAAO;QAE7B,kDAAkD;QAClDjC,gBAAgBsG;IAClB;AACF;AAEA,MAAMG,WAAWC;AA8BjB,8EAA8E;AAC9E,gFAAgF;AAChF,8EAA8E;AAC9E,mEAAmE;AACnE,SAASR,cAAcS,KAAU;IAC/B,OAAOA,SAASA,MAAMC,GAAG,KAAKH;AAChC;AAEA,SAASvB;IACP,IAAIiB;IACJ,IAAIE;IACJ,MAAMQ,aAAa,IAAIC,QAAyB,CAACC,KAAKC;QACpDb,UAAUY;QACVV,SAASW;IACX;IACAH,WAAWL,MAAM,GAAG;IACpBK,WAAWV,OAAO,GAAG,CAACQ;QACpB,IAAIE,WAAWL,MAAM,KAAK,WAAW;YACnC,MAAMS,eAAqCJ;YAC3CI,aAAaT,MAAM,GAAG;YACtBS,aAAaN,KAAK,GAAGA;YACrBR,QAAQQ;QACV;IACF;IACAE,WAAWR,MAAM,GAAG,CAAClC;QACnB,IAAI0C,WAAWL,MAAM,KAAK,WAAW;YACnC,MAAMU,cAAmCL;YACzCK,YAAYV,MAAM,GAAG;YACrBU,YAAYC,MAAM,GAAGhD;YACrBkC,OAAOlC;QACT;IACF;IACA0C,WAAWD,GAAG,GAAGH;IACjB,OAAOI;AACT"}