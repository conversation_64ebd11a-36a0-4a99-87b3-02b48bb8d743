"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[51],{8633:(e,t)=>{t.q=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var r={},i=e.length;if(i<2)return r;var n=t&&t.decode||c,s=0,o=0,u=0;do{if(-1===(o=e.indexOf("=",s)))break;if(-1===(u=e.indexOf(";",s)))u=i;else if(o>u){s=e.lastIndexOf(";",o-1)+1;continue}var d=a(e,s,o),h=l(e,o,d),p=e.slice(d,h);if(!r.hasOwnProperty(p)){var f=a(e,o+1,u),y=l(e,u,f);34===e.charCodeAt(f)&&34===e.charCodeAt(y-1)&&(f++,y--);var m=e.slice(f,y);r[p]=function(e,t){try{return t(e)}catch(t){return e}}(m,n)}s=u+1}while(s<i);return r},t.l=function(e,t,a){var l=a&&a.encode||encodeURIComponent;if("function"!=typeof l)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var c=l(t);if(!n.test(c))throw TypeError("argument val is invalid");var u=e+"="+c;if(!a)return u;if(null!=a.maxAge){var d=Math.floor(a.maxAge);if(!isFinite(d))throw TypeError("option maxAge is invalid");u+="; Max-Age="+d}if(a.domain){if(!s.test(a.domain))throw TypeError("option domain is invalid");u+="; Domain="+a.domain}if(a.path){if(!o.test(a.path))throw TypeError("option path is invalid");u+="; Path="+a.path}if(a.expires){var h=a.expires;if("[object Date]"!==r.call(h)||isNaN(h.valueOf()))throw TypeError("option expires is invalid");u+="; Expires="+h.toUTCString()}if(a.httpOnly&&(u+="; HttpOnly"),a.secure&&(u+="; Secure"),a.partitioned&&(u+="; Partitioned"),a.priority)switch("string"==typeof a.priority?a.priority.toLowerCase():a.priority){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var r=Object.prototype.toString,i=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,n=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/;function a(e,t,r){do{var i=e.charCodeAt(t);if(32!==i&&9!==i)return t}while(++t<r);return r}function l(e,t,r){for(;t>r;){var i=e.charCodeAt(--t);if(32!==i&&9!==i)return t+1}return r}function c(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},144:(e,t,r)=>{r.d(t,{UL:()=>h,b3:()=>v});var i=r(945),n=r(5266),s=r(2296),o=r(8629),a=r(5871),l=r(7627),c=r(4384),u=r(5690),d=r(331);function h(){let e="cookies",t=s.workAsyncStorage.getStore(),r=o.FP.getStore();if(t){if(r&&"after"===r.phase&&!(0,d.iC)())throw Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`);if(t.forceStatic)return f(i.Ck.seal(new n.tm(new Headers({}))));if(r){if("cache"===r.type)throw Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===r.type)throw Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`)}if(t.dynamicShouldError)throw new l.f(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(r){if("prerender"===r.type)return function(e,t){let r=p.get(t);if(r)return r;let i=(0,c.W)(t.renderSignal,"`cookies()`");return p.set(t,i),Object.defineProperties(i,{[Symbol.iterator]:{value:function(){let r="`cookies()[Symbol.iterator]()`",i=m(e,r);(0,a.t3)(e,r,i,t)}},size:{get(){let r="`cookies().size`",i=m(e,r);(0,a.t3)(e,r,i,t)}},get:{value:function(){let r;r=0==arguments.length?"`cookies().get()`":`\`cookies().get(${y(arguments[0])})\``;let i=m(e,r);(0,a.t3)(e,r,i,t)}},getAll:{value:function(){let r;r=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${y(arguments[0])})\``;let i=m(e,r);(0,a.t3)(e,r,i,t)}},has:{value:function(){let r;r=0==arguments.length?"`cookies().has()`":`\`cookies().has(${y(arguments[0])})\``;let i=m(e,r);(0,a.t3)(e,r,i,t)}},set:{value:function(){let r;if(0==arguments.length)r="`cookies().set()`";else{let e=arguments[0];r=e?`\`cookies().set(${y(e)}, ...)\``:"`cookies().set(...)`"}let i=m(e,r);(0,a.t3)(e,r,i,t)}},delete:{value:function(){let r;r=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${y(arguments[0])})\``:`\`cookies().delete(${y(arguments[0])}, ...)\``;let i=m(e,r);(0,a.t3)(e,r,i,t)}},clear:{value:function(){let r="`cookies().clear()`",i=m(e,r);(0,a.t3)(e,r,i,t)}},toString:{value:function(){let r="`cookies().toString()`",i=m(e,r);(0,a.t3)(e,r,i,t)}}}),i}(t.route,r);"prerender-ppr"===r.type?(0,a.Ui)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,a.xI)(e,t,r)}(0,a.Pk)(t,r)}let u=(0,o.XN)(e);return f((0,i.Xj)(u)?u.userspaceMutableCookies:u.cookies)}let p=new WeakMap;function f(e){let t=p.get(e);if(t)return t;let r=Promise.resolve(e);return p.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):g.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):b.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function y(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}function m(e,t){let r=e?`Route "${e}" `:"This route ";return Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`)}function g(){return this.getAll().map(e=>[e.name,e]).values()}function b(e){for(let e of this.getAll())this.delete(e.name);return e}(0,u.I)(m);var w=r(2060);function v(){let e=s.workAsyncStorage.getStore(),t=o.FP.getStore();if(e){if(t&&"after"===t.phase&&!(0,d.iC)())throw Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`);if(e.forceStatic)return S(w.o.seal(new Headers({})));if(t){if("cache"===t.type)throw Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===t.type)throw Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`)}if(e.dynamicShouldError)throw new l.f(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(t){if("prerender"===t.type)return function(e,t){let r=_.get(t);if(r)return r;let i=(0,c.W)(t.renderSignal,"`headers()`");return _.set(t,i),Object.defineProperties(i,{append:{value:function(){let r=`\`headers().append(${k(arguments[0])}, ...)\``,i=A(e,r);(0,a.t3)(e,r,i,t)}},delete:{value:function(){let r=`\`headers().delete(${k(arguments[0])})\``,i=A(e,r);(0,a.t3)(e,r,i,t)}},get:{value:function(){let r=`\`headers().get(${k(arguments[0])})\``,i=A(e,r);(0,a.t3)(e,r,i,t)}},has:{value:function(){let r=`\`headers().has(${k(arguments[0])})\``,i=A(e,r);(0,a.t3)(e,r,i,t)}},set:{value:function(){let r=`\`headers().set(${k(arguments[0])}, ...)\``,i=A(e,r);(0,a.t3)(e,r,i,t)}},getSetCookie:{value:function(){let r="`headers().getSetCookie()`",i=A(e,r);(0,a.t3)(e,r,i,t)}},forEach:{value:function(){let r="`headers().forEach(...)`",i=A(e,r);(0,a.t3)(e,r,i,t)}},keys:{value:function(){let r="`headers().keys()`",i=A(e,r);(0,a.t3)(e,r,i,t)}},values:{value:function(){let r="`headers().values()`",i=A(e,r);(0,a.t3)(e,r,i,t)}},entries:{value:function(){let r="`headers().entries()`",i=A(e,r);(0,a.t3)(e,r,i,t)}},[Symbol.iterator]:{value:function(){let r="`headers()[Symbol.iterator]()`",i=A(e,r);(0,a.t3)(e,r,i,t)}}}),i}(e.route,t);"prerender-ppr"===t.type?(0,a.Ui)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,a.xI)("headers",e,t)}(0,a.Pk)(e,t)}return S((0,o.XN)("headers").headers)}let _=new WeakMap;function S(e){let t=_.get(e);if(t)return t;let r=Promise.resolve(e);return _.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function k(e){return"string"==typeof e?`'${e}'`:"..."}function A(e,t){let r=e?`Route "${e}" `:"This route ";return Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`)}(0,u.I)(A),r(4035),new WeakMap;function x(e){let t=workAsyncStorage.getStore(),r=workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===r.type)throw Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`);if("after"===r.phase)throw Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`)}if(t.dynamicShouldError)throw new StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(r){if("prerender"===r.type){let i=Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`);abortAndThrowOnSynchronousRequestDataAccess(t.route,e,i,r)}else if("prerender-ppr"===r.type)postponeWithTracking(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let i=new DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw t.dynamicUsageDescription=e,t.dynamicUsageStack=i.stack,i}}}}(0,u.I)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`)})},8149:(e,t,r)=>{r.d(t,{V2:()=>i.V2});var i=r(2245);r(2910);var n=r(9412);n.s8,n.s8,n.s8,r(4035),r(6026),r(8126),r(5871),Symbol.for("react.postpone")},236:(e,t,r)=>{r.d(t,{A:()=>i});function i(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},7031:(e,t,r)=>{r.d(t,{A:()=>i});function i(e){let t=e?.enterprise?.baseUrl??"https://github.com",r=e?.enterprise?.baseUrl?`${e?.enterprise?.baseUrl}/api/v3`:"https://api.github.com";return{id:"github",name:"GitHub",type:"oauth",authorization:{url:`${t}/login/oauth/authorize`,params:{scope:"read:user user:email"}},token:`${t}/login/oauth/access_token`,userinfo:{url:`${r}/user`,async request({tokens:e,provider:t}){let i=await fetch(t.userinfo?.url,{headers:{Authorization:`Bearer ${e.access_token}`,"User-Agent":"authjs"}}).then(async e=>await e.json());if(!i.email){let t=await fetch(`${r}/user/emails`,{headers:{Authorization:`Bearer ${e.access_token}`,"User-Agent":"authjs"}});if(t.ok){let e=await t.json();i.email=(e.find(e=>e.primary)??e[0]).email}}return i}},profile:e=>({id:e.id.toString(),name:e.name??e.login,email:e.email,image:e.avatar_url}),style:{bg:"#24292f",text:"#fff"},options:e}}},1049:(e,t,r)=>{r.d(t,{_:()=>ri});var i=r(66),n=r(4294),s=r(7536),o=r(2923);class a extends s.Xs{constructor(e){super(a.buildEmbeddedCount(e.source,e.filters).queryChunks),this.params=e,this.mapWith(Number),this.session=e.session,this.sql=a.buildCount(e.source,e.filters)}sql;static [i.i]="MySqlCountBuilder";[Symbol.toStringTag]="MySqlCountBuilder";session;static buildEmbeddedCount(e,t){return(0,s.ll)`(select count(*) from ${e}${s.ll.raw(" where ").if(t)}${t})`}static buildCount(e,t){return(0,s.ll)`select count(*) as count from ${e}${s.ll.raw(" where ").if(t)}${t}`}then(e,t){return Promise.resolve(this.session.count(this.sql)).then(e,t)}catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}}var l=r(8421),c=r(4650),u=r(823),d=r(7430),h=r(9066),p=r(652),f=r(8685),y=r(6104),m=r(4996),g=r(9877),b=r(326);class w{static [i.i]="MySqlForeignKeyBuilder";reference;_onUpdate;_onDelete;constructor(e,t){this.reference=()=>{let{name:t,columns:r,foreignColumns:i}=e();return{name:t,columns:r,foreignTable:i[0].table,foreignColumns:i}},t&&(this._onUpdate=t.onUpdate,this._onDelete=t.onDelete)}onUpdate(e){return this._onUpdate=e,this}onDelete(e){return this._onDelete=e,this}build(e){return new v(e,this)}}class v{constructor(e,t){this.table=e,this.reference=t.reference,this.onUpdate=t._onUpdate,this.onDelete=t._onDelete}static [i.i]="MySqlForeignKey";reference;onUpdate;onDelete;getName(){let{name:e,columns:t,foreignColumns:r}=this.reference(),i=t.map(e=>e.name),n=r.map(e=>e.name),s=[this.table[b.E],...i,r[0].table[b.E],...n];return e??`${s.join("_")}_fk`}}function _(e,t){return`${e[b.E]}_${t.join("_")}_unique`}class S{constructor(e,t){this.name=t,this.columns=e}static [i.i]=null;columns;build(e){return new A(e,this.columns,this.name)}}class k{static [i.i]=null;name;constructor(e){this.name=e}on(...e){return new S(e,this.name)}}class A{constructor(e,t,r){this.table=e,this.columns=t,this.name=r??_(this.table,this.columns.map(e=>e.name))}static [i.i]=null;columns;name;nullsNotDistinct=!1;getName(){return this.name}}class x extends g.Q{static [i.i]="MySqlColumnBuilder";foreignKeyConfigs=[];references(e,t={}){return this.foreignKeyConfigs.push({ref:e,actions:t}),this}unique(e){return this.config.isUnique=!0,this.config.uniqueName=e,this}generatedAlwaysAs(e,t){return this.config.generated={as:e,type:"always",mode:t?.mode??"virtual"},this}buildForeignKeys(e,t){return this.foreignKeyConfigs.map(({ref:r,actions:i})=>((r,i)=>{let n=new w(()=>({columns:[e],foreignColumns:[r()]}));return i.onUpdate&&n.onUpdate(i.onUpdate),i.onDelete&&n.onDelete(i.onDelete),n.build(t)})(r,i))}}class T extends u.V{constructor(e,t){t.uniqueName||(t.uniqueName=_(e,[t.name])),super(e,t),this.table=e}static [i.i]="MySqlColumn"}class E extends x{static [i.i]="MySqlColumnBuilderWithAutoIncrement";constructor(e,t,r){super(e,t,r),this.config.autoIncrement=!1}autoincrement(){return this.config.autoIncrement=!0,this.config.hasDefault=!0,this}}class $ extends T{static [i.i]="MySqlColumnWithAutoIncrement";autoIncrement=this.config.autoIncrement}class C extends E{static [i.i]="MySqlBigInt53Builder";constructor(e,t=!1){super(e,"number","MySqlBigInt53"),this.config.unsigned=t}build(e){return new I(e,this.config)}}class I extends ${static [i.i]="MySqlBigInt53";getSQLType(){return`bigint${this.config.unsigned?" unsigned":""}`}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class U extends E{static [i.i]="MySqlBigInt64Builder";constructor(e,t=!1){super(e,"bigint","MySqlBigInt64"),this.config.unsigned=t}build(e){return new q(e,this.config)}}class q extends ${static [i.i]="MySqlBigInt64";getSQLType(){return`bigint${this.config.unsigned?" unsigned":""}`}mapFromDriverValue(e){return BigInt(e)}}function P(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return"number"===i.mode?new C(r,i.unsigned):new U(r,i.unsigned)}class O extends x{static [i.i]="MySqlBinaryBuilder";constructor(e,t){super(e,"string","MySqlBinary"),this.config.length=t}build(e){return new N(e,this.config)}}class N extends T{static [i.i]="MySqlBinary";length=this.config.length;getSQLType(){return void 0===this.length?"binary":`binary(${this.length})`}}function j(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return new O(r,i.length)}class R extends x{static [i.i]="MySqlBooleanBuilder";constructor(e){super(e,"boolean","MySqlBoolean")}build(e){return new D(e,this.config)}}class D extends T{static [i.i]="MySqlBoolean";getSQLType(){return"boolean"}mapFromDriverValue(e){return"boolean"==typeof e?e:1===e}}function M(e){return new R(e??"")}class L extends x{static [i.i]="MySqlCharBuilder";constructor(e,t){super(e,"string","MySqlChar"),this.config.length=t.length,this.config.enum=t.enum}build(e){return new H(e,this.config)}}class H extends T{static [i.i]="MySqlChar";length=this.config.length;enumValues=this.config.enum;getSQLType(){return void 0===this.length?"char":`char(${this.length})`}}function W(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return new L(r,i)}class K extends x{static [i.i]="MySqlCustomColumnBuilder";constructor(e,t,r){super(e,"custom","MySqlCustomColumn"),this.config.fieldConfig=t,this.config.customTypeParams=r}build(e){return new B(e,this.config)}}class B extends T{static [i.i]="MySqlCustomColumn";sqlName;mapTo;mapFrom;constructor(e,t){super(e,t),this.sqlName=t.customTypeParams.dataType(t.fieldConfig),this.mapTo=t.customTypeParams.toDriver,this.mapFrom=t.customTypeParams.fromDriver}getSQLType(){return this.sqlName}mapFromDriverValue(e){return"function"==typeof this.mapFrom?this.mapFrom(e):e}mapToDriverValue(e){return"function"==typeof this.mapTo?this.mapTo(e):e}}function J(e){return(t,r)=>{let{name:i,config:n}=(0,y.Ll)(t,r);return new K(i,n,e)}}class Q extends x{static [i.i]="MySqlDateBuilder";constructor(e){super(e,"date","MySqlDate")}build(e){return new z(e,this.config)}}class z extends T{static [i.i]="MySqlDate";constructor(e,t){super(e,t)}getSQLType(){return"date"}mapFromDriverValue(e){return new Date(e)}}class F extends x{static [i.i]="MySqlDateStringBuilder";constructor(e){super(e,"string","MySqlDateString")}build(e){return new V(e,this.config)}}class V extends T{static [i.i]="MySqlDateString";constructor(e,t){super(e,t)}getSQLType(){return"date"}}function X(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return i?.mode==="string"?new F(r):new Q(r)}class G extends x{static [i.i]="MySqlDateTimeBuilder";constructor(e,t){super(e,"date","MySqlDateTime"),this.config.fsp=t?.fsp}build(e){return new Y(e,this.config)}}class Y extends T{static [i.i]="MySqlDateTime";fsp;constructor(e,t){super(e,t),this.fsp=t.fsp}getSQLType(){let e=void 0===this.fsp?"":`(${this.fsp})`;return`datetime${e}`}mapToDriverValue(e){return e.toISOString().replace("T"," ").replace("Z","")}mapFromDriverValue(e){return new Date(e.replace(" ","T")+"Z")}}class Z extends x{static [i.i]="MySqlDateTimeStringBuilder";constructor(e,t){super(e,"string","MySqlDateTimeString"),this.config.fsp=t?.fsp}build(e){return new ee(e,this.config)}}class ee extends T{static [i.i]="MySqlDateTimeString";fsp;constructor(e,t){super(e,t),this.fsp=t.fsp}getSQLType(){let e=void 0===this.fsp?"":`(${this.fsp})`;return`datetime${e}`}}function et(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return i?.mode==="string"?new Z(r,i):new G(r,i)}class er extends E{static [i.i]="MySqlDecimalBuilder";constructor(e,t){super(e,"string","MySqlDecimal"),this.config.precision=t?.precision,this.config.scale=t?.scale,this.config.unsigned=t?.unsigned}build(e){return new ei(e,this.config)}}class ei extends ${static [i.i]="MySqlDecimal";precision=this.config.precision;scale=this.config.scale;unsigned=this.config.unsigned;getSQLType(){let e="";return void 0!==this.precision&&void 0!==this.scale?e+=`decimal(${this.precision},${this.scale})`:void 0===this.precision?e+="decimal":e+=`decimal(${this.precision})`,e="decimal(10,0)"===e||"decimal(10)"===e?"decimal":e,this.unsigned?`${e} unsigned`:e}}function en(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return new er(r,i)}class es extends E{static [i.i]="MySqlDoubleBuilder";constructor(e,t){super(e,"number","MySqlDouble"),this.config.precision=t?.precision,this.config.scale=t?.scale,this.config.unsigned=t?.unsigned}build(e){return new eo(e,this.config)}}class eo extends ${static [i.i]="MySqlDouble";precision=this.config.precision;scale=this.config.scale;unsigned=this.config.unsigned;getSQLType(){let e="";return void 0!==this.precision&&void 0!==this.scale?e+=`double(${this.precision},${this.scale})`:void 0===this.precision?e+="double":e+=`double(${this.precision})`,this.unsigned?`${e} unsigned`:e}}function ea(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new es(r,i)}class el extends x{static [i.i]="MySqlEnumColumnBuilder";constructor(e,t){super(e,"string","MySqlEnumColumn"),this.config.enumValues=t}build(e){return new ec(e,this.config)}}class ec extends T{static [i.i]="MySqlEnumColumn";enumValues=this.config.enumValues;getSQLType(){return`enum(${this.enumValues.map(e=>`'${e}'`).join(",")})`}}function eu(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);if(0===i.length)throw Error(`You have an empty array for "${r}" enum values`);return new el(r,i)}class ed extends E{static [i.i]="MySqlFloatBuilder";constructor(e,t){super(e,"number","MySqlFloat"),this.config.precision=t?.precision,this.config.scale=t?.scale,this.config.unsigned=t?.unsigned}build(e){return new eh(e,this.config)}}class eh extends ${static [i.i]="MySqlFloat";precision=this.config.precision;scale=this.config.scale;unsigned=this.config.unsigned;getSQLType(){let e="";return void 0!==this.precision&&void 0!==this.scale?e+=`float(${this.precision},${this.scale})`:void 0===this.precision?e+="float":e+=`float(${this.precision})`,this.unsigned?`${e} unsigned`:e}}function ep(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new ed(r,i)}class ef extends E{static [i.i]="MySqlIntBuilder";constructor(e,t){super(e,"number","MySqlInt"),this.config.unsigned=!!t&&t.unsigned}build(e){return new ey(e,this.config)}}class ey extends ${static [i.i]="MySqlInt";getSQLType(){return`int${this.config.unsigned?" unsigned":""}`}mapFromDriverValue(e){return"string"==typeof e?Number(e):e}}function em(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new ef(r,i)}class eg extends x{static [i.i]="MySqlJsonBuilder";constructor(e){super(e,"json","MySqlJson")}build(e){return new eb(e,this.config)}}class eb extends T{static [i.i]="MySqlJson";getSQLType(){return"json"}mapToDriverValue(e){return JSON.stringify(e)}}function ew(e){return new eg(e??"")}class ev extends E{static [i.i]="MySqlMediumIntBuilder";constructor(e,t){super(e,"number","MySqlMediumInt"),this.config.unsigned=!!t&&t.unsigned}build(e){return new e_(e,this.config)}}class e_ extends ${static [i.i]="MySqlMediumInt";getSQLType(){return`mediumint${this.config.unsigned?" unsigned":""}`}mapFromDriverValue(e){return"string"==typeof e?Number(e):e}}function eS(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new ev(r,i)}class ek extends E{static [i.i]="MySqlRealBuilder";constructor(e,t){super(e,"number","MySqlReal"),this.config.precision=t?.precision,this.config.scale=t?.scale}build(e){return new eA(e,this.config)}}class eA extends ${static [i.i]="MySqlReal";precision=this.config.precision;scale=this.config.scale;getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`real(${this.precision}, ${this.scale})`:void 0===this.precision?"real":`real(${this.precision})`}}function ex(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return new ek(r,i)}class eT extends E{static [i.i]="MySqlSerialBuilder";constructor(e){super(e,"number","MySqlSerial"),this.config.hasDefault=!0,this.config.autoIncrement=!0}build(e){return new eE(e,this.config)}}class eE extends ${static [i.i]="MySqlSerial";getSQLType(){return"serial"}mapFromDriverValue(e){return"string"==typeof e?Number(e):e}}function e$(e){return new eT(e??"")}class eC extends E{static [i.i]="MySqlSmallIntBuilder";constructor(e,t){super(e,"number","MySqlSmallInt"),this.config.unsigned=!!t&&t.unsigned}build(e){return new eI(e,this.config)}}class eI extends ${static [i.i]="MySqlSmallInt";getSQLType(){return`smallint${this.config.unsigned?" unsigned":""}`}mapFromDriverValue(e){return"string"==typeof e?Number(e):e}}function eU(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new eC(r,i)}class eq extends x{static [i.i]="MySqlTextBuilder";constructor(e,t,r){super(e,"string","MySqlText"),this.config.textType=t,this.config.enumValues=r.enum}build(e){return new eP(e,this.config)}}class eP extends T{static [i.i]="MySqlText";textType=this.config.textType;enumValues=this.config.enumValues;getSQLType(){return this.textType}}function eO(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return new eq(r,"text",i)}class eN extends x{static [i.i]="MySqlTimeBuilder";constructor(e,t){super(e,"string","MySqlTime"),this.config.fsp=t?.fsp}build(e){return new ej(e,this.config)}}class ej extends T{static [i.i]="MySqlTime";fsp=this.config.fsp;getSQLType(){let e=void 0===this.fsp?"":`(${this.fsp})`;return`time${e}`}}function eR(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new eN(r,i)}class eD extends x{static [i.i]="MySqlDateColumnBuilder";defaultNow(){return this.default((0,s.ll)`(now())`)}onUpdateNow(){return this.config.hasOnUpdateNow=!0,this.config.hasDefault=!0,this}}class eM extends T{static [i.i]="MySqlDateColumn";hasOnUpdateNow=this.config.hasOnUpdateNow}class eL extends eD{static [i.i]="MySqlTimestampBuilder";constructor(e,t){super(e,"date","MySqlTimestamp"),this.config.fsp=t?.fsp}build(e){return new eH(e,this.config)}}class eH extends eM{static [i.i]="MySqlTimestamp";fsp=this.config.fsp;getSQLType(){let e=void 0===this.fsp?"":`(${this.fsp})`;return`timestamp${e}`}mapFromDriverValue(e){return new Date(e+"+0000")}mapToDriverValue(e){return e.toISOString().slice(0,-1).replace("T"," ")}}class eW extends eD{static [i.i]="MySqlTimestampStringBuilder";constructor(e,t){super(e,"string","MySqlTimestampString"),this.config.fsp=t?.fsp}build(e){return new eK(e,this.config)}}class eK extends eM{static [i.i]="MySqlTimestampString";fsp=this.config.fsp;getSQLType(){let e=void 0===this.fsp?"":`(${this.fsp})`;return`timestamp${e}`}}function eB(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return i?.mode==="string"?new eW(r,i):new eL(r,i)}class eJ extends E{static [i.i]="MySqlTinyIntBuilder";constructor(e,t){super(e,"number","MySqlTinyInt"),this.config.unsigned=!!t&&t.unsigned}build(e){return new eQ(e,this.config)}}class eQ extends ${static [i.i]="MySqlTinyInt";getSQLType(){return`tinyint${this.config.unsigned?" unsigned":""}`}mapFromDriverValue(e){return"string"==typeof e?Number(e):e}}function ez(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new eJ(r,i)}class eF extends x{static [i.i]="MySqlVarBinaryBuilder";constructor(e,t){super(e,"string","MySqlVarBinary"),this.config.length=t?.length}build(e){return new eV(e,this.config)}}class eV extends T{static [i.i]="MySqlVarBinary";length=this.config.length;getSQLType(){return void 0===this.length?"varbinary":`varbinary(${this.length})`}}function eX(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new eF(r,i)}class eG extends x{static [i.i]="MySqlVarCharBuilder";constructor(e,t){super(e,"string","MySqlVarChar"),this.config.length=t.length,this.config.enum=t.enum}build(e){return new eY(e,this.config)}}class eY extends T{static [i.i]="MySqlVarChar";length=this.config.length;enumValues=this.config.enum;getSQLType(){return void 0===this.length?"varchar":`varchar(${this.length})`}}function eZ(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new eG(r,i)}class e0 extends x{static [i.i]="MySqlYearBuilder";constructor(e){super(e,"number","MySqlYear")}build(e){return new e1(e,this.config)}}class e1 extends T{static [i.i]="MySqlYear";getSQLType(){return"year"}}function e2(e){return new e0(e??"")}let e5=Symbol.for("drizzle:MySqlInlineForeignKeys");class e3 extends f.XI{static [i.i]="MySqlTable";static Symbol=Object.assign({},f.XI.Symbol,{InlineForeignKeys:e5});[f.XI.Symbol.Columns];[e5]=[];[f.XI.Symbol.ExtraConfigBuilder]=void 0}let e8=(e,t,r)=>(function(e,t,r,i,n=e){let s=new e3(e,i,n),o=Object.fromEntries(Object.entries("function"==typeof t?t({bigint:P,binary:j,boolean:M,char:W,customType:J,date:X,datetime:et,decimal:en,double:ea,mysqlEnum:eu,float:ep,int:em,json:ew,mediumint:eS,real:ex,serial:e$,smallint:eU,text:eO,time:eR,timestamp:eB,tinyint:ez,varbinary:eX,varchar:eZ,year:e2}):t).map(([e,t])=>{t.setName(e);let r=t.build(s);return s[e5].push(...t.buildForeignKeys(r,s)),[e,r]})),a=Object.assign(s,o);return a[f.XI.Symbol.Columns]=o,a[f.XI.Symbol.ExtraConfigColumns]=o,r&&(a[e3.Symbol.ExtraConfigBuilder]=r),a})(e,t,r,void 0,e);class e6 extends s.Ss{static [i.i]="MySqlViewBase"}class e4{static [i.i]="MySqlDialect";casing;constructor(e){this.casing=new c.Yn(e?.casing)}async migrate(e,t,r){let i=r.migrationsTable??"__drizzle_migrations",n=(0,s.ll)`
			create table if not exists ${s.ll.identifier(i)} (
				id serial primary key,
				hash text not null,
				created_at bigint
			)
		`;await t.execute(n);let o=(await t.all((0,s.ll)`select id, hash, created_at from ${s.ll.identifier(i)} order by created_at desc limit 1`))[0];await t.transaction(async t=>{for(let r of e)if(!o||Number(o.created_at)<r.folderMillis){for(let e of r.sql)await t.execute(s.ll.raw(e));await t.execute((0,s.ll)`insert into ${s.ll.identifier(i)} (\`hash\`, \`created_at\`) values(${r.hash}, ${r.folderMillis})`)}})}escapeName(e){return`\`${e}\``}escapeParam(e){return"?"}escapeString(e){return`'${e.replace(/'/g,"''")}'`}buildWithCTE(e){if(!e?.length)return;let t=[(0,s.ll)`with `];for(let[r,i]of e.entries())t.push((0,s.ll)`${s.ll.identifier(i._.alias)} as (${i._.sql})`),r<e.length-1&&t.push((0,s.ll)`, `);return t.push((0,s.ll)` `),s.ll.join(t)}buildDeleteQuery({table:e,where:t,returning:r,withList:i,limit:n,orderBy:o}){let a=this.buildWithCTE(i),l=r?(0,s.ll)` returning ${this.buildSelection(r,{isSingleTable:!0})}`:void 0,c=t?(0,s.ll)` where ${t}`:void 0,u=this.buildOrderBy(o),d=this.buildLimit(n);return(0,s.ll)`${a}delete from ${e}${c}${u}${d}${l}`}buildUpdateSet(e,t){let r=e[f.XI.Symbol.Columns],i=Object.keys(r).filter(e=>void 0!==t[e]||r[e]?.onUpdateFn!==void 0),n=i.length;return s.ll.join(i.flatMap((e,i)=>{let o=r[e],a=t[e]??s.ll.param(o.onUpdateFn(),o),l=(0,s.ll)`${s.ll.identifier(this.casing.getColumnCasing(o))} = ${a}`;return i<n-1?[l,s.ll.raw(", ")]:[l]}))}buildUpdateQuery({table:e,set:t,where:r,returning:i,withList:n,limit:o,orderBy:a}){let l=this.buildWithCTE(n),c=this.buildUpdateSet(e,t),u=i?(0,s.ll)` returning ${this.buildSelection(i,{isSingleTable:!0})}`:void 0,d=r?(0,s.ll)` where ${r}`:void 0,h=this.buildOrderBy(a),p=this.buildLimit(o);return(0,s.ll)`${l}update ${e} set ${c}${d}${h}${p}${u}`}buildSelection(e,{isSingleTable:t=!1}={}){let r=e.length,n=e.flatMap(({field:e},n)=>{let o=[];if((0,i.is)(e,s.Xs.Aliased)&&e.isSelectionField)o.push(s.ll.identifier(e.fieldAlias));else if((0,i.is)(e,s.Xs.Aliased)||(0,i.is)(e,s.Xs)){let r=(0,i.is)(e,s.Xs.Aliased)?e.sql:e;t?o.push(new s.Xs(r.queryChunks.map(e=>(0,i.is)(e,T)?s.ll.identifier(this.casing.getColumnCasing(e)):e))):o.push(r),(0,i.is)(e,s.Xs.Aliased)&&o.push((0,s.ll)` as ${s.ll.identifier(e.fieldAlias)}`)}else(0,i.is)(e,u.V)&&(t?o.push(s.ll.identifier(this.casing.getColumnCasing(e))):o.push(e));return n<r-1&&o.push((0,s.ll)`, `),o});return s.ll.join(n)}buildLimit(e){return"object"==typeof e||"number"==typeof e&&e>=0?(0,s.ll)` limit ${e}`:void 0}buildOrderBy(e){return e&&e.length>0?(0,s.ll)` order by ${s.ll.join(e,(0,s.ll)`, `)}`:void 0}buildSelectQuery({withList:e,fields:t,fieldsFlat:r,where:n,having:a,table:l,joins:c,orderBy:d,groupBy:h,limit:p,offset:g,lockingClause:b,distinct:w,setOperators:v}){let _;let S=r??(0,y.He)(t);for(let e of S){let t;if((0,i.is)(e.field,u.V)&&(0,f.Io)(e.field.table)!==((0,i.is)(l,o.n)?l._.alias:(0,i.is)(l,e6)?l[m.n].name:(0,i.is)(l,s.Xs)?void 0:(0,f.Io)(l))&&(t=e.field.table,!c?.some(({alias:e})=>e===(t[f.XI.Symbol.IsAlias]?f.Io(t):t[f.XI.Symbol.BaseName])))){let t=(0,f.Io)(e.field.table);throw Error(`Your "${e.path.join("->")}" field references a column "${t}"."${e.field.name}", but the table "${t}" is not part of the query! Did you forget to join it?`)}}let k=!c||0===c.length,A=this.buildWithCTE(e),x=w?(0,s.ll)` distinct`:void 0,T=this.buildSelection(S,{isSingleTable:k}),E=(0,i.is)(l,f.XI)&&l[f.XI.Symbol.OriginalName]!==l[f.XI.Symbol.Name]?(0,s.ll)`${s.ll.identifier(l[f.XI.Symbol.OriginalName])} ${s.ll.identifier(l[f.XI.Symbol.Name])}`:l,$=[];if(c)for(let[e,t]of c.entries()){0===e&&$.push((0,s.ll)` `);let r=t.table,n=t.lateral?(0,s.ll)` lateral`:void 0;if((0,i.is)(r,e3)){let e=r[e3.Symbol.Name],i=r[e3.Symbol.Schema],o=r[e3.Symbol.OriginalName],a=e===o?void 0:t.alias;$.push((0,s.ll)`${s.ll.raw(t.joinType)} join${n} ${i?(0,s.ll)`${s.ll.identifier(i)}.`:void 0}${s.ll.identifier(o)}${a&&(0,s.ll)` ${s.ll.identifier(a)}`} on ${t.on}`)}else if((0,i.is)(r,s.Ss)){let e=r[m.n].name,i=r[m.n].schema,o=r[m.n].originalName,a=e===o?void 0:t.alias;$.push((0,s.ll)`${s.ll.raw(t.joinType)} join${n} ${i?(0,s.ll)`${s.ll.identifier(i)}.`:void 0}${s.ll.identifier(o)}${a&&(0,s.ll)` ${s.ll.identifier(a)}`} on ${t.on}`)}else $.push((0,s.ll)`${s.ll.raw(t.joinType)} join${n} ${r} on ${t.on}`);e<c.length-1&&$.push((0,s.ll)` `)}let C=s.ll.join($),I=n?(0,s.ll)` where ${n}`:void 0,U=a?(0,s.ll)` having ${a}`:void 0,q=this.buildOrderBy(d),P=h&&h.length>0?(0,s.ll)` group by ${s.ll.join(h,(0,s.ll)`, `)}`:void 0,O=this.buildLimit(p),N=g?(0,s.ll)` offset ${g}`:void 0;if(b){let{config:e,strength:t}=b;_=(0,s.ll)` for ${s.ll.raw(t)}`,e.noWait?_.append((0,s.ll)` no wait`):e.skipLocked&&_.append((0,s.ll)` skip locked`)}let j=(0,s.ll)`${A}select${x} ${T} from ${E}${C}${I}${P}${U}${q}${O}${N}${_}`;return v.length>0?this.buildSetOperations(j,v):j}buildSetOperations(e,t){let[r,...i]=t;if(!r)throw Error("Cannot pass undefined values to any set operator");return 0===i.length?this.buildSetOperationQuery({leftSelect:e,setOperator:r}):this.buildSetOperations(this.buildSetOperationQuery({leftSelect:e,setOperator:r}),i)}buildSetOperationQuery({leftSelect:e,setOperator:{type:t,isAll:r,rightSelect:n,limit:o,orderBy:a,offset:l}}){let c;let u=(0,s.ll)`(${e.getSQL()}) `,d=(0,s.ll)`(${n.getSQL()})`;if(a&&a.length>0){let e=[];for(let t of a)if((0,i.is)(t,T))e.push(s.ll.identifier(this.casing.getColumnCasing(t)));else if((0,i.is)(t,s.Xs)){for(let e=0;e<t.queryChunks.length;e++){let r=t.queryChunks[e];(0,i.is)(r,T)&&(t.queryChunks[e]=s.ll.identifier(this.casing.getColumnCasing(r)))}e.push((0,s.ll)`${t}`)}else e.push((0,s.ll)`${t}`);c=(0,s.ll)` order by ${s.ll.join(e,(0,s.ll)`, `)} `}let h="object"==typeof o||"number"==typeof o&&o>=0?(0,s.ll)` limit ${o}`:void 0,p=s.ll.raw(`${t} ${r?"all ":""}`),f=l?(0,s.ll)` offset ${l}`:void 0;return(0,s.ll)`${u}${p}${d}${c}${h}${f}`}buildInsertQuery({table:e,values:t,ignore:r,onConflict:n,select:o}){let a=[],l=Object.entries(e[f.XI.Symbol.Columns]).filter(([e,t])=>!t.shouldDisableInsert()),c=l.map(([,e])=>s.ll.identifier(this.casing.getColumnCasing(e))),u=[];if(o)(0,i.is)(t,s.Xs)?a.push(t):a.push(t.getSQL());else for(let[e,r]of(a.push(s.ll.raw("values ")),t.entries())){let n={},o=[];for(let[e,t]of l){let a=r[e];if(void 0===a||(0,i.is)(a,s.Iw)&&void 0===a.value){if(void 0!==t.defaultFn){let r=t.defaultFn();n[e]=r;let a=(0,i.is)(r,s.Xs)?r:s.ll.param(r,t);o.push(a)}else if(t.default||void 0===t.onUpdateFn)o.push((0,s.ll)`default`);else{let e=t.onUpdateFn(),r=(0,i.is)(e,s.Xs)?e:s.ll.param(e,t);o.push(r)}}else t.defaultFn&&(0,i.is)(a,s.Iw)&&(n[e]=a.value),o.push(a)}u.push(n),a.push(o),e<t.length-1&&a.push((0,s.ll)`, `)}let d=s.ll.join(a),h=r?(0,s.ll)` ignore`:void 0,p=n?(0,s.ll)` on duplicate key ${n}`:void 0;return{sql:(0,s.ll)`insert${h} into ${e} ${c} ${d}${p}`,generatedIds:u}}sqlToQuery(e,t){return e.toQuery({casing:this.casing,escapeName:this.escapeName,escapeParam:this.escapeParam,escapeString:this.escapeString,invokeSource:t})}buildRelationalQuery({fullSchema:e,schema:t,tableNamesMap:r,table:n,tableConfig:a,queryConfig:c,tableAlias:y,nestedQueryRelation:m,joinOn:g}){let b,w,v,_,S,k=[],A=[];if(!0===c)k=Object.entries(a.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:(0,l.ug)(t,y),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let n=Object.fromEntries(Object.entries(a.columns).map(([e,t])=>[e,(0,l.ug)(t,y)]));if(c.where){let e="function"==typeof c.where?c.where(n,(0,p.mm)()):c.where;_=e&&(0,l.yY)(e,y)}let d=[],m=[];if(c.columns){let e=!1;for(let[t,r]of Object.entries(c.columns))void 0!==r&&t in a.columns&&(e||!0!==r||(e=!0),m.push(t));m.length>0&&(m=e?m.filter(e=>c.columns?.[e]===!0):Object.keys(a.columns).filter(e=>!m.includes(e)))}else m=Object.keys(a.columns);for(let e of m){let t=a.columns[e];d.push({tsKey:e,value:t})}let g=[];if(c.with&&(g=Object.entries(c.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:a.relations[e]}))),c.extras)for(let[e,t]of Object.entries("function"==typeof c.extras?c.extras(n,{sql:s.ll}):c.extras))d.push({tsKey:e,value:(0,l.Hs)(t,y)});for(let{tsKey:e,value:t}of d)k.push({dbKey:(0,i.is)(t,s.Xs.Aliased)?t.fieldAlias:a.columns[e].name,tsKey:e,field:(0,i.is)(t,u.V)?(0,l.ug)(t,y):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let S="function"==typeof c.orderBy?c.orderBy(n,(0,p.rl)()):c.orderBy??[];for(let{tsKey:n,queryConfig:a,relation:d}of(Array.isArray(S)||(S=[S]),v=S.map(e=>(0,i.is)(e,u.V)?(0,l.ug)(e,y):(0,l.yY)(e,y)),b=c.limit,w=c.offset,g)){let c=(0,p.W0)(t,r,d),u=r[(0,f.Lf)(d.referencedTable)],m=`${y}_${n}`,g=(0,h.Uo)(...c.fields.map((e,t)=>(0,h.eq)((0,l.ug)(c.references[t],m),(0,l.ug)(e,y)))),b=this.buildRelationalQuery({fullSchema:e,schema:t,tableNamesMap:r,table:e[u],tableConfig:t[u],queryConfig:(0,i.is)(d,p.pD)?!0===a?{limit:1}:{...a,limit:1}:a,tableAlias:m,joinOn:g,nestedQueryRelation:d}),w=(0,s.ll)`${s.ll.identifier(m)}.${s.ll.identifier("data")}`.as(n);A.push({on:(0,s.ll)`true`,table:new o.n(b.sql,{},m),alias:m,joinType:"left",lateral:!0}),k.push({dbKey:n,tsKey:n,field:w,relationTableTsKey:u,isJson:!0,selection:b.selection})}}if(0===k.length)throw new d.n({message:`No fields selected for table "${a.tsName}" ("${y}")`});if(_=(0,h.Uo)(g,_),m){let e=(0,s.ll)`json_array(${s.ll.join(k.map(({field:e,tsKey:t,isJson:r})=>r?(0,s.ll)`${s.ll.identifier(`${y}_${t}`)}.${s.ll.identifier("data")}`:(0,i.is)(e,s.Xs.Aliased)?e.sql:e),(0,s.ll)`, `)})`;(0,i.is)(m,p.iv)&&(e=(0,s.ll)`coalesce(json_arrayagg(${e}), json_array())`);let t=[{dbKey:"data",tsKey:"data",field:e.as("data"),isJson:!0,relationTableTsKey:a.tsName,selection:k}];void 0!==b||void 0!==w||(v?.length??0)>0?(S=this.buildSelectQuery({table:(0,l.oG)(n,y),fields:{},fieldsFlat:[{path:[],field:s.ll.raw("*")},...(v?.length??0)>0?[{path:[],field:(0,s.ll)`row_number() over (order by ${s.ll.join(v,(0,s.ll)`, `)})`}]:[]],where:_,limit:b,offset:w,setOperators:[]}),_=void 0,b=void 0,w=void 0,v=void 0):S=(0,l.oG)(n,y),S=this.buildSelectQuery({table:(0,i.is)(S,e3)?S:new o.n(S,{},y),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:(0,i.is)(e,u.V)?(0,l.ug)(e,y):e})),joins:A,where:_,limit:b,offset:w,orderBy:v,setOperators:[]})}else S=this.buildSelectQuery({table:(0,l.oG)(n,y),fields:{},fieldsFlat:k.map(({field:e})=>({path:[],field:(0,i.is)(e,u.V)?(0,l.ug)(e,y):e})),joins:A,where:_,limit:b,offset:w,orderBy:v,setOperators:[]});return{tableTsKey:a.tsName,sql:S,selection:k}}buildRelationalQueryWithoutLateralSubqueries({fullSchema:e,schema:t,tableNamesMap:r,table:n,tableConfig:a,queryConfig:c,tableAlias:y,nestedQueryRelation:m,joinOn:g}){let b,w=[],v,_,S=[],k;if(!0===c)w=Object.entries(a.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:(0,l.ug)(t,y),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let n=Object.fromEntries(Object.entries(a.columns).map(([e,t])=>[e,(0,l.ug)(t,y)]));if(c.where){let e="function"==typeof c.where?c.where(n,(0,p.mm)()):c.where;k=e&&(0,l.yY)(e,y)}let o=[],d=[];if(c.columns){let e=!1;for(let[t,r]of Object.entries(c.columns))void 0!==r&&t in a.columns&&(e||!0!==r||(e=!0),d.push(t));d.length>0&&(d=e?d.filter(e=>c.columns?.[e]===!0):Object.keys(a.columns).filter(e=>!d.includes(e)))}else d=Object.keys(a.columns);for(let e of d){let t=a.columns[e];o.push({tsKey:e,value:t})}let m=[];if(c.with&&(m=Object.entries(c.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:a.relations[e]}))),c.extras)for(let[e,t]of Object.entries("function"==typeof c.extras?c.extras(n,{sql:s.ll}):c.extras))o.push({tsKey:e,value:(0,l.Hs)(t,y)});for(let{tsKey:e,value:t}of o)w.push({dbKey:(0,i.is)(t,s.Xs.Aliased)?t.fieldAlias:a.columns[e].name,tsKey:e,field:(0,i.is)(t,u.V)?(0,l.ug)(t,y):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let g="function"==typeof c.orderBy?c.orderBy(n,(0,p.rl)()):c.orderBy??[];for(let{tsKey:n,queryConfig:o,relation:a}of(Array.isArray(g)||(g=[g]),S=g.map(e=>(0,i.is)(e,u.V)?(0,l.ug)(e,y):(0,l.yY)(e,y)),v=c.limit,_=c.offset,m)){let c=(0,p.W0)(t,r,a),u=r[(0,f.Lf)(a.referencedTable)],d=`${y}_${n}`,m=(0,h.Uo)(...c.fields.map((e,t)=>(0,h.eq)((0,l.ug)(c.references[t],d),(0,l.ug)(e,y)))),g=this.buildRelationalQueryWithoutLateralSubqueries({fullSchema:e,schema:t,tableNamesMap:r,table:e[u],tableConfig:t[u],queryConfig:(0,i.is)(a,p.pD)?!0===o?{limit:1}:{...o,limit:1}:o,tableAlias:d,joinOn:m,nestedQueryRelation:a}),b=(0,s.ll)`(${g.sql})`;(0,i.is)(a,p.iv)&&(b=(0,s.ll)`coalesce(${b}, json_array())`);let v=b.as(n);w.push({dbKey:n,tsKey:n,field:v,relationTableTsKey:u,isJson:!0,selection:g.selection})}}if(0===w.length)throw new d.n({message:`No fields selected for table "${a.tsName}" ("${y}"). You need to have at least one item in "columns", "with" or "extras". If you need to select all columns, omit the "columns" key or set it to undefined.`});if(k=(0,h.Uo)(g,k),m){let e=(0,s.ll)`json_array(${s.ll.join(w.map(({field:e})=>(0,i.is)(e,T)?s.ll.identifier(this.casing.getColumnCasing(e)):(0,i.is)(e,s.Xs.Aliased)?e.sql:e),(0,s.ll)`, `)})`;(0,i.is)(m,p.iv)&&(e=(0,s.ll)`json_arrayagg(${e})`);let t=[{dbKey:"data",tsKey:"data",field:e,isJson:!0,relationTableTsKey:a.tsName,selection:w}];void 0!==v||void 0!==_||S.length>0?(b=this.buildSelectQuery({table:(0,l.oG)(n,y),fields:{},fieldsFlat:[{path:[],field:s.ll.raw("*")},...S.length>0?[{path:[],field:(0,s.ll)`row_number() over (order by ${s.ll.join(S,(0,s.ll)`, `)})`}]:[]],where:k,limit:v,offset:_,setOperators:[]}),k=void 0,v=void 0,_=void 0,S=void 0):b=(0,l.oG)(n,y),b=this.buildSelectQuery({table:(0,i.is)(b,e3)?b:new o.n(b,{},y),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:(0,i.is)(e,u.V)?(0,l.ug)(e,y):e})),where:k,limit:v,offset:_,orderBy:S,setOperators:[]})}else b=this.buildSelectQuery({table:(0,l.oG)(n,y),fields:{},fieldsFlat:w.map(({field:e})=>({path:[],field:(0,i.is)(e,u.V)?(0,l.ug)(e,y):e})),where:k,limit:v,offset:_,orderBy:S,setOperators:[]});return{tableTsKey:a.tsName,sql:b,selection:w}}}var e9=r(8471),e7=r(9389);class te{static [i.i]="MySqlSelectBuilder";fields;session;dialect;withList=[];distinct;constructor(e){this.fields=e.fields,this.session=e.session,this.dialect=e.dialect,e.withList&&(this.withList=e.withList),this.distinct=e.distinct}from(e){let t;let r=!!this.fields;return t=this.fields?this.fields:(0,i.is)(e,o.n)?Object.fromEntries(Object.keys(e._.selectedFields).map(t=>[t,e[t]])):(0,i.is)(e,e6)?e[m.n].selectedFields:(0,i.is)(e,s.Xs)?{}:(0,y.YD)(e),new tr({table:e,fields:t,isPartialSelect:r,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct})}}class tt extends e9.O{static [i.i]="MySqlSelectQueryBuilder";_;config;joinsNotNullableMap;tableName;isPartialSelect;session;dialect;constructor({table:e,fields:t,isPartialSelect:r,session:i,dialect:n,withList:s,distinct:o}){super(),this.config={withList:s,table:e,fields:{...t},distinct:o,setOperators:[]},this.isPartialSelect=r,this.session=i,this.dialect=n,this._={selectedFields:t},this.tableName=(0,y.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}createJoin(e){return(t,r)=>{let a=this.tableName,l=(0,y.zN)(t);if("string"==typeof l&&this.config.joins?.some(e=>e.alias===l))throw Error(`Alias "${l}" is already used in this query`);if(!this.isPartialSelect&&(1===Object.keys(this.joinsNotNullableMap).length&&"string"==typeof a&&(this.config.fields={[a]:this.config.fields}),"string"==typeof l&&!(0,i.is)(t,s.Xs))){let e=(0,i.is)(t,o.n)?t._.selectedFields:(0,i.is)(t,s.Ss)?t[m.n].selectedFields:t[f.XI.Symbol.Columns];this.config.fields[l]=e}if("function"==typeof r&&(r=r(new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.joins||(this.config.joins=[]),this.config.joins.push({on:r,table:t,joinType:e,alias:l}),"string"==typeof l)switch(e){case"left":this.joinsNotNullableMap[l]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[l]=!0;break;case"inner":this.joinsNotNullableMap[l]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[l]=!1}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");createSetOperator(e,t){return r=>{let i="function"==typeof r?r(tn()):r;if(!(0,y.DV)(this.getSelectedFields(),i.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return this.config.setOperators.push({type:e,isAll:t,rightSelect:i}),this}}union=this.createSetOperator("union",!1);unionAll=this.createSetOperator("union",!0);intersect=this.createSetOperator("intersect",!1);intersectAll=this.createSetOperator("intersect",!0);except=this.createSetOperator("except",!1);exceptAll=this.createSetOperator("except",!0);addSetOperators(e){return this.config.setOperators.push(...e),this}where(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.where=e,this}having(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.having=e,this}groupBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"})));this.config.groupBy=Array.isArray(t)?t:[t]}else this.config.groupBy=e;return this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(t)?t:[t];this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=r:this.config.orderBy=r}else this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=e:this.config.orderBy=e;return this}limit(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).limit=e:this.config.limit=e,this}offset(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).offset=e:this.config.offset=e,this}for(e,t={}){return this.config.lockingClause={strength:e,config:t},this}getSQL(){return this.dialect.buildSelectQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}as(e){return new Proxy(new o.n(this.getSQL(),this.config.fields,e),new n.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}getSelectedFields(){return new Proxy(this.config.fields,new n.b({alias:this.tableName,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}$dynamic(){return this}}class tr extends tt{static [i.i]="MySqlSelect";prepare(){if(!this.session)throw Error("Cannot execute a query on a query builder. Please use a database instance instead.");let e=(0,y.He)(this.config.fields),t=this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),e);return t.joinsNotNullableMap=this.joinsNotNullableMap,t}execute=e=>this.prepare().execute(e);createIterator=()=>{let e=this;return async function*(t){yield*e.prepare().iterator(t)}};iterator=this.createIterator()}function ti(e,t){return(r,i,...n)=>{let s=[i,...n].map(r=>({type:e,isAll:t,rightSelect:r}));for(let e of s)if(!(0,y.DV)(r.getSelectedFields(),e.rightSelect.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return r.addSetOperators(s)}}(0,y.XJ)(tr,[e7.k]);let tn=()=>({union:ts,unionAll:to,intersect:ta,intersectAll:tl,except:tc,exceptAll:tu}),ts=ti("union",!1),to=ti("union",!0),ta=ti("intersect",!1),tl=ti("intersect",!0),tc=ti("except",!1),tu=ti("except",!0);class td{static [i.i]="MySqlQueryBuilder";dialect;dialectConfig;constructor(e){this.dialect=(0,i.is)(e,e4)?e:void 0,this.dialectConfig=(0,i.is)(e,e4)?void 0:e}$with(e){let t=this;return{as:r=>("function"==typeof r&&(r=r(t)),new Proxy(new o.J(r.getSQL(),r.getSelectedFields(),e,!0),new n.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}}with(...e){let t=this;return{select:function(r){return new te({fields:r??void 0,session:void 0,dialect:t.getDialect(),withList:e})},selectDistinct:function(r){return new te({fields:r??void 0,session:void 0,dialect:t.getDialect(),withList:e,distinct:!0})}}}select(e){return new te({fields:e??void 0,session:void 0,dialect:this.getDialect()})}selectDistinct(e){return new te({fields:e??void 0,session:void 0,dialect:this.getDialect(),distinct:!0})}getDialect(){return this.dialect||(this.dialect=new e4(this.dialectConfig)),this.dialect}}class th{constructor(e,t,r,i){this.table=e,this.session=t,this.dialect=r,this.withList=i}static [i.i]="MySqlUpdateBuilder";set(e){return new tp(this.table,(0,y.q)(this.table,e),this.session,this.dialect,this.withList)}}class tp extends e7.k{constructor(e,t,r,i,n){super(),this.session=r,this.dialect=i,this.config={set:t,table:e,withList:n}}static [i.i]="MySqlUpdate";config;where(e){return this.config.where=e,this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.table[f.XI.Symbol.Columns],new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(t)?t:[t];this.config.orderBy=r}else this.config.orderBy=e;return this}limit(e){return this.config.limit=e,this}getSQL(){return this.dialect.buildUpdateQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}prepare(){return this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning)}execute=e=>this.prepare().execute(e);createIterator=()=>{let e=this;return async function*(t){yield*e.prepare().iterator(t)}};iterator=this.createIterator();$dynamic(){return this}}class tf extends e7.k{constructor(e,t,r,i){super(),this.table=e,this.session=t,this.dialect=r,this.config={table:e,withList:i}}static [i.i]="MySqlDelete";config;where(e){return this.config.where=e,this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.table[f.XI.Symbol.Columns],new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(t)?t:[t];this.config.orderBy=r}else this.config.orderBy=e;return this}limit(e){return this.config.limit=e,this}getSQL(){return this.dialect.buildDeleteQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}prepare(){return this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning)}execute=e=>this.prepare().execute(e);createIterator=()=>{let e=this;return async function*(t){yield*e.prepare().iterator(t)}};iterator=this.createIterator();$dynamic(){return this}}class ty{constructor(e,t,r){this.table=e,this.session=t,this.dialect=r}static [i.i]="MySqlInsertBuilder";shouldIgnore=!1;ignore(){return this.shouldIgnore=!0,this}values(e){if(0===(e=Array.isArray(e)?e:[e]).length)throw Error("values() must be called with at least one value");let t=e.map(e=>{let t={},r=this.table[f.XI.Symbol.Columns];for(let n of Object.keys(e)){let o=e[n];t[n]=(0,i.is)(o,s.Xs)?o:new s.Iw(o,r[n])}return t});return new tm(this.table,t,this.shouldIgnore,this.session,this.dialect)}select(e){let t="function"==typeof e?e(new td):e;if(!(0,i.is)(t,s.Xs)&&!(0,y.DV)(this.table[f.e],t._.selectedFields))throw Error("Insert select error: selected fields are not the same or are in a different order compared to the table definition");return new tm(this.table,t,this.shouldIgnore,this.session,this.dialect,!0)}}class tm extends e7.k{constructor(e,t,r,i,n,s){super(),this.session=i,this.dialect=n,this.config={table:e,values:t,select:s,ignore:r}}static [i.i]="MySqlInsert";config;onDuplicateKeyUpdate(e){let t=this.dialect.buildUpdateSet(this.config.table,(0,y.q)(this.config.table,e.set));return this.config.onConflict=(0,s.ll)`update ${t}`,this}$returningId(){let e=[];for(let[t,r]of Object.entries(this.config.table[f.XI.Symbol.Columns]))r.primary&&e.push({field:r,path:[t]});return this.config.returning=e,this}getSQL(){return this.dialect.buildInsertQuery(this.config).sql}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}prepare(){let{sql:e,generatedIds:t}=this.dialect.buildInsertQuery(this.config);return this.session.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,t,this.config.returning)}execute=e=>this.prepare().execute(e);createIterator=()=>{let e=this;return async function*(t){yield*e.prepare().iterator(t)}};iterator=this.createIterator();$dynamic(){return this}}class tg{constructor(e,t,r,i,n,s,o,a){this.fullSchema=e,this.schema=t,this.tableNamesMap=r,this.table=i,this.tableConfig=n,this.dialect=s,this.session=o,this.mode=a}static [i.i]="MySqlRelationalQueryBuilder";findMany(e){return new tb(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many",this.mode)}findFirst(e){return new tb(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first",this.mode)}}class tb extends e7.k{constructor(e,t,r,i,n,s,o,a,l,c){super(),this.fullSchema=e,this.schema=t,this.tableNamesMap=r,this.table=i,this.tableConfig=n,this.dialect=s,this.session=o,this.config=a,this.queryMode=l,this.mode=c}static [i.i]="MySqlRelationalQuery";prepare(){let{query:e,builtQuery:t}=this._toSQL();return this.session.prepareQuery(t,void 0,t=>{let r=t.map(t=>(0,p.I$)(this.schema,this.tableConfig,t,e.selection));return"first"===this.queryMode?r[0]:r})}_getQuery(){return"planetscale"===this.mode?this.dialect.buildRelationalQueryWithoutLateralSubqueries({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName}):this.dialect.buildRelationalQuery({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName})}_toSQL(){let e=this._getQuery();return{builtQuery:this.dialect.sqlToQuery(e.sql),query:e}}getSQL(){return this._getQuery().sql}toSQL(){return this._toSQL().builtQuery}execute(){return this.prepare().execute()}}class tw{constructor(e,t,r,i){if(this.dialect=e,this.session=t,this.mode=i,this._=r?{schema:r.schema,fullSchema:r.fullSchema,tableNamesMap:r.tableNamesMap}:{schema:void 0,fullSchema:{},tableNamesMap:{}},this.query={},this._.schema)for(let[i,n]of Object.entries(this._.schema))this.query[i]=new tg(r.fullSchema,this._.schema,this._.tableNamesMap,r.fullSchema[i],n,e,t,this.mode)}static [i.i]="MySqlDatabase";query;$with(e){let t=this;return{as:r=>("function"==typeof r&&(r=r(new td(t.dialect))),new Proxy(new o.J(r.getSQL(),r.getSelectedFields(),e,!0),new n.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}}$count(e,t){return new a({source:e,filters:t,session:this.session})}with(...e){let t=this;return{select:function(r){return new te({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e})},selectDistinct:function(r){return new te({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:!0})},update:function(r){return new th(r,t.session,t.dialect,e)},delete:function(r){return new tf(r,t.session,t.dialect,e)}}}select(e){return new te({fields:e??void 0,session:this.session,dialect:this.dialect})}selectDistinct(e){return new te({fields:e??void 0,session:this.session,dialect:this.dialect,distinct:!0})}update(e){return new th(e,this.session,this.dialect)}insert(e){return new ty(e,this.session,this.dialect)}delete(e){return new tf(e,this.session,this.dialect)}execute(e){return this.session.execute("string"==typeof e?s.ll.raw(e):e.getSQL())}transaction(e,t){return this.session.transaction(e,t)}}var tv=r(277),t_=r(4061),tS=r(2583),tk=r(8266),tA=r(3704),tx=r(6197),tT=r(4777),tE=r(5594),t$=r(3345);class tC extends s.Ss{static [i.i]="PgViewBase"}class tI{static [i.i]="PgDialect";casing;constructor(e){this.casing=new c.Yn(e?.casing)}async migrate(e,t,r){let i="string"==typeof r?"__drizzle_migrations":r.migrationsTable??"__drizzle_migrations",n="string"==typeof r?"drizzle":r.migrationsSchema??"drizzle",o=(0,s.ll)`
			CREATE TABLE IF NOT EXISTS ${s.ll.identifier(n)}.${s.ll.identifier(i)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at bigint
			)
		`;await t.execute((0,s.ll)`CREATE SCHEMA IF NOT EXISTS ${s.ll.identifier(n)}`),await t.execute(o);let a=(await t.all((0,s.ll)`select id, hash, created_at from ${s.ll.identifier(n)}.${s.ll.identifier(i)} order by created_at desc limit 1`))[0];await t.transaction(async t=>{for await(let r of e)if(!a||Number(a.created_at)<r.folderMillis){for(let e of r.sql)await t.execute(s.ll.raw(e));await t.execute((0,s.ll)`insert into ${s.ll.identifier(n)}.${s.ll.identifier(i)} ("hash", "created_at") values(${r.hash}, ${r.folderMillis})`)}})}escapeName(e){return`"${e}"`}escapeParam(e){return`$${e+1}`}escapeString(e){return`'${e.replace(/'/g,"''")}'`}buildWithCTE(e){if(!e?.length)return;let t=[(0,s.ll)`with `];for(let[r,i]of e.entries())t.push((0,s.ll)`${s.ll.identifier(i._.alias)} as (${i._.sql})`),r<e.length-1&&t.push((0,s.ll)`, `);return t.push((0,s.ll)` `),s.ll.join(t)}buildDeleteQuery({table:e,where:t,returning:r,withList:i}){let n=this.buildWithCTE(i),o=r?(0,s.ll)` returning ${this.buildSelection(r,{isSingleTable:!0})}`:void 0,a=t?(0,s.ll)` where ${t}`:void 0;return(0,s.ll)`${n}delete from ${e}${a}${o}`}buildUpdateSet(e,t){let r=e[f.XI.Symbol.Columns],i=Object.keys(r).filter(e=>void 0!==t[e]||r[e]?.onUpdateFn!==void 0),n=i.length;return s.ll.join(i.flatMap((e,i)=>{let o=r[e],a=t[e]??s.ll.param(o.onUpdateFn(),o),l=(0,s.ll)`${s.ll.identifier(this.casing.getColumnCasing(o))} = ${a}`;return i<n-1?[l,s.ll.raw(", ")]:[l]}))}buildUpdateQuery({table:e,set:t,where:r,returning:i,withList:n,from:o,joins:a}){let l=this.buildWithCTE(n),c=e[t$.mu.Symbol.Name],u=e[t$.mu.Symbol.Schema],d=e[t$.mu.Symbol.OriginalName],h=c===d?void 0:c,p=(0,s.ll)`${u?(0,s.ll)`${s.ll.identifier(u)}.`:void 0}${s.ll.identifier(d)}${h&&(0,s.ll)` ${s.ll.identifier(h)}`}`,f=this.buildUpdateSet(e,t),y=o&&s.ll.join([s.ll.raw(" from "),this.buildFromTable(o)]),m=this.buildJoins(a),g=i?(0,s.ll)` returning ${this.buildSelection(i,{isSingleTable:!o})}`:void 0,b=r?(0,s.ll)` where ${r}`:void 0;return(0,s.ll)`${l}update ${p} set ${f}${y}${m}${b}${g}`}buildSelection(e,{isSingleTable:t=!1}={}){let r=e.length,n=e.flatMap(({field:e},n)=>{let o=[];if((0,i.is)(e,s.Xs.Aliased)&&e.isSelectionField)o.push(s.ll.identifier(e.fieldAlias));else if((0,i.is)(e,s.Xs.Aliased)||(0,i.is)(e,s.Xs)){let r=(0,i.is)(e,s.Xs.Aliased)?e.sql:e;t?o.push(new s.Xs(r.queryChunks.map(e=>(0,i.is)(e,tv.Kl)?s.ll.identifier(this.casing.getColumnCasing(e)):e))):o.push(r),(0,i.is)(e,s.Xs.Aliased)&&o.push((0,s.ll)` as ${s.ll.identifier(e.fieldAlias)}`)}else(0,i.is)(e,u.V)&&(t?o.push(s.ll.identifier(this.casing.getColumnCasing(e))):o.push(e));return n<r-1&&o.push((0,s.ll)`, `),o});return s.ll.join(n)}buildJoins(e){if(!e||0===e.length)return;let t=[];for(let[r,n]of e.entries()){0===r&&t.push((0,s.ll)` `);let o=n.table,a=n.lateral?(0,s.ll)` lateral`:void 0;if((0,i.is)(o,t$.mu)){let e=o[t$.mu.Symbol.Name],r=o[t$.mu.Symbol.Schema],i=o[t$.mu.Symbol.OriginalName],l=e===i?void 0:n.alias;t.push((0,s.ll)`${s.ll.raw(n.joinType)} join${a} ${r?(0,s.ll)`${s.ll.identifier(r)}.`:void 0}${s.ll.identifier(i)}${l&&(0,s.ll)` ${s.ll.identifier(l)}`} on ${n.on}`)}else if((0,i.is)(o,s.Ss)){let e=o[m.n].name,r=o[m.n].schema,i=o[m.n].originalName,l=e===i?void 0:n.alias;t.push((0,s.ll)`${s.ll.raw(n.joinType)} join${a} ${r?(0,s.ll)`${s.ll.identifier(r)}.`:void 0}${s.ll.identifier(i)}${l&&(0,s.ll)` ${s.ll.identifier(l)}`} on ${n.on}`)}else t.push((0,s.ll)`${s.ll.raw(n.joinType)} join${a} ${o} on ${n.on}`);r<e.length-1&&t.push((0,s.ll)` `)}return s.ll.join(t)}buildFromTable(e){if((0,i.is)(e,f.XI)&&e[f.XI.Symbol.OriginalName]!==e[f.XI.Symbol.Name]){let t=(0,s.ll)`${s.ll.identifier(e[f.XI.Symbol.OriginalName])}`;return e[f.XI.Symbol.Schema]&&(t=(0,s.ll)`${s.ll.identifier(e[f.XI.Symbol.Schema])}.${t}`),(0,s.ll)`${t} ${s.ll.identifier(e[f.XI.Symbol.Name])}`}return e}buildSelectQuery({withList:e,fields:t,fieldsFlat:r,where:n,having:a,table:l,joins:c,orderBy:d,groupBy:h,limit:p,offset:g,lockingClause:b,distinct:w,setOperators:v}){let _,S,k;let A=r??(0,y.He)(t);for(let e of A){let t;if((0,i.is)(e.field,u.V)&&(0,f.Io)(e.field.table)!==((0,i.is)(l,o.n)?l._.alias:(0,i.is)(l,tC)?l[m.n].name:(0,i.is)(l,s.Xs)?void 0:(0,f.Io)(l))&&(t=e.field.table,!c?.some(({alias:e})=>e===(t[f.XI.Symbol.IsAlias]?f.Io(t):t[f.XI.Symbol.BaseName])))){let t=(0,f.Io)(e.field.table);throw Error(`Your "${e.path.join("->")}" field references a column "${t}"."${e.field.name}", but the table "${t}" is not part of the query! Did you forget to join it?`)}}let x=!c||0===c.length,T=this.buildWithCTE(e);w&&(_=!0===w?(0,s.ll)` distinct`:(0,s.ll)` distinct on (${s.ll.join(w.on,(0,s.ll)`, `)})`);let E=this.buildSelection(A,{isSingleTable:x}),$=this.buildFromTable(l),C=this.buildJoins(c),I=n?(0,s.ll)` where ${n}`:void 0,U=a?(0,s.ll)` having ${a}`:void 0;d&&d.length>0&&(S=(0,s.ll)` order by ${s.ll.join(d,(0,s.ll)`, `)}`),h&&h.length>0&&(k=(0,s.ll)` group by ${s.ll.join(h,(0,s.ll)`, `)}`);let q="object"==typeof p||"number"==typeof p&&p>=0?(0,s.ll)` limit ${p}`:void 0,P=g?(0,s.ll)` offset ${g}`:void 0,O=s.ll.empty();if(b){let e=(0,s.ll)` for ${s.ll.raw(b.strength)}`;b.config.of&&e.append((0,s.ll)` of ${s.ll.join(Array.isArray(b.config.of)?b.config.of:[b.config.of],(0,s.ll)`, `)}`),b.config.noWait?e.append((0,s.ll)` no wait`):b.config.skipLocked&&e.append((0,s.ll)` skip locked`),O.append(e)}let N=(0,s.ll)`${T}select${_} ${E} from ${$}${C}${I}${k}${U}${S}${q}${P}${O}`;return v.length>0?this.buildSetOperations(N,v):N}buildSetOperations(e,t){let[r,...i]=t;if(!r)throw Error("Cannot pass undefined values to any set operator");return 0===i.length?this.buildSetOperationQuery({leftSelect:e,setOperator:r}):this.buildSetOperations(this.buildSetOperationQuery({leftSelect:e,setOperator:r}),i)}buildSetOperationQuery({leftSelect:e,setOperator:{type:t,isAll:r,rightSelect:n,limit:o,orderBy:a,offset:l}}){let c;let u=(0,s.ll)`(${e.getSQL()}) `,d=(0,s.ll)`(${n.getSQL()})`;if(a&&a.length>0){let e=[];for(let t of a)if((0,i.is)(t,tv.Kl))e.push(s.ll.identifier(t.name));else if((0,i.is)(t,s.Xs)){for(let e=0;e<t.queryChunks.length;e++){let r=t.queryChunks[e];(0,i.is)(r,tv.Kl)&&(t.queryChunks[e]=s.ll.identifier(r.name))}e.push((0,s.ll)`${t}`)}else e.push((0,s.ll)`${t}`);c=(0,s.ll)` order by ${s.ll.join(e,(0,s.ll)`, `)} `}let h="object"==typeof o||"number"==typeof o&&o>=0?(0,s.ll)` limit ${o}`:void 0,p=s.ll.raw(`${t} ${r?"all ":""}`),f=l?(0,s.ll)` offset ${l}`:void 0;return(0,s.ll)`${u}${p}${d}${c}${h}${f}`}buildInsertQuery({table:e,values:t,onConflict:r,returning:n,withList:o,select:a,overridingSystemValue_:l}){let c=[],u=Object.entries(e[f.XI.Symbol.Columns]).filter(([e,t])=>!t.shouldDisableInsert()),d=u.map(([,e])=>s.ll.identifier(this.casing.getColumnCasing(e)));if(a)(0,i.is)(t,s.Xs)?c.push(t):c.push(t.getSQL());else for(let[e,r]of(c.push(s.ll.raw("values ")),t.entries())){let n=[];for(let[e,t]of u){let o=r[e];if(void 0===o||(0,i.is)(o,s.Iw)&&void 0===o.value){if(void 0!==t.defaultFn){let e=t.defaultFn(),r=(0,i.is)(e,s.Xs)?e:s.ll.param(e,t);n.push(r)}else if(t.default||void 0===t.onUpdateFn)n.push((0,s.ll)`default`);else{let e=t.onUpdateFn(),r=(0,i.is)(e,s.Xs)?e:s.ll.param(e,t);n.push(r)}}else n.push(o)}c.push(n),e<t.length-1&&c.push((0,s.ll)`, `)}let h=this.buildWithCTE(o),p=s.ll.join(c),y=n?(0,s.ll)` returning ${this.buildSelection(n,{isSingleTable:!0})}`:void 0,m=r?(0,s.ll)` on conflict ${r}`:void 0,g=!0===l?(0,s.ll)`overriding system value `:void 0;return(0,s.ll)`${h}insert into ${e} ${d} ${g}${p}${m}${y}`}buildRefreshMaterializedViewQuery({view:e,concurrently:t,withNoData:r}){let i=t?(0,s.ll)` concurrently`:void 0,n=r?(0,s.ll)` with no data`:void 0;return(0,s.ll)`refresh materialized view${i} ${e}${n}`}prepareTyping(e){return(0,i.is)(e,t_.kn)||(0,i.is)(e,tS.iX)?"json":(0,i.is)(e,tk.Z5)?"decimal":(0,i.is)(e,tA.Xd)?"time":(0,i.is)(e,tx.KM)||(0,i.is)(e,tx.xQ)?"timestamp":(0,i.is)(e,tT.qw)||(0,i.is)(e,tT.dw)?"date":(0,i.is)(e,tE.dL)?"uuid":"none"}sqlToQuery(e,t){return e.toQuery({casing:this.casing,escapeName:this.escapeName,escapeParam:this.escapeParam,escapeString:this.escapeString,prepareTyping:this.prepareTyping,invokeSource:t})}buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:r,table:n,tableConfig:a,queryConfig:c,tableAlias:y,nestedQueryRelation:m,joinOn:g}){let b,w=[],v,_,S=[],k,A=[];if(!0===c)w=Object.entries(a.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:(0,l.ug)(t,y),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let n=Object.fromEntries(Object.entries(a.columns).map(([e,t])=>[e,(0,l.ug)(t,y)]));if(c.where){let e="function"==typeof c.where?c.where(n,(0,p.mm)()):c.where;k=e&&(0,l.yY)(e,y)}let d=[],m=[];if(c.columns){let e=!1;for(let[t,r]of Object.entries(c.columns))void 0!==r&&t in a.columns&&(e||!0!==r||(e=!0),m.push(t));m.length>0&&(m=e?m.filter(e=>c.columns?.[e]===!0):Object.keys(a.columns).filter(e=>!m.includes(e)))}else m=Object.keys(a.columns);for(let e of m){let t=a.columns[e];d.push({tsKey:e,value:t})}let g=[];if(c.with&&(g=Object.entries(c.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:a.relations[e]}))),c.extras)for(let[e,t]of Object.entries("function"==typeof c.extras?c.extras(n,{sql:s.ll}):c.extras))d.push({tsKey:e,value:(0,l.Hs)(t,y)});for(let{tsKey:e,value:t}of d)w.push({dbKey:(0,i.is)(t,s.Xs.Aliased)?t.fieldAlias:a.columns[e].name,tsKey:e,field:(0,i.is)(t,u.V)?(0,l.ug)(t,y):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let b="function"==typeof c.orderBy?c.orderBy(n,(0,p.rl)()):c.orderBy??[];for(let{tsKey:n,queryConfig:a,relation:d}of(Array.isArray(b)||(b=[b]),S=b.map(e=>(0,i.is)(e,u.V)?(0,l.ug)(e,y):(0,l.yY)(e,y)),v=c.limit,_=c.offset,g)){let c=(0,p.W0)(t,r,d),u=r[(0,f.Lf)(d.referencedTable)],m=`${y}_${n}`,g=(0,h.Uo)(...c.fields.map((e,t)=>(0,h.eq)((0,l.ug)(c.references[t],m),(0,l.ug)(e,y)))),b=this.buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:r,table:e[u],tableConfig:t[u],queryConfig:(0,i.is)(d,p.pD)?!0===a?{limit:1}:{...a,limit:1}:a,tableAlias:m,joinOn:g,nestedQueryRelation:d}),v=(0,s.ll)`${s.ll.identifier(m)}.${s.ll.identifier("data")}`.as(n);A.push({on:(0,s.ll)`true`,table:new o.n(b.sql,{},m),alias:m,joinType:"left",lateral:!0}),w.push({dbKey:n,tsKey:n,field:v,relationTableTsKey:u,isJson:!0,selection:b.selection})}}if(0===w.length)throw new d.n({message:`No fields selected for table "${a.tsName}" ("${y}")`});if(k=(0,h.Uo)(g,k),m){let e=(0,s.ll)`json_build_array(${s.ll.join(w.map(({field:e,tsKey:t,isJson:r})=>r?(0,s.ll)`${s.ll.identifier(`${y}_${t}`)}.${s.ll.identifier("data")}`:(0,i.is)(e,s.Xs.Aliased)?e.sql:e),(0,s.ll)`, `)})`;(0,i.is)(m,p.iv)&&(e=(0,s.ll)`coalesce(json_agg(${e}${S.length>0?(0,s.ll)` order by ${s.ll.join(S,(0,s.ll)`, `)}`:void 0}), '[]'::json)`);let t=[{dbKey:"data",tsKey:"data",field:e.as("data"),isJson:!0,relationTableTsKey:a.tsName,selection:w}];void 0!==v||void 0!==_||S.length>0?(b=this.buildSelectQuery({table:(0,l.oG)(n,y),fields:{},fieldsFlat:[{path:[],field:s.ll.raw("*")}],where:k,limit:v,offset:_,orderBy:S,setOperators:[]}),k=void 0,v=void 0,_=void 0,S=[]):b=(0,l.oG)(n,y),b=this.buildSelectQuery({table:(0,i.is)(b,t$.mu)?b:new o.n(b,{},y),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:(0,i.is)(e,u.V)?(0,l.ug)(e,y):e})),joins:A,where:k,limit:v,offset:_,orderBy:S,setOperators:[]})}else b=this.buildSelectQuery({table:(0,l.oG)(n,y),fields:{},fieldsFlat:w.map(({field:e})=>({path:[],field:(0,i.is)(e,u.V)?(0,l.ug)(e,y):e})),joins:A,where:k,limit:v,offset:_,orderBy:S,setOperators:[]});return{tableTsKey:a.tsName,sql:b,selection:w}}}var tU=r(7424);class tq{static [i.i]="PgSelectBuilder";fields;session;dialect;withList=[];distinct;constructor(e){this.fields=e.fields,this.session=e.session,this.dialect=e.dialect,e.withList&&(this.withList=e.withList),this.distinct=e.distinct}authToken;setToken(e){return this.authToken=e,this}from(e){let t;let r=!!this.fields;return t=this.fields?this.fields:(0,i.is)(e,o.n)?Object.fromEntries(Object.keys(e._.selectedFields).map(t=>[t,e[t]])):(0,i.is)(e,tC)?e[m.n].selectedFields:(0,i.is)(e,s.Xs)?{}:(0,y.YD)(e),void 0===this.authToken?new tO({table:e,fields:t,isPartialSelect:r,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct}):new tO({table:e,fields:t,isPartialSelect:r,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct}).setToken(this.authToken)}}class tP extends e9.O{static [i.i]="PgSelectQueryBuilder";_;config;joinsNotNullableMap;tableName;isPartialSelect;session;dialect;constructor({table:e,fields:t,isPartialSelect:r,session:i,dialect:n,withList:s,distinct:o}){super(),this.config={withList:s,table:e,fields:{...t},distinct:o,setOperators:[]},this.isPartialSelect=r,this.session=i,this.dialect=n,this._={selectedFields:t},this.tableName=(0,y.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}createJoin(e){return(t,r)=>{let a=this.tableName,l=(0,y.zN)(t);if("string"==typeof l&&this.config.joins?.some(e=>e.alias===l))throw Error(`Alias "${l}" is already used in this query`);if(!this.isPartialSelect&&(1===Object.keys(this.joinsNotNullableMap).length&&"string"==typeof a&&(this.config.fields={[a]:this.config.fields}),"string"==typeof l&&!(0,i.is)(t,s.Xs))){let e=(0,i.is)(t,o.n)?t._.selectedFields:(0,i.is)(t,s.Ss)?t[m.n].selectedFields:t[f.XI.Symbol.Columns];this.config.fields[l]=e}if("function"==typeof r&&(r=r(new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.joins||(this.config.joins=[]),this.config.joins.push({on:r,table:t,joinType:e,alias:l}),"string"==typeof l)switch(e){case"left":this.joinsNotNullableMap[l]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[l]=!0;break;case"inner":this.joinsNotNullableMap[l]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[l]=!1}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");createSetOperator(e,t){return r=>{let i="function"==typeof r?r(tj()):r;if(!(0,y.DV)(this.getSelectedFields(),i.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return this.config.setOperators.push({type:e,isAll:t,rightSelect:i}),this}}union=this.createSetOperator("union",!1);unionAll=this.createSetOperator("union",!0);intersect=this.createSetOperator("intersect",!1);intersectAll=this.createSetOperator("intersect",!0);except=this.createSetOperator("except",!1);exceptAll=this.createSetOperator("except",!0);addSetOperators(e){return this.config.setOperators.push(...e),this}where(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.where=e,this}having(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.having=e,this}groupBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"})));this.config.groupBy=Array.isArray(t)?t:[t]}else this.config.groupBy=e;return this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(t)?t:[t];this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=r:this.config.orderBy=r}else this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=e:this.config.orderBy=e;return this}limit(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).limit=e:this.config.limit=e,this}offset(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).offset=e:this.config.offset=e,this}for(e,t={}){return this.config.lockingClause={strength:e,config:t},this}getSQL(){return this.dialect.buildSelectQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}as(e){return new Proxy(new o.n(this.getSQL(),this.config.fields,e),new n.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}getSelectedFields(){return new Proxy(this.config.fields,new n.b({alias:this.tableName,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}$dynamic(){return this}}class tO extends tP{static [i.i]="PgSelect";_prepare(e){let{session:t,config:r,dialect:i,joinsNotNullableMap:n,authToken:s}=this;if(!t)throw Error("Cannot execute a query on a query builder. Please use a database instance instead.");return tU.k.startActiveSpan("drizzle.prepareQuery",()=>{let o=(0,y.He)(r.fields),a=t.prepareQuery(i.sqlToQuery(this.getSQL()),o,e,!0);return a.joinsNotNullableMap=n,void 0===s?a:a.setToken(s)})}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>tU.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken))}function tN(e,t){return(r,i,...n)=>{let s=[i,...n].map(r=>({type:e,isAll:t,rightSelect:r}));for(let e of s)if(!(0,y.DV)(r.getSelectedFields(),e.rightSelect.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return r.addSetOperators(s)}}(0,y.XJ)(tO,[e7.k]);let tj=()=>({union:tR,unionAll:tD,intersect:tM,intersectAll:tL,except:tH,exceptAll:tW}),tR=tN("union",!1),tD=tN("union",!0),tM=tN("intersect",!1),tL=tN("intersect",!0),tH=tN("except",!1),tW=tN("except",!0);class tK{static [i.i]="PgQueryBuilder";dialect;dialectConfig;constructor(e){this.dialect=(0,i.is)(e,tI)?e:void 0,this.dialectConfig=(0,i.is)(e,tI)?void 0:e}$with(e){let t=this;return{as:r=>("function"==typeof r&&(r=r(t)),new Proxy(new o.J(r.getSQL(),r.getSelectedFields(),e,!0),new n.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}}with(...e){let t=this;return{select:function(r){return new tq({fields:r??void 0,session:void 0,dialect:t.getDialect(),withList:e})},selectDistinct:function(e){return new tq({fields:e??void 0,session:void 0,dialect:t.getDialect(),distinct:!0})},selectDistinctOn:function(e,r){return new tq({fields:r??void 0,session:void 0,dialect:t.getDialect(),distinct:{on:e}})}}}select(e){return new tq({fields:e??void 0,session:void 0,dialect:this.getDialect()})}selectDistinct(e){return new tq({fields:e??void 0,session:void 0,dialect:this.getDialect(),distinct:!0})}selectDistinctOn(e,t){return new tq({fields:t??void 0,session:void 0,dialect:this.getDialect(),distinct:{on:e}})}getDialect(){return this.dialect||(this.dialect=new tI(this.dialectConfig)),this.dialect}}class tB{constructor(e,t,r,i){this.table=e,this.session=t,this.dialect=r,this.withList=i}static [i.i]="PgUpdateBuilder";authToken;setToken(e){return this.authToken=e,this}set(e){return void 0===this.authToken?new tJ(this.table,(0,y.q)(this.table,e),this.session,this.dialect,this.withList):new tJ(this.table,(0,y.q)(this.table,e),this.session,this.dialect,this.withList).setToken(this.authToken)}}class tJ extends e7.k{constructor(e,t,r,i,n){super(),this.session=r,this.dialect=i,this.config={set:t,table:e,withList:n,joins:[]},this.tableName=(0,y.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}static [i.i]="PgUpdate";config;tableName;joinsNotNullableMap;from(e){let t=(0,y.zN)(e);return"string"==typeof t&&(this.joinsNotNullableMap[t]=!0),this.config.from=e,this}getTableLikeFields(e){return(0,i.is)(e,t$.mu)?e[f.XI.Symbol.Columns]:(0,i.is)(e,o.n)?e._.selectedFields:e[m.n].selectedFields}createJoin(e){return(t,r)=>{let o=(0,y.zN)(t);if("string"==typeof o&&this.config.joins.some(e=>e.alias===o))throw Error(`Alias "${o}" is already used in this query`);if("function"==typeof r){let e=this.config.from&&!(0,i.is)(this.config.from,s.Xs)?this.getTableLikeFields(this.config.from):void 0;r=r(new Proxy(this.config.table[f.XI.Symbol.Columns],new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})),e&&new Proxy(e,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))}if(this.config.joins.push({on:r,table:t,joinType:e,alias:o}),"string"==typeof o)switch(e){case"left":this.joinsNotNullableMap[o]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[o]=!0;break;case"inner":this.joinsNotNullableMap[o]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[o]=!1}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");where(e){return this.config.where=e,this}returning(e){if(!e&&(e=Object.assign({},this.config.table[f.XI.Symbol.Columns]),this.config.from)){let t=(0,y.zN)(this.config.from);if("string"==typeof t&&this.config.from&&!(0,i.is)(this.config.from,s.Xs)){let r=this.getTableLikeFields(this.config.from);e[t]=r}for(let t of this.config.joins){let r=(0,y.zN)(t.table);if("string"==typeof r&&!(0,i.is)(t.table,s.Xs)){let i=this.getTableLikeFields(t.table);e[r]=i}}}return this.config.returning=(0,y.He)(e),this}getSQL(){return this.dialect.buildUpdateQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){let t=this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0);return t.joinsNotNullableMap=this.joinsNotNullableMap,t}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>this._prepare().execute(e,this.authToken);$dynamic(){return this}}class tQ{constructor(e,t,r,i,n){this.table=e,this.session=t,this.dialect=r,this.withList=i,this.overridingSystemValue_=n}static [i.i]="PgInsertBuilder";authToken;setToken(e){return this.authToken=e,this}overridingSystemValue(){return this.overridingSystemValue_=!0,this}values(e){if(0===(e=Array.isArray(e)?e:[e]).length)throw Error("values() must be called with at least one value");let t=e.map(e=>{let t={},r=this.table[f.XI.Symbol.Columns];for(let n of Object.keys(e)){let o=e[n];t[n]=(0,i.is)(o,s.Xs)?o:new s.Iw(o,r[n])}return t});return void 0===this.authToken?new tz(this.table,t,this.session,this.dialect,this.withList,!1,this.overridingSystemValue_):new tz(this.table,t,this.session,this.dialect,this.withList,!1,this.overridingSystemValue_).setToken(this.authToken)}select(e){let t="function"==typeof e?e(new tK):e;if(!(0,i.is)(t,s.Xs)&&!(0,y.DV)(this.table[f.e],t._.selectedFields))throw Error("Insert select error: selected fields are not the same or are in a different order compared to the table definition");return new tz(this.table,t,this.session,this.dialect,this.withList,!0)}}class tz extends e7.k{constructor(e,t,r,i,n,s,o){super(),this.session=r,this.dialect=i,this.config={table:e,values:t,withList:n,select:s,overridingSystemValue_:o}}static [i.i]="PgInsert";config;returning(e=this.config.table[f.XI.Symbol.Columns]){return this.config.returning=(0,y.He)(e),this}onConflictDoNothing(e={}){if(void 0===e.target)this.config.onConflict=(0,s.ll)`do nothing`;else{let t="";t=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(this.dialect.casing.getColumnCasing(e))).join(","):this.dialect.escapeName(this.dialect.casing.getColumnCasing(e.target));let r=e.where?(0,s.ll)` where ${e.where}`:void 0;this.config.onConflict=(0,s.ll)`(${s.ll.raw(t)})${r} do nothing`}return this}onConflictDoUpdate(e){if(e.where&&(e.targetWhere||e.setWhere))throw Error('You cannot use both "where" and "targetWhere"/"setWhere" at the same time - "where" is deprecated, use "targetWhere" or "setWhere" instead.');let t=e.where?(0,s.ll)` where ${e.where}`:void 0,r=e.targetWhere?(0,s.ll)` where ${e.targetWhere}`:void 0,i=e.setWhere?(0,s.ll)` where ${e.setWhere}`:void 0,n=this.dialect.buildUpdateSet(this.config.table,(0,y.q)(this.config.table,e.set)),o="";return o=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(this.dialect.casing.getColumnCasing(e))).join(","):this.dialect.escapeName(this.dialect.casing.getColumnCasing(e.target)),this.config.onConflict=(0,s.ll)`(${s.ll.raw(o)})${r} do update set ${n}${t}${i}`,this}getSQL(){return this.dialect.buildInsertQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return tU.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>tU.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken));$dynamic(){return this}}class tF extends e7.k{constructor(e,t,r,i){super(),this.session=t,this.dialect=r,this.config={table:e,withList:i}}static [i.i]="PgDelete";config;where(e){return this.config.where=e,this}returning(e=this.config.table[f.XI.Symbol.Columns]){return this.config.returning=(0,y.He)(e),this}getSQL(){return this.dialect.buildDeleteQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return tU.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>tU.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken));$dynamic(){return this}}class tV extends s.Xs{constructor(e){super(tV.buildEmbeddedCount(e.source,e.filters).queryChunks),this.params=e,this.mapWith(Number),this.session=e.session,this.sql=tV.buildCount(e.source,e.filters)}sql;token;static [i.i]="PgCountBuilder";[Symbol.toStringTag]="PgCountBuilder";session;static buildEmbeddedCount(e,t){return(0,s.ll)`(select count(*) from ${e}${s.ll.raw(" where ").if(t)}${t})`}static buildCount(e,t){return(0,s.ll)`select count(*) as count from ${e}${s.ll.raw(" where ").if(t)}${t};`}setToken(e){this.token=e}then(e,t){return Promise.resolve(this.session.count(this.sql,this.token)).then(e,t)}catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}}class tX{constructor(e,t,r,i,n,s,o){this.fullSchema=e,this.schema=t,this.tableNamesMap=r,this.table=i,this.tableConfig=n,this.dialect=s,this.session=o}static [i.i]="PgRelationalQueryBuilder";findMany(e){return new tG(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many")}findFirst(e){return new tG(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first")}}class tG extends e7.k{constructor(e,t,r,i,n,s,o,a,l){super(),this.fullSchema=e,this.schema=t,this.tableNamesMap=r,this.table=i,this.tableConfig=n,this.dialect=s,this.session=o,this.config=a,this.mode=l}static [i.i]="PgRelationalQuery";_prepare(e){return tU.k.startActiveSpan("drizzle.prepareQuery",()=>{let{query:t,builtQuery:r}=this._toSQL();return this.session.prepareQuery(r,void 0,e,!0,(e,r)=>{let i=e.map(e=>(0,p.I$)(this.schema,this.tableConfig,e,t.selection,r));return"first"===this.mode?i[0]:i})})}prepare(e){return this._prepare(e)}_getQuery(){return this.dialect.buildRelationalQueryWithoutPK({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName})}getSQL(){return this._getQuery().sql}_toSQL(){let e=this._getQuery(),t=this.dialect.sqlToQuery(e.sql);return{query:e,builtQuery:t}}toSQL(){return this._toSQL().builtQuery}authToken;setToken(e){return this.authToken=e,this}execute(){return tU.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(void 0,this.authToken))}}class tY extends e7.k{constructor(e,t,r,i){super(),this.execute=e,this.sql=t,this.query=r,this.mapBatchResult=i}static [i.i]="PgRaw";getSQL(){return this.sql}getQuery(){return this.query}mapResult(e,t){return t?this.mapBatchResult(e):e}_prepare(){return this}isResponseInArrayMode(){return!1}}class tZ extends e7.k{constructor(e,t,r){super(),this.session=t,this.dialect=r,this.config={view:e}}static [i.i]="PgRefreshMaterializedView";config;concurrently(){if(void 0!==this.config.withNoData)throw Error("Cannot use concurrently and withNoData together");return this.config.concurrently=!0,this}withNoData(){if(void 0!==this.config.concurrently)throw Error("Cannot use concurrently and withNoData together");return this.config.withNoData=!0,this}getSQL(){return this.dialect.buildRefreshMaterializedViewQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return tU.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),void 0,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>tU.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken))}class t0{constructor(e,t,r){if(this.dialect=e,this.session=t,this._=r?{schema:r.schema,fullSchema:r.fullSchema,tableNamesMap:r.tableNamesMap,session:t}:{schema:void 0,fullSchema:{},tableNamesMap:{},session:t},this.query={},this._.schema)for(let[i,n]of Object.entries(this._.schema))this.query[i]=new tX(r.fullSchema,this._.schema,this._.tableNamesMap,r.fullSchema[i],n,e,t)}static [i.i]="PgDatabase";query;$with(e){let t=this;return{as:r=>("function"==typeof r&&(r=r(new tK(t.dialect))),new Proxy(new o.J(r.getSQL(),r.getSelectedFields(),e,!0),new n.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}}$count(e,t){return new tV({source:e,filters:t,session:this.session})}with(...e){let t=this;return{select:function(r){return new tq({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e})},selectDistinct:function(r){return new tq({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:!0})},selectDistinctOn:function(r,i){return new tq({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:{on:r}})},update:function(r){return new tB(r,t.session,t.dialect,e)},insert:function(r){return new tQ(r,t.session,t.dialect,e)},delete:function(r){return new tF(r,t.session,t.dialect,e)}}}select(e){return new tq({fields:e??void 0,session:this.session,dialect:this.dialect})}selectDistinct(e){return new tq({fields:e??void 0,session:this.session,dialect:this.dialect,distinct:!0})}selectDistinctOn(e,t){return new tq({fields:t??void 0,session:this.session,dialect:this.dialect,distinct:{on:e}})}update(e){return new tB(e,this.session,this.dialect)}insert(e){return new tQ(e,this.session,this.dialect)}delete(e){return new tF(e,this.session,this.dialect)}refreshMaterializedView(e){return new tZ(e,this.session,this.dialect)}authToken;execute(e){let t="string"==typeof e?s.ll.raw(e):e.getSQL(),r=this.dialect.sqlToQuery(t),i=this.session.prepareQuery(r,void 0,void 0,!1);return new tY(()=>i.execute(void 0,this.authToken),t,r,e=>i.mapResult(e,!0))}transaction(e,t){return this.session.transaction(e,t)}}var t1=r(4986);function t2(...e){return e[0].columns?new t5(e[0].columns,e[0].name):new t5(e)}class t5{static [i.i]="MySqlPrimaryKeyBuilder";columns;name;constructor(e,t){this.columns=e,this.name=t}build(e){return new t3(e,this.columns,this.name)}}class t3{constructor(e,t,r){this.table=e,this.columns=t,this.name=r}static [i.i]="MySqlPrimaryKey";columns;name;getName(){return this.name??`${this.table[e3.Symbol.Name]}_${this.columns.map(e=>e.name).join("_")}_pk`}}var t8=r(3606),t6=r(6071),t4=r(344),t9=r(3829),t7=r(7243),re=r(1939),rt=r(3268),rr=r(3797);function ri(e,t){if((0,i.is)(e,tw))return function(e,t){let{usersTable:r,accountsTable:i,sessionsTable:n,verificationTokensTable:s,authenticatorsTable:o}=function(e={}){let t=e.usersTable??e8("user",{id:eZ("id",{length:255}).primaryKey().$defaultFn(()=>crypto.randomUUID()),name:eZ("name",{length:255}),email:eZ("email",{length:255}).unique(),emailVerified:eB("emailVerified",{mode:"date",fsp:3}),image:eZ("image",{length:255})}),r=e.accountsTable??e8("account",{userId:eZ("userId",{length:255}).notNull().references(()=>t.id,{onDelete:"cascade"}),type:eZ("type",{length:255}).$type().notNull(),provider:eZ("provider",{length:255}).notNull(),providerAccountId:eZ("providerAccountId",{length:255}).notNull(),refresh_token:eZ("refresh_token",{length:255}),access_token:eZ("access_token",{length:255}),expires_at:em("expires_at"),token_type:eZ("token_type",{length:255}),scope:eZ("scope",{length:255}),id_token:eZ("id_token",{length:2048}),session_state:eZ("session_state",{length:255})},e=>({compositePk:t2({columns:[e.provider,e.providerAccountId]})})),i=e.sessionsTable??e8("session",{sessionToken:eZ("sessionToken",{length:255}).primaryKey(),userId:eZ("userId",{length:255}).notNull().references(()=>t.id,{onDelete:"cascade"}),expires:eB("expires",{mode:"date"}).notNull()}),n=e.verificationTokensTable??e8("verificationToken",{identifier:eZ("identifier",{length:255}).notNull(),token:eZ("token",{length:255}).notNull(),expires:eB("expires",{mode:"date"}).notNull()},e=>({compositePk:t2({columns:[e.identifier,e.token]})})),s=e.authenticatorsTable??e8("authenticator",{credentialID:eZ("credentialID",{length:255}).notNull().unique(),userId:eZ("userId",{length:255}).notNull().references(()=>t.id,{onDelete:"cascade"}),providerAccountId:eZ("providerAccountId",{length:255}).notNull(),credentialPublicKey:eZ("credentialPublicKey",{length:255}).notNull(),counter:em("counter").notNull(),credentialDeviceType:eZ("credentialDeviceType",{length:255}).notNull(),credentialBackedUp:M("credentialBackedUp").notNull(),transports:eZ("transports",{length:255})},e=>({compositePk:t2({columns:[e.userId,e.credentialID]})}));return{usersTable:t,accountsTable:r,sessionsTable:i,verificationTokensTable:n,authenticatorsTable:s}}(t);return{async createUser(t){let{id:i,...n}=t,s=(0,y.YD)(r).id.defaultFn,[o]=await e.insert(r).values(s?n:{...n,id:i}).$returningId();return e.select().from(r).where((0,h.eq)(r.id,o?o.id:i)).then(e=>e[0])},getUser:async t=>e.select().from(r).where((0,h.eq)(r.id,t)).then(e=>e.length>0?e[0]:null),getUserByEmail:async t=>e.select().from(r).where((0,h.eq)(r.email,t)).then(e=>e.length>0?e[0]:null),createSession:async t=>(await e.insert(n).values(t),e.select().from(n).where((0,h.eq)(n.sessionToken,t.sessionToken)).then(e=>e[0])),getSessionAndUser:async t=>e.select({session:n,user:r}).from(n).where((0,h.eq)(n.sessionToken,t)).innerJoin(r,(0,h.eq)(r.id,n.userId)).then(e=>e.length>0?e[0]:null),async updateUser(t){if(!t.id)throw Error("No user id.");await e.update(r).set(t).where((0,h.eq)(r.id,t.id));let[i]=await e.select().from(r).where((0,h.eq)(r.id,t.id));if(!i)throw Error("No user found.");return i},updateSession:async t=>(await e.update(n).set(t).where((0,h.eq)(n.sessionToken,t.sessionToken)),e.select().from(n).where((0,h.eq)(n.sessionToken,t.sessionToken)).then(e=>e[0])),async linkAccount(t){await e.insert(i).values(t)},async getUserByAccount(t){let n=await e.select({account:i,user:r}).from(i).innerJoin(r,(0,h.eq)(i.userId,r.id)).where((0,h.Uo)((0,h.eq)(i.provider,t.provider),(0,h.eq)(i.providerAccountId,t.providerAccountId))).then(e=>e[0]);return n?.user??null},async deleteSession(t){await e.delete(n).where((0,h.eq)(n.sessionToken,t))},createVerificationToken:async t=>(await e.insert(s).values(t),e.select().from(s).where((0,h.eq)(s.identifier,t.identifier)).then(e=>e[0])),async useVerificationToken(t){let r=await e.select().from(s).where((0,h.Uo)((0,h.eq)(s.identifier,t.identifier),(0,h.eq)(s.token,t.token))).then(e=>e.length>0?e[0]:null);return r&&await e.delete(s).where((0,h.Uo)((0,h.eq)(s.identifier,t.identifier),(0,h.eq)(s.token,t.token))),r},async deleteUser(t){await e.delete(r).where((0,h.eq)(r.id,t))},async unlinkAccount(t){await e.delete(i).where((0,h.Uo)((0,h.eq)(i.provider,t.provider),(0,h.eq)(i.providerAccountId,t.providerAccountId)))},getAccount:async(t,r)=>e.select().from(i).where((0,h.Uo)((0,h.eq)(i.provider,r),(0,h.eq)(i.providerAccountId,t))).then(e=>e[0]??null),createAuthenticator:async t=>(await e.insert(o).values(t),await e.select().from(o).where((0,h.eq)(o.credentialID,t.credentialID)).then(e=>e[0]??null)),getAuthenticator:async t=>await e.select().from(o).where((0,h.eq)(o.credentialID,t)).then(e=>e[0]??null),listAuthenticatorsByUserId:async t=>await e.select().from(o).where((0,h.eq)(o.userId,t)).then(e=>e),async updateAuthenticatorCounter(t,r){await e.update(o).set({counter:r}).where((0,h.eq)(o.credentialID,t));let i=await e.select().from(o).where((0,h.eq)(o.credentialID,t)).then(e=>e[0]);if(!i)throw Error("Authenticator not found.");return i}}}(e,t);if((0,i.is)(e,t0))return function(e,t){let{usersTable:r,accountsTable:i,sessionsTable:n,verificationTokensTable:s,authenticatorsTable:o}=function(e={}){let t=e.usersTable??(0,t$.cJ)("user",{id:(0,t8.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,t8.Qq)("name"),email:(0,t8.Qq)("email").unique(),emailVerified:(0,tx.vE)("emailVerified",{mode:"date"}),image:(0,t8.Qq)("image")}),r=e.accountsTable??(0,t$.cJ)("account",{userId:(0,t8.Qq)("userId").notNull().references(()=>t.id,{onDelete:"cascade"}),type:(0,t8.Qq)("type").$type().notNull(),provider:(0,t8.Qq)("provider").notNull(),providerAccountId:(0,t8.Qq)("providerAccountId").notNull(),refresh_token:(0,t8.Qq)("refresh_token"),access_token:(0,t8.Qq)("access_token"),expires_at:(0,t6.nd)("expires_at"),token_type:(0,t8.Qq)("token_type"),scope:(0,t8.Qq)("scope"),id_token:(0,t8.Qq)("id_token"),session_state:(0,t8.Qq)("session_state")},e=>({compositePk:(0,t4.ie)({columns:[e.provider,e.providerAccountId]})})),i=e.sessionsTable??(0,t$.cJ)("session",{sessionToken:(0,t8.Qq)("sessionToken").primaryKey(),userId:(0,t8.Qq)("userId").notNull().references(()=>t.id,{onDelete:"cascade"}),expires:(0,tx.vE)("expires",{mode:"date"}).notNull()}),n=e.verificationTokensTable??(0,t$.cJ)("verificationToken",{identifier:(0,t8.Qq)("identifier").notNull(),token:(0,t8.Qq)("token").notNull(),expires:(0,tx.vE)("expires",{mode:"date"}).notNull()},e=>({compositePk:(0,t4.ie)({columns:[e.identifier,e.token]})})),s=e.authenticatorsTable??(0,t$.cJ)("authenticator",{credentialID:(0,t8.Qq)("credentialID").notNull().unique(),userId:(0,t8.Qq)("userId").notNull().references(()=>t.id,{onDelete:"cascade"}),providerAccountId:(0,t8.Qq)("providerAccountId").notNull(),credentialPublicKey:(0,t8.Qq)("credentialPublicKey").notNull(),counter:(0,t6.nd)("counter").notNull(),credentialDeviceType:(0,t8.Qq)("credentialDeviceType").notNull(),credentialBackedUp:(0,t9.zM)("credentialBackedUp").notNull(),transports:(0,t8.Qq)("transports")},e=>({compositePK:(0,t4.ie)({columns:[e.userId,e.credentialID]})}));return{usersTable:t,accountsTable:r,sessionsTable:i,verificationTokensTable:n,authenticatorsTable:s}}(t);return{async createUser(t){let{id:i,...n}=t,s=(0,y.YD)(r).id.hasDefault;return e.insert(r).values(s?n:{...n,id:i}).returning().then(e=>e[0])},getUser:async t=>e.select().from(r).where((0,h.eq)(r.id,t)).then(e=>e.length>0?e[0]:null),getUserByEmail:async t=>e.select().from(r).where((0,h.eq)(r.email,t)).then(e=>e.length>0?e[0]:null),createSession:async t=>e.insert(n).values(t).returning().then(e=>e[0]),getSessionAndUser:async t=>e.select({session:n,user:r}).from(n).where((0,h.eq)(n.sessionToken,t)).innerJoin(r,(0,h.eq)(r.id,n.userId)).then(e=>e.length>0?e[0]:null),async updateUser(t){if(!t.id)throw Error("No user id.");let[i]=await e.update(r).set(t).where((0,h.eq)(r.id,t.id)).returning();if(!i)throw Error("No user found.");return i},updateSession:async t=>e.update(n).set(t).where((0,h.eq)(n.sessionToken,t.sessionToken)).returning().then(e=>e[0]),async linkAccount(t){await e.insert(i).values(t)},async getUserByAccount(t){let n=await e.select({account:i,user:r}).from(i).innerJoin(r,(0,h.eq)(i.userId,r.id)).where((0,h.Uo)((0,h.eq)(i.provider,t.provider),(0,h.eq)(i.providerAccountId,t.providerAccountId))).then(e=>e[0]);return n?.user??null},async deleteSession(t){await e.delete(n).where((0,h.eq)(n.sessionToken,t))},createVerificationToken:async t=>e.insert(s).values(t).returning().then(e=>e[0]),useVerificationToken:async t=>e.delete(s).where((0,h.Uo)((0,h.eq)(s.identifier,t.identifier),(0,h.eq)(s.token,t.token))).returning().then(e=>e.length>0?e[0]:null),async deleteUser(t){await e.delete(r).where((0,h.eq)(r.id,t))},async unlinkAccount(t){await e.delete(i).where((0,h.Uo)((0,h.eq)(i.provider,t.provider),(0,h.eq)(i.providerAccountId,t.providerAccountId)))},getAccount:async(t,r)=>e.select().from(i).where((0,h.Uo)((0,h.eq)(i.provider,r),(0,h.eq)(i.providerAccountId,t))).then(e=>e[0]??null),createAuthenticator:async t=>e.insert(o).values(t).returning().then(e=>e[0]??null),getAuthenticator:async t=>e.select().from(o).where((0,h.eq)(o.credentialID,t)).then(e=>e[0]??null),listAuthenticatorsByUserId:async t=>e.select().from(o).where((0,h.eq)(o.userId,t)).then(e=>e),async updateAuthenticatorCounter(t,r){let i=await e.update(o).set({counter:r}).where((0,h.eq)(o.credentialID,t)).returning().then(e=>e[0]);if(!i)throw Error("Authenticator not found.");return i}}}(e,t);if((0,i.is)(e,t1.N))return function(e,t){let{usersTable:r,accountsTable:i,sessionsTable:n,verificationTokensTable:s,authenticatorsTable:o}=function(e={}){let t=e.usersTable??(0,t7.D)("user",{id:(0,re.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,re.Qq)("name"),email:(0,re.Qq)("email").unique(),emailVerified:(0,rt.nd)("emailVerified",{mode:"timestamp_ms"}),image:(0,re.Qq)("image")}),r=e.accountsTable??(0,t7.D)("account",{userId:(0,re.Qq)("userId").notNull().references(()=>t.id,{onDelete:"cascade"}),type:(0,re.Qq)("type").$type().notNull(),provider:(0,re.Qq)("provider").notNull(),providerAccountId:(0,re.Qq)("providerAccountId").notNull(),refresh_token:(0,re.Qq)("refresh_token"),access_token:(0,re.Qq)("access_token"),expires_at:(0,rt.nd)("expires_at"),token_type:(0,re.Qq)("token_type"),scope:(0,re.Qq)("scope"),id_token:(0,re.Qq)("id_token"),session_state:(0,re.Qq)("session_state")},e=>({compositePk:(0,rr.ie)({columns:[e.provider,e.providerAccountId]})})),i=e.sessionsTable??(0,t7.D)("session",{sessionToken:(0,re.Qq)("sessionToken").primaryKey(),userId:(0,re.Qq)("userId").notNull().references(()=>t.id,{onDelete:"cascade"}),expires:(0,rt.nd)("expires",{mode:"timestamp_ms"}).notNull()}),n=e.verificationTokensTable??(0,t7.D)("verificationToken",{identifier:(0,re.Qq)("identifier").notNull(),token:(0,re.Qq)("token").notNull(),expires:(0,rt.nd)("expires",{mode:"timestamp_ms"}).notNull()},e=>({compositePk:(0,rr.ie)({columns:[e.identifier,e.token]})})),s=e.authenticatorsTable??(0,t7.D)("authenticator",{credentialID:(0,re.Qq)("credentialID").notNull().unique(),userId:(0,re.Qq)("userId").notNull().references(()=>t.id,{onDelete:"cascade"}),providerAccountId:(0,re.Qq)("providerAccountId").notNull(),credentialPublicKey:(0,re.Qq)("credentialPublicKey").notNull(),counter:(0,rt.nd)("counter").notNull(),credentialDeviceType:(0,re.Qq)("credentialDeviceType").notNull(),credentialBackedUp:(0,rt.nd)("credentialBackedUp",{mode:"boolean"}).notNull(),transports:(0,re.Qq)("transports")},e=>({compositePK:(0,rr.ie)({columns:[e.userId,e.credentialID]})}));return{usersTable:t,accountsTable:r,sessionsTable:i,verificationTokensTable:n,authenticatorsTable:s}}(t);return{async createUser(t){let{id:i,...n}=t,s=(0,y.YD)(r).id.hasDefault;return e.insert(r).values(s?n:{...n,id:i}).returning().get()},getUser:async t=>await e.select().from(r).where((0,h.eq)(r.id,t)).get()??null,getUserByEmail:async t=>await e.select().from(r).where((0,h.eq)(r.email,t)).get()??null,createSession:async t=>e.insert(n).values(t).returning().get(),getSessionAndUser:async t=>await e.select({session:n,user:r}).from(n).where((0,h.eq)(n.sessionToken,t)).innerJoin(r,(0,h.eq)(r.id,n.userId)).get()??null,async updateUser(t){if(!t.id)throw Error("No user id.");let i=await e.update(r).set(t).where((0,h.eq)(r.id,t.id)).returning().get();if(!i)throw Error("User not found.");return i},updateSession:async t=>await e.update(n).set(t).where((0,h.eq)(n.sessionToken,t.sessionToken)).returning().get()??null,async linkAccount(t){await e.insert(i).values(t).run()},async getUserByAccount(t){let n=await e.select({account:i,user:r}).from(i).innerJoin(r,(0,h.eq)(i.userId,r.id)).where((0,h.Uo)((0,h.eq)(i.provider,t.provider),(0,h.eq)(i.providerAccountId,t.providerAccountId))).get();return n?.user??null},async deleteSession(t){await e.delete(n).where((0,h.eq)(n.sessionToken,t)).run()},createVerificationToken:async t=>e.insert(s).values(t).returning().get(),useVerificationToken:async t=>await e.delete(s).where((0,h.Uo)((0,h.eq)(s.identifier,t.identifier),(0,h.eq)(s.token,t.token))).returning().get()??null,async deleteUser(t){await e.delete(r).where((0,h.eq)(r.id,t)).run()},async unlinkAccount(t){await e.delete(i).where((0,h.Uo)((0,h.eq)(i.provider,t.provider),(0,h.eq)(i.providerAccountId,t.providerAccountId))).run()},getAccount:async(t,r)=>e.select().from(i).where((0,h.Uo)((0,h.eq)(i.provider,r),(0,h.eq)(i.providerAccountId,t))).then(e=>e[0]??null),createAuthenticator:async t=>e.insert(o).values(t).returning().then(e=>e[0]??null),getAuthenticator:async t=>e.select().from(o).where((0,h.eq)(o.credentialID,t)).then(e=>e[0]??null),listAuthenticatorsByUserId:async t=>e.select().from(o).where((0,h.eq)(o.userId,t)).then(e=>e),async updateAuthenticatorCounter(t,r){let i=await e.update(o).set({counter:r}).where((0,h.eq)(o.credentialID,t)).returning().then(e=>e[0]);if(!i)throw Error("Authenticator not found.");return i}}}(e,t);throw Error(`Unsupported database type (${typeof e}) in Auth.js Drizzle adapter.`)}},8649:(e,t,r)=>{r.d(t,{$:()=>i});function i(){for(var e,t,r=0,i="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,i,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t){if(Array.isArray(t)){var s=t.length;for(r=0;r<s;r++)t[r]&&(i=e(t[r]))&&(n&&(n+=" "),n+=i)}else for(i in t)t[i]&&(n&&(n+=" "),n+=i)}return n}(e))&&(i&&(i+=" "),i+=t);return i}},3757:(e,t,r)=>{let i,n,s,o,a;r.d(t,{Ay:()=>sY});var l=function(e,t,r,i,n){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?n.call(e,r):n?n.value=r:t.set(e,r),r},c=function(e,t,r,i){if("a"===r&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?i:"a"===r?i.call(e):i?i.value:t.get(e)};function u(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class d{constructor(e,t,r){if(ry.add(this),rm.set(this,{}),rg.set(this,void 0),rb.set(this,void 0),l(this,rb,r,"f"),l(this,rg,e,"f"),!t)return;let{name:i}=e;for(let[e,r]of Object.entries(t))e.startsWith(i)&&r&&(c(this,rm,"f")[e]=r)}get value(){return Object.keys(c(this,rm,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>c(this,rm,"f")[e]).join("")}chunk(e,t){let r=c(this,ry,"m",rv).call(this);for(let i of c(this,ry,"m",rw).call(this,{name:c(this,rg,"f").name,value:e,options:{...c(this,rg,"f").options,...t}}))r[i.name]=i;return Object.values(r)}clean(){return Object.values(c(this,ry,"m",rv).call(this))}}rm=new WeakMap,rg=new WeakMap,rb=new WeakMap,ry=new WeakSet,rw=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return c(this,rm,"f")[e.name]=e.value,[e];let r=[];for(let i=0;i<t;i++){let t=`${e.name}.${i}`,n=e.value.substr(3936*i,3936);r.push({...e,name:t,value:n}),c(this,rm,"f")[t]=n}return c(this,rb,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},rv=function(){let e={};for(let t in c(this,rm,"f"))delete c(this,rm,"f")?.[t],e[t]={name:t,value:"",options:{...c(this,rg,"f").options,maxAge:0}};return e};class h extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class p extends h{}p.kind="signIn";class f extends h{}f.type="AdapterError";class y extends h{}y.type="AccessDenied";class m extends h{}m.type="CallbackRouteError";class g extends h{}g.type="ErrorPageLoop";class b extends h{}b.type="EventError";class w extends h{}w.type="InvalidCallbackUrl";class v extends p{constructor(){super(...arguments),this.code="credentials"}}v.type="CredentialsSignin";class _ extends h{}_.type="InvalidEndpoints";class S extends h{}S.type="InvalidCheck";class k extends h{}k.type="JWTSessionError";class A extends h{}A.type="MissingAdapter";class x extends h{}x.type="MissingAdapterMethods";class T extends h{}T.type="MissingAuthorize";class E extends h{}E.type="MissingSecret";class $ extends p{}$.type="OAuthAccountNotLinked";class C extends p{}C.type="OAuthCallbackError";class I extends h{}I.type="OAuthProfileParseError";class U extends h{}U.type="SessionTokenError";class q extends p{}q.type="OAuthSignInError";class P extends p{}P.type="EmailSignInError";class O extends h{}O.type="SignOutError";class N extends h{}N.type="UnknownAction";class j extends h{}j.type="UnsupportedStrategy";class R extends h{}R.type="InvalidProvider";class D extends h{}D.type="UntrustedHost";class M extends h{}M.type="Verification";class L extends p{}L.type="MissingCSRF";let H=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class W extends h{}W.type="DuplicateConditionalUI";class K extends h{}K.type="MissingWebAuthnAutocomplete";class B extends h{}B.type="WebAuthnVerificationError";class J extends p{}J.type="AccountNotLinked";class Q extends h{}Q.type="ExperimentalFeatureNotEnabled";let z=!1;function F(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let V=!1,X=!1,G=!1,Y=["createVerificationToken","useVerificationToken","getUserByEmail"],Z=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],ee=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"],et=()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;throw Error("unable to locate global object")},er=async(e,t,r,i,n)=>{let{crypto:{subtle:s}}=et();return new Uint8Array(await s.deriveBits({name:"HKDF",hash:`SHA-${e.substr(3)}`,salt:r,info:i},await s.importKey("raw",t,"HKDF",!1,["deriveBits"]),n<<3))};function ei(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function en(e,t,r,i,n){return er(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=ei(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),ei(r,"salt"),function(e){let t=ei(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(i),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(n,e))}let es=crypto,eo=e=>e instanceof CryptoKey,ea=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await es.subtle.digest(r,t))},el=new TextEncoder,ec=new TextDecoder;function eu(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let i of e)t.set(i,r),r+=i.length;return t}function ed(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function eh(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return ed(r,t,0),ed(r,e%0x100000000,4),r}function ep(e){let t=new Uint8Array(4);return ed(t,e),t}function ef(e){return eu(ep(e.length),e)}async function ey(e,t,r){let i=Math.ceil((t>>3)/32),n=new Uint8Array(32*i);for(let t=0;t<i;t++){let i=new Uint8Array(4+e.length+r.length);i.set(ep(t+1)),i.set(e,4),i.set(r,4+e.length),n.set(await ea("sha256",i),32*t)}return n.slice(0,t>>3)}let em=e=>{let t=e;"string"==typeof t&&(t=el.encode(t));let r=[];for(let e=0;e<t.length;e+=32768)r.push(String.fromCharCode.apply(null,t.subarray(e,e+32768)));return btoa(r.join(""))},eg=e=>em(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),eb=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},ew=e=>{let t=e;t instanceof Uint8Array&&(t=ec.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return eb(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}};class ev extends Error{constructor(e,t){super(e,t),this.code="ERR_JOSE_GENERIC",this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}ev.code="ERR_JOSE_GENERIC";class e_ extends ev{constructor(e,t,r="unspecified",i="unspecified"){super(e,{cause:{claim:r,reason:i,payload:t}}),this.code="ERR_JWT_CLAIM_VALIDATION_FAILED",this.claim=r,this.reason=i,this.payload=t}}e_.code="ERR_JWT_CLAIM_VALIDATION_FAILED";class eS extends ev{constructor(e,t,r="unspecified",i="unspecified"){super(e,{cause:{claim:r,reason:i,payload:t}}),this.code="ERR_JWT_EXPIRED",this.claim=r,this.reason=i,this.payload=t}}eS.code="ERR_JWT_EXPIRED";class ek extends ev{constructor(){super(...arguments),this.code="ERR_JOSE_ALG_NOT_ALLOWED"}}ek.code="ERR_JOSE_ALG_NOT_ALLOWED";class eA extends ev{constructor(){super(...arguments),this.code="ERR_JOSE_NOT_SUPPORTED"}}eA.code="ERR_JOSE_NOT_SUPPORTED";class ex extends ev{constructor(e="decryption operation failed",t){super(e,t),this.code="ERR_JWE_DECRYPTION_FAILED"}}ex.code="ERR_JWE_DECRYPTION_FAILED";class eT extends ev{constructor(){super(...arguments),this.code="ERR_JWE_INVALID"}}eT.code="ERR_JWE_INVALID";class eE extends ev{constructor(){super(...arguments),this.code="ERR_JWS_INVALID"}}eE.code="ERR_JWS_INVALID";class e$ extends ev{constructor(){super(...arguments),this.code="ERR_JWT_INVALID"}}e$.code="ERR_JWT_INVALID";class eC extends ev{constructor(){super(...arguments),this.code="ERR_JWK_INVALID"}}eC.code="ERR_JWK_INVALID";class eI extends ev{constructor(){super(...arguments),this.code="ERR_JWKS_INVALID"}}eI.code="ERR_JWKS_INVALID";class eU extends ev{constructor(e="no applicable key found in the JSON Web Key Set",t){super(e,t),this.code="ERR_JWKS_NO_MATCHING_KEY"}}eU.code="ERR_JWKS_NO_MATCHING_KEY";class eq extends ev{constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t),this.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS"}}Symbol.asyncIterator,eq.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";class eP extends ev{constructor(e="request timed out",t){super(e,t),this.code="ERR_JWKS_TIMEOUT"}}eP.code="ERR_JWKS_TIMEOUT";class eO extends ev{constructor(e="signature verification failed",t){super(e,t),this.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED"}}function eN(e){if(!("object"==typeof e&&null!==e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}eO.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";let ej=(e,t)=>{if("string"!=typeof e||!e)throw new eC(`${t} missing or invalid`)};async function eR(e,t){let r;if(!eN(e))throw TypeError("JWK must be an object");if(t??(t="sha256"),"sha256"!==t&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(e.kty){case"EC":ej(e.crv,'"crv" (Curve) Parameter'),ej(e.x,'"x" (X Coordinate) Parameter'),ej(e.y,'"y" (Y Coordinate) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x,y:e.y};break;case"OKP":ej(e.crv,'"crv" (Subtype of Key Pair) Parameter'),ej(e.x,'"x" (Public Key) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x};break;case"RSA":ej(e.e,'"e" (Exponent) Parameter'),ej(e.n,'"n" (Modulus) Parameter'),r={e:e.e,kty:e.kty,n:e.n};break;case"oct":ej(e.k,'"k" (Key Value) Parameter'),r={k:e.k,kty:e.kty};break;default:throw new eA('"kty" (Key Type) Parameter missing or unsupported')}let i=el.encode(JSON.stringify(r));return eg(await ea(t,i))}let eD=Symbol(),eM=es.getRandomValues.bind(es);function eL(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new eA(`Unsupported JWE Algorithm: ${e}`)}}let eH=e=>eM(new Uint8Array(eL(e)>>3)),eW=(e,t)=>{if(t.length<<3!==eL(e))throw new eT("Invalid Initialization Vector length")},eK=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new eT(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function eB(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function eJ(e,t){return e.name===t}function eQ(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!eJ(e.algorithm,"AES-GCM"))throw eB("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eB(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!eJ(e.algorithm,"AES-KW"))throw eB("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eB(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw eB("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!eJ(e.algorithm,"PBKDF2"))throw eB("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!eJ(e.algorithm,"RSA-OAEP"))throw eB("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw eB(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}!function(e,t){if(t.length&&!t.some(t=>e.usages.includes(t))){let e="CryptoKey does not support this operation, its usages must include ";if(t.length>2){let r=t.pop();e+=`one of ${t.join(", ")}, or ${r}.`}else 2===t.length?e+=`one of ${t[0]} or ${t[1]}.`:e+=`${t[0]}.`;throw TypeError(e)}}(e,r)}function ez(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let eF=(e,...t)=>ez("Key must be ",e,...t);function eV(e,t,...r){return ez(`Key for the ${e} algorithm must be `,t,...r)}let eX=e=>!!eo(e)||e?.[Symbol.toStringTag]==="KeyObject",eG=["CryptoKey"];async function eY(e,t,r,i,n){if(!(r instanceof Uint8Array))throw TypeError(eF(r,"Uint8Array"));let s=parseInt(e.slice(1,4),10),o=await es.subtle.importKey("raw",r.subarray(s>>3),"AES-CBC",!1,["encrypt"]),a=await es.subtle.importKey("raw",r.subarray(0,s>>3),{hash:`SHA-${s<<1}`,name:"HMAC"},!1,["sign"]),l=new Uint8Array(await es.subtle.encrypt({iv:i,name:"AES-CBC"},o,t)),c=eu(n,i,l,eh(n.length<<3));return{ciphertext:l,tag:new Uint8Array((await es.subtle.sign("HMAC",a,c)).slice(0,s>>3)),iv:i}}async function eZ(e,t,r,i,n){let s;r instanceof Uint8Array?s=await es.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(eQ(r,e,"encrypt"),s=r);let o=new Uint8Array(await es.subtle.encrypt({additionalData:n,iv:i,name:"AES-GCM",tagLength:128},s,t)),a=o.slice(-16);return{ciphertext:o.slice(0,-16),tag:a,iv:i}}let e0=async(e,t,r,i,n)=>{if(!eo(r)&&!(r instanceof Uint8Array))throw TypeError(eF(r,...eG,"Uint8Array"));switch(i?eW(e,i):i=eH(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&eK(r,parseInt(e.slice(-3),10)),eY(e,t,r,i,n);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&eK(r,parseInt(e.slice(1,4),10)),eZ(e,t,r,i,n);default:throw new eA("Unsupported JWE Content Encryption Algorithm")}},e1=[{hash:"SHA-256",name:"HMAC"},!0,["sign"]];function e2(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function e5(e,t,r){if(eo(e))return eQ(e,t,r),e;if(e instanceof Uint8Array)return es.subtle.importKey("raw",e,"AES-KW",!0,[r]);throw TypeError(eF(e,...eG,"Uint8Array"))}let e3=async(e,t,r)=>{let i=await e5(t,e,"wrapKey");e2(i,e);let n=await es.subtle.importKey("raw",r,...e1);return new Uint8Array(await es.subtle.wrapKey("raw",n,i,"AES-KW"))},e8=async(e,t,r)=>{let i=await e5(t,e,"unwrapKey");e2(i,e);let n=await es.subtle.unwrapKey("raw",r,i,"AES-KW",...e1);return new Uint8Array(await es.subtle.exportKey("raw",n))};async function e6(e,t,r,i,n=new Uint8Array(0),s=new Uint8Array(0)){let o;if(!eo(e))throw TypeError(eF(e,...eG));if(eQ(e,"ECDH"),!eo(t))throw TypeError(eF(t,...eG));eQ(t,"ECDH","deriveBits");let a=eu(ef(el.encode(r)),ef(n),ef(s),ep(i));return o="X25519"===e.algorithm.name?256:"X448"===e.algorithm.name?448:Math.ceil(parseInt(e.algorithm.namedCurve.substr(-3),10)/8)<<3,ey(new Uint8Array(await es.subtle.deriveBits({name:e.algorithm.name,public:e},t,o)),i,a)}async function e4(e){if(!eo(e))throw TypeError(eF(e,...eG));return es.subtle.generateKey(e.algorithm,!0,["deriveBits"])}function e9(e){if(!eo(e))throw TypeError(eF(e,...eG));return["P-256","P-384","P-521"].includes(e.algorithm.namedCurve)||"X25519"===e.algorithm.name||"X448"===e.algorithm.name}async function e7(e,t,r,i){!function(e){if(!(e instanceof Uint8Array)||e.length<8)throw new eT("PBES2 Salt Input must be 8 or more octets")}(e);let n=eu(el.encode(t),new Uint8Array([0]),e),s=parseInt(t.slice(13,16),10),o={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:n},a=await function(e,t){if(e instanceof Uint8Array)return es.subtle.importKey("raw",e,"PBKDF2",!1,["deriveBits"]);if(eo(e))return eQ(e,t,"deriveBits","deriveKey"),e;throw TypeError(eF(e,...eG,"Uint8Array"))}(i,t);if(a.usages.includes("deriveBits"))return new Uint8Array(await es.subtle.deriveBits(o,a,s));if(a.usages.includes("deriveKey"))return es.subtle.deriveKey(o,a,{length:s,name:"AES-KW"},!1,["wrapKey","unwrapKey"]);throw TypeError('PBKDF2 key "usages" must include "deriveBits" or "deriveKey"')}let te=async(e,t,r,i=2048,n=eM(new Uint8Array(16)))=>{let s=await e7(n,e,i,t);return{encryptedKey:await e3(e.slice(-6),s,r),p2c:i,p2s:eg(n)}},tt=async(e,t,r,i,n)=>{let s=await e7(n,e,i,t);return e8(e.slice(-6),s,r)};function tr(e){switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new eA(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}let ti=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},tn=async(e,t,r)=>{if(!eo(t))throw TypeError(eF(t,...eG));if(eQ(t,e,"encrypt","wrapKey"),ti(e,t),t.usages.includes("encrypt"))return new Uint8Array(await es.subtle.encrypt(tr(e),t,r));if(t.usages.includes("wrapKey")){let i=await es.subtle.importKey("raw",r,...e1);return new Uint8Array(await es.subtle.wrapKey("raw",i,t,tr(e)))}throw TypeError('RSA-OAEP key "usages" must include "encrypt" or "wrapKey" for this operation')},ts=async(e,t,r)=>{if(!eo(t))throw TypeError(eF(t,...eG));if(eQ(t,e,"decrypt","unwrapKey"),ti(e,t),t.usages.includes("decrypt"))return new Uint8Array(await es.subtle.decrypt(tr(e),t,r));if(t.usages.includes("unwrapKey")){let i=await es.subtle.unwrapKey("raw",r,t,tr(e),...e1);return new Uint8Array(await es.subtle.exportKey("raw",i))}throw TypeError('RSA-OAEP key "usages" must include "decrypt" or "unwrapKey" for this operation')};function to(e){return eN(e)&&"string"==typeof e.kty}let ta=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new eA('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new eA('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"EdDSA":t={name:e.crv},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new eA('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new eA('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),i=[t,e.ext??!1,e.key_ops??r],n={...e};return delete n.alg,delete n.use,es.subtle.importKey("jwk",n,...i)},tl=e=>ew(e),tc=e=>e?.[Symbol.toStringTag]==="KeyObject",tu=async(e,t,r,i,n=!1)=>{let s=e.get(t);if(s?.[i])return s[i];let o=await ta({...r,alg:i});return n&&Object.freeze(t),s?s[i]=o:e.set(t,{[i]:o}),o},td={normalizePublicKey:(e,t)=>{if(tc(e)){let r=e.export({format:"jwk"});return(delete r.d,delete r.dp,delete r.dq,delete r.p,delete r.q,delete r.qi,r.k)?tl(r.k):(n||(n=new WeakMap),tu(n,e,r,t))}return to(e)?e.k?ew(e.k):(n||(n=new WeakMap),tu(n,e,e,t,!0)):e},normalizePrivateKey:(e,t)=>{if(tc(e)){let r=e.export({format:"jwk"});return r.k?tl(r.k):(i||(i=new WeakMap),tu(i,e,r,t))}return to(e)?e.k?ew(e.k):(i||(i=new WeakMap),tu(i,e,e,t,!0)):e}};function th(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new eA(`Unsupported JWE Algorithm: ${e}`)}}let tp=e=>eM(new Uint8Array(th(e)>>3)),tf=async e=>{if(e instanceof Uint8Array)return{kty:"oct",k:eg(e)};if(!eo(e))throw TypeError(eF(e,...eG,"Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:i,use:n,...s}=await es.subtle.exportKey("jwk",e);return s};async function ty(e){return tf(e)}let tm=e=>e?.[Symbol.toStringTag],tg=(e,t,r)=>{if(void 0!==t.use&&"sig"!==t.use)throw TypeError("Invalid key for this operation, when present its use must be sig");if(void 0!==t.key_ops&&t.key_ops.includes?.(r)!==!0)throw TypeError(`Invalid key for this operation, when present its key_ops must include ${r}`);if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, when present its alg must be ${e}`);return!0},tb=(e,t,r,i)=>{if(!(t instanceof Uint8Array)){if(i&&to(t)){if(function(e){return to(e)&&"oct"===e.kty&&"string"==typeof e.k}(t)&&tg(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!eX(t))throw TypeError(eV(e,t,...eG,"Uint8Array",i?"JSON Web Key":null));if("secret"!==t.type)throw TypeError(`${tm(t)} instances for symmetric algorithms must be of type "secret"`)}},tw=(e,t,r,i)=>{if(i&&to(t))switch(r){case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&tg(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&tg(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!eX(t))throw TypeError(eV(e,t,...eG,i?"JSON Web Key":null));if("secret"===t.type)throw TypeError(`${tm(t)} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${tm(t)} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${tm(t)} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${tm(t)} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${tm(t)} instances for asymmetric algorithm encryption must be of type "public"`)};function tv(e,t,r,i){t.startsWith("HS")||"dir"===t||t.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(t)?tb(t,r,i,e):tw(t,r,i,e)}let t_=tv.bind(void 0,!1);tv.bind(void 0,!0);let tS=(e,t)=>{if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");if(e.length!==t.length)throw TypeError("Input buffers must have the same length");let r=e.length,i=0,n=-1;for(;++n<r;)i|=e[n]^t[n];return 0===i};async function tk(e,t,r,i,n,s){let o,a;if(!(t instanceof Uint8Array))throw TypeError(eF(t,"Uint8Array"));let l=parseInt(e.slice(1,4),10),c=await es.subtle.importKey("raw",t.subarray(l>>3),"AES-CBC",!1,["decrypt"]),u=await es.subtle.importKey("raw",t.subarray(0,l>>3),{hash:`SHA-${l<<1}`,name:"HMAC"},!1,["sign"]),d=eu(s,i,r,eh(s.length<<3)),h=new Uint8Array((await es.subtle.sign("HMAC",u,d)).slice(0,l>>3));try{o=tS(n,h)}catch{}if(!o)throw new ex;try{a=new Uint8Array(await es.subtle.decrypt({iv:i,name:"AES-CBC"},c,r))}catch{}if(!a)throw new ex;return a}async function tA(e,t,r,i,n,s){let o;t instanceof Uint8Array?o=await es.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(eQ(t,e,"decrypt"),o=t);try{return new Uint8Array(await es.subtle.decrypt({additionalData:s,iv:i,name:"AES-GCM",tagLength:128},o,eu(r,n)))}catch{throw new ex}}let tx=async(e,t,r,i,n,s)=>{if(!eo(t)&&!(t instanceof Uint8Array))throw TypeError(eF(t,...eG,"Uint8Array"));if(!i)throw new eT("JWE Initialization Vector missing");if(!n)throw new eT("JWE Authentication Tag missing");switch(eW(e,i),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&eK(t,parseInt(e.slice(-3),10)),tk(e,t,r,i,n,s);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&eK(t,parseInt(e.slice(1,4),10)),tA(e,t,r,i,n,s);default:throw new eA("Unsupported JWE Content Encryption Algorithm")}};async function tT(e,t,r,i){let n=e.slice(0,7),s=await e0(n,r,t,i,new Uint8Array(0));return{encryptedKey:s.ciphertext,iv:eg(s.iv),tag:eg(s.tag)}}async function tE(e,t,r,i,n){return tx(e.slice(0,7),t,r,i,n,new Uint8Array(0))}async function t$(e,t,r,i,n={}){let s,o,a;switch(t_(e,r,"encrypt"),r=await td.normalizePublicKey?.(r,e)||r,e){case"dir":a=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!e9(r))throw new eA("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:l,apv:c}=n,{epk:u}=n;u||(u=(await e4(r)).privateKey);let{x:d,y:h,crv:p,kty:f}=await ty(u),y=await e6(r,u,"ECDH-ES"===e?t:e,"ECDH-ES"===e?th(t):parseInt(e.slice(-5,-2),10),l,c);if(o={epk:{x:d,crv:p,kty:f}},"EC"===f&&(o.epk.y=h),l&&(o.apu=eg(l)),c&&(o.apv=eg(c)),"ECDH-ES"===e){a=y;break}a=i||tp(t);let m=e.slice(-6);s=await e3(m,y,a);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":a=i||tp(t),s=await tn(e,r,a);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{a=i||tp(t);let{p2c:l,p2s:c}=n;({encryptedKey:s,...o}=await te(e,r,a,l,c));break}case"A128KW":case"A192KW":case"A256KW":a=i||tp(t),s=await e3(e,r,a);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{a=i||tp(t);let{iv:l}=n;({encryptedKey:s,...o}=await tT(e,r,a,l));break}default:throw new eA('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:a,encryptedKey:s,parameters:o}}let tC=(...e)=>{let t;let r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},tI=function(e,t,r,i,n){let s;if(void 0!==n.crit&&i?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!i||void 0===i.crit)return new Set;if(!Array.isArray(i.crit)||0===i.crit.length||i.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(s=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,i.crit)){if(!s.has(o))throw new eA(`Extension Header Parameter "${o}" is not recognized`);if(void 0===n[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(s.get(o)&&void 0===i[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(i.crit)};class tU{constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=e}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}async encrypt(e,t){let r,i,n,s,o;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new eT("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!tC(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new eT("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let a={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if(tI(eT,new Map,t?.crit,this._protectedHeader,a),void 0!==a.zip)throw new eA('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:l,enc:c}=a;if("string"!=typeof l||!l)throw new eT('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof c||!c)throw new eT('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this._cek&&("dir"===l||"ECDH-ES"===l))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${l}`);{let n;({cek:i,encryptedKey:r,parameters:n}=await t$(l,c,e,this._cek,this._keyManagementParameters)),n&&(t&&eD in t?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...n}:this.setUnprotectedHeader(n):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...n}:this.setProtectedHeader(n))}s=this._protectedHeader?el.encode(eg(JSON.stringify(this._protectedHeader))):el.encode(""),this._aad?(o=eg(this._aad),n=eu(s,el.encode("."),el.encode(o))):n=s;let{ciphertext:u,tag:d,iv:h}=await e0(c,this._plaintext,i,this._iv,n),p={ciphertext:eg(u)};return h&&(p.iv=eg(h)),d&&(p.tag=eg(d)),r&&(p.encrypted_key=eg(r)),o&&(p.aad=o),this._protectedHeader&&(p.protected=ec.decode(s)),this._sharedUnprotectedHeader&&(p.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(p.header=this._unprotectedHeader),p}}class tq{constructor(e){this._flattened=new tU(e)}setContentEncryptionKey(e){return this._flattened.setContentEncryptionKey(e),this}setInitializationVector(e){return this._flattened.setInitializationVector(e),this}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}setKeyManagementParameters(e){return this._flattened.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this._flattened.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let tP=e=>Math.floor(e.getTime()/1e3),tO=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,tN=e=>{let t;let r=tO.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let i=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(i);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*i);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*i);break;case"day":case"days":case"d":t=Math.round(86400*i);break;case"week":case"weeks":case"w":t=Math.round(604800*i);break;default:t=Math.round(0x1e187e0*i)}return"-"===r[1]||"ago"===r[4]?-t:t};function tj(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}class tR{constructor(e={}){if(!eN(e))throw TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return"number"==typeof e?this._payload={...this._payload,nbf:tj("setNotBefore",e)}:e instanceof Date?this._payload={...this._payload,nbf:tj("setNotBefore",tP(e))}:this._payload={...this._payload,nbf:tP(new Date)+tN(e)},this}setExpirationTime(e){return"number"==typeof e?this._payload={...this._payload,exp:tj("setExpirationTime",e)}:e instanceof Date?this._payload={...this._payload,exp:tj("setExpirationTime",tP(e))}:this._payload={...this._payload,exp:tP(new Date)+tN(e)},this}setIssuedAt(e){return void 0===e?this._payload={...this._payload,iat:tP(new Date)}:e instanceof Date?this._payload={...this._payload,iat:tj("setIssuedAt",tP(e))}:"string"==typeof e?this._payload={...this._payload,iat:tj("setIssuedAt",tP(new Date)+tN(e))}:this._payload={...this._payload,iat:tj("setIssuedAt",e)},this}}class tD extends tR{setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(e,t){let r=new tq(el.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),r.setProtectedHeader(this._protectedHeader),this._iv&&r.setInitializationVector(this._iv),this._cek&&r.setContentEncryptionKey(this._cek),this._keyManagementParameters&&r.setKeyManagementParameters(this._keyManagementParameters),r.encrypt(e,t)}}async function tM(e,t){if(!eN(e))throw TypeError("JWK must be an object");switch(t||(t=e.alg),e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return ew(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new eA('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return ta({...e,alg:t});default:throw new eA('Unsupported "kty" (Key Type) Parameter value')}}async function tL(e,t,r,i,n){switch(t_(e,t,"decrypt"),t=await td.normalizePrivateKey?.(t,e)||t,e){case"dir":if(void 0!==r)throw new eT("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new eT("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let n,s;if(!eN(i.epk))throw new eT('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!e9(t))throw new eA("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await tM(i.epk,e);if(void 0!==i.apu){if("string"!=typeof i.apu)throw new eT('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{n=ew(i.apu)}catch{throw new eT("Failed to base64url decode the apu")}}if(void 0!==i.apv){if("string"!=typeof i.apv)throw new eT('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{s=ew(i.apv)}catch{throw new eT("Failed to base64url decode the apv")}}let a=await e6(o,t,"ECDH-ES"===e?i.enc:e,"ECDH-ES"===e?th(i.enc):parseInt(e.slice(-5,-2),10),n,s);if("ECDH-ES"===e)return a;if(void 0===r)throw new eT("JWE Encrypted Key missing");return e8(e.slice(-6),a,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new eT("JWE Encrypted Key missing");return ts(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let s;if(void 0===r)throw new eT("JWE Encrypted Key missing");if("number"!=typeof i.p2c)throw new eT('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=n?.maxPBES2Count||1e4;if(i.p2c>o)throw new eT('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof i.p2s)throw new eT('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{s=ew(i.p2s)}catch{throw new eT("Failed to base64url decode the p2s")}return tt(e,t,r,i.p2c,s)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new eT("JWE Encrypted Key missing");return e8(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let n,s;if(void 0===r)throw new eT("JWE Encrypted Key missing");if("string"!=typeof i.iv)throw new eT('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof i.tag)throw new eT('JOSE Header "tag" (Authentication Tag) missing or invalid');try{n=ew(i.iv)}catch{throw new eT("Failed to base64url decode the iv")}try{s=ew(i.tag)}catch{throw new eT("Failed to base64url decode the tag")}return tE(e,t,r,n,s)}default:throw new eA('Invalid or unsupported "alg" (JWE Algorithm) header value')}}let tH=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function tW(e,t,r){let i,n,s,o,a,l,c;if(!eN(e))throw new eT("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new eT("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new eT("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new eT("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new eT("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new eT("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new eT("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new eT("JWE AAD incorrect type");if(void 0!==e.header&&!eN(e.header))throw new eT("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!eN(e.unprotected))throw new eT("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=ew(e.protected);i=JSON.parse(ec.decode(t))}catch{throw new eT("JWE Protected Header is invalid")}if(!tC(i,e.header,e.unprotected))throw new eT("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let u={...i,...e.header,...e.unprotected};if(tI(eT,new Map,r?.crit,i,u),void 0!==u.zip)throw new eA('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:h}=u;if("string"!=typeof d||!d)throw new eT("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof h||!h)throw new eT("missing JWE Encryption Algorithm (enc) in JWE Header");let p=r&&tH("keyManagementAlgorithms",r.keyManagementAlgorithms),f=r&&tH("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(p&&!p.has(d)||!p&&d.startsWith("PBES2"))throw new ek('"alg" (Algorithm) Header Parameter value not allowed');if(f&&!f.has(h))throw new ek('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{n=ew(e.encrypted_key)}catch{throw new eT("Failed to base64url decode the encrypted_key")}let y=!1;"function"==typeof t&&(t=await t(i,e),y=!0);try{s=await tL(d,t,n,u,r)}catch(e){if(e instanceof TypeError||e instanceof eT||e instanceof eA)throw e;s=tp(h)}if(void 0!==e.iv)try{o=ew(e.iv)}catch{throw new eT("Failed to base64url decode the iv")}if(void 0!==e.tag)try{a=ew(e.tag)}catch{throw new eT("Failed to base64url decode the tag")}let m=el.encode(e.protected??"");l=void 0!==e.aad?eu(m,el.encode("."),el.encode(e.aad)):m;try{c=ew(e.ciphertext)}catch{throw new eT("Failed to base64url decode the ciphertext")}let g={plaintext:await tx(h,s,c,o,a,l)};if(void 0!==e.protected&&(g.protectedHeader=i),void 0!==e.aad)try{g.additionalAuthenticatedData=ew(e.aad)}catch{throw new eT("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(g.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(g.unprotectedHeader=e.header),y)?{...g,key:t}:g}async function tK(e,t,r){if(e instanceof Uint8Array&&(e=ec.decode(e)),"string"!=typeof e)throw new eT("Compact JWE must be a string or Uint8Array");let{0:i,1:n,2:s,3:o,4:a,length:l}=e.split(".");if(5!==l)throw new eT("Invalid Compact JWE");let c=await tW({ciphertext:o,iv:s||void 0,protected:i,tag:a||void 0,encrypted_key:n||void 0},t,r),u={plaintext:c.plaintext,protectedHeader:c.protectedHeader};return"function"==typeof t?{...u,key:c.key}:u}let tB=e=>e.toLowerCase().replace(/^application\//,""),tJ=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),tQ=(e,t,r={})=>{let i,n;try{i=JSON.parse(ec.decode(t))}catch{}if(!eN(i))throw new e$("JWT Claims Set must be a top-level JSON object");let{typ:s}=r;if(s&&("string"!=typeof e.typ||tB(e.typ)!==tB(s)))throw new e_('unexpected "typ" JWT header value',i,"typ","check_failed");let{requiredClaims:o=[],issuer:a,subject:l,audience:c,maxTokenAge:u}=r,d=[...o];for(let e of(void 0!==u&&d.push("iat"),void 0!==c&&d.push("aud"),void 0!==l&&d.push("sub"),void 0!==a&&d.push("iss"),new Set(d.reverse())))if(!(e in i))throw new e_(`missing required "${e}" claim`,i,e,"missing");if(a&&!(Array.isArray(a)?a:[a]).includes(i.iss))throw new e_('unexpected "iss" claim value',i,"iss","check_failed");if(l&&i.sub!==l)throw new e_('unexpected "sub" claim value',i,"sub","check_failed");if(c&&!tJ(i.aud,"string"==typeof c?[c]:c))throw new e_('unexpected "aud" claim value',i,"aud","check_failed");switch(typeof r.clockTolerance){case"string":n=tN(r.clockTolerance);break;case"number":n=r.clockTolerance;break;case"undefined":n=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:h}=r,p=tP(h||new Date);if((void 0!==i.iat||u)&&"number"!=typeof i.iat)throw new e_('"iat" claim must be a number',i,"iat","invalid");if(void 0!==i.nbf){if("number"!=typeof i.nbf)throw new e_('"nbf" claim must be a number',i,"nbf","invalid");if(i.nbf>p+n)throw new e_('"nbf" claim timestamp check failed',i,"nbf","check_failed")}if(void 0!==i.exp){if("number"!=typeof i.exp)throw new e_('"exp" claim must be a number',i,"exp","invalid");if(i.exp<=p-n)throw new eS('"exp" claim timestamp check failed',i,"exp","check_failed")}if(u){let e=p-i.iat;if(e-n>("number"==typeof u?u:tN(u)))throw new eS('"iat" claim timestamp check failed (too far in the past)',i,"iat","check_failed");if(e<0-n)throw new e_('"iat" claim timestamp check failed (it should be in the past)',i,"iat","check_failed")}return i};async function tz(e,t,r){let i=await tK(e,t,r),n=tQ(i.protectedHeader,i.plaintext,r),{protectedHeader:s}=i;if(void 0!==s.iss&&s.iss!==n.iss)throw new e_('replicated "iss" claim header parameter mismatch',n,"iss","mismatch");if(void 0!==s.sub&&s.sub!==n.sub)throw new e_('replicated "sub" claim header parameter mismatch',n,"sub","mismatch");if(void 0!==s.aud&&JSON.stringify(s.aud)!==JSON.stringify(n.aud))throw new e_('replicated "aud" claim header parameter mismatch',n,"aud","mismatch");let o={payload:n,protectedHeader:s};return"function"==typeof t?{...o,key:i.key}:o}var tF=r(8633);let tV=()=>Date.now()/1e3|0,tX="A256CBC-HS512";async function tG(e){let{token:t={},secret:r,maxAge:i=2592e3,salt:n}=e,s=Array.isArray(r)?r:[r],o=await tZ(tX,s[0],n),a=await eR({kty:"oct",k:eg(o)},`sha${o.byteLength<<3}`);return await new tD(t).setProtectedHeader({alg:"dir",enc:tX,kid:a}).setIssuedAt().setExpirationTime(tV()+i).setJti(crypto.randomUUID()).encrypt(o)}async function tY(e){let{token:t,secret:r,salt:i}=e,n=Array.isArray(r)?r:[r];if(!t)return null;let{payload:s}=await tz(t,async({kid:e,enc:t})=>{for(let r of n){let n=await tZ(t,r,i);if(void 0===e||e===await eR({kty:"oct",k:eg(n)},`sha${n.byteLength<<3}`))return n}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[tX,"A256GCM"]});return s}async function tZ(e,t,r){let i;switch(e){case"A256CBC-HS512":i=64;break;case"A256GCM":i=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await en("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,i)}async function t0({options:e,paramValue:t,cookieValue:r}){let{url:i,callbacks:n}=e,s=i.origin;return t?s=await n.redirect({url:t,baseUrl:i.origin}):r&&(s=await n.redirect({url:r,baseUrl:i.origin})),{callbackUrl:s,callbackUrlCookie:s!==r?s:void 0}}let t1="\x1b[31m",t2="\x1b[0m",t5={error(e){let t=e instanceof h?e.type:e.name;if(console.error(`${t1}[auth][error]${t2} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${t1}[auth][cause]${t2}:`,t.stack),r&&console.error(`${t1}[auth][details]${t2}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){let t=`https://warnings.authjs.dev#${e}`;console.warn(`\x1b[33m[auth][warn][${e}]${t2}`,`Read more: ${t}`)},debug(e,t){console.log(`\x1b[90m[auth][debug]:${t2} ${e}`,JSON.stringify(t,null,2))}};function t3(e){let t={...t5};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let t8=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"];async function t6(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function t4(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new N("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:i,providerId:n}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new N(`Cannot parse action at ${e}`);let i=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==i.length&&2!==i.length)throw new N(`Cannot parse action at ${e}`);let[n,s]=i;if(!t8.includes(n)||s&&!["signin","callback","webauthn-options"].includes(n))throw new N(`Cannot parse action at ${e}`);return{action:n,providerId:s}}(r.pathname,t.basePath);return{url:r,action:i,providerId:n,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await t6(e):void 0,cookies:(0,tF.q)(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(i){let r=t3(t);r.error(i),r.debug("request",e)}}function t9(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:i,options:n}=e,s=tF.l(r,i,n);t.has("Set-Cookie")?t.append("Set-Cookie",s):t.set("Set-Cookie",s)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let i=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&i.headers.set("Location",e.redirect),i}async function t7(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function re(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function rt({options:e,cookieValue:t,isPost:r,bodyValue:i}){if(t){let[n,s]=t.split("|");if(s===await t7(`${n}${e.secret}`))return{csrfTokenVerified:r&&n===i,csrfToken:n}}let n=re(32),s=await t7(`${n}${e.secret}`);return{cookie:`${n}|${s}`,csrfToken:n}}function rr(e,t){if(!t)throw new L(`CSRF token was missing during an action ${e}`)}function ri(e){return null!==e&&"object"==typeof e}function rn(e,...t){if(!t.length)return e;let r=t.shift();if(ri(e)&&ri(r))for(let t in r)ri(r[t])?(ri(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),rn(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return rn(e,...t)}let rs=Symbol("skip-csrf-check"),ro=Symbol("return-type-raw"),ra=Symbol("custom-fetch"),rl=Symbol("conform-internal"),rc=e=>rd({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),ru=e=>rd({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function rd(e){let t={};for(let[r,i]of Object.entries(e))void 0!==i&&(t[r]=i);return t}function rh(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,i]of Object.entries(e.params))"claims"===t&&(i=JSON.stringify(i)),r.searchParams.set(t,String(i));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let rp={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function rf({authOptions:e,providerId:t,action:r,url:i,cookies:n,callbackUrl:s,csrfToken:o,csrfDisabled:a,isPost:l}){var c;let d=t3(e),{providers:h,provider:p}=function(e){let{providerId:t,config:r}=e,i=new URL(r.basePath??"/auth",e.url.origin),n=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:n,...s}=t,o=n?.id??s.id,a=rn(s,n,{signinUrl:`${i}/signin/${o}`,callbackUrl:`${i}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){a.redirectProxyUrl??(a.redirectProxyUrl=n?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=rh(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=rh(e.token,e.issuer),i=rh(e.userinfo,e.issuer),n=e.checks??["pkce"];return e.redirectProxyUrl&&(n.includes("state")||n.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:n,userinfo:i,profile:e.profile??rc,account:e.account??ru}}(a);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[ra]??(e[ra]=n?.[ra]),e}return a});return{providers:n,provider:n.find(({id:e})=>e===t)}}({url:i,providerId:t,config:e}),y=!1;if((p?.type==="oauth"||p?.type==="oidc")&&p.redirectProxyUrl)try{y=new URL(p.redirectProxyUrl).origin===i.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${p.redirectProxyUrl}`)}let m={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:i,action:r,provider:p,cookies:rn(u(e.useSecureCookies??"https:"===i.protocol),e.cookies),providers:h,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:tG,decode:tY,...e.jwt},events:Object.keys(c=e.events??{}).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=c[t];return await r(...e)}catch(e){d.error(new b(e))}},e),{}),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,i)=>(r[i]=async(...r)=>{try{t.debug(`adapter_${i}`,{args:r});let n=e[i];return await n(...r)}catch(r){let e=new f(r);throw t.error(e),e}},r),{})}(e.adapter,d),callbacks:{...rp,...e.callbacks},logger:d,callbackUrl:i.origin,isOnRedirectProxy:y,experimental:{...e.experimental}},g=[];if(a)m.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await rt({options:m,cookieValue:n?.[m.cookies.csrfToken.name],isPost:l,bodyValue:o});m.csrfToken=e,m.csrfTokenVerified=r,t&&g.push({name:m.cookies.csrfToken.name,value:t,options:m.cookies.csrfToken.options})}let{callbackUrl:w,callbackUrlCookie:v}=await t0({options:m,cookieValue:n?.[m.cookies.callbackUrl.name],paramValue:s});return m.callbackUrl=w,v&&g.push({name:m.cookies.callbackUrl.name,value:v,options:m.cookies.callbackUrl.options}),{options:m,cookies:g}}var ry,rm,rg,rb,rw,rv,r_,rS,rk,rA,rx,rT={},rE=[],r$=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function rC(e,t){for(var r in t)e[r]=t[r];return e}function rI(e){var t=e.parentNode;t&&t.removeChild(e)}function rU(e,t,r,i,n){var s={type:e,props:t,key:r,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==n?++rk:n};return null==n&&null!=rS.vnode&&rS.vnode(s),s}function rq(e){return e.children}function rP(e,t){this.props=e,this.context=t}function rO(e,t){if(null==t)return e.__?rO(e.__,e.__.__k.indexOf(e)+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?rO(e):null}function rN(e){(!e.__d&&(e.__d=!0)&&rA.push(e)&&!rj.__r++||rx!==rS.debounceRendering)&&((rx=rS.debounceRendering)||setTimeout)(rj)}function rj(){for(var e;rj.__r=rA.length;)e=rA.sort(function(e,t){return e.__v.__b-t.__v.__b}),rA=[],e.some(function(e){var t,r,i,n,s;e.__d&&(n=(i=e.__v).__e,(s=e.__P)&&(t=[],(r=rC({},i)).__v=i.__v+1,rK(s,i,r,e.__n,void 0!==s.ownerSVGElement,null!=i.__h?[n]:null,t,null==n?rO(i):n,i.__h),rB(t,i),i.__e!=n&&function e(t){var r,i;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(i=t.__k[r])&&null!=i.__e){t.__e=t.__c.base=i.__e;break}return e(t)}}(i)))})}function rR(e,t,r,i,n,s,o,a,l,c){var u,d,h,p,f,y,m,g=i&&i.__k||rE,b=g.length;for(r.__k=[],u=0;u<t.length;u++)if(null!=(p=r.__k[u]=null==(p=t[u])||"boolean"==typeof p?null:"string"==typeof p||"number"==typeof p||"bigint"==typeof p?rU(null,p,null,null,p):Array.isArray(p)?rU(rq,{children:p},null,null,null):p.__b>0?rU(p.type,p.props,p.key,p.ref?p.ref:null,p.__v):p)){if(p.__=r,p.__b=r.__b+1,null===(h=g[u])||h&&p.key==h.key&&p.type===h.type)g[u]=void 0;else for(d=0;d<b;d++){if((h=g[d])&&p.key==h.key&&p.type===h.type){g[d]=void 0;break}h=null}rK(e,p,h=h||rT,n,s,o,a,l,c),f=p.__e,(d=p.ref)&&h.ref!=d&&(m||(m=[]),h.ref&&m.push(h.ref,null,p),m.push(d,p.__c||f,p)),null!=f?(null==y&&(y=f),"function"==typeof p.type&&p.__k===h.__k?p.__d=l=function e(t,r,i){for(var n,s=t.__k,o=0;s&&o<s.length;o++)(n=s[o])&&(n.__=t,r="function"==typeof n.type?e(n,r,i):rD(i,n,n,s,n.__e,r));return r}(p,l,e):l=rD(e,p,h,g,f,l),"function"==typeof r.type&&(r.__d=l)):l&&h.__e==l&&l.parentNode!=e&&(l=rO(h))}for(r.__e=y,u=b;u--;)null!=g[u]&&function e(t,r,i){var n,s;if(rS.unmount&&rS.unmount(t),(n=t.ref)&&(n.current&&n.current!==t.__e||rJ(n,null,r)),null!=(n=t.__c)){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(e){rS.__e(e,r)}n.base=n.__P=null,t.__c=void 0}if(n=t.__k)for(s=0;s<n.length;s++)n[s]&&e(n[s],r,i||"function"!=typeof t.type);i||null==t.__e||rI(t.__e),t.__=t.__e=t.__d=void 0}(g[u],g[u]);if(m)for(u=0;u<m.length;u++)rJ(m[u],m[++u],m[++u])}function rD(e,t,r,i,n,s){var o,a,l;if(void 0!==t.__d)o=t.__d,t.__d=void 0;else if(null==r||n!=s||null==n.parentNode)e:if(null==s||s.parentNode!==e)e.appendChild(n),o=null;else{for(a=s,l=0;(a=a.nextSibling)&&l<i.length;l+=1)if(a==n)break e;e.insertBefore(n,s),o=s}return void 0!==o?o:n.nextSibling}function rM(e,t,r){"-"===t[0]?e.setProperty(t,r):e[t]=null==r?"":"number"!=typeof r||r$.test(t)?r:r+"px"}function rL(e,t,r,i,n){var s;e:if("style"===t){if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof i&&(e.style.cssText=i=""),i)for(t in i)r&&t in r||rM(e.style,t,"");if(r)for(t in r)i&&r[t]===i[t]||rM(e.style,t,r[t])}}else if("o"===t[0]&&"n"===t[1])s=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+s]=r,r?i||e.addEventListener(t,s?rW:rH,s):e.removeEventListener(t,s?rW:rH,s);else if("dangerouslySetInnerHTML"!==t){if(n)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&-1==t.indexOf("-")?e.removeAttribute(t):e.setAttribute(t,r))}}function rH(e){this.l[e.type+!1](rS.event?rS.event(e):e)}function rW(e){this.l[e.type+!0](rS.event?rS.event(e):e)}function rK(e,t,r,i,n,s,o,a,l){var c,u,d,h,p,f,y,m,g,b,w,v,_,S,k,A=t.type;if(void 0!==t.constructor)return null;null!=r.__h&&(l=r.__h,a=t.__e=r.__e,t.__h=null,s=[a]),(c=rS.__b)&&c(t);try{e:if("function"==typeof A){if(m=t.props,g=(c=A.contextType)&&i[c.__c],b=c?g?g.props.value:c.__:i,r.__c?y=(u=t.__c=r.__c).__=u.__E:("prototype"in A&&A.prototype.render?t.__c=u=new A(m,b):(t.__c=u=new rP(m,b),u.constructor=A,u.render=rQ),g&&g.sub(u),u.props=m,u.state||(u.state={}),u.context=b,u.__n=i,d=u.__d=!0,u.__h=[],u._sb=[]),null==u.__s&&(u.__s=u.state),null!=A.getDerivedStateFromProps&&(u.__s==u.state&&(u.__s=rC({},u.__s)),rC(u.__s,A.getDerivedStateFromProps(m,u.__s))),h=u.props,p=u.state,d)null==A.getDerivedStateFromProps&&null!=u.componentWillMount&&u.componentWillMount(),null!=u.componentDidMount&&u.__h.push(u.componentDidMount);else{if(null==A.getDerivedStateFromProps&&m!==h&&null!=u.componentWillReceiveProps&&u.componentWillReceiveProps(m,b),!u.__e&&null!=u.shouldComponentUpdate&&!1===u.shouldComponentUpdate(m,u.__s,b)||t.__v===r.__v){for(u.props=m,u.state=u.__s,t.__v!==r.__v&&(u.__d=!1),u.__v=t,t.__e=r.__e,t.__k=r.__k,t.__k.forEach(function(e){e&&(e.__=t)}),w=0;w<u._sb.length;w++)u.__h.push(u._sb[w]);u._sb=[],u.__h.length&&o.push(u);break e}null!=u.componentWillUpdate&&u.componentWillUpdate(m,u.__s,b),null!=u.componentDidUpdate&&u.__h.push(function(){u.componentDidUpdate(h,p,f)})}if(u.context=b,u.props=m,u.__v=t,u.__P=e,v=rS.__r,_=0,"prototype"in A&&A.prototype.render){for(u.state=u.__s,u.__d=!1,v&&v(t),c=u.render(u.props,u.state,u.context),S=0;S<u._sb.length;S++)u.__h.push(u._sb[S]);u._sb=[]}else do u.__d=!1,v&&v(t),c=u.render(u.props,u.state,u.context),u.state=u.__s;while(u.__d&&++_<25);u.state=u.__s,null!=u.getChildContext&&(i=rC(rC({},i),u.getChildContext())),d||null==u.getSnapshotBeforeUpdate||(f=u.getSnapshotBeforeUpdate(h,p)),k=null!=c&&c.type===rq&&null==c.key?c.props.children:c,rR(e,Array.isArray(k)?k:[k],t,r,i,n,s,o,a,l),u.base=t.__e,t.__h=null,u.__h.length&&o.push(u),y&&(u.__E=u.__=null),u.__e=!1}else null==s&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,i,n,s,o,a){var l,c,u,d=r.props,h=t.props,p=t.type,f=0;if("svg"===p&&(n=!0),null!=s){for(;f<s.length;f++)if((l=s[f])&&"setAttribute"in l==!!p&&(p?l.localName===p:3===l.nodeType)){e=l,s[f]=null;break}}if(null==e){if(null===p)return document.createTextNode(h);e=n?document.createElementNS("http://www.w3.org/2000/svg",p):document.createElement(p,h.is&&h),s=null,a=!1}if(null===p)d===h||a&&e.data===h||(e.data=h);else{if(s=s&&r_.call(e.childNodes),c=(d=r.props||rT).dangerouslySetInnerHTML,u=h.dangerouslySetInnerHTML,!a){if(null!=s)for(d={},f=0;f<e.attributes.length;f++)d[e.attributes[f].name]=e.attributes[f].value;(u||c)&&(u&&(c&&u.__html==c.__html||u.__html===e.innerHTML)||(e.innerHTML=u&&u.__html||""))}if(function(e,t,r,i,n){var s;for(s in r)"children"===s||"key"===s||s in t||rL(e,s,null,r[s],i);for(s in t)n&&"function"!=typeof t[s]||"children"===s||"key"===s||"value"===s||"checked"===s||r[s]===t[s]||rL(e,s,t[s],r[s],i)}(e,h,d,n,a),u)t.__k=[];else if(rR(e,Array.isArray(f=t.props.children)?f:[f],t,r,i,n&&"foreignObject"!==p,s,o,s?s[0]:r.__k&&rO(r,0),a),null!=s)for(f=s.length;f--;)null!=s[f]&&rI(s[f]);a||("value"in h&&void 0!==(f=h.value)&&(f!==e.value||"progress"===p&&!f||"option"===p&&f!==d.value)&&rL(e,"value",f,d.value,!1),"checked"in h&&void 0!==(f=h.checked)&&f!==e.checked&&rL(e,"checked",f,d.checked,!1))}return e}(r.__e,t,r,i,n,s,o,l);(c=rS.diffed)&&c(t)}catch(e){t.__v=null,(l||null!=s)&&(t.__e=a,t.__h=!!l,s[s.indexOf(a)]=null),rS.__e(e,t,r)}}function rB(e,t){rS.__c&&rS.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){rS.__e(e,t.__v)}})}function rJ(e,t,r){try{"function"==typeof e?e(t):e.current=t}catch(e){rS.__e(e,r)}}function rQ(e,t,r){return this.constructor(e,r)}function rz(e,t){var r,i,n,s;r=e,rS.__&&rS.__(r,t),n=(i="function"==typeof rz)?null:rz&&rz.__k||t.__k,s=[],rK(t,r=(!i&&rz||t).__k=function(e,t,r){var i,n,s,o={};for(s in t)"key"==s?i=t[s]:"ref"==s?n=t[s]:o[s]=t[s];if(arguments.length>2&&(o.children=arguments.length>3?r_.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(s in e.defaultProps)void 0===o[s]&&(o[s]=e.defaultProps[s]);return rU(e,o,i,n,null)}(rq,null,[r]),n||rT,rT,void 0!==t.ownerSVGElement,!i&&rz?[rz]:n?null:t.firstChild?r_.call(t.childNodes):null,s,!i&&rz?rz:n?n.__e:t.firstChild,i),rB(s,r)}r_=rE.slice,rS={__e:function(e,t,r,i){for(var n,s,o;t=t.__;)if((n=t.__c)&&!n.__)try{if((s=n.constructor)&&null!=s.getDerivedStateFromError&&(n.setState(s.getDerivedStateFromError(e)),o=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e,i||{}),o=n.__d),o)return n.__E=n}catch(t){e=t}throw e}},rk=0,rP.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rC({},this.state),"function"==typeof e&&(e=e(rC({},r),this.props)),e&&rC(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),rN(this))},rP.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rN(this))},rP.prototype.render=rq,rA=[],rj.__r=0;var rF=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,rV=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,rX=/[\s\n\\/='"\0<>]/,rG=/^xlink:?./,rY=/["&<]/;function rZ(e){if(!1===rY.test(e+=""))return e;for(var t=0,r=0,i="",n="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:n="&quot;";break;case 38:n="&amp;";break;case 60:n="&lt;";break;default:continue}r!==t&&(i+=e.slice(t,r)),i+=n,t=r+1}return r!==t&&(i+=e.slice(t,r)),i}var r0=function(e,t){return String(e).replace(/(\n+)/g,"$1"+(t||"	"))},r1=function(e,t,r){return String(e).length>(t||40)||!r&&-1!==String(e).indexOf("\n")||-1!==String(e).indexOf("<")},r2={},r5=/([A-Z])/g;function r3(e){var t="";for(var r in e){var i=e[r];null!=i&&""!==i&&(t&&(t+=" "),t+="-"==r[0]?r:r2[r]||(r2[r]=r.replace(r5,"-$1").toLowerCase()),t="number"==typeof i&&!1===rF.test(r)?t+": "+i+"px;":t+": "+i+";")}return t||void 0}function r8(e,t){return Array.isArray(t)?t.reduce(r8,e):null!=t&&!1!==t&&e.push(t),e}function r6(){this.__d=!0}function r4(e,t){return{__v:e,context:t,props:e.props,setState:r6,forceUpdate:r6,__d:!0,__h:[]}}function r9(e,t){var r=e.contextType,i=r&&t[r.__c];return null!=r?i?i.props.value:r.__:t}var r7=[],ie={shallow:!0};ir.render=ir;var it=[];function ir(e,t,r){t=t||{};var i,n=rS.__s;return rS.__s=!0,i=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?function e(t,r,i,n,s,o){if(null==t||"boolean"==typeof t)return"";if("object"!=typeof t)return rZ(t);var a=i.pretty,l=a&&"string"==typeof a?a:"	";if(Array.isArray(t)){for(var c="",u=0;u<t.length;u++)a&&u>0&&(c+="\n"),c+=e(t[u],r,i,n,s,o);return c}var d,h=t.type,p=t.props,f=!1;if("function"==typeof h){if(f=!0,!i.shallow||!n&&!1!==i.renderRootComponent){if(h===rq){var y=[];return r8(y,t.props.children),e(y,r,i,!1!==i.shallowHighOrder,s,o)}var m,g=t.__c=r4(t,r);rS.__b&&rS.__b(t);var b=rS.__r;if(h.prototype&&"function"==typeof h.prototype.render){var w=r9(h,r);(g=t.__c=new h(p,w)).__v=t,g._dirty=g.__d=!0,g.props=p,null==g.state&&(g.state={}),null==g._nextState&&null==g.__s&&(g._nextState=g.__s=g.state),g.context=w,h.getDerivedStateFromProps?g.state=Object.assign({},g.state,h.getDerivedStateFromProps(g.props,g.state)):g.componentWillMount&&(g.componentWillMount(),g.state=g._nextState!==g.state?g._nextState:g.__s!==g.state?g.__s:g.state),b&&b(t),m=g.render(g.props,g.state,g.context)}else for(var v=r9(h,r),_=0;g.__d&&_++<25;)g.__d=!1,b&&b(t),m=h.call(t.__c,p,v);return g.getChildContext&&(r=Object.assign({},r,g.getChildContext())),rS.diffed&&rS.diffed(t),e(m,r,i,!1!==i.shallowHighOrder,s,o)}h=(d=h).displayName||d!==Function&&d.name||function(e){var t=(Function.prototype.toString.call(e).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!t){for(var r=-1,i=r7.length;i--;)if(r7[i]===e){r=i;break}r<0&&(r=r7.push(e)-1),t="UnnamedComponent"+r}return t}(d)}var S,k,A="<"+h;if(p){var x=Object.keys(p);i&&!0===i.sortAttributes&&x.sort();for(var T=0;T<x.length;T++){var E=x[T],$=p[E];if("children"!==E){if(!rX.test(E)&&(i&&i.allAttributes||"key"!==E&&"ref"!==E&&"__self"!==E&&"__source"!==E)){if("defaultValue"===E)E="value";else if("defaultChecked"===E)E="checked";else if("defaultSelected"===E)E="selected";else if("className"===E){if(void 0!==p.class)continue;E="class"}else s&&rG.test(E)&&(E=E.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===E){if(p.for)continue;E="for"}"style"===E&&$&&"object"==typeof $&&($=r3($)),"a"===E[0]&&"r"===E[1]&&"boolean"==typeof $&&($=String($));var C=i.attributeHook&&i.attributeHook(E,$,r,i,f);if(C||""===C)A+=C;else if("dangerouslySetInnerHTML"===E)k=$&&$.__html;else if("textarea"===h&&"value"===E)S=$;else if(($||0===$||""===$)&&"function"!=typeof $){if(!(!0!==$&&""!==$||($=E,i&&i.xml))){A=A+" "+E;continue}if("value"===E){if("select"===h){o=$;continue}"option"===h&&o==$&&void 0===p.selected&&(A+=" selected")}A=A+" "+E+'="'+rZ($)+'"'}}}else S=$}}if(a){var I=A.replace(/\n\s*/," ");I===A||~I.indexOf("\n")?a&&~A.indexOf("\n")&&(A+="\n"):A=I}if(A+=">",rX.test(h))throw Error(h+" is not a valid HTML tag name in "+A);var U,q=rV.test(h)||i.voidElements&&i.voidElements.test(h),P=[];if(k)a&&r1(k)&&(k="\n"+l+r0(k,l)),A+=k;else if(null!=S&&r8(U=[],S).length){for(var O=a&&~A.indexOf("\n"),N=!1,j=0;j<U.length;j++){var R=U[j];if(null!=R&&!1!==R){var D=e(R,r,i,!0,"svg"===h||"foreignObject"!==h&&s,o);if(a&&!O&&r1(D)&&(O=!0),D){if(a){var M=D.length>0&&"<"!=D[0];N&&M?P[P.length-1]+=D:P.push(D),N=M}else P.push(D)}}}if(a&&O)for(var L=P.length;L--;)P[L]="\n"+l+r0(P[L],l)}if(P.length||k)A+=P.join("");else if(i&&i.xml)return A.substring(0,A.length-1)+" />";return!q||U||k?(a&&~A.indexOf("\n")&&(A+="\n"),A=A+"</"+h+">"):A=A.replace(/>$/," />"),A}(e,t,r):function e(t,r,i,n){if(null==t||!0===t||!1===t||""===t)return"";if("object"!=typeof t)return rZ(t);if(ii(t)){for(var s="",o=0;o<t.length;o++)s+=e(t[o],r,i,n);return s}rS.__b&&rS.__b(t);var a=t.type,l=t.props;if("function"==typeof a){if(a===rq)return e(t.props.children,r,i,n);f=a.prototype&&"function"==typeof a.prototype.render?(c=r,d=r9(u=t.type,c),h=new u(t.props,d),t.__c=h,h.__v=t,h.__d=!0,h.props=t.props,null==h.state&&(h.state={}),null==h.__s&&(h.__s=h.state),h.context=d,u.getDerivedStateFromProps?h.state=is({},h.state,u.getDerivedStateFromProps(h.props,h.state)):h.componentWillMount&&(h.componentWillMount(),h.state=h.__s!==h.state?h.__s:h.state),(p=rS.__r)&&p(t),h.render(h.props,h.state,h.context)):function(e,t){var r,i=r4(e,t),n=r9(e.type,t);e.__c=i;for(var s=rS.__r,o=0;i.__d&&o++<25;)i.__d=!1,s&&s(e),r=e.type.call(i,e.props,n);return r}(t,r);var c,u,d,h,p,f,y=t.__c;y.getChildContext&&(r=is({},r,y.getChildContext()));var m=e(f,r,i,n);return rS.diffed&&rS.diffed(t),m}var g,b,w="<";if(w+=a,l)for(var v in g=l.children,l){var _,S,k,A=l[v];if(!("key"===v||"ref"===v||"__self"===v||"__source"===v||"children"===v||"className"===v&&"class"in l||"htmlFor"===v&&"for"in l||rX.test(v))){if(S=v="className"===(_=v)?"class":"htmlFor"===_?"for":"defaultValue"===_?"value":"defaultChecked"===_?"checked":"defaultSelected"===_?"selected":i&&rG.test(_)?_.toLowerCase().replace(/^xlink:?/,"xlink:"):_,k=A,A="style"===S&&null!=k&&"object"==typeof k?r3(k):"a"===S[0]&&"r"===S[1]&&"boolean"==typeof k?String(k):k,"dangerouslySetInnerHTML"===v)b=A&&A.__html;else if("textarea"===a&&"value"===v)g=A;else if((A||0===A||""===A)&&"function"!=typeof A){if(!0===A||""===A){A=v,w=w+" "+v;continue}if("value"===v){if("select"===a){n=A;continue}"option"!==a||n!=A||"selected"in l||(w+=" selected")}w=w+" "+v+'="'+rZ(A)+'"'}}}var x=w;if(w+=">",rX.test(a))throw Error(a+" is not a valid HTML tag name in "+w);var T="",E=!1;if(b)T+=b,E=!0;else if("string"==typeof g)T+=rZ(g),E=!0;else if(ii(g))for(var $=0;$<g.length;$++){var C=g[$];if(null!=C&&!1!==C){var I=e(C,r,"svg"===a||"foreignObject"!==a&&i,n);I&&(T+=I,E=!0)}}else if(null!=g&&!1!==g&&!0!==g){var U=e(g,r,"svg"===a||"foreignObject"!==a&&i,n);U&&(T+=U,E=!0)}if(rS.diffed&&rS.diffed(t),E)w+=T;else if(rV.test(a))return x+" />";return w+"</"+a+">"}(e,t,!1,void 0),rS.__c&&rS.__c(e,it),rS.__s=n,it.length=0,i}var ii=Array.isArray,is=Object.assign;ir.shallowRender=function(e,t){return ir(e,t,ie)};var io=0;function ia(e,t,r,i,n){var s,o,a={};for(o in t)"ref"==o?s=t[o]:a[o]=t[o];var l={type:e,props:a,key:r,ref:s,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--io,__source:n,__self:i};if("function"==typeof e&&(s=e.defaultProps))for(o in s)void 0===a[o]&&(a[o]=s[o]);return rS.vnode&&rS.vnode(l),l}async function il(e,t){let r=window.SimpleWebAuthnBrowser;async function i(r){let i=new URL(`${e}/webauthn-options/${t}`);r&&i.searchParams.append("action",r),s().forEach(e=>{i.searchParams.append(e.name,e.value)});let n=await fetch(i);if(!n.ok){console.error("Failed to fetch options",n);return}return n.json()}function n(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function s(){return Array.from(n().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=n();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function a(e,t){let i=await r.startAuthentication(e,t);return await o("authenticate",i)}async function l(e){s().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function c(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await i("authenticate");if(!e){console.error("Failed to fetch option for autofill authentication");return}try{await a(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=n();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await i(void 0);if(!t){console.error("Failed to fetch options for form submission");return}if("authenticate"===t.action)try{await a(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await l(t.options)}catch(e){console.error(e)}})})(),c()}let ic={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},iu=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
}

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
  }

  button,
  a.button {
    color: var(--provider-dark-color, var(--color-primary)) !important;
    background-color: var(
      --provider-dark-bg,
      var(--color-background)
    ) !important;
  }

    :is(button,a.button):hover {
      background-color: var(
        --provider-dark-bg-hover,
        var(--color-background-hover)
      ) !important;
    }

    :is(button,a.button) span {
      color: var(--provider-dark-bg) !important;
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: #fff;
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function id({html:e,title:t,status:r,cookies:i,theme:n,headTags:s}){return{cookies:i,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${iu}</style><title>${t}</title>${s??""}</head><body class="__next-auth-theme-${n?.colorScheme??"auto"}"><div class="page">${ir(e)}</div></body></html>`}}function ih(e){let{url:t,theme:r,query:i,cookies:n,pages:s,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:i,signinUrl:n,callbackUrl:s})=>(e[t]={id:t,name:r,type:i,signinUrl:n,callbackUrl:s},e),{})}),signin(t,a){if(t)throw new N("Unsupported action");if(s?.signIn){let t=`${s.signIn}${s.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return a&&(t=`${t}&${new URLSearchParams({error:a})}`),{redirect:t,cookies:n}}let l=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),c="";if(l){let{simpleWebAuthnBrowserVersion:e}=l;c=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return id({cookies:n,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:i,theme:n,email:s,error:o}=e;"undefined"!=typeof document&&n?.brandColor&&document.documentElement.style.setProperty("--brand-color",n.brandColor),"undefined"!=typeof document&&n?.buttonText&&document.documentElement.style.setProperty("--button-text-color",n.buttonText);let a=o&&(ic[o]??ic.default),l=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return ia("div",{className:"signin",children:[n?.brandColor&&ia("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${n.brandColor}}`}}),n?.buttonText&&ia("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),ia("div",{className:"card",children:[a&&ia("div",{className:"error",children:ia("p",{children:a})}),n?.logo&&ia("img",{src:n.logo,alt:"Logo",className:"logo"}),r.map((e,n)=>{let o,a,l;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:a,logo:l=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let c=a??o??"#fff";return ia("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?ia("form",{action:e.signinUrl,method:"POST",children:[ia("input",{type:"hidden",name:"csrfToken",value:t}),i&&ia("input",{type:"hidden",name:"callbackUrl",value:i}),ia("button",{type:"submit",className:"button",style:{"--provider-bg":"#fff","--provider-bg-hover":`color-mix(in srgb, ${c} 30%, #fff)`,"--provider-dark-bg":"#161b22","--provider-dark-bg-hover":`color-mix(in srgb, ${c} 30%, #000)`},tabIndex:0,children:[ia("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),l&&ia("img",{loading:"lazy",height:24,src:l})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&n>0&&"email"!==r[n-1].type&&"credentials"!==r[n-1].type&&"webauthn"!==r[n-1].type&&ia("hr",{}),"email"===e.type&&ia("form",{action:e.signinUrl,method:"POST",children:[ia("input",{type:"hidden",name:"csrfToken",value:t}),ia("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),ia("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:s,placeholder:"<EMAIL>",required:!0}),ia("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&ia("form",{action:e.callbackUrl,method:"POST",children:[ia("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>ia("div",{children:[ia("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),ia("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),ia("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&ia("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[ia("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>ia("div",{children:[ia("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),ia("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),ia("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&n+1<r.length&&ia("hr",{})]},e.id)})]}),l&&ia(rq,{children:ia("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${il})(authURL, "${l}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:a,...i}),title:"Sign In",headTags:c})},signout:()=>s?.signOut?{redirect:s.signOut,cookies:n}:id({cookies:n,theme:r,html:function(e){let{url:t,csrfToken:r,theme:i}=e;return ia("div",{className:"signout",children:[i?.brandColor&&ia("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${i.brandColor}
        }
      `}}),i?.buttonText&&ia("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${i.buttonText}
        }
      `}}),ia("div",{className:"card",children:[i?.logo&&ia("img",{src:i.logo,alt:"Logo",className:"logo"}),ia("h1",{children:"Signout"}),ia("p",{children:"Are you sure you want to sign out?"}),ia("form",{action:t?.toString(),method:"POST",children:[ia("input",{type:"hidden",name:"csrfToken",value:r}),ia("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>s?.verifyRequest?{redirect:s.verifyRequest,cookies:n}:id({cookies:n,theme:r,html:function(e){let{url:t,theme:r}=e;return ia("div",{className:"verify-request",children:[r.brandColor&&ia("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),ia("div",{className:"card",children:[r.logo&&ia("img",{src:r.logo,alt:"Logo",className:"logo"}),ia("h1",{children:"Check your email"}),ia("p",{children:"A sign in link has been sent to your email address."}),ia("p",{children:ia("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>s?.error?{redirect:`${s.error}${s.error.includes("?")?"&":"?"}error=${e}`,cookies:n}:id({cookies:n,theme:r,...function(e){let{url:t,error:r="default",theme:i}=e,n=`${t}/signin`,s={default:{status:200,heading:"Error",message:ia("p",{children:ia("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:ia("div",{children:[ia("p",{children:"There is a problem with the server configuration."}),ia("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:ia("div",{children:[ia("p",{children:"You do not have permission to sign in."}),ia("p",{children:ia("a",{className:"button",href:n,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:ia("div",{children:[ia("p",{children:"The sign in link is no longer valid."}),ia("p",{children:"It may have been used already or it may have expired."})]}),signin:ia("a",{className:"button",href:n,children:"Sign in"})}},{status:o,heading:a,message:l,signin:c}=s[r]??s.default;return{status:o,html:ia("div",{className:"error",children:[i?.brandColor&&ia("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${i?.brandColor}
        }
      `}}),ia("div",{className:"card",children:[i?.logo&&ia("img",{src:i?.logo,alt:"Logo",className:"logo"}),ia("h1",{children:a}),ia("div",{className:"message",children:l}),c]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function ip(e,t=Date.now()){return new Date(t+1e3*e)}async function iy(e,t,r,i){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:n,jwt:s,events:o,session:{strategy:a,generateSessionToken:l}}=i;if(!n)return{user:t,account:r};let c=r,{createUser:u,updateUser:d,getUser:h,getUserByAccount:p,getUserByEmail:f,linkAccount:y,createSession:m,getSessionAndUser:g,deleteSession:b}=n,w=null,v=null,_=!1,S="jwt"===a;if(e){if(S)try{let t=i.cookies.sessionToken.name;(w=await s.decode({...s,token:e,salt:t}))&&"sub"in w&&w.sub&&(v=await h(w.sub))}catch{}else{let t=await g(e);t&&(w=t.session,v=t.user)}}if("email"===c.type){let r=await f(t.email);return r?(v?.id!==r.id&&!S&&e&&await b(e),v=await d({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:v})):(v=await u({...t,emailVerified:new Date}),await o.createUser?.({user:v}),_=!0),{session:w=S?{}:await m({sessionToken:l(),userId:v.id,expires:ip(i.session.maxAge)}),user:v,isNewUser:_}}if("webauthn"===c.type){let e=await p({providerAccountId:c.providerAccountId,provider:c.provider});if(e){if(v){if(e.id===v.id){let e={...c,userId:v.id};return{session:w,user:v,isNewUser:_,account:e}}throw new J("The account is already associated with another user",{provider:c.provider})}w=S?{}:await m({sessionToken:l(),userId:e.id,expires:ip(i.session.maxAge)});let t={...c,userId:e.id};return{session:w,user:e,isNewUser:_,account:t}}{if(v){await y({...c,userId:v.id}),await o.linkAccount?.({user:v,account:c,profile:t});let e={...c,userId:v.id};return{session:w,user:v,isNewUser:_,account:e}}if(t.email?await f(t.email):null)throw new J("Another account already exists with the same e-mail address",{provider:c.provider});v=await u({...t}),await o.createUser?.({user:v}),await y({...c,userId:v.id}),await o.linkAccount?.({user:v,account:c,profile:t}),w=S?{}:await m({sessionToken:l(),userId:v.id,expires:ip(i.session.maxAge)});let e={...c,userId:v.id};return{session:w,user:v,isNewUser:!0,account:e}}}let k=await p({providerAccountId:c.providerAccountId,provider:c.provider});if(k){if(v){if(k.id===v.id)return{session:w,user:v,isNewUser:_};throw new $("The account is already associated with another user",{provider:c.provider})}return{session:w=S?{}:await m({sessionToken:l(),userId:k.id,expires:ip(i.session.maxAge)}),user:k,isNewUser:_}}{let{provider:e}=i,{type:r,provider:n,providerAccountId:s,userId:a,...d}=c;if(c=Object.assign(e.account(d)??{},{providerAccountId:s,provider:n,type:r,userId:a}),v)return await y({...c,userId:v.id}),await o.linkAccount?.({user:v,account:c,profile:t}),{session:w,user:v,isNewUser:_};let h=t.email?await f(t.email):null;if(h){let e=i.provider;if(e?.allowDangerousEmailAccountLinking)v=h,_=!1;else throw new $("Another account already exists with the same e-mail address",{provider:c.provider})}else v=await u({...t,emailVerified:null}),_=!0;return await o.createUser?.({user:v}),await y({...c,userId:v.id}),await o.linkAccount?.({user:v,account:c,profile:t}),{session:w=S?{}:await m({sessionToken:l(),userId:v.id,expires:ip(i.session.maxAge)}),user:v,isNewUser:_}}}function im(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(s="oauth4webapi/v3.5.1");let ig="ERR_INVALID_ARG_VALUE",ib="ERR_INVALID_ARG_TYPE";function iw(e,t,r){let i=TypeError(e,{cause:r});return Object.assign(i,{code:t}),i}let iv=Symbol(),i_=Symbol(),iS=Symbol(),ik=Symbol(),iA=Symbol(),ix=Symbol(),iT=Symbol(),iE=new TextEncoder,i$=new TextDecoder;function iC(e){return"string"==typeof e?iE.encode(e):i$.decode(e)}function iI(e){return"string"==typeof e?function(e){try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw iw("The input to be decoded is not correctly encoded.",ig,e)}}(e):function(e){e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}(e)}class iU extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=nN,Error.captureStackTrace?.(this,this.constructor)}}class iq extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function iP(e,t,r){return new iq(e,{code:t,cause:r})}function iO(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function iN(e){im(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e);if(s&&!t.has("user-agent")&&t.set("user-agent",s),t.has("authorization"))throw iw('"options.headers" must not include the "authorization" header name',ig);return t}function ij(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw iw('"options.signal" must return or be an instance of AbortSignal',ib);return e}function iR(e){return e.includes("//")?e.replace("//","/"):e}async function iD(e,t,r,i){if(!(e instanceof URL))throw iw(`"${t}" must be an instance of URL`,ib);i2(e,i?.[iv]!==!0);let n=r(new URL(e.href)),s=iN(i?.headers);return s.set("accept","application/json"),(i?.[ik]||fetch)(n.href,{body:void 0,headers:Object.fromEntries(s.entries()),method:"GET",redirect:"manual",signal:i?.signal?ij(i.signal):void 0})}async function iM(e,t){return iD(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=iR(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r;r=".well-known/oauth-authorization-server","/"===e.pathname?e.pathname=r:e.pathname=iR(`${r}/${e.pathname}`);break;default:throw iw('"options.algorithm" must be "oidc" (default), or "oauth2"',ig)}return e},t)}function iL(e,t,r,i,n){try{if("number"!=typeof e||!Number.isFinite(e))throw iw(`${r} must be a number`,ib,n);if(e>0)return;if(t){if(0!==e)throw iw(`${r} must be a non-negative number`,ig,n);return}throw iw(`${r} must be a positive number`,ig,n)}catch(e){if(i)throw iP(e.message,i,n);throw e}}function iH(e,t,r,i){try{if("string"!=typeof e)throw iw(`${t} must be a string`,ib,i);if(0===e.length)throw iw(`${t} must not be empty`,ig,i)}catch(e){if(r)throw iP(e.message,r,i);throw e}}async function iW(e,t){if(!(e instanceof URL)&&e!==st)throw iw('"expectedIssuerIdentifier" must be an instance of URL',ib);if(!im(t,Response))throw iw('"response" must be an instance of Response',ib);if(200!==t.status)throw iP('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',nH,t);nX(t);let r=await n7(t);if(iH(r.issuer,'"response" body "issuer" property',nM,{body:r}),e!==st&&new URL(r.issuer).href!==e.href)throw iP('"response" body "issuer" property does not match the expected value',nQ,{expected:e.href,body:r,attribute:"issuer"});return r}function iK(e){!function(e,t){if(nd(e)!==t)throw iB(e,t)}(e,"application/json")}function iB(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return iP(r,nL,e)}function iJ(){return iI(crypto.getRandomValues(new Uint8Array(32)))}async function iQ(e){return iH(e,"codeVerifier"),iI(await crypto.subtle.digest("SHA-256",iC(e)))}function iz(e){let t=e?.[i_];return"number"==typeof t&&Number.isFinite(t)?t:0}function iF(e){let t=e?.[iS];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function iV(){return Math.floor(Date.now()/1e3)}function iX(e){if("object"!=typeof e||null===e)throw iw('"as" must be an object',ib);iH(e.issuer,'"as.issuer"')}function iG(e){if("object"!=typeof e||null===e)throw iw('"client" must be an object',ib);iH(e.client_id,'"client.client_id"')}function iY(e,t){let r=iV()+iz(t);return{jti:iJ(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function iZ(e,t,r){if(!r.usages.includes("sign"))throw iw('CryptoKey instances used for signing assertions must include "sign" in their "usages"',ig);let i=`${iI(iC(JSON.stringify(e)))}.${iI(iC(JSON.stringify(t)))}`,n=iI(await crypto.subtle.sign(n0(r),r,iC(i)));return`${i}.${n}`}async function i0(e){let{kty:t,e:r,n:i,x:n,y:s,crv:a}=await crypto.subtle.exportKey("jwk",e),l={kty:t,e:r,n:i,x:n,y:s,crv:a};return o.set(e,l),l}let i1=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function i2(e,t){if(t&&"https:"!==e.protocol)throw iP("only requests to HTTPS are allowed",nW,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw iP("only HTTP and HTTPS requests are allowed",nK,e)}function i5(e,t,r,i){let n;if("string"!=typeof e||!(n=i1(e)))throw iP(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?nF:nV,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return i2(n,i),n}function i3(e,t,r,i){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?i5(e.mtls_endpoint_aliases[t],t,r,i):i5(e[t],t,r,i)}class i8 extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=nO,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class i6 extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=nj,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class i4 extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=nP,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let i9="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",i7=RegExp("^[,\\s]*("+i9+")\\s(.*)"),ne=RegExp("^[,\\s]*("+i9+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),nt=RegExp("^[,\\s]*("+i9+")\\s*=\\s*("+i9+")[,\\s]*(.*)"),nr=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function ni(e){if(e.status>399&&e.status<500){nX(e),iK(e);try{let t=await e.clone().json();if(iO(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function nn(e,t,r){if(e.status!==t){let t;if(t=await ni(e))throw await e.body?.cancel(),new i8("server responded with an error in the response body",{cause:t,response:e});throw iP(`"response" is not a conform ${r} response (unexpected HTTP status code)`,nH,e)}}function ns(e){if(!nA.has(e))throw iw('"options.DPoP" is not a valid DPoPHandle',ig)}async function no(e,t,r,i,n,s){if(iH(e,'"accessToken"'),!(r instanceof URL))throw iw('"url" must be an instance of URL',ib);i2(r,s?.[iv]!==!0),i=iN(i),s?.DPoP&&(ns(s.DPoP),await s.DPoP.addProof(r,i,t.toUpperCase(),e)),i.set("authorization",`${i.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (s?.[ik]||fetch)(r.href,{body:n,headers:Object.fromEntries(i.entries()),method:t,redirect:"manual",signal:s?.signal?ij(s.signal):void 0});return s?.DPoP?.cacheNonce(o),o}async function na(e,t,r,i){iX(e),iG(t);let n=i3(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,i?.[iv]!==!0),s=iN(i?.headers);return t.userinfo_signed_response_alg?s.set("accept","application/jwt"):(s.set("accept","application/json"),s.append("accept","application/jwt")),no(r,"GET",n,s,null,{...i,[i_]:iz(t)})}function nl(e,t,r,i){(a||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return iV()-this.uat}}),i&&Object.assign(i,{jwks:structuredClone(t),uat:r})}function nc(e,t){a?.delete(e),delete t?.jwks,delete t?.uat}let nu=Symbol();function nd(e){return e.headers.get("content-type")?.split(";")[0]}async function nh(e,t,r,i,n){let s;if(iX(e),iG(t),!im(i,Response))throw iw('"response" must be an instance of Response',ib);if(nw(i),200!==i.status)throw iP('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',nH,i);if(nX(i),"application/jwt"===nd(i)){let{claims:r,jwt:o}=await n1(await i.text(),n3.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),iz(t),iF(t),n?.[ix]).then(nv.bind(void 0,t.client_id)).then(nS.bind(void 0,e));nm.set(i,o),s=r}else{if(t.userinfo_signed_response_alg)throw iP("JWT UserInfo Response expected",nR,i);s=await n7(i)}if(iH(s.sub,'"response" body "sub" property',nM,{body:s}),r===nu);else if(iH(r,'"expectedSubject"'),s.sub!==r)throw iP('unexpected "response" body "sub" property value',nQ,{expected:r,body:s,attribute:"sub"});return s}async function np(e,t,r,i,n,s,o){return await r(e,t,n,s),s.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[ik]||fetch)(i.href,{body:n,headers:Object.fromEntries(s.entries()),method:"POST",redirect:"manual",signal:o?.signal?ij(o.signal):void 0})}async function nf(e,t,r,i,n,s){let o=i3(e,"token_endpoint",t.use_mtls_endpoint_aliases,s?.[iv]!==!0);n.set("grant_type",i);let a=iN(s?.headers);a.set("accept","application/json"),s?.DPoP!==void 0&&(ns(s.DPoP),await s.DPoP.addProof(o,a,"POST"));let l=await np(e,t,r,o,n,a,s);return s?.DPoP?.cacheNonce(l),l}let ny=new WeakMap,nm=new WeakMap;function ng(e){if(!e.id_token)return;let t=ny.get(e);if(!t)throw iw('"ref" was already garbage collected or did not resolve from the proper sources',ig);return t}async function nb(e,t,r,i,n){if(iX(e),iG(t),!im(r,Response))throw iw('"response" must be an instance of Response',ib);nw(r),await nn(r,200,"Token Endpoint"),nX(r);let s=await n7(r);if(iH(s.access_token,'"response" body "access_token" property',nM,{body:s}),iH(s.token_type,'"response" body "token_type" property',nM,{body:s}),s.token_type=s.token_type.toLowerCase(),"dpop"!==s.token_type&&"bearer"!==s.token_type)throw new iU("unsupported `token_type` value",{cause:{body:s}});if(void 0!==s.expires_in){let e="number"!=typeof s.expires_in?parseFloat(s.expires_in):s.expires_in;iL(e,!1,'"response" body "expires_in" property',nM,{body:s}),s.expires_in=e}if(void 0!==s.refresh_token&&iH(s.refresh_token,'"response" body "refresh_token" property',nM,{body:s}),void 0!==s.scope&&"string"!=typeof s.scope)throw iP('"response" body "scope" property must be a string',nM,{body:s});if(void 0!==s.id_token){iH(s.id_token,'"response" body "id_token" property',nM,{body:s});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(iL(t.default_max_age,!1,'"client.default_max_age"'),o.push("auth_time")),i?.length&&o.push(...i);let{claims:a,jwt:l}=await n1(s.id_token,n3.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),iz(t),iF(t),n?.[ix]).then(nE.bind(void 0,o)).then(nk.bind(void 0,e)).then(n_.bind(void 0,t.client_id));if(Array.isArray(a.aud)&&1!==a.aud.length){if(void 0===a.azp)throw iP('ID Token "aud" (audience) claim includes additional untrusted audiences',nJ,{claims:a,claim:"aud"});if(a.azp!==t.client_id)throw iP('unexpected ID Token "azp" (authorized party) claim value',nJ,{expected:t.client_id,claims:a,claim:"azp"})}void 0!==a.auth_time&&iL(a.auth_time,!1,'ID Token "auth_time" (authentication time)',nM,{claims:a}),nm.set(r,l),ny.set(s,a)}return s}function nw(e){let t;if(t=function(e){if(!im(e,Response))throw iw('"response" must be an instance of Response',ib);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],i=t;for(;i;){let e,t=i.match(i7),n=t?.["1"].toLowerCase();if(i=t?.["2"],!n)return;let s={};for(;i;){let r,n;if(t=i.match(ne)){if([,r,n,i]=t,n.includes("\\"))try{n=JSON.parse(`"${n}"`)}catch{}s[r.toLowerCase()]=n;continue}if(t=i.match(nt)){[,r,n,i]=t,s[r.toLowerCase()]=n;continue}if(t=i.match(nr)){if(Object.keys(s).length)break;[,e,i]=t;break}return}let o={scheme:n,parameters:s};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new i4("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function nv(e,t){return void 0!==t.claims.aud?n_(e,t):t}function n_(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw iP('unexpected JWT "aud" (audience) claim value',nJ,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw iP('unexpected JWT "aud" (audience) claim value',nJ,{expected:e,claims:t.claims,claim:"aud"});return t}function nS(e,t){return void 0!==t.claims.iss?nk(e,t):t}function nk(e,t){let r=e[sr]?.(t)??e.issuer;if(t.claims.iss!==r)throw iP('unexpected JWT "iss" (issuer) claim value',nJ,{expected:r,claims:t.claims,claim:"iss"});return t}let nA=new WeakSet;async function nx(e,t,r,i,n,s,o){if(iX(e),iG(t),!nA.has(i))throw iw('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',ig);iH(n,'"redirectUri"');let a=n8(i,"code");if(!a)throw iP('no authorization code in "callbackParameters"',nM);let l=new URLSearchParams(o?.additionalParameters);return l.set("redirect_uri",n),l.set("code",a),s!==se&&(iH(s,'"codeVerifier"'),l.set("code_verifier",s)),nf(e,t,r,"authorization_code",l,o)}let nT={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function nE(e,t){for(let r of e)if(void 0===t.claims[r])throw iP(`JWT "${r}" (${nT[r]}) claim missing`,nM,{claims:t.claims});return t}let n$=Symbol(),nC=Symbol();async function nI(e,t,r,i){return"string"==typeof i?.expectedNonce||"number"==typeof i?.maxAge||i?.requireIdToken?nU(e,t,r,i.expectedNonce,i.maxAge,{[ix]:i[ix]}):nq(e,t,r,i)}async function nU(e,t,r,i,n,s){let o=[];switch(i){case void 0:i=n$;break;case n$:break;default:iH(i,'"expectedNonce" argument'),o.push("nonce")}switch(n??=t.default_max_age){case void 0:n=nC;break;case nC:break;default:iL(n,!1,'"maxAge" argument'),o.push("auth_time")}let a=await nb(e,t,r,o,s);iH(a.id_token,'"response" body "id_token" property',nM,{body:a});let l=ng(a);if(n!==nC){let e=iV()+iz(t),r=iF(t);if(l.auth_time+n<e-r)throw iP("too much time has elapsed since the last End-User authentication",nB,{claims:l,now:e,tolerance:r,claim:"auth_time"})}if(i===n$){if(void 0!==l.nonce)throw iP('unexpected ID Token "nonce" claim value',nJ,{expected:void 0,claims:l,claim:"nonce"})}else if(l.nonce!==i)throw iP('unexpected ID Token "nonce" claim value',nJ,{expected:i,claims:l,claim:"nonce"});return a}async function nq(e,t,r,i){let n=await nb(e,t,r,void 0,i),s=ng(n);if(s){if(void 0!==t.default_max_age){iL(t.default_max_age,!1,'"client.default_max_age"');let e=iV()+iz(t),r=iF(t);if(s.auth_time+t.default_max_age<e-r)throw iP("too much time has elapsed since the last End-User authentication",nB,{claims:s,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==s.nonce)throw iP('unexpected ID Token "nonce" claim value',nJ,{expected:void 0,claims:s,claim:"nonce"})}return n}let nP="OAUTH_WWW_AUTHENTICATE_CHALLENGE",nO="OAUTH_RESPONSE_BODY_ERROR",nN="OAUTH_UNSUPPORTED_OPERATION",nj="OAUTH_AUTHORIZATION_RESPONSE_ERROR",nR="OAUTH_JWT_USERINFO_EXPECTED",nD="OAUTH_PARSE_ERROR",nM="OAUTH_INVALID_RESPONSE",nL="OAUTH_RESPONSE_IS_NOT_JSON",nH="OAUTH_RESPONSE_IS_NOT_CONFORM",nW="OAUTH_HTTP_REQUEST_FORBIDDEN",nK="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",nB="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",nJ="OAUTH_JWT_CLAIM_COMPARISON_FAILED",nQ="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",nz="OAUTH_KEY_SELECTION_FAILED",nF="OAUTH_MISSING_SERVER_METADATA",nV="OAUTH_INVALID_SERVER_METADATA";function nX(e){if(e.bodyUsed)throw iw('"response" body has been used already',ig)}async function nG(e,t){iX(e);let r=i3(e,"jwks_uri",!1,t?.[iv]!==!0),i=iN(t?.headers);return i.set("accept","application/json"),i.append("accept","application/jwk-set+json"),(t?.[ik]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(i.entries()),method:"GET",redirect:"manual",signal:t?.signal?ij(t.signal):void 0})}async function nY(e){if(!im(e,Response))throw iw('"response" must be an instance of Response',ib);if(200!==e.status)throw iP('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',nH,e);nX(e);let t=await n7(e,e=>(function(e,...t){if(!t.includes(nd(e)))throw iB(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw iP('"response" body "keys" property must be an array',nM,{body:t});if(!Array.prototype.every.call(t.keys,iO))throw iP('"response" body "keys" property members must be JWK formatted objects',nM,{body:t});return t}function nZ(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new iU(`unsupported ${t.name} modulusLength`,{cause:e})}function n0(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new iU("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(nZ(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new iU("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return nZ(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new iU("unsupported CryptoKey algorithm name",{cause:e})}async function n1(e,t,r,i,n){let s,o,{0:a,1:l,length:c}=e.split(".");if(5===c){if(void 0!==n)e=await n(e),{0:a,1:l,length:c}=e.split(".");else throw new iU("JWE decryption is not configured",{cause:e})}if(3!==c)throw iP("Invalid JWT",nM,e);try{s=JSON.parse(iC(iI(a)))}catch(e){throw iP("failed to parse JWT Header body as base64url encoded JSON",nD,e)}if(!iO(s))throw iP("JWT Header must be a top level object",nM,e);if(t(s),void 0!==s.crit)throw new iU('no JWT "crit" header parameter extensions are supported',{cause:{header:s}});try{o=JSON.parse(iC(iI(l)))}catch(e){throw iP("failed to parse JWT Payload body as base64url encoded JSON",nD,e)}if(!iO(o))throw iP("JWT Payload must be a top level object",nM,e);let u=iV()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw iP('unexpected JWT "exp" (expiration time) claim type',nM,{claims:o});if(o.exp<=u-i)throw iP('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',nB,{claims:o,now:u,tolerance:i,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw iP('unexpected JWT "iat" (issued at) claim type',nM,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw iP('unexpected JWT "iss" (issuer) claim type',nM,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw iP('unexpected JWT "nbf" (not before) claim type',nM,{claims:o});if(o.nbf>u+i)throw iP('unexpected JWT "nbf" (not before) claim value',nB,{claims:o,now:u,tolerance:i,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw iP('unexpected JWT "aud" (audience) claim type',nM,{claims:o});return{header:s,claims:o,jwt:e}}async function n2(e,t,r){let i;switch(t.alg){case"RS256":case"PS256":case"ES256":i="SHA-256";break;case"RS384":case"PS384":case"ES384":i="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":i="SHA-512";break;default:throw new iU(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let n=await crypto.subtle.digest(i,iC(e));return iI(n.slice(0,n.byteLength/2))}async function n5(e){if(e.bodyUsed)throw iw("form_post Request instances must contain a readable body",ig,{cause:e});return e.text()}function n3(e,t,r,i){if(void 0!==e){if("string"==typeof e?i.alg!==e:!e.includes(i.alg))throw iP('unexpected JWT "alg" header parameter',nM,{header:i,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(i.alg))throw iP('unexpected JWT "alg" header parameter',nM,{header:i,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?i.alg!==r:"function"==typeof r?!r(i.alg):!r.includes(i.alg))throw iP('unexpected JWT "alg" header parameter',nM,{header:i,expected:r,reason:"default value"});return}throw iP('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function n8(e,t){let{0:r,length:i}=e.getAll(t);if(i>1)throw iP(`"${t}" parameter must be provided only once`,nM);return r}let n6=Symbol(),n4=Symbol();async function n9(e,t){let{ext:r,key_ops:i,use:n,...s}=t;return crypto.subtle.importKey("jwk",s,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new iU("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}async function n7(e,t=iK){let r;try{r=await e.json()}catch(r){throw t(e),iP('failed to parse "response" body as JSON',nD,r)}if(!iO(r))throw iP('"response" body must be a top level object',nM,{body:r});return r}let se=Symbol(),st=Symbol(),sr=Symbol();async function si(e,t,r){let{cookies:i,logger:n}=r,s=i[e],o=new Date;o.setTime(o.getTime()+9e5),n.debug(`CREATE_${e.toUpperCase()}`,{name:s.name,payload:t,COOKIE_TTL:900,expires:o});let a=await tG({...r.jwt,maxAge:900,token:{value:t},salt:s.name}),l={...s.options,expires:o};return{name:s.name,value:a,options:l}}async function sn(e,t,r){try{let{logger:i,cookies:n,jwt:s}=r;if(i.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new S(`${e} cookie was missing`);let o=await tY({...s,token:t,salt:n[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new S(`${e} value could not be parsed`,{cause:t})}}function ss(e,t,r){let{logger:i,cookies:n}=t,s=n[e];i.debug(`CLEAR_${e.toUpperCase()}`,{cookie:s}),r.push({name:s.name,value:"",options:{...n[e].options,maxAge:0}})}function so(e,t){return async function(r,i,n){let{provider:s,logger:o}=n;if(!s?.checks?.includes(e))return;let a=r?.[n.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:a});let l=await sn(t,a,n);return ss(t,n,i),l}}let sa={async create(e){let t=iJ(),r=await iQ(t);return{cookie:await si("pkceCodeVerifier",t,e),value:r}},use:so("pkce","pkceCodeVerifier")},sl="encodedState",sc={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new S("State data was provided but the provider is not configured to use state");return}let i={origin:t,random:iJ()},n=await tG({secret:e.jwt.secret,token:i,salt:sl,maxAge:900});return{cookie:await si("state",n,e),value:n}},use:so("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await tY({secret:t.jwt.secret,token:e,salt:sl});if(r)return r;throw Error("Invalid state")}catch(e){throw new S("State could not be decoded",{cause:e})}}},su={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=iJ();return{cookie:await si("nonce",t,e),value:t}},use:so("nonce","nonce")},sd="encodedWebauthnChallenge",sh={create:async(e,t,r)=>({cookie:await si("webauthnChallenge",await tG({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:sd,maxAge:900}),e)}),async use(e,t,r){let i=t?.[e.cookies.webauthnChallenge.name],n=await sn("webauthnChallenge",i,e),s=await tY({secret:e.jwt.secret,token:n,salt:sd});if(ss("webauthnChallenge",e,r),!s)throw new S("WebAuthn challenge was missing");return s}};function sp(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function sf(e,t,r){let i,n,s;let{logger:o,provider:a}=r,{token:l,userinfo:c}=a;if(l?.url&&"authjs.dev"!==l.url.host||c?.url&&"authjs.dev"!==c.url.host)i={issuer:a.issuer??"https://authjs.dev",token_endpoint:l?.url.toString(),userinfo_endpoint:c?.url.toString()};else{let e=new URL(a.issuer),t=await iM(e,{[iv]:!0,[ik]:a[ra]});if(!(i=await iW(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!i.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let u={client_id:a.clientId,...a.client};switch(u.token_endpoint_auth_method){case void 0:case"client_secret_basic":n=(e,t,r,i)=>{i.set("authorization",function(e,t){let r=sp(e),i=sp(t),n=btoa(`${r}:${i}`);return`Basic ${n}`}(a.clientId,a.clientSecret))};break;case"client_secret_post":var d;iH(d=a.clientSecret,'"clientSecret"'),n=(e,t,r,i)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":n=function(e,t){let r;iH(e,'"clientSecret"');let i=void 0;return async(t,n,s,o)=>{r||=await crypto.subtle.importKey("raw",iC(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let a={alg:"HS256"},l=iY(t,n);i?.(a,l);let c=`${iI(iC(JSON.stringify(a)))}.${iI(iC(JSON.stringify(l)))}`,u=await crypto.subtle.sign(r.algorithm,r,iC(c));s.set("client_id",n.client_id),s.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),s.set("client_assertion",`${c}.${iI(new Uint8Array(u))}`)}}(a.clientSecret);break;case"private_key_jwt":n=function(e,t){let{key:r,kid:i}=e instanceof CryptoKey?{key:e}:e?.key instanceof CryptoKey?(void 0!==e.kid&&iH(e.kid,'"kid"'),{key:e.key,kid:e.kid}):{};return function(e,t){if(function(e,t){if(!(e instanceof CryptoKey))throw iw(`${t} must be a CryptoKey`,ib)}(e,t),"private"!==e.type)throw iw(`${t} must be a private CryptoKey`,ig)}(r,'"clientPrivateKey.key"'),async(e,n,s,o)=>{let a={alg:function(e){switch(e.algorithm.name){case"RSA-PSS":return function(e){switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new iU("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}}(e);case"RSASSA-PKCS1-v1_5":return function(e){switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new iU("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}}(e);case"ECDSA":return function(e){switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new iU("unsupported EcKeyAlgorithm namedCurve",{cause:e})}}(e);case"Ed25519":case"EdDSA":return"Ed25519";default:throw new iU("unsupported CryptoKey algorithm name",{cause:e})}}(r),kid:i},l=iY(e,n);t?.[iA]?.(a,l),s.set("client_id",n.client_id),s.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),s.set("client_assertion",await iZ(a,l,r))}}(a.token.clientPrivateKey,{[iA](e,t){t.aud=[i.issuer,i.token_endpoint]}});break;case"none":n=(e,t,r,i)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let h=[],p=await sc.use(t,h,r);try{s=function(e,t,r,i){var n;if(iX(e),iG(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw iw('"parameters" must be an instance of URLSearchParams, or URL',ib);if(n8(r,"response"))throw iP('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',nM,{parameters:r});let s=n8(r,"iss"),o=n8(r,"state");if(!s&&e.authorization_response_iss_parameter_supported)throw iP('response parameter "iss" (issuer) missing',nM,{parameters:r});if(s&&s!==e.issuer)throw iP('unexpected "iss" (issuer) response parameter value',nM,{expected:e.issuer,parameters:r});switch(i){case void 0:case n4:if(void 0!==o)throw iP('unexpected "state" response parameter encountered',nM,{expected:void 0,parameters:r});break;case n6:break;default:if(iH(i,'"expectedState" argument'),o!==i)throw iP(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',nM,{expected:i,parameters:r})}if(n8(r,"error"))throw new i6("authorization response from the server is an error",{cause:r});let a=n8(r,"id_token"),l=n8(r,"token");if(void 0!==a||void 0!==l)throw new iU("implicit and hybrid flows are not supported");return n=new URLSearchParams(r),nA.add(n),n}(i,u,new URLSearchParams(e),a.checks.includes("state")?p:n6)}catch(e){if(e instanceof i6){let t={providerId:a.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new C("OAuth Provider returned an error",t)}throw e}let f=await sa.use(t,h,r),y=a.callbackUrl;!r.isOnRedirectProxy&&a.redirectProxyUrl&&(y=a.redirectProxyUrl);let m=await nx(i,u,n,s,y,f??"decoy",{[iv]:!0,[ik]:(...e)=>(a.checks.includes("pkce")||e[1].body.delete("code_verifier"),(a[ra]??fetch)(...e))});a.token?.conform&&(m=await a.token.conform(m.clone())??m);let g={},b="oidc"===a.type;if(a[rl])switch(a.id){case"microsoft-entra-id":case"azure-ad":{let{tid:e}=function(e){let t,r;if("string"!=typeof e)throw new e$("JWTs must use Compact JWS serialization, JWT must be a string");let{1:i,length:n}=e.split(".");if(5===n)throw new e$("Only JWTs using Compact JWS serialization can be decoded");if(3!==n)throw new e$("Invalid JWT");if(!i)throw new e$("JWTs must contain a payload");try{t=ew(i)}catch{throw new e$("Failed to base64url decode the payload")}try{r=JSON.parse(ec.decode(t))}catch{throw new e$("Failed to parse the decoded payload as JSON")}if(!eN(r))throw new e$("Invalid JWT Claims Set");return r}((await m.clone().json()).id_token);if("string"==typeof e){let t=i.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(i.issuer.replace(t,e)),n=await iM(r,{[ik]:a[ra]});i=await iW(r,n)}}}let w=await nI(i,u,m,{expectedNonce:await su.use(t,h,r),requireIdToken:b});if(b){let t=ng(w);if(g=t,a[rl]&&"apple"===a.id)try{g.user=JSON.parse(e?.user)}catch{}if(!1===a.idToken){let e=await na(i,u,w.access_token,{[ik]:a[ra],[iv]:!0});g=await nh(i,u,t.sub,e)}}else if(c?.request){let e=await c.request({tokens:w,provider:a});e instanceof Object&&(g=e)}else if(c?.url){let e=await na(i,u,w.access_token,{[ik]:a[ra]});g=await e.json()}else throw TypeError("No userinfo endpoint configured");return w.expires_in&&(w.expires_at=Math.floor(Date.now()/1e3)+Number(w.expires_in)),{...await sy(g,a,w,o),profile:g,cookies:h}}async function sy(e,t,r,i){try{let i=await t.profile(e,r);return{user:{...i,id:crypto.randomUUID(),email:i.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:i.id??crypto.randomUUID()}}}catch(r){i.debug("getProfile error details",e),i.error(new I(r,{provider:t.id}))}}var sm=r(5356).Buffer;async function sg(e,t,r,i){let n=await sS(e,t,r),{cookie:s}=await sh.create(e,n.challenge,r);return{status:200,cookies:[...i??[],s],body:{action:"register",options:n},headers:{"Content-Type":"application/json"}}}async function sb(e,t,r,i){let n=await s_(e,t,r),{cookie:s}=await sh.create(e,n.challenge);return{status:200,cookies:[...i??[],s],body:{action:"authenticate",options:n},headers:{"Content-Type":"application/json"}}}async function sw(e,t,r){let i;let{adapter:n,provider:s}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new h("Invalid WebAuthn Authentication response");let a=sx(sA(o.id)),l=await n.getAuthenticator(a);if(!l)throw new h(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:a})}`);let{challenge:c}=await sh.use(e,t.cookies,r);try{let r=s.getRelayingParty(e,t);i=await s.simpleWebAuthn.verifyAuthenticationResponse({...s.verifyAuthenticationOptions,expectedChallenge:c,response:o,authenticator:{...l,credentialDeviceType:l.credentialDeviceType,transports:sT(l.transports),credentialID:sA(l.credentialID),credentialPublicKey:sA(l.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new B(e)}let{verified:u,authenticationInfo:d}=i;if(!u)throw new B("WebAuthn authentication response could not be verified");try{let{newCounter:e}=d;await n.updateAuthenticatorCounter(l.credentialID,e)}catch(e){throw new f(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:a,oldCounter:l.counter,newCounter:d.newCounter})}`,e)}let p=await n.getAccount(l.providerAccountId,s.id);if(!p)throw new h(`WebAuthn account not found in database: ${JSON.stringify({credentialID:a,providerAccountId:l.providerAccountId})}`);let y=await n.getUser(p.userId);if(!y)throw new h(`WebAuthn user not found in database: ${JSON.stringify({credentialID:a,providerAccountId:l.providerAccountId,userID:p.userId})}`);return{account:p,user:y}}async function sv(e,t,r){var i;let n;let{provider:s}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new h("Invalid WebAuthn Registration response");let{challenge:a,registerData:l}=await sh.use(e,t.cookies,r);if(!l)throw new h("Missing user registration data in WebAuthn challenge cookie");try{let r=s.getRelayingParty(e,t);n=await s.simpleWebAuthn.verifyRegistrationResponse({...s.verifyRegistrationOptions,expectedChallenge:a,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new B(e)}if(!n.verified||!n.registrationInfo)throw new B("WebAuthn registration response could not be verified");let c={providerAccountId:sx(n.registrationInfo.credentialID),provider:e.provider.id,type:s.type},u={providerAccountId:c.providerAccountId,counter:n.registrationInfo.counter,credentialID:sx(n.registrationInfo.credentialID),credentialPublicKey:sx(n.registrationInfo.credentialPublicKey),credentialBackedUp:n.registrationInfo.credentialBackedUp,credentialDeviceType:n.registrationInfo.credentialDeviceType,transports:(i=o.response.transports,i?.join(","))};return{user:l,account:c,authenticator:u}}async function s_(e,t,r){let{provider:i,adapter:n}=e,s=r&&r.id?await n.listAuthenticatorsByUserId(r.id):null,o=i.getRelayingParty(e,t);return await i.simpleWebAuthn.generateAuthenticationOptions({...i.authenticationOptions,rpID:o.id,allowCredentials:s?.map(e=>({id:sA(e.credentialID),type:"public-key",transports:sT(e.transports)}))})}async function sS(e,t,r){let{provider:i,adapter:n}=e,s=r.id?await n.listAuthenticatorsByUserId(r.id):null,o=re(32),a=i.getRelayingParty(e,t);return await i.simpleWebAuthn.generateRegistrationOptions({...i.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:a.id,rpName:a.name,excludeCredentials:s?.map(e=>({id:sA(e.credentialID),type:"public-key",transports:sT(e.transports)}))})}function sk(e){let{provider:t,adapter:r}=e;if(!r)throw new A("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new R("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function sA(e){return new Uint8Array(sm.from(e,"base64"))}function sx(e){return sm.from(e).toString("base64")}function sT(e){return e?e.split(","):void 0}async function sE(e,t,r,i){if(!t.provider)throw new R("Callback route called without provider");let{query:n,body:s,method:o,headers:a}=e,{provider:l,adapter:c,url:u,callbackUrl:d,pages:p,jwt:f,events:y,callbacks:g,session:{strategy:b,maxAge:w},logger:_}=t,S="jwt"===b;try{if("oauth"===l.type||"oidc"===l.type){let o;let a=l.authorization?.url.searchParams.get("response_mode")==="form_post"?s:n;if(t.isOnRedirectProxy&&a?.state){let e=await sc.decode(a.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(a)}`;return _.debug("Proxy redirecting to",t),{redirect:t,cookies:i}}}let h=await sf(a,e.cookies,t);h.cookies.length&&i.push(...h.cookies),_.debug("authorization result",h);let{user:m,account:b,profile:v}=h;if(!m||!b||!v)return{redirect:`${u}/signin`,cookies:i};if(c){let{getUserByAccount:e}=c;o=await e({providerAccountId:b.providerAccountId,provider:l.id})}let k=await s$({user:o??m,account:b,profile:v},t);if(k)return{redirect:k,cookies:i};let{user:A,session:x,isNewUser:T}=await iy(r.value,m,b,t);if(S){let e={name:A.name,email:A.email,picture:A.image,sub:A.id?.toString()},n=await g.jwt({token:e,user:A,account:b,profile:v,isNewUser:T,trigger:T?"signUp":"signIn"});if(null===n)i.push(...r.clean());else{let e=t.cookies.sessionToken.name,s=await f.encode({...f,token:n,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*w);let a=r.chunk(s,{expires:o});i.push(...a)}}else i.push({name:t.cookies.sessionToken.name,value:x.sessionToken,options:{...t.cookies.sessionToken.options,expires:x.expires}});if(await y.signIn?.({user:A,account:b,profile:v,isNewUser:T}),T&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:i};return{redirect:d,cookies:i}}if("email"===l.type){let e=n?.token,s=n?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=l.secret??t.secret,a=await c.useVerificationToken({identifier:s,token:await t7(`${e}${o}`)}),u=!!a,h=u&&a.expires.valueOf()<Date.now();if(!u||h||s&&a.identifier!==s)throw new M({hasInvite:u,expired:h});let{identifier:m}=a,b=await c.getUserByEmail(m)??{id:crypto.randomUUID(),email:m,emailVerified:null},v={providerAccountId:b.email,userId:b.id,type:"email",provider:l.id},_=await s$({user:b,account:v},t);if(_)return{redirect:_,cookies:i};let{user:k,session:A,isNewUser:x}=await iy(r.value,b,v,t);if(S){let e={name:k.name,email:k.email,picture:k.image,sub:k.id?.toString()},n=await g.jwt({token:e,user:k,account:v,isNewUser:x,trigger:x?"signUp":"signIn"});if(null===n)i.push(...r.clean());else{let e=t.cookies.sessionToken.name,s=await f.encode({...f,token:n,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*w);let a=r.chunk(s,{expires:o});i.push(...a)}}else i.push({name:t.cookies.sessionToken.name,value:A.sessionToken,options:{...t.cookies.sessionToken.options,expires:A.expires}});if(await y.signIn?.({user:k,account:v,isNewUser:x}),x&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:i};return{redirect:d,cookies:i}}if("credentials"===l.type&&"POST"===o){let e=s??{};Object.entries(n??{}).forEach(([e,t])=>u.searchParams.set(e,t));let c=await l.authorize(e,new Request(u,{headers:a,method:o,body:JSON.stringify(s)}));if(c)c.id=c.id?.toString()??crypto.randomUUID();else throw new v;let h={providerAccountId:c.id,type:"credentials",provider:l.id},p=await s$({user:c,account:h,credentials:e},t);if(p)return{redirect:p,cookies:i};let m={name:c.name,email:c.email,picture:c.image,sub:c.id},b=await g.jwt({token:m,user:c,account:h,isNewUser:!1,trigger:"signIn"});if(null===b)i.push(...r.clean());else{let e=t.cookies.sessionToken.name,n=await f.encode({...f,token:b,salt:e}),s=new Date;s.setTime(s.getTime()+1e3*w);let o=r.chunk(n,{expires:s});i.push(...o)}return await y.signIn?.({user:c,account:h}),{redirect:d,cookies:i}}if("webauthn"===l.type&&"POST"===o){let n,s,o;let a=e.body?.action;if("string"!=typeof a||"authenticate"!==a&&"register"!==a)throw new h("Invalid action parameter");let l=sk(t);switch(a){case"authenticate":{let t=await sw(l,e,i);n=t.user,s=t.account;break}case"register":{let r=await sv(t,e,i);n=r.user,s=r.account,o=r.authenticator}}await s$({user:n,account:s},t);let{user:c,isNewUser:u,session:m,account:b}=await iy(r.value,n,s,t);if(!b)throw new h("Error creating or finding account");if(o&&c.id&&await l.adapter.createAuthenticator({...o,userId:c.id}),S){let e={name:c.name,email:c.email,picture:c.image,sub:c.id?.toString()},n=await g.jwt({token:e,user:c,account:b,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===n)i.push(...r.clean());else{let e=t.cookies.sessionToken.name,s=await f.encode({...f,token:n,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*w);let a=r.chunk(s,{expires:o});i.push(...a)}}else i.push({name:t.cookies.sessionToken.name,value:m.sessionToken,options:{...t.cookies.sessionToken.options,expires:m.expires}});if(await y.signIn?.({user:c,account:b,isNewUser:u}),u&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:i};return{redirect:d,cookies:i}}throw new R(`Callback for provider type (${l.type}) is not supported`)}catch(t){if(t instanceof h)throw t;let e=new m(t,{provider:l.id});throw _.debug("callback route error details",{method:o,query:n,body:s}),e}}async function s$(e,t){let r;let{signIn:i,redirect:n}=t.callbacks;try{r=await i(e)}catch(e){if(e instanceof h)throw e;throw new y(e)}if(!r)throw new y("AccessDenied");if("string"==typeof r)return await n({url:r,baseUrl:t.url.origin})}async function sC(e,t,r,i,n){let{adapter:s,jwt:o,events:a,callbacks:l,logger:c,session:{strategy:u,maxAge:d}}=e,h={body:null,headers:{"Content-Type":"application/json"},cookies:r},p=t.value;if(!p)return h;if("jwt"===u){try{let r=e.cookies.sessionToken.name,s=await o.decode({...o,token:p,salt:r});if(!s)throw Error("Invalid JWT");let c=await l.jwt({token:s,...i&&{trigger:"update"},session:n}),u=ip(d);if(null!==c){let e={user:{name:c.name,email:c.email,image:c.picture},expires:u.toISOString()},i=await l.session({session:e,token:c});h.body=i;let n=await o.encode({...o,token:c,salt:r}),s=t.chunk(n,{expires:u});h.cookies?.push(...s),await a.session?.({session:i,token:c})}else h.cookies?.push(...t.clean())}catch(e){c.error(new k(e)),h.cookies?.push(...t.clean())}return h}try{let{getSessionAndUser:r,deleteSession:o,updateSession:c}=s,u=await r(p);if(u&&u.session.expires.valueOf()<Date.now()&&(await o(p),u=null),u){let{user:t,session:r}=u,s=e.session.updateAge,o=r.expires.valueOf()-1e3*d+1e3*s,f=ip(d);o<=Date.now()&&await c({sessionToken:p,expires:f});let y=await l.session({session:{...r,user:t},user:t,newSession:n,...i?{trigger:"update"}:{}});h.body=y,h.cookies?.push({name:e.cookies.sessionToken.name,value:p,options:{...e.cookies.sessionToken.options,expires:f}}),await a.session?.({session:y})}else p&&h.cookies?.push(...t.clean())}catch(e){c.error(new U(e))}return h}async function sI(e,t){let r,i;let{logger:n,provider:s}=t,o=s.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(s.issuer),t=await iM(e,{[ik]:s[ra],[iv]:!0}),r=await iW(e,t);if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let a=o.searchParams,l=s.callbackUrl;!t.isOnRedirectProxy&&s.redirectProxyUrl&&(l=s.redirectProxyUrl,i=s.callbackUrl,n.debug("using redirect proxy",{redirect_uri:l,data:i}));let c=Object.assign({response_type:"code",client_id:s.clientId,redirect_uri:l,...s.authorization?.params},Object.fromEntries(s.authorization?.url.searchParams??[]),e);for(let e in c)a.set(e,c[e]);let u=[];s.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await sc.create(t,i);if(d&&(a.set("state",d.value),u.push(d.cookie)),s.checks?.includes("pkce")){if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===s.type&&(s.checks=["nonce"]);else{let{value:e,cookie:r}=await sa.create(t);a.set("code_challenge",e),a.set("code_challenge_method","S256"),u.push(r)}}let h=await su.create(t);return h&&(a.set("nonce",h.value),u.push(h.cookie)),"oidc"!==s.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),n.debug("authorization url is ready",{url:o,cookies:u,provider:s}),{redirect:o.toString(),cookies:u}}async function sU(e,t){let r;let{body:i}=e,{provider:n,callbacks:s,adapter:o}=t,a=(n.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(i?.email),l={id:crypto.randomUUID(),email:a,emailVerified:null},c=await o.getUserByEmail(a)??l,u={providerAccountId:a,userId:c.id,type:"email",provider:n.id};try{r=await s.signIn({user:c,account:u,email:{verificationRequest:!0}})}catch(e){throw new y(e)}if(!r)throw new y("AccessDenied");if("string"==typeof r)return{redirect:await s.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:h}=t,p=await n.generateVerificationToken?.()??re(32),f=new Date(Date.now()+(n.maxAge??86400)*1e3),m=n.secret??t.secret,g=new URL(t.basePath,t.url.origin),b=n.sendVerificationRequest({identifier:a,token:p,expires:f,url:`${g}/callback/${n.id}?${new URLSearchParams({callbackUrl:d,token:p,email:a})}`,provider:n,theme:h,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),w=o.createVerificationToken?.({identifier:a,token:await t7(`${p}${m}`),expires:f});return await Promise.all([b,w]),{redirect:`${g}/verify-request?${new URLSearchParams({provider:n.id,type:n.type})}`}}async function sq(e,t,r){let i=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:i,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:i,cookies:n}=await sI(e.query,r);return n&&t.push(...n),{redirect:i,cookies:t}}case"email":return{...await sU(e,r),cookies:t};default:return{redirect:i,cookies:t}}}async function sP(e,t,r){let{jwt:i,events:n,callbackUrl:s,logger:o,session:a}=r,l=t.value;if(!l)return{redirect:s,cookies:e};try{if("jwt"===a.strategy){let e=r.cookies.sessionToken.name,t=await i.decode({...i,token:l,salt:e});await n.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(l);await n.signOut?.({session:e})}}catch(e){o.error(new O(e))}return e.push(...t.clean()),{redirect:s,cookies:e}}async function sO(e,t){let{adapter:r,jwt:i,session:{strategy:n}}=e,s=t.value;if(!s)return null;if("jwt"===n){let t=e.cookies.sessionToken.name,r=await i.decode({...i,token:s,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(s);if(e)return e.user}return null}async function sN(e,t,r,i){let n=sk(t),{provider:s}=n,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:i,headers:{"Content-Type":"application/json"}};let a=await sO(t,r),l=a?{user:a,exists:!0}:await s.getUserInfo(t,e),c=l?.user;switch(function(e,t,r){let{user:i,exists:n=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(i&&t===n)return"register";break;case void 0:if(!t){if(!i||n)return"authenticate";return"register"}}return null}(o,!!a,l)){case"authenticate":return sb(n,e,c,i);case"register":if("string"==typeof c?.email)return sg(n,e,c,i);break;default:return{status:400,body:{error:"Invalid request"},cookies:i,headers:{"Content-Type":"application/json"}}}}async function sj(e,t){let{action:r,providerId:i,error:n,method:s}=e,o=t.skipCSRFCheck===rs,{options:a,cookies:l}=await rf({authOptions:t,action:r,providerId:i,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===s,csrfDisabled:o}),c=new d(a.cookies.sessionToken,e.cookies,a.logger);if("GET"===s){let t=ih({...a,query:e.query,cookies:l});switch(r){case"callback":return await sE(e,a,c,l);case"csrf":return t.csrf(o,a,l);case"error":return t.error(n);case"providers":return t.providers(a.providers);case"session":return await sC(a,c,l);case"signin":return t.signin(i,n);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await sN(e,a,c,l)}}else{let{csrfTokenVerified:t}=a;switch(r){case"callback":return"credentials"===a.provider.type&&rr(r,t),await sE(e,a,c,l);case"session":return rr(r,t),await sC(a,c,l,!0,e.body?.data);case"signin":return rr(r,t),await sq(e,l,a);case"signout":return rr(r,t),await sP(l,c,a)}}throw new N(`Cannot handle action: ${r}`)}function sR(e,t,r,i,n){let s;let o=n?.basePath,a=i.AUTH_URL??i.NEXTAUTH_URL;if(a)s=new URL(a),o&&"/"!==o&&"/"!==s.pathname&&(s.pathname!==o&&t3(n).warn("env-url-basepath-mismatch"),s.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),i=r.get("x-forwarded-proto")??t??"https",n=i.endsWith(":")?i:i+":";s=new URL(`${n}//${e}`)}let l=s.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${l}/${t}/${e}`)}return new URL(`${l}/${e}`)}async function sD(e,t){let r=t3(t),i=await t4(e,t);if(!i)return Response.json("Bad request.",{status:400});let n=function(e,t){let{url:r}=e,i=[];if(!z&&t.debug&&i.push("debug-enabled"),!t.trustHost)return new D(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new E("Please define a `secret`");let n=e.query?.callbackUrl;if(n&&!F(n,r.origin))return new w(`Invalid callback URL. Received: ${n}`);let{callbackUrl:s}=u(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??s.name];if(o&&!F(o,r.origin))return new w(`Invalid callback URL. Received: ${o}`);let a=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e;let{authorization:r,token:i,userinfo:n}=t;if("string"==typeof r||r?.url?"string"==typeof i||i?.url?"string"==typeof n||n?.url||(e="userinfo"):e="token":e="authorization",e)return new _(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)V=!0;else if("email"===t.type)X=!0;else if("webauthn"===t.type){var l;if(G=!0,t.simpleWebAuthnBrowserVersion&&(l=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(l)))return new h(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(a)return new W("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(a=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new K(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(V){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new j("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new T("Must define an authorize() handler to use credentials authentication provider")}let{adapter:c,session:d}=t,p=[];if(X||d?.strategy==="database"||!d?.strategy&&c){if(X){if(!c)return new A("Email login requires an adapter");p.push(...Y)}else{if(!c)return new A("Database session requires an adapter");p.push(...Z)}}if(G){if(!t.experimental?.enableWebAuthn)return new Q("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(i.push("experimental-webauthn"),!c)return new A("WebAuthn requires an adapter");p.push(...ee)}if(c){let e=p.filter(e=>!(e in c));if(e.length)return new x(`Required adapter methods were missing: ${e.join(", ")}`)}return z||(z=!0),i}(i,t);if(Array.isArray(n))n.forEach(r.warn);else if(n){if(r.error(n),!new Set(["signin","signout","error","verify-request"]).has(i.action)||"GET"!==i.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:s}=t,o=e?.error&&i.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new g(`The error page ${e?.error} should not require authentication`)),t9(ih({theme:s}).error("Configuration"));let a=`${i.url.origin}${e.error}?error=Configuration`;return Response.redirect(a)}let s=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===ro;try{let e=await sj(i,t);if(o)return e;let r=t9(e),n=r.headers.get("Location");if(!s||!n)return r;return Response.json({url:n},{headers:r.headers})}catch(d){r.error(d);let n=d instanceof h;if(n&&o&&!s)throw d;if("POST"===e.method&&"session"===i.action)return Response.json(null,{status:400});let a=new URLSearchParams({error:d instanceof h&&H.has(d.type)?d.type:"Configuration"});d instanceof v&&a.set("code",d.code);let l=n&&d.kind||"error",c=t.pages?.[l]??`${t.basePath}/${l.toLowerCase()}`,u=`${i.url.origin}${c}?${a}`;if(s)return Response.json({url:u});return Response.redirect(u)}}var sM=r(658);function sL(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:i,origin:n}=e.nextUrl;return new sM.J8(i.replace(n,r),e)}function sH(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let i=e.AUTH_URL;i&&(t.basePath?r||t3(t).warn("env-url-basepath-redundant"):t.basePath=new URL(i).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let i of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${i}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,i=r.toUpperCase().replace(/-/g,"_"),n=e[`AUTH_${i}_ID`],s=e[`AUTH_${i}_SECRET`],o=e[`AUTH_${i}_ISSUER`],a=e[`AUTH_${i}_KEY`],l="function"==typeof t?t({clientId:n,clientSecret:s,issuer:o,apiKey:a}):t;return"oauth"===l.type||"oidc"===l.type?(l.clientId??(l.clientId=n),l.clientSecret??(l.clientSecret=s),l.issuer??(l.issuer=o)):"email"===l.type&&(l.apiKey??(l.apiKey=a)),l})}(process.env,e,!0)}}var sW=r(144);async function sK(e,t){return sD(new Request(sR("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function sB(e){return"function"==typeof e}function sJ(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,sW.b3)(),i=await e(void 0);return t?.(i),sK(r,i).then(e=>e.json())}if(r[0]instanceof Request){let i=r[0],n=r[1],s=await e(i);return t?.(s),sQ([i,n],s)}if(sB(r[0])){let i=r[0];return async(...r)=>{let n=await e(r[0]);return t?.(n),sQ(r,n,i)}}let i="req"in r[0]?r[0].req:r[0],n="res"in r[0]?r[0].res:r[1],s=await e(i);return t?.(s),sK(new Headers(i.headers),s).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,sW.b3)()).then(t=>sK(t,e).then(e=>e.json()));if(t[0]instanceof Request)return sQ([t[0],t[1]],e);if(sB(t[0])){let r=t[0];return async(...t)=>sQ(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],i="res"in t[0]?t[0].res:t[1];return sK(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in i?i.headers.append("set-cookie",t):i.appendHeader("set-cookie",t);return t})}}async function sQ(e,t,r){let i=sL(e[0]),n=await sK(i.headers,t),s=await n.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:i,auth:s}));let a=sM.Rp.next?.();if(o instanceof Response){a=o;let e=o.headers.get("Location"),{pathname:r}=i.nextUrl;e&&function(e,t,r){let i=t.replace(`${e}/`,""),n=Object.values(r.pages??{});return(sz.has(i)||n.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)i.auth=s,a=await r(i,e[1])??sM.Rp.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(i.nextUrl.pathname!==e){let t=i.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",i.nextUrl.href),a=sM.Rp.redirect(t)}}let l=new Response(a?.body,a);for(let e of n.headers.getSetCookie())l.headers.append("set-cookie",e);return l}let sz=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var sF=r(8149);async function sV(e,t={},r,i){let n=new Headers(await (0,sW.b3)()),{redirect:s=!0,redirectTo:o,...a}=t instanceof FormData?Object.fromEntries(t):t,l=o?.toString()??n.get("Referer")??"/",c=sR("signin",n.get("x-forwarded-proto"),n,process.env,i);if(!e)return c.searchParams.append("callbackUrl",l),s&&(0,sF.V2)(c.toString()),c.toString();let u=`${c}/${e}?${new URLSearchParams(r)}`,d={};for(let t of i.providers){let{options:r,...i}="function"==typeof t?t():t,n=r?.id??i.id;if(n===e){d={id:n,type:r?.type??i.type};break}}if(!d.id){let e=`${c}?${new URLSearchParams({callbackUrl:l})}`;return s&&(0,sF.V2)(e),e}"credentials"===d.type&&(u=u.replace("signin","callback")),n.set("Content-Type","application/x-www-form-urlencoded");let h=new Request(u,{method:"POST",headers:n,body:new URLSearchParams({...a,callbackUrl:l})}),p=await sD(h,{...i,raw:ro,skipCSRFCheck:rs}),f=await (0,sW.UL)();for(let e of p?.cookies??[])f.set(e.name,e.value,e.options);let y=(p instanceof Response?p.headers.get("Location"):p.redirect)??u;return s?(0,sF.V2)(y):y}async function sX(e,t){let r=new Headers(await (0,sW.b3)());r.set("Content-Type","application/x-www-form-urlencoded");let i=sR("signout",r.get("x-forwarded-proto"),r,process.env,t),n=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),s=new Request(i,{method:"POST",headers:r,body:n}),o=await sD(s,{...t,raw:ro,skipCSRFCheck:rs}),a=await (0,sW.UL)();for(let e of o?.cookies??[])a.set(e.name,e.value,e.options);return e?.redirect??!0?(0,sF.V2)(o.redirect):o}async function sG(e,t){let r=new Headers(await (0,sW.b3)());r.set("Content-Type","application/json");let i=new Request(sR("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),n=await sD(i,{...t,raw:ro,skipCSRFCheck:rs}),s=await (0,sW.UL)();for(let e of n?.cookies??[])s.set(e.name,e.value,e.options);return n.body}function sY(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return sH(r),sD(sL(t),r)};return{handlers:{GET:t,POST:t},auth:sJ(e,e=>sH(e)),signIn:async(t,r,i)=>{let n=await e(void 0);return sH(n),sV(t,r,i,n)},signOut:async t=>{let r=await e(void 0);return sH(r),sX(t,r)},unstable_update:async t=>{let r=await e(void 0);return sH(r),sG(t,r)}}}sH(e);let t=t=>sD(sL(t),e);return{handlers:{GET:t,POST:t},auth:sJ(e),signIn:(t,r,i)=>sV(t,r,i,e),signOut:t=>sX(t,e),unstable_update:t=>sG(t,e)}}},5588:(e,t,r)=>{r.d(t,{QP:()=>V});let i=e=>{let t=a(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:i}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||o(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&i[e]?[...n,...i[e]]:n}}},n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],i=t.nextPart.get(r),s=i?n(e.slice(1),i):void 0;if(s)return s;if(0===t.validators.length)return;let o=e.join("-");return t.validators.find(({validator:e})=>e(o))?.classGroupId},s=/^\[(.+)\]$/,o=e=>{if(s.test(e)){let t=s.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},a=e=>{let{theme:t,prefix:r}=e,i={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),r).forEach(([e,r])=>{l(r,i,e,t)}),i},l=(e,t,r,i)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:c(t,e)).classGroupId=r;return}if("function"==typeof e){if(u(e)){l(e(i),t,r,i);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,n])=>{l(n,c(t,e),r,i)})})},c=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},u=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,h=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,i=new Map,n=(n,s)=>{r.set(n,s),++t>e&&(t=0,i=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=i.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},p=e=>{let{separator:t,experimentalParseClassName:r}=e,i=1===t.length,n=t[0],s=t.length,o=e=>{let r;let o=[],a=0,l=0;for(let c=0;c<e.length;c++){let u=e[c];if(0===a){if(u===n&&(i||e.slice(c,c+s)===t)){o.push(e.slice(l,c)),l=c+s;continue}if("/"===u){r=c;continue}}"["===u?a++:"]"===u&&a--}let c=0===o.length?e:e.substring(l),u=c.startsWith("!"),d=u?c.substring(1):c;return{modifiers:o,hasImportantModifier:u,baseClassName:d,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?e=>r({className:e,parseClassName:o}):o},f=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},y=e=>({cache:h(e.cacheSize),parseClassName:p(e),...i(e)}),m=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:i,getConflictingClassGroupIds:n}=t,s=[],o=e.trim().split(m),a="";for(let e=o.length-1;e>=0;e-=1){let t=o[e],{modifiers:l,hasImportantModifier:c,baseClassName:u,maybePostfixModifierPosition:d}=r(t),h=!!d,p=i(h?u.substring(0,d):u);if(!p){if(!h||!(p=i(u))){a=t+(a.length>0?" "+a:a);continue}h=!1}let y=f(l).join(":"),m=c?y+"!":y,g=m+p;if(s.includes(g))continue;s.push(g);let b=n(p,h);for(let e=0;e<b.length;++e){let t=b[e];s.push(m+t)}a=t+(a.length>0?" "+a:a)}return a};function b(){let e,t,r=0,i="";for(;r<arguments.length;)(e=arguments[r++])&&(t=w(e))&&(i&&(i+=" "),i+=t);return i}let w=e=>{let t;if("string"==typeof e)return e;let r="";for(let i=0;i<e.length;i++)e[i]&&(t=w(e[i]))&&(r&&(r+=" "),r+=t);return r},v=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},_=/^\[(?:([a-z-]+):)?(.+)\]$/i,S=/^\d+\/\d+$/,k=new Set(["px","full","screen"]),A=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,x=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,T=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,E=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,$=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,C=e=>U(e)||k.has(e)||S.test(e),I=e=>B(e,"length",J),U=e=>!!e&&!Number.isNaN(Number(e)),q=e=>B(e,"number",U),P=e=>!!e&&Number.isInteger(Number(e)),O=e=>e.endsWith("%")&&U(e.slice(0,-1)),N=e=>_.test(e),j=e=>A.test(e),R=new Set(["length","size","percentage"]),D=e=>B(e,R,Q),M=e=>B(e,"position",Q),L=new Set(["image","url"]),H=e=>B(e,L,F),W=e=>B(e,"",z),K=()=>!0,B=(e,t,r)=>{let i=_.exec(e);return!!i&&(i[1]?"string"==typeof t?i[1]===t:t.has(i[1]):r(i[2]))},J=e=>x.test(e)&&!T.test(e),Q=()=>!1,z=e=>E.test(e),F=e=>$.test(e);Symbol.toStringTag;let V=function(e,...t){let r,i,n;let s=function(a){return i=(r=y(t.reduce((e,t)=>t(e),e()))).cache.get,n=r.cache.set,s=o,o(a)};function o(e){let t=i(e);if(t)return t;let s=g(e,r);return n(e,s),s}return function(){return s(b.apply(null,arguments))}}(()=>{let e=v("colors"),t=v("spacing"),r=v("blur"),i=v("brightness"),n=v("borderColor"),s=v("borderRadius"),o=v("borderSpacing"),a=v("borderWidth"),l=v("contrast"),c=v("grayscale"),u=v("hueRotate"),d=v("invert"),h=v("gap"),p=v("gradientColorStops"),f=v("gradientColorStopPositions"),y=v("inset"),m=v("margin"),g=v("opacity"),b=v("padding"),w=v("saturate"),_=v("scale"),S=v("sepia"),k=v("skew"),A=v("space"),x=v("translate"),T=()=>["auto","contain","none"],E=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto",N,t],R=()=>[N,t],L=()=>["",C,I],B=()=>["auto",U,N],J=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Q=()=>["solid","dashed","dotted","double","none"],z=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],F=()=>["start","end","center","between","around","evenly","stretch"],V=()=>["","0",N],X=()=>["auto","avoid","all","avoid-page","page","left","right","column"],G=()=>[U,N];return{cacheSize:500,separator:":",theme:{colors:[K],spacing:[C,I],blur:["none","",j,N],brightness:G(),borderColor:[e],borderRadius:["none","","full",j,N],borderSpacing:R(),borderWidth:L(),contrast:G(),grayscale:V(),hueRotate:G(),invert:V(),gap:R(),gradientColorStops:[e],gradientColorStopPositions:[O,I],inset:$(),margin:$(),opacity:G(),padding:R(),saturate:G(),scale:G(),sepia:V(),skew:G(),space:R(),translate:R()},classGroups:{aspect:[{aspect:["auto","square","video",N]}],container:["container"],columns:[{columns:[j]}],"break-after":[{"break-after":X()}],"break-before":[{"break-before":X()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...J(),N]}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",P,N]}],basis:[{basis:$()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",N]}],grow:[{grow:V()}],shrink:[{shrink:V()}],order:[{order:["first","last","none",P,N]}],"grid-cols":[{"grid-cols":[K]}],"col-start-end":[{col:["auto",{span:["full",P,N]},N]}],"col-start":[{"col-start":B()}],"col-end":[{"col-end":B()}],"grid-rows":[{"grid-rows":[K]}],"row-start-end":[{row:["auto",{span:[P,N]},N]}],"row-start":[{"row-start":B()}],"row-end":[{"row-end":B()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",N]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",N]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",...F()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...F(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...F(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[m]}],mx:[{mx:[m]}],my:[{my:[m]}],ms:[{ms:[m]}],me:[{me:[m]}],mt:[{mt:[m]}],mr:[{mr:[m]}],mb:[{mb:[m]}],ml:[{ml:[m]}],"space-x":[{"space-x":[A]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[A]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",N,t]}],"min-w":[{"min-w":[N,t,"min","max","fit"]}],"max-w":[{"max-w":[N,t,"none","full","min","max","fit","prose",{screen:[j]},j]}],h:[{h:[N,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[N,t,"auto","min","max","fit"]}],"font-size":[{text:["base",j,I]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",q]}],"font-family":[{font:[K]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",N]}],"line-clamp":[{"line-clamp":["none",U,q]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",C,N]}],"list-image":[{"list-image":["none",N]}],"list-style-type":[{list:["none","disc","decimal",N]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Q(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",C,I]}],"underline-offset":[{"underline-offset":["auto",C,N]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:R()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...J(),M]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",D]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},H]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[f]}],"gradient-via-pos":[{via:[f]}],"gradient-to-pos":[{to:[f]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...Q(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:Q()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...Q()]}],"outline-offset":[{"outline-offset":[C,N]}],"outline-w":[{outline:[C,I]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:L()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[C,I]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",j,W]}],"shadow-color":[{shadow:[K]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...z(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":z()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[i]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",j,N]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[d]}],saturate:[{saturate:[w]}],sepia:[{sepia:[S]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[i]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[S]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",N]}],duration:[{duration:G()}],ease:[{ease:["linear","in","out","in-out",N]}],delay:[{delay:G()}],animate:[{animate:["none","spin","ping","pulse","bounce",N]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[_]}],"scale-x":[{"scale-x":[_]}],"scale-y":[{"scale-y":[_]}],rotate:[{rotate:[P,N]}],"translate-x":[{"translate-x":[x]}],"translate-y":[{"translate-y":[x]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",N]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":R()}],"scroll-mx":[{"scroll-mx":R()}],"scroll-my":[{"scroll-my":R()}],"scroll-ms":[{"scroll-ms":R()}],"scroll-me":[{"scroll-me":R()}],"scroll-mt":[{"scroll-mt":R()}],"scroll-mr":[{"scroll-mr":R()}],"scroll-mb":[{"scroll-mb":R()}],"scroll-ml":[{"scroll-ml":R()}],"scroll-p":[{"scroll-p":R()}],"scroll-px":[{"scroll-px":R()}],"scroll-py":[{"scroll-py":R()}],"scroll-ps":[{"scroll-ps":R()}],"scroll-pe":[{"scroll-pe":R()}],"scroll-pt":[{"scroll-pt":R()}],"scroll-pr":[{"scroll-pr":R()}],"scroll-pb":[{"scroll-pb":R()}],"scroll-pl":[{"scroll-pl":R()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[C,I,q]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})}}]);
//# sourceMappingURL=51.js.map