(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{5521:e=>{"use strict";e.exports=require("node:async_hooks")},5356:e=>{"use strict";e.exports=require("node:buffer")},5661:(e,s,a)=>{"use strict";a.r(s),a.d(s,{ComponentMod:()=>S,default:()=>I});var t,r={};a.r(r),a.d(r,{ClientPageRoot:()=>p.Fy,ClientSegmentRoot:()=>p.pl,GlobalError:()=>m.default,HTTPAccessFallbackBoundary:()=>p.nQ,LayoutRouter:()=>p.C3,MetadataBoundary:()=>p.qB,OutletBoundary:()=>p.Cr,Postpone:()=>p.fK,RenderFromTemplateContext:()=>p.IY,ViewportBoundary:()=>p.PX,__next_app__:()=>u,actionAsyncStorage:()=>p.sc,collectSegmentData:()=>p.Uy,createMetadataComponents:()=>p.IB,createPrerenderParamsForClientSegment:()=>p.lu,createPrerenderSearchParamsForClientPage:()=>p.jO,createServerParamsForMetadata:()=>p.Kx,createServerParamsForServerSegment:()=>p.LV,createServerSearchParamsForMetadata:()=>p.mh,createServerSearchParamsForServerPage:()=>p.Vv,createTemporaryReferenceSet:()=>p.XI,decodeAction:()=>p.Jk,decodeFormState:()=>p.Am,decodeReply:()=>p.X$,pages:()=>x,patchFetch:()=>p.V5,preconnect:()=>p.kZ,preloadFont:()=>p.PY,preloadStyle:()=>p.vI,prerender:()=>p.CR,renderToReadableStream:()=>p.WK,routeModule:()=>f,serverHooks:()=>p.ge,taintObjectReference:()=>p.N2,tree:()=>h,workAsyncStorage:()=>p.J_,workUnitAsyncStorage:()=>p.FP}),a(9569);var i=a(981),n=a(9040),l=a(7598),c=a(5717),o=a(3307),d=a(6292),m=a(3094),p=a(6953);let h=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,7548)),"F:\\CODE\\Project\\Mail\\moemail_source\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4452))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,189)),"F:\\CODE\\Project\\Mail\\moemail_source\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,4962)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.bind(a,1648)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.bind(a,6408)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,4452))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],x=["F:\\CODE\\Project\\Mail\\moemail_source\\app\\profile\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},f=new o.AppPageRouteModule({definition:{kind:d.A.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}});var g=a(5711),j=a(4194),y=a(7285);globalThis.__nextCacheHandlers||(globalThis.__nextCacheHandlers={});let v=e=>e?JSON.parse(e):void 0,N=self.__BUILD_MANIFEST,b=v(self.__REACT_LOADABLE_MANIFEST),w=null==(t=self.__RSC_MANIFEST)?void 0:t["/profile/page"],P=v(self.__RSC_SERVER_MANIFEST),k=v(self.__NEXT_FONT_MANIFEST),A=v(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];w&&P&&(0,j.fQ)({page:"/profile/page",clientReferenceManifest:w,serverActionsManifest:P,serverModuleMap:(0,y.e)({serverActionsManifest:P})});let C=(0,n.R)({pagesType:g.g.APP,dev:!1,page:"/profile/page",appMod:null,pageMod:r,errorMod:null,error500Mod:null,Document:null,buildManifest:N,renderToHTML:c.W,reactLoadableManifest:b,clientReferenceManifest:w,serverActionsManifest:P,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\Mail\\moemail_source",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\Mail\\moemail_source"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\Mail\\moemail_source\\next.config.ts",configFileName:"next.config.ts"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:k,incrementalCacheHandler:null,interceptionRouteRewrites:A}),S=r;function I(e){return(0,i.O)({...e,IncrementalCache:l.N,handler:C})}},3053:(e,s,a)=>{Promise.resolve().then(a.bind(a,2483)),Promise.resolve().then(a.bind(a,1632)),Promise.resolve().then(a.bind(a,38)),Promise.resolve().then(a.bind(a,9134))},7021:(e,s,a)=>{Promise.resolve().then(a.bind(a,2396)),Promise.resolve().then(a.bind(a,4432)),Promise.resolve().then(a.bind(a,5354)),Promise.resolve().then(a.bind(a,4794))},4432:(e,s,a)=>{"use strict";a.d(s,{ProfileCard:()=>ec});var t=a(9796),r=a(1813),i=a(1511),n=a(4123),l=a(297);let c=(0,l.A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]),o=(0,l.A)("Gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]]),d=(0,l.A)("Sword",[["polyline",{points:"14.5 17.5 3 6 3 3 6 3 17.5 14.5",key:"1hfsw2"}],["line",{x1:"13",x2:"19",y1:"19",y2:"13",key:"1vrmhu"}],["line",{x1:"16",x2:"20",y1:"16",y2:"20",key:"1bron3"}],["line",{x1:"19",x2:"21",y1:"21",y2:"19",key:"13pww6"}]]);var m=a(4145),p=a(4040);let h=(0,l.A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var x=a(5139),u=a(6079),f=a(2992),g=a(5935),j=a(1673),y=a(173),v=a(3359),N=a(6442),b=a(3919),w=a(3263),P=a(5453),k=a(7608),A="Switch",[C,S]=(0,N.A)(A),[I,E]=C(A),_=f.forwardRef((e,s)=>{let{__scopeSwitch:a,name:r,checked:i,defaultChecked:n,required:l,disabled:c,value:o="on",onCheckedChange:d,form:m,...p}=e,[h,x]=f.useState(null),u=(0,v.s)(s,e=>x(e)),g=f.useRef(!1),j=!h||m||!!h.closest("form"),[N,w]=(0,b.i)({prop:i,defaultProp:n??!1,onChange:d,caller:A});return(0,t.jsxs)(I,{scope:a,checked:N,disabled:c,children:[(0,t.jsx)(k.sG.button,{type:"button",role:"switch","aria-checked":N,"aria-required":l,"data-state":M(N),"data-disabled":c?"":void 0,disabled:c,value:o,...p,ref:u,onClick:(0,y.m)(e.onClick,e=>{w(e=>!e),j&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),j&&(0,t.jsx)(O,{control:h,bubbles:!g.current,name:r,value:o,checked:N,required:l,disabled:c,form:m,style:{transform:"translateX(-100%)"}})]})});_.displayName=A;var T="SwitchThumb",R=f.forwardRef((e,s)=>{let{__scopeSwitch:a,...r}=e,i=E(T,a);return(0,t.jsx)(k.sG.span,{"data-state":M(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:s})});R.displayName=T;var O=f.forwardRef(({__scopeSwitch:e,control:s,checked:a,bubbles:r=!0,...i},n)=>{let l=f.useRef(null),c=(0,v.s)(l,n),o=(0,w.Z)(a),d=(0,P.X)(s);return f.useEffect(()=>{let e=l.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==a&&s){let t=new Event("click",{bubbles:r});s.call(e,a),e.dispatchEvent(t)}},[o,a,r]),(0,t.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...i,tabIndex:-1,ref:c,style:{...i.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function M(e){return e?"checked":"unchecked"}O.displayName="SwitchBubbleInput";var K=a(2304);let $=f.forwardRef(({className:e,...s},a)=>(0,t.jsx)(_,{className:(0,K.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:a,children:(0,t.jsx)(R,{className:(0,K.cn)("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})}));$.displayName=_.displayName;var F=a(120),L=a(4881);let H=(0,l.A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),U=(0,l.A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var D=a(6457),q=a(4270);function z(){let[e,s]=(0,f.useState)(!1),[a,r]=(0,f.useState)(""),[n,l]=(0,f.useState)(!1),[c,o]=(0,f.useState)(!1),[d,m]=(0,f.useState)(!1),[p,h]=(0,f.useState)(!0),{toast:x}=(0,F.dj)();if(p)return(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)(L.A,{className:"w-6 h-6 text-primary animate-spin"})}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"加载中..."})})]});let u=async s=>{if(s.preventDefault(),a){l(!0);try{if(!(await fetch("/api/webhook",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:a,enabled:e})})).ok)throw Error("Failed to save");x({title:"保存成功",description:"Webhook 配置已更新"})}catch(e){x({title:"保存失败",description:"请稍后重试",variant:"destructive"})}finally{l(!1)}}},y=async()=>{if(a){o(!0);try{if(!(await fetch("/api/webhook/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:a})})).ok)throw Error("测试失败");x({title:"测试成功",description:"Webhook 调用成功,请检查目标服务器是否收到请求"})}catch(e){x({title:"测试失败",description:"请检查 URL 是否正确且可访问",variant:"destructive"})}finally{o(!1)}}};return(0,t.jsxs)("form",{onSubmit:u,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-0.5",children:[(0,t.jsx)(j.J,{children:"启用 Webhook"}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"当收到新邮件时通知指定的 URL"})]}),(0,t.jsx)($,{checked:e,onCheckedChange:s})]}),e&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(j.J,{htmlFor:"webhook-url",children:"Webhook URL"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(g.p,{id:"webhook-url",placeholder:"https://example.com/webhook",value:a,onChange:e=>r(e.target.value),type:"url",required:!0}),(0,t.jsx)(i.$,{type:"submit",disabled:n,className:"flex-shrink-0",children:n?(0,t.jsx)(L.A,{className:"w-4 h-4 animate-spin"}):"保存"}),(0,t.jsx)(q.Bc,{children:(0,t.jsxs)(q.m_,{children:[(0,t.jsx)(q.k$,{asChild:!0,children:(0,t.jsx)(i.$,{type:"button",variant:"outline",onClick:y,disabled:c||!a,children:c?(0,t.jsx)(L.A,{className:"w-4 h-4 animate-spin"}):(0,t.jsx)(H,{className:"w-4 h-4"})})}),(0,t.jsx)(q.ZI,{children:(0,t.jsx)("p",{children:"发送测试消息到此 Webhook"})})]})})]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"我们会向此 URL 发送 POST 请求,包含新邮件的相关信息"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("button",{type:"button",className:"flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors",onClick:()=>m(!d),children:[d?(0,t.jsx)(U,{className:"w-4 h-4"}):(0,t.jsx)(D.A,{className:"w-4 h-4"}),"查看数据格式说明"]}),d&&(0,t.jsxs)("div",{className:"rounded-md bg-muted p-4 text-sm space-y-3",children:[(0,t.jsx)("p",{children:"当收到新邮件时，我们会向配置的 URL 发送 POST 请求，请求头包含:"}),(0,t.jsxs)("pre",{className:"bg-background p-2 rounded text-xs",children:["Content-Type: application/json","\n","X-Webhook-Event: new_message"]}),(0,t.jsx)("p",{children:"请求体示例:"}),(0,t.jsx)("pre",{className:"bg-background p-2 rounded text-xs overflow-auto",children:`{
  "emailId": "email-uuid",
  "messageId": "message-uuid",
  "fromAddress": "<EMAIL>",
  "subject": "邮件主题",
  "content": "邮件文本内容",
  "html": "邮件HTML内容",
  "receivedAt": "2024-01-01T12:00:00.000Z",
  "toAddress": "your-email@${window.location.host}"
}`})]})]})]})]})}var Y=a(9513),X=a(1043);let B={[Y.gg.DUKE]:o,[Y.gg.KNIGHT]:d,[Y.gg.CIVILIAN]:m.A},G={[Y.gg.DUKE]:"公爵",[Y.gg.KNIGHT]:"骑士",[Y.gg.CIVILIAN]:"平民"};function V(){let[e,s]=(0,f.useState)(""),[a,r]=(0,f.useState)(!1),[n,l]=(0,f.useState)(Y.gg.KNIGHT),{toast:c}=(0,F.dj)(),p=async()=>{if(e){r(!0);try{let a=await fetch("/api/roles/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({searchText:e})}),t=await a.json();if(!a.ok)throw Error(t.error||"未知错误");if(!t.user){c({title:"未找到用户",description:"请确认用户名或邮箱地址是否正确",variant:"destructive"});return}if(t.user.role===n){c({title:`用户已是${G[n]}`,description:"无需重复设置"});return}let r=await fetch("/api/roles/promote",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t.user.id,roleName:n})});if(!r.ok){let e=await r.json();throw Error(e.error||"设置失败")}c({title:"设置成功",description:`已将用户 ${t.user.username||t.user.email} 设为${G[n]}`}),s("")}catch(e){c({title:"设置失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"})}finally{r(!1)}}},h=B[n];return(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,t.jsx)(h,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"角色管理"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(g.p,{value:e,onChange:e=>s(e.target.value),placeholder:"输入用户名或邮箱"})}),(0,t.jsxs)(X.l6,{value:n,onValueChange:e=>l(e),children:[(0,t.jsx)(X.bq,{className:"w-32",children:(0,t.jsx)(X.yv,{})}),(0,t.jsxs)(X.gC,{children:[(0,t.jsx)(X.eb,{value:Y.gg.DUKE,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o,{className:"w-4 h-4"}),"公爵"]})}),(0,t.jsx)(X.eb,{value:Y.gg.KNIGHT,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d,{className:"w-4 h-4"}),"骑士"]})}),(0,t.jsx)(X.eb,{value:Y.gg.CIVILIAN,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"w-4 h-4"}),"平民"]})})]})]})]}),(0,t.jsx)(i.$,{onClick:p,disabled:a||!e.trim(),className:"w-full",children:a?(0,t.jsx)(L.A,{className:"w-4 h-4 animate-spin"}):`设为${G[n]}`})]})]})}function J(){let{data:e}=(0,n.wV)(),s=e?.user?.roles;return{checkPermission:e=>!!s&&(0,Y._m)(s.map(e=>e.name),e),hasRole:e=>!!s&&s.some(s=>s.name===e),roles:s}}var W=a(5746);function Z(){let[e,s]=(0,f.useState)(""),[a,r]=(0,f.useState)(""),[n,l]=(0,f.useState)(""),[c,o]=(0,f.useState)(W.q.MAX_ACTIVE_EMAILS.toString()),[d,m]=(0,f.useState)(!1),{toast:p}=(0,F.dj)(),x=async()=>{m(!0);try{if(!(await fetch("/api/config",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({defaultRole:e,emailDomains:a,adminContact:n,maxEmails:c||W.q.MAX_ACTIVE_EMAILS.toString()})})).ok)throw Error("保存失败");p({title:"保存成功",description:"网站设置已更新"})}catch(e){p({title:"保存失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"})}finally{m(!1)}};return(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,t.jsx)(h,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"网站设置"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-sm",children:"新用户默认角色:"}),(0,t.jsxs)(X.l6,{value:e,onValueChange:s,children:[(0,t.jsx)(X.bq,{className:"w-32",children:(0,t.jsx)(X.yv,{})}),(0,t.jsxs)(X.gC,{children:[(0,t.jsx)(X.eb,{value:Y.gg.DUKE,children:"公爵"}),(0,t.jsx)(X.eb,{value:Y.gg.KNIGHT,children:"骑士"}),(0,t.jsx)(X.eb,{value:Y.gg.CIVILIAN,children:"平民"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-sm",children:"邮箱域名:"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(g.p,{value:a,onChange:e=>r(e.target.value),placeholder:"多个域名用逗号分隔，如: moemail.app,bitibiti.com"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-sm",children:"管理员联系方式:"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(g.p,{value:n,onChange:e=>l(e.target.value),placeholder:"如: 微信号、邮箱等"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-sm",children:"最大邮箱数量:"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(g.p,{type:"number",min:"1",max:"100",value:c,onChange:e=>o(e.target.value),placeholder:`默认为 ${W.q.MAX_ACTIVE_EMAILS}`})})]}),(0,t.jsx)(i.$,{onClick:x,disabled:d,className:"w-full",children:"保存"})]})]})}let Q=(0,l.A)("Key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);var ee=a(4383),es=a(758),ea=a(3390),et=a(9851),er=a(4399),ei=a(5984);function en(){let[e,s]=(0,f.useState)([]),[a,r]=(0,f.useState)(!1),[n,l]=(0,f.useState)(!1),[c,o]=(0,f.useState)(""),[d,m]=(0,f.useState)(null),{toast:p}=(0,F.dj)(),{copyToClipboard:h}=(0,er.T)(),[x,u]=(0,f.useState)(!1),[y,v]=(0,f.useState)(!0),{checkPermission:N}=J(),b=N(Y.Jj.MANAGE_API_KEY),w=async()=>{try{let e=await fetch("/api/api-keys");if(!e.ok)throw Error("获取 API Keys 失败");let a=await e.json();s(a.apiKeys)}catch(e){console.error(e),p({title:"获取失败",description:"获取 API Keys 列表失败",variant:"destructive"})}finally{v(!1)}},{config:P}=(0,ei.U)(),k=async()=>{if(c.trim()){r(!0);try{let e=await fetch("/api/api-keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:c})});if(!e.ok)throw Error("创建 API Key 失败");let s=await e.json();m(s.key),w()}catch(e){p({title:"创建失败",description:e instanceof Error?e.message:"请稍后重试",variant:"destructive"}),l(!1)}finally{r(!1)}}},A=async(e,a)=>{try{if(!(await fetch(`/api/api-keys/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({enabled:a})})).ok)throw Error("更新失败");s(s=>s.map(s=>s.id===e?{...s,enabled:a}:s))}catch(e){console.error(e),p({title:"更新失败",description:"更新 API Key 状态失败",variant:"destructive"})}},C=async e=>{try{if(!(await fetch(`/api/api-keys/${e}`,{method:"DELETE"})).ok)throw Error("删除失败");s(s=>s.filter(s=>s.id!==e)),p({title:"删除成功",description:"API Key 已删除"})}catch(e){console.error(e),p({title:"删除失败",description:"删除 API Key 失败",variant:"destructive"})}};return(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(Q,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"API Keys"})]}),b&&(0,t.jsxs)(et.lG,{open:n,onOpenChange:l,children:[(0,t.jsx)(et.zM,{asChild:!0,children:(0,t.jsxs)(i.$,{className:"gap-2",onClick:()=>l(!0),children:[(0,t.jsx)(ee.A,{className:"w-4 h-4"}),"创建 API Key"]})}),(0,t.jsxs)(et.Cf,{children:[(0,t.jsxs)(et.c7,{children:[(0,t.jsx)(et.L3,{children:d?"API Key 创建成功":"创建新的 API Key"}),d&&(0,t.jsx)(et.rr,{className:"text-destructive",children:"请立即保存此密钥，它只会显示一次且无法恢复"})]}),d?(0,t.jsx)("div",{className:"space-y-4 py-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(j.J,{children:"API Key"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(g.p,{value:d,readOnly:!0,className:"font-mono text-sm"}),(0,t.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>h(d),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]})]})}):(0,t.jsx)("div",{className:"space-y-4 py-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(j.J,{children:"名称"}),(0,t.jsx)(g.p,{value:c,onChange:e=>o(e.target.value),placeholder:"为你的 API Key 起个名字"})]})}),(0,t.jsxs)(et.Es,{children:[(0,t.jsx)(et.HM,{asChild:!0,children:(0,t.jsx)(i.$,{variant:"outline",onClick:()=>{l(!1),o(""),m(null)},disabled:a,children:d?"完成":"取消"})}),!d&&(0,t.jsx)(i.$,{onClick:k,disabled:a||!c.trim(),children:a?(0,t.jsx)(L.A,{className:"w-4 h-4 animate-spin"}):"创建"})]})]})]})]}),b?(0,t.jsx)("div",{className:"space-y-4",children:y?(0,t.jsxs)("div",{className:"text-center py-8 space-y-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)(L.A,{className:"w-6 h-6 text-primary animate-spin"})}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"加载中..."})})]}):0===e.length?(0,t.jsxs)("div",{className:"text-center py-8 space-y-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto",children:(0,t.jsx)(Q,{className:"w-6 h-6 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"没有 API Keys"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:'点击上方的创建 "API Key" 按钮来创建你的第一个 API Key'})]})]}):(0,t.jsxs)(t.Fragment,{children:[e.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-lg border bg-card",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-medium",children:e.name}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["创建于 ",new Date(e.createdAt).toLocaleString()]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)($,{checked:e.enabled,onCheckedChange:s=>A(e.id,s)}),(0,t.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>C(e.id),children:(0,t.jsx)(ea.A,{className:"w-4 h-4"})})]})]},e.id)),(0,t.jsxs)("div",{className:"mt-8 space-y-4",children:[(0,t.jsxs)("button",{type:"button",className:"flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors",onClick:()=>u(!x),children:[x?(0,t.jsx)(U,{className:"w-4 h-4"}):(0,t.jsx)(D.A,{className:"w-4 h-4"}),"查看使用文档"]}),x&&(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"获取系统配置"}),(0,t.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>h(`curl ${window.location.protocol}//${window.location.host}/api/config \\
  -H "X-API-Key: YOUR_API_KEY"`),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:`curl ${window.location.protocol}//${window.location.host}/api/config \\
  -H "X-API-Key: YOUR_API_KEY"`})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"生成临时邮箱"}),(0,t.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>h(`curl -X POST ${window.location.protocol}//${window.location.host}/api/emails/generate \\
  -H "X-API-Key: YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "name": "test",
    "expiryTime": 3600000,
    "domain": "moemail.app"
  }'`),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:`curl -X POST ${window.location.protocol}//${window.location.host}/api/emails/generate \\
  -H "X-API-Key: YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "name": "test",
    "expiryTime": 3600000,
    "domain": "moemail.app"
  }'`})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"获取邮箱列表"}),(0,t.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>h(`curl ${window.location.protocol}//${window.location.host}/api/emails?cursor=CURSOR \\
  -H "X-API-Key: YOUR_API_KEY"`),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:`curl ${window.location.protocol}//${window.location.host}/api/emails?cursor=CURSOR \\
  -H "X-API-Key: YOUR_API_KEY"`})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"获取邮件列表"}),(0,t.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>h(`curl ${window.location.protocol}//${window.location.host}/api/emails/{emailId}?cursor=CURSOR \\
  -H "X-API-Key: YOUR_API_KEY"`),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:`curl ${window.location.protocol}//${window.location.host}/api/emails/{emailId}?cursor=CURSOR \\
  -H "X-API-Key: YOUR_API_KEY"`})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:"获取单封邮件"}),(0,t.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>h(`curl ${window.location.protocol}//${window.location.host}/api/emails/{emailId}/{messageId} \\
  -H "X-API-Key: YOUR_API_KEY"`),children:(0,t.jsx)(es.A,{className:"w-4 h-4"})})]}),(0,t.jsx)("pre",{className:"text-xs bg-muted/50 rounded-lg p-4 overflow-x-auto",children:`curl ${window.location.protocol}//${window.location.host}/api/emails/{emailId}/{messageId} \\
  -H "X-API-Key: YOUR_API_KEY"`})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground mt-4",children:[(0,t.jsx)("p",{children:"注意："}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 mt-2",children:[(0,t.jsx)("li",{children:"请将 YOUR_API_KEY 替换为你的实际 API Key"}),(0,t.jsx)("li",{children:"/api/config 接口可获取系统配置，包括可用的邮箱域名列表"}),(0,t.jsx)("li",{children:"emailId 是邮箱的唯一标识符"}),(0,t.jsx)("li",{children:"messageId 是邮件的唯一标识符"}),(0,t.jsx)("li",{children:"expiryTime 是邮箱的有效期（毫秒），可选值：3600000（1小时）、86400000（1天）、604800000（7天）、0（永久）"}),(0,t.jsx)("li",{children:"domain 是邮箱域名，可通过 /api/config 接口获取可用域名列表"}),(0,t.jsx)("li",{children:"cursor 用于分页，从上一次请求的响应中获取 nextCursor"}),(0,t.jsx)("li",{children:"所有请求都需要包含 X-API-Key 请求头"})]})]})]})]})]})}):(0,t.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,t.jsx)("p",{children:"需要公爵或更高权限才能管理 API Key"}),(0,t.jsx)("p",{className:"mt-2",children:"请联系网站管理员升级您的角色"}),P?.adminContact&&(0,t.jsxs)("p",{className:"mt-2",children:["管理员联系方式：",P.adminContact]})]})]})}let el={emperor:{name:"皇帝",icon:c},duke:{name:"公爵",icon:o},knight:{name:"骑士",icon:d},civilian:{name:"平民",icon:m.A}};function ec({user:e}){let s=(0,u.rd)(),{checkPermission:a}=J(),l=a(Y.Jj.MANAGE_WEBHOOK),c=a(Y.Jj.PROMOTE_USER),o=a(Y.Jj.MANAGE_CONFIG);return(0,t.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,t.jsx)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6",children:(0,t.jsxs)("div",{className:"flex items-center gap-6",children:[(0,t.jsx)("div",{className:"relative",children:e.image&&(0,t.jsx)(r.A,{src:e.image,alt:e.name||"用户头像",width:80,height:80,className:"rounded-full ring-2 ring-primary/20"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h2",{className:"text-xl font-bold truncate",children:e.name}),e.email&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-xs text-primary bg-primary/10 px-2 py-0.5 rounded-full flex-shrink-0",children:[(0,t.jsx)(p.A,{className:"w-3 h-3"}),"已关联"]})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground truncate mt-1",children:e.email?e.email:`用户名: ${e.username}`}),e.roles&&(0,t.jsx)("div",{className:"flex gap-2 mt-2",children:e.roles.map(({name:e})=>{let s=el[e],a=s.icon;return(0,t.jsxs)("div",{className:"flex items-center gap-1 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded",title:s.name,children:[(0,t.jsx)(a,{className:"w-3 h-3"}),s.name]},e)})})]})]})}),l&&(0,t.jsxs)("div",{className:"bg-background rounded-lg border-2 border-primary/20 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,t.jsx)(h,{className:"w-5 h-5 text-primary"}),(0,t.jsx)("h2",{className:"text-lg font-semibold",children:"Webhook 配置"})]}),(0,t.jsx)(z,{})]}),o&&(0,t.jsx)(Z,{}),c&&(0,t.jsx)(V,{}),l&&(0,t.jsx)(en,{}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 px-1",children:[(0,t.jsxs)(i.$,{onClick:()=>s.push("/moe"),className:"gap-2 flex-1",children:[(0,t.jsx)(x.A,{className:"w-4 h-4"}),"返回邮箱"]}),(0,t.jsx)(i.$,{variant:"outline",onClick:()=>(0,n.CI)({callbackUrl:"/"}),className:"flex-1",children:"退出登录"})]})]})}},4145:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(297).A)("UserRound",[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]])},1632:(e,s,a)=>{"use strict";a.d(s,{ProfileCard:()=>t});let t=(0,a(6853).YR)(function(){throw Error("Attempted to call ProfileCard() from the server but ProfileCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\profile\\profile-card.tsx","ProfileCard")},7548:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o,runtime:()=>c});var t=a(861),r=a(1024),i=a(1632),n=a(1639),l=a(8149);let c="edge";async function o(){let e=await (0,n.j2)();return e?.user||(0,l.V2)("/"),(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 lg:px-8 max-w-[1600px]",children:[(0,t.jsx)(r.Y,{}),(0,t.jsx)("main",{className:"pt-20 pb-5",children:(0,t.jsx)(i.ProfileCard,{user:e.user})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[810,702,267,658,51,886,293,363,559,473],()=>s(5661));var a=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/profile/page"]=a}]);
//# sourceMappingURL=page.js.map