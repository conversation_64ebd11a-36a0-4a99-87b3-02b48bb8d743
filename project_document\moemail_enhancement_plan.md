# Context
Project_Name/ID: moemail-enhancement-20250607
Task_Filename: moemail_enhancement_plan.md 
Created_At: 2025-06-07 16:24:39 +08:00
Creator: AI (<PERSON><PERSON> drafted, <PERSON><PERSON> organized)
Associated_Protocol: RIPER-5 + Multi-Dimensional Thinking + Agent Execution Protocol (Refined v3.9)
Project_Workspace_Path: `project_document/` 

# 0. Team Collaboration Log & Key Decision Points (Located in /project_document/team_collaboration_log.md or within this file, D<PERSON> maintains, <PERSON> chairs meetings)
---
**Meeting Record**
* **Date & Time:** 2025-06-07 16:24:39 +08:00
* **Meeting Type:** Task Kickoff/Requirements Clarification (Simulated)
* **Chair:** PM
* **Recorder:** DW
* **Attendees:** PM, PDM, AR, LD, TE, SE
* **Agenda Overview:** 
    1.  Initial review of the user request for `moemail` enhancement.
    2.  Understand the scope of each requested feature.
    3.  Define the initial steps for the RESEARCH phase.
    4.  Establish the project documentation structure.
* **Discussion Points:**
    *   **PM:** "Welcome, team. We have a significant project ahead to enhance the `moemail` application. The user has provided a detailed list of features. Our first step is to thoroughly understand the existing codebase and the feasibility of these new features."
    *   **PDM:** "The requested features like a credential system, OTP extraction, and RBAC are substantial user value-adds. We need to analyze the primary `moemail` project and the reference `cloudflare_temp_email` to understand the best implementation path that aligns with user needs."
    *   **AR:** "My initial thought is to analyze the architecture of both projects. `moemail` is based on Next.js and Cloudflare. We need to see how the credential system from `cloudflare_temp_email` can be integrated without compromising the existing architecture. I'll start by mapping out the current system components."
    *   **LD:** "Agreed. I'll clone the `moemail` repository and get it running locally. This will help us understand the code quality, dependencies, and identify areas for refactoring before we add new features. I'm particularly interested in the API structure and data models."
    *   **TE:** "From a testing perspective, we need to plan for unit, integration, and end-to-end tests for each new feature. The verification code extraction will be tricky to test; we'll need a variety of email templates. I'll also start thinking about how to use Playwright for E2E testing of the new UI components."
    *   **SE:** "The credential system, attachment handling, and account management changes all have significant security implications. I'll focus on threat modeling for these areas, ensuring we follow best practices for secure coding, data storage, and access control."
* **Action Items/Decisions:**
    1.  **All:** Review the user's request and the provided GitHub links.
    2.  **LD:** Clone, install, and run the `moemail` project locally.
    3.  **AR:** Analyze the architecture of `moemail` and `cloudflare_temp_email`.
    4.  **DW:** Create the initial project documentation file (`moemail_enhancement_plan.md`).
    5.  **PM:** Schedule the next sync meeting after initial research is complete.
* **DW Confirmation:** Minutes are complete and compliant with standards.
---

# Task Description
Based on the [moemail project](https://github.com/beilunyang/moemail), implement a series of new features and optimizations, including a temporary email credential system (referencing [cloudflare_temp_email](https://github.com/dreamhunter2333/cloudflare_temp_email)), verification code extraction, RBAC, attachment handling, improved account management, frontend enhancements, and a logging system. The development must follow a structured process of design, testing, and review, culminating in a cloud deployment with detailed documentation.

# Project Overview (Populated in RESEARCH or PLAN phase)
[This section will be populated after our initial research.]

---
*The following sections are maintained by AI during protocol execution. DW is responsible for overall document quality. All referenced paths are relative to `project_document/` unless specified. All documents should include an update log section where applicable. All timestamps are obtained via `mcp-server-time`.*
---

# 1. Analysis (RESEARCH Mode Population)
*   **Code/System Investigation (Updated: 2025-06-07 16:26:14 +08:00):**
    *   **`moemail` Project (`moemail_source/`):**
        *   **Architecture:** A modern web application built with Next.js (App Router) and deployed on Cloudflare Pages.
        *   **Technology Stack:** Next.js, React, TypeScript, Tailwind CSS.
        *   **Backend & Data:** It uses Cloudflare Workers (`workers/`) for handling email receiving logic and Drizzle ORM (`drizzle/`) to interact with a Cloudflare D1 database.
        *   **Authentication:** User authentication is handled via GitHub OAuth through NextAuth.js.
        *   **Key Observation:** The project currently lacks a mechanism to grant access to a temporary mailbox via a credential. Access appears to be tied to the authenticated user session that created the mailbox.

    *   **`cloudflare_temp_email` Project (`reference_source/`):**
        *   **Architecture:** A more complex, multi-component system. It includes a distinct frontend (`frontend/`), a Cloudflare Worker (`worker/`), and notably, a self-hosted `smtp_proxy_server/`. It also uses WebAssembly (`mail-parser-wasm/`) for performance.
        *   **Credential System Analysis:** The key finding is the distinction between two credential types:
            1.  **User Authentication:** It uses **Passkeys (WebAuthn)** for passwordless user account login. This is managed under the `/user_api/` routes and results in a user-specific JWT (`x-user-token`).
            2.  **Mailbox Access Credential:** This is the feature relevant to our task. It's a separate **JWT-based "Address Credential"**. This token is generated for a specific temporary email address. The Cloudflare Worker (`worker/src/worker.ts`) secures the `/api/*` routes with this JWT. If an invalid token is provided, it correctly returns a `401 Unauthorized` error.
        *   **Conclusion for our project:** The "Address Credential" from `cloudflare_temp_email` is the model to follow. It provides a secure, stateless way to grant access to a specific mailbox, which is precisely what's needed for the "temporary email凭证" feature.

*   **Technical Constraints & Challenges:**
    *   **Integration:** The primary challenge will be to seamlessly integrate the new JWT-based "Address Credential" system into the existing `moemail` Next.js architecture without disrupting the current GitHub OAuth flow.
    *   **Security:** Generating, storing, and managing JWTs requires careful security considerations to prevent token hijacking or replay attacks. The `JWT_SECRET` must be managed securely.
    *   **Verification Code Extraction:** This is a non-trivial algorithm to develop. It will require research into common verification code patterns in emails and robust string parsing.
    *   **RBAC & Attachments:** These features will require significant database schema changes (in Drizzle) and new API endpoints. Secure handling of file uploads and storage (for attachments) is critical.

*   **Implicit Assumptions:**
    *   The user wants the "Address Credential" to be distinct from the user's main account authentication.
    *   The existing Cloudflare D1 database can be extended to support the new features.
    *   The core email processing logic in the `moemail` worker can be adapted for new features like attachment handling and forwarding.

*   **Early Edge Case Considerations:**
    *   What happens if a credential (JWT) expires? The user should be clearly informed.
    *   How are credentials managed for permanent mailboxes vs. those with a TTL?
    *   For code extraction: What if an email contains multiple numbers that look like codes? What if the code is in an image (out of scope for now)?

*   **Preliminary Risk Assessment:**
    *   **Medium:** Integrating a new credential system requires careful API design and security implementation.
    *   **Medium:** The verification code extraction algorithm might have a high rate of false positives/negatives initially and will require iterative refinement.
    *   **Low:** The other features (RBAC, UI improvements, logging) are relatively standard and pose a lower implementation risk.

*   **Knowledge Gaps:**
    *   The exact implementation details of the verification code extraction algorithm need to be researched.
    *   The best strategy for storing and securing attachments using Cloudflare R2 within the `moemail` architecture needs to be determined.

*   **DW Confirmation:** This section is complete, clear, synced, and meets documentation standards.

# 2. Proposed Solutions (INNOVATE Mode Population)
*   **Meeting Record (Solution Brainstorming - 2025-06-07 16:27:59 +08:00)**
    *   **Attendees:** PM, PDM, AR, LD, TE
    *   **Topic:** Temporary Email Credential System

*   **Solution A: JWT-based Bearer Token (Recommended & Approved)**
    *   **Core Idea & Mechanism:** When a user creates a temporary email, the server generates a signed JWT. This JWT contains the `email_address` and an `exp` (expiration) claim. This token is the "Address Credential". To access the mailbox, the user presents this JWT as a Bearer Token in the `Authorization` header of API requests. A middleware on the server validates the token and grants access.
    *   **Architectural Design (AR led):** This involves creating a new Next.js middleware to protect mailbox-specific API routes. The solution is stateless and requires no database schema changes. It aligns with KISS and High Cohesion/Low Coupling principles. A detailed architectural document will be created in the PLAN phase.
    *   **Multi-Role Evaluation:**
        *   **Pros:** Stateless, secure, fast (no DB lookup for validation), standard practice.
        *   **Cons:** Inability to revoke individual tokens before expiration.
        *   **Complexity/Cost:** Low.

*   **Solution B: Opaque Token + Database Lookup**
    *   **Core Idea & Mechanism:** The server generates a random string, stores it hashed in a new database table, and links it to the email address. Validation requires a database lookup.
    *   **Multi-Role Evaluation:**
        *   **Pros:** Tokens can be individually revoked.
        *   **Cons:** Slower (DB lookup per request), stateful, more complex logic for token management.
        *   **Complexity/Cost:** Medium.

*   **Solution C: Signed URL**
    *   **Core Idea & Mechanism:** The server generates a URL with a cryptographic signature in the query parameters that grants access.
    *   **Multi-Role Evaluation:**
        *   **Pros:** Simple for the user, stateless.
        *   **Cons:** Long/ugly URLs, less secure as tokens can be logged in server/browser history.
        *   **Complexity/Cost:** Low to Medium.

*   **Solution Comparison & Decision Process:**
    *   The team unanimously chose **Solution A**. It provides the best balance of security, performance, and implementation simplicity, fitting perfectly with the project's existing serverless architecture. It avoids the database overhead of Solution B and the security concerns of Solution C. The approach is validated by its successful implementation in the reference project.

*   **Final Preferred Solution:** **Solution A: JWT-based Bearer Token**

*   **DW Confirmation:** This section is complete, the decision process is traceable, synced, and meets documentation standards.

# 3. Implementation Plan (PLAN Mode Generation - Checklist Format)
*   **Plan Generated:** 2025-06-07 16:28:56 +08:00
*   **Plan Revised:** 2025-06-07 16:34:42 +08:00 (Based on user feedback)
*   **Architectural Reference:** `[PROJECT_ROOT]/project_document/architecture/credential_system_architecture_v1.md` (Version 1.1)

This plan details the implementation of the **Temporary Email Credential System**, incorporating user feedback for enhanced security and robustness.

**Implementation Checklist:**

1.  `[P1-AR-001]` **Action:** **Environment Setup: Add New Secret**
    *   **Rationale:** To sign and verify the new Address Credentials (JWTs).
    *   **Inputs:** A securely generated random string.
    *   **Processing:** Add `JWT_ADDRESS_SECRET` to the `.env.example` file and instruct developers on setting it in their local `.env` file.
    *   **Outputs:** Updated `.env.example` file.
    *   **Acceptance Criteria:** The application correctly loads the `JWT_ADDRESS_SECRET` into its environment.
    *   **Test Points (TE):** No direct test. Verified by the successful execution of subsequent JWT-related tests.
    *   **Security Notes (SE):** The secret must be high-entropy and must NOT be committed to version control.

2.  `[P1-DB-001]` **Action:** **Database Schema: Create Revocation Table** (New Task)
    *   **Rationale:** To support credential revocation as per architecture v1.1.
    *   **Processing:** Create a new Drizzle schema file for the `revoked_credentials` table with columns `jti` (primary key) and `expires_at`. Generate and apply the migration.
    *   **Acceptance Criteria:** The `revoked_credentials` table exists in the D1 database.
    *   **Test Points (TE):** `[P1-TE-013]` Verify via migration script success.

3.  `[P1-LD-001]` **Action:** **Implement Credential Generation Logic**
    *   **Rationale:** Create a reusable function to generate the Address Credential JWT.
    *   **Inputs:** `emailAddress` (string), `expiresIn` (number, in seconds).
    *   **Processing:** Create a utility function `generateAddressCredential(emailAddress, expiresIn)` that takes the inputs and generates a signed JWT according to the structure defined in the architecture document. Use the `jose` library for JWT operations.
    *   **Outputs:** A JWT string.
    *   **Acceptance Criteria:** The function returns a valid, correctly structured JWT string.
    *   **Test Points (TE):**
        *   `[P1-TE-001]` Unit test: Verify that the generated JWT can be decoded and its payload (`sub`, `exp`, `iss`, `aud`) is correct.
        *   `[P1-TE-002]` Unit test: Verify that providing different `expiresIn` values results in the correct `exp` claim.
        *   `[P1-TE-003]` Unit test: Test edge case where `expiresIn` is 0 (for a permanent token, which should result in a very distant `exp` or no `exp` claim, TBD).

4.  `[P1-LD-002]` **Action:** **Modify Email Generation API**
    *   **Rationale:** To provide the credential to the user immediately upon creating a new email address.
    *   **Inputs:** A request to `POST /api/emails/generate`.
    *   **Processing:** After successfully creating the new email address in the database, call the `generateAddressCredential` function from `[P1-LD-001]` and add the resulting JWT to the API response.
    *   **Outputs:** A JSON response including the `credential` field.
    *   **Acceptance Criteria:** The API response for creating an email now contains a valid `credential` JWT string.
    *   **Test Points (TE):**
        *   `[P1-TE-004]` Integration test: Call the `/api/emails/generate` endpoint and assert that the response body contains a non-empty `credential` field. Decode the token to ensure its `sub` claim matches the newly created email address.

5.  `[P1-LD-003]` **Action:** **Implement New Credential API Endpoint**
    *   **Rationale:** To allow users to regenerate a credential for an address they own.
    *   **Inputs:** A request to `POST /api/emails/{emailAddress}/credentials`.
    *   **Processing:** Create the new API route. It should first authenticate the user via their primary session (NextAuth). Then, it should verify that the authenticated user owns the `{emailAddress}`. Finally, it should call `generateAddressCredential` and return the new credential.
    *   **Outputs:** A JSON response with the `credential` and `expiresAt` fields.
    *   **Acceptance Criteria:** The endpoint successfully returns a new credential for an owned email address and denies access for non-owned addresses.
    *   **Test Points (TE):**
        *   `[P1-TE-005]` Integration test: As an authenticated user, request a credential for an owned address and verify the response.
        *   `[P1-TE-006]` Integration test: As an authenticated user, attempt to request a credential for a non-owned address and verify a `403 Forbidden` or `404 Not Found` response.

6.  `[P1-LD-005]` **Action:** **Implement Credential Revocation API** (New Task)
    *   **Rationale:** To allow users to explicitly revoke an active credential.
    *   **Inputs:** Request to `DELETE /api/emails/{emailAddress}/credentials`. The credential to be revoked is passed in the `Authorization` header.
    *   **Processing:** Create a new API route. It must first authenticate the *request itself* using the provided credential (via the middleware). If valid, it extracts the `jti` and `exp` from the token and adds them to the `revoked_credentials` table.
    *   **Acceptance Criteria:** The endpoint successfully adds a `jti` to the revocation list.
    *   **Test Points (TE):** `[P1-TE-014]` Integration test: Call the endpoint with a valid credential and verify the `jti` appears in the database. Then, try to use the same credential again and verify it is rejected by the middleware.

7.  `[P1-LD-004]` **Action:** **Implement API Protection Middleware**
    *   **Rationale:** To secure the mailbox access endpoints using the new Address Credential.
    *   **Inputs:** An incoming request to a protected route (e.g., `GET /api/emails/{emailAddress}/messages`).
    *   **Processing:** Enhance the middleware logic. Before verifying the token signature, it must first query the `revoked_credentials` table to see if the token's `jti` is on the list. Also, update all error responses to follow the standardized JSON format defined in architecture v1.1.
    *   **Outputs:** Either passes the request to the next handler or returns a `401`/`403` error response.
    *   **Acceptance Criteria:** Protected routes are inaccessible without a valid JWT. A valid JWT for address `A` cannot access routes for address `B`.
    *   **Test Points (TE):**
        *   `[P1-TE-015]` (New) Integration test: Revoke a valid token, then try to access a protected route with it; expect a 401 with the standardized error format.
        *   All previous middleware tests (`P1-TE-007` to `P1-TE-011`) must be updated to assert the new standardized error JSON format on failure.

8.  `[P1-LD-006]` **Action:** **Implement Audit Logging** (New Task)
    *   **Rationale:** To create a security audit trail as required by architecture v1.1.
    *   **Processing:** Create a centralized logging service. Integrate this service into the credential generation, validation (middleware), and revocation flows to log the key events defined in the architecture document (generation, failure, revocation).
    *   **Acceptance Criteria:** Security-sensitive events related to credentials are logged in a structured format.
    *   **Test Points (TE):** `[P1-TE-016]` Verify that after generating, failing validation with, and revoking a token, the corresponding log entries are created.

9.  `[P1-BE-001]` **Action:** **Implement Revocation List Cleanup Worker** (New Task)
    *   **Rationale:** To prevent the `revoked_credentials` table from growing indefinitely.
    *   **Processing:** Create a new scheduled Cloudflare Worker (or use an existing one) that runs periodically (e.g., daily). The worker will query the `revoked_credentials` table and delete all rows where `expires_at` is in the past.
    *   **Acceptance Criteria:** Expired JTIs are automatically removed from the database.
    *   **Test Points (TE):** `[P1-TE-017]` Manually add an expired JTI to the table, trigger the worker, and verify the entry is deleted.

10. `[P1-UX-001]` **Action:** **Frontend UI/UX Integration**
    *   **Rationale:** To allow users to see, copy, and understand the new credential.
    *   **Inputs:** The `credential` string from the API.
    *   **Processing:**
        *   *No change to the copy/display functionality.*
        *   (New) Implement client-side storage using `sessionStorage` by default, with an option to move to `localStorage`.
        *   (New) Add a "Revoke" button next to the credential display, which calls the `DELETE` endpoint. The UI must handle the success and error states of this action.
    *   **Test Points (TE):**
        *   `[P1-TE-018]` (New) E2E test: Verify the "Revoke" button works and the UI reflects that the token is no longer valid.

*   **DW Confirmation:** Checklist has been revised to be fully consistent with architecture v1.1. All new requirements are covered by specific, testable tasks.

# 4. Current Execution Step (EXECUTE Mode - Updated when starting a step)
*This section will be updated during the EXECUTE phase.*

# 5. Task Progress (EXECUTE Mode - Appended after each step/node)
*This section will be updated during the EXECUTE phase.*

# 6. Final Review (REVIEW Mode Population)
*This section will be populated during the REVIEW phase.* 