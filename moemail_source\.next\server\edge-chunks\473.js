"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[473],{2396:(e,a,t)=>{t.d(a,{SignButton:()=>m});var s=t(9796),o=t(1511),r=t(1813),l=t(4123),n=t(7149),i=t(6079),d=t(772),c=t(2304);function m({size:e="default"}){let a=(0,i.rd)(),{data:t,status:m}=(0,l.wV)();return"loading"===m?(0,s.jsx)("div",{className:"h-9"}):t?.user?(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)(d.A,{href:"/profile",className:"flex items-center gap-2 hover:opacity-80 transition-opacity",children:[t.user.image&&(0,s.jsx)(r.A,{src:t.user.image,alt:t.user.name||"用户头像",width:24,height:24,className:"rounded-full"}),(0,s.jsx)("span",{className:"text-sm",children:t.user.name})]}),(0,s.jsx)(o.$,{onClick:()=>(0,l.CI)({callbackUrl:"/"}),variant:"outline",className:(0,c.cn)("flex-shrink-0","lg"===e?"px-8":""),size:e,children:"登出"})]}):(0,s.jsxs)(o.$,{onClick:()=>a.push("/login"),className:(0,c.cn)("gap-2","lg"===e?"px-8":""),size:e,children:[(0,s.jsx)(n.A,{className:"lg"===e?"w-5 h-5":"w-4 h-4"}),"登录/注册"]})}},5354:(e,a,t)=>{t.d(a,{ThemeToggle:()=>i});var s=t(9796),o=t(9205),r=t(7322),l=t(9845),n=t(1511);function i(){let{theme:e,setTheme:a}=(0,l.D)();return(0,s.jsxs)(n.$,{variant:"ghost",size:"icon",onClick:()=>a("light"===e?"dark":"light"),className:"rounded-full",children:[(0,s.jsx)(o.A,{className:"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(r.A,{className:"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"切换主题"})]})}},9851:(e,a,t)=>{t.d(a,{Cf:()=>f,Es:()=>h,HM:()=>m,L3:()=>g,c7:()=>u,lG:()=>i,rr:()=>x,zM:()=>d});var s=t(9796),o=t(2992),r=t(5360),l=t(3751),n=t(2304);let i=r.bL,d=r.l9,c=r.ZL,m=r.bm,p=o.forwardRef(({className:e,...a},t)=>(0,s.jsx)(r.hJ,{ref:t,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));p.displayName=r.hJ.displayName;let f=o.forwardRef(({className:e,children:a,...t},o)=>(0,s.jsxs)(c,{children:[(0,s.jsx)(p,{}),(0,s.jsxs)(r.UC,{ref:o,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[a,(0,s.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"关闭"})]})]})]}));f.displayName=r.UC.displayName;let u=({className:e,...a})=>(0,s.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});u.displayName="DialogHeader";let h=({className:e,...a})=>(0,s.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});h.displayName="DialogFooter";let g=o.forwardRef(({className:e,...a},t)=>(0,s.jsx)(r.hE,{ref:t,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",e),...a}));g.displayName=r.hE.displayName;let x=o.forwardRef(({className:e,...a},t)=>(0,s.jsx)(r.VY,{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...a}));x.displayName=r.VY.displayName},5935:(e,a,t)=>{t.d(a,{p:()=>l});var s=t(9796),o=t(2992),r=t(2304);let l=o.forwardRef(({className:e,type:a,...t},o)=>(0,s.jsx)("input",{type:a,className:(0,r.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:o,...t}));l.displayName="Input"},1673:(e,a,t)=>{t.d(a,{J:()=>n});var s=t(9796),o=t(2992),r=t(1454),l=t(2304);let n=o.forwardRef(({className:e,...a},t)=>(0,s.jsx)(r.b,{ref:t,className:(0,l.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...a}));n.displayName=r.b.displayName},4794:(e,a,t)=>{t.d(a,{Logo:()=>r});var s=t(9796),o=t(772);function r(){return(0,s.jsxs)(o.A,{href:"/",className:"flex items-center gap-2 hover:opacity-80 transition-opacity",children:[(0,s.jsx)("div",{className:"relative w-8 h-8",children:(0,s.jsx)("div",{className:"absolute inset-0 grid grid-cols-8 grid-rows-8 gap-px",children:(0,s.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-primary",children:[(0,s.jsx)("path",{d:"M4 8h24v16H4V8z",className:"fill-primary/20"}),(0,s.jsx)("path",{d:"M4 8h24v2H4V8zM4 22h24v2H4v-2z",className:"fill-primary"}),(0,s.jsx)("path",{d:"M14 12h4v4h-4v-4zM12 14h2v4h-2v-4zM18 14h2v4h-2v-4zM14 18h4v2h-4v-2z",className:"fill-primary"}),(0,s.jsx)("path",{d:"M4 8l12 8 12-8",className:"stroke-primary stroke-2",fill:"none"}),(0,s.jsx)("path",{d:"M8 18h2v2H8v-2zM22 18h2v2h-2v-2z",className:"fill-primary/60"}),(0,s.jsx)("path",{d:"M8 14h2v2H8v-2zM22 14h2v2h-2v-2z",className:"fill-primary/40"})]})})}),(0,s.jsx)("span",{className:"font-bold tracking-wider bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600",children:"MoeMail"})]})}},1043:(e,a,t)=>{t.d(a,{bq:()=>c,eb:()=>p,gC:()=>m,l6:()=>d,yv:()=>f});var s=t(9796),o=t(2992),r=t(3382),l=t(6457),n=t(231),i=t(2304);let d=r.bL,c=o.forwardRef(({className:e,children:a,...t},o)=>(0,s.jsxs)(r.l9,{ref:o,className:(0,i.cn)("flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:[a,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]}));c.displayName=r.l9.displayName;let m=o.forwardRef(({className:e,children:a,position:t="popper",...o},l)=>(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{ref:l,className:(0,i.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...o,children:(0,s.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a})})}));m.displayName=r.UC.displayName;let p=o.forwardRef(({className:e,children:a,...t},o)=>(0,s.jsxs)(r.q7,{ref:o,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"h-4 w-4"})})}),(0,s.jsx)(r.p4,{children:a})]}));p.displayName=r.q7.displayName;let f=r.WT},5746:(e,a,t)=>{t.d(a,{q:()=>s});let s={MAX_ACTIVE_EMAILS:30,POLL_INTERVAL:1e4}},5984:(e,a,t)=>{t.d(a,{U:()=>n});var s=t(3082),o=t(9513),r=t(5746);t(2992);let l=(0,s.v)(e=>({config:null,loading:!1,error:null,fetch:async()=>{try{e({loading:!0,error:null});let a=await fetch("/api/config");if(!a.ok)throw Error("获取配置失败");let t=await a.json();e({config:{defaultRole:t.defaultRole||o.gg.CIVILIAN,emailDomains:t.emailDomains,emailDomainsArray:t.emailDomains.split(","),adminContact:t.adminContact||"",maxEmails:Number(t.maxEmails)||r.q.MAX_ACTIVE_EMAILS},loading:!1})}catch(a){e({error:a instanceof Error?a.message:"获取配置失败",loading:!1})}}}));function n(){return l()}},4399:(e,a,t)=>{t.d(a,{T:()=>r});var s=t(2992),o=t(120);function r(e={}){let{toast:a}=(0,o.dj)(),{successMessage:t="已复制到剪贴板",errorMessage:l="复制失败"}=e;return{copyToClipboard:(0,s.useCallback)(async e=>{try{return await navigator.clipboard.writeText(e),a({title:"成功",description:t}),!0}catch{return a({title:"错误",description:l,variant:"destructive"}),!1}},[t,l,a])}}},9513:(e,a,t)=>{t.d(a,{Jj:()=>o,_m:()=>l,gg:()=>s});let s={EMPEROR:"emperor",DUKE:"duke",KNIGHT:"knight",CIVILIAN:"civilian"},o={MANAGE_EMAIL:"manage_email",MANAGE_WEBHOOK:"manage_webhook",PROMOTE_USER:"promote_user",MANAGE_CONFIG:"manage_config",MANAGE_API_KEY:"manage_api_key"},r={[s.EMPEROR]:Object.values(o),[s.DUKE]:[o.MANAGE_EMAIL,o.MANAGE_WEBHOOK,o.MANAGE_API_KEY],[s.KNIGHT]:[o.MANAGE_EMAIL,o.MANAGE_WEBHOOK],[s.CIVILIAN]:[]};function l(e,a){return e.some(e=>r[e]?.includes(a))}},2483:(e,a,t)=>{t.d(a,{SignButton:()=>s});let s=(0,t(6853).YR)(function(){throw Error("Attempted to call SignButton() from the server but SignButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\auth\\sign-button.tsx","SignButton")},1024:(e,a,t)=>{t.d(a,{Y:()=>n});var s=t(861),o=t(2483),r=t(38),l=t(9134);function n(){return(0,s.jsx)("header",{className:"fixed top-0 left-0 right-0 z-50 h-16 bg-background/80 backdrop-blur-sm border-b",children:(0,s.jsx)("div",{className:"container mx-auto h-full px-4",children:(0,s.jsxs)("div",{className:"h-full flex items-center justify-between",children:[(0,s.jsx)(l.Logo,{}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(r.ThemeToggle,{}),(0,s.jsx)(o.SignButton,{})]})]})})})}},38:(e,a,t)=>{t.d(a,{ThemeToggle:()=>s});let s=(0,t(6853).YR)(function(){throw Error("Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\theme\\theme-toggle.tsx","ThemeToggle")},9134:(e,a,t)=>{t.d(a,{Logo:()=>s});let s=(0,t(6853).YR)(function(){throw Error("Attempted to call Logo() from the server but Logo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\logo.tsx","Logo")}}]);
//# sourceMappingURL=473.js.map