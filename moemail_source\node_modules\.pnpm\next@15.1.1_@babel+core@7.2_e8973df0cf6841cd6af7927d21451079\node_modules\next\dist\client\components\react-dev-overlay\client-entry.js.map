{"version": 3, "sources": ["../../../../src/client/components/react-dev-overlay/client-entry.tsx"], "sourcesContent": ["import React from 'react'\nimport ReactDevOverlay from './app/ReactDevOverlay'\nimport { getSocketUrl } from './internal/helpers/get-socket-url'\nimport { INITIAL_OVERLAY_STATE } from './shared'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../server/dev/hot-reloader-types'\n\n// if an error is thrown while rendering an RSC stream, this will catch it in dev\n// and show the error overlay\nexport function createDevOverlayElement(reactEl: React.ReactElement) {\n  const rootLayoutMissingTags = window.__next_root_layout_missing_tags\n  const hasMissingTags = !!rootLayoutMissingTags?.length\n  const socketUrl = getSocketUrl(process.env.__NEXT_ASSET_PREFIX || '')\n  const socket = new window.WebSocket(`${socketUrl}/_next/webpack-hmr`)\n\n  // add minimal \"hot reload\" support for RSC errors\n  const handler = (event: MessageEvent) => {\n    let obj\n    try {\n      obj = JSON.parse(event.data)\n    } catch {}\n\n    if (!obj || !('action' in obj)) {\n      return\n    }\n\n    if (obj.action === HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES) {\n      window.location.reload()\n    }\n  }\n\n  socket.addEventListener('message', handler)\n\n  const FallbackLayout = hasMissingTags\n    ? ({ children }: { children: React.ReactNode }) => (\n        <html id=\"__next_error__\">\n          <body>{children}</body>\n        </html>\n      )\n    : React.Fragment\n\n  return (\n    <FallbackLayout>\n      <ReactDevOverlay\n        state={{ ...INITIAL_OVERLAY_STATE, rootLayoutMissingTags }}\n      >\n        {reactEl}\n      </ReactDevOverlay>\n    </FallbackLayout>\n  )\n}\n"], "names": ["createDevOverlayElement", "reactEl", "rootLayoutMissingTags", "window", "__next_root_layout_missing_tags", "hasMissingTags", "length", "socketUrl", "getSocketUrl", "process", "env", "__NEXT_ASSET_PREFIX", "socket", "WebSocket", "handler", "event", "obj", "JSON", "parse", "data", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "SERVER_COMPONENT_CHANGES", "location", "reload", "addEventListener", "FallbackLayout", "children", "html", "id", "body", "React", "Fragment", "ReactDevOverlay", "state", "INITIAL_OVERLAY_STATE"], "mappings": ";;;;+BAQgBA;;;eAAAA;;;;;gEARE;0EACU;8BACC;wBACS;kCACM;AAIrC,SAASA,wBAAwBC,OAA2B;IACjE,MAAMC,wBAAwBC,OAAOC,+BAA+B;IACpE,MAAMC,iBAAiB,CAAC,EAACH,yCAAAA,sBAAuBI,MAAM;IACtD,MAAMC,YAAYC,IAAAA,0BAAY,EAACC,QAAQC,GAAG,CAACC,mBAAmB,IAAI;IAClE,MAAMC,SAAS,IAAIT,OAAOU,SAAS,CAAC,AAAC,KAAEN,YAAU;IAEjD,kDAAkD;IAClD,MAAMO,UAAU,CAACC;QACf,IAAIC;QACJ,IAAI;YACFA,MAAMC,KAAKC,KAAK,CAACH,MAAMI,IAAI;QAC7B,EAAE,UAAM,CAAC;QAET,IAAI,CAACH,OAAO,CAAE,CAAA,YAAYA,GAAE,GAAI;YAC9B;QACF;QAEA,IAAIA,IAAII,MAAM,KAAKC,6CAA2B,CAACC,wBAAwB,EAAE;YACvEnB,OAAOoB,QAAQ,CAACC,MAAM;QACxB;IACF;IAEAZ,OAAOa,gBAAgB,CAAC,WAAWX;IAEnC,MAAMY,iBAAiBrB,iBACnB;YAAC,EAAEsB,QAAQ,EAAiC;6BAC1C,qBAACC;YAAKC,IAAG;sBACP,cAAA,qBAACC;0BAAMH;;;QAGXI,cAAK,CAACC,QAAQ;IAElB,qBACE,qBAACN;kBACC,cAAA,qBAACO,wBAAe;YACdC,OAAO;gBAAE,GAAGC,6BAAqB;gBAAEjC;YAAsB;sBAExDD;;;AAIT"}