# Moemail 凭证系统重构任务

**任务创建时间**: `[2025-06-07 19:07:12 +08:00]`
**创建人**: AI (<PERSON><PERSON> Dasheng - PM 提议, DW 创建)
**关联协议**: RIPER-5 + Multi-Dimensional Thinking + Agent Execution Protocol (Refined v3.9)
**项目工作区路径**: `[PROJECT_ROOT]/project_document/`
**关联分析报告**: `issues_analysis_report_20250607.md`

---

## 0. 团队协作日志与关键决策点

(此部分将记录此重构任务期间的所有关键会议和决策)

---
**会议记录**
*   **日期与时间:** `[2025-06-07 19:07:12 +08:00]`
*   **会议类型:** 紧急问题评审与对策会议 (模拟)
*   **主持人:** PM
*   **记录员:** DW
*   **与会者:** PM, AR, LD, SE, TE, DW
*   **议程摘要:** 评审`augment`生成的问题分析报告，制定全面的修复和重构计划。
*   **核心决策:**
    1.  批准立即启动基于分析报告的重构计划。
    2.  暂停所有其他开发活动，优先解决架构和安全问题。
    3.  进入 `INNOVATE` 模式，设计统一的凭证系统解决方案。
    4.  创建此任务文件以跟踪整个重构过程。
*   **DW 确认:** 会议纪要完整并符合标准。
---

## 任务描述

根据 `issues_analysis_report_20250607.md` 的深度分析，本任务旨在对 `moemail` 项目的JWT凭证系统进行一次彻底的重构。目标是解决当前系统中存在的架构与实现不符、代码冲突、安全漏洞、配置混乱等多重问题，最终交付一个统一、健壮、安全且可维护的凭证系统。

## 项目概览 (将在 PLAN 阶段填充)

[目标、核心特性、成功指标等]

---
*以下各部分将由AI在协议执行期间动态填充。*
---

## 1. 分析 (RESEARCH Mode Population)

*本阶段的分析已经由 `issues_analysis_report_20250607.md` 完成，其核心结论是启动此重构任务的依据。*

*   **核心问题**: 架构漂移、代码实现冲突、中间件安全漏洞、配置不一致。
*   **根本原因**: 缺乏统一规范和代码审查，导致技术债累积。
*   **风险**: 系统不稳定、存在严重安全漏洞、开发和维护成本极高。
*   **DW 确认**: 分析阶段的输入清晰、完整，为后续工作提供了坚实的基础。

## 2. 提议的解决方案 (INNOVATE Mode Population)

*最后更新于: `[2025-06-07 19:16:11 +08:00]` (AR, LD, SE 共同设计, DW 整理)*

基于对 `issues_analysis_report_20250607.md` 的深度分析和团队的创新研讨，我们提出以下统一解决方案，旨在根除现有问题并建立一个健壮、可维护的凭证系统。

### **核心原则：分层认证网关 (Layered Authentication Gateway)**

我们将采用一种清晰的分层认证模式来彻底解决 Edge Runtime 与 Node.js Runtime 的架构冲突，并增强安全性。

1.  **第一层 - Edge 中间件 (Stateless Gatekeeper):**
    *   **职责**: 作为快速的"守门人"，仅执行**无状态 (Stateless)** 的JWT验证。
    *   **操作**: 只验证JWT的签名、签发者(`iss`)、受众(`aud`)和有效期(`exp`)。**不执行任何数据库查询。**
    *   **结果**: 快速拒绝格式错误或签名无效的令牌，并将看似合法的请求传递给下一层。

2.  **第二层 - Node.js API 端点 (Stateful Verification):**
    *   **职责**: 作为安全的核心防线，执行**有状态 (Stateful)** 的业务和安全验证。
    *   **操作**: 在处理业务逻辑前，必须调用完整的验证函数，对令牌进行**撤销检查 (Revocation Check)**，即查询数据库确认令牌的 `jti` 未被列入黑名单。
    *   **结果**: 确保即使是签名合法的令牌，如果已被撤销，也绝对无法访问到核心业务逻辑和数据。

### **具体技术方案**

1.  **代码实现统一 (Single Source of Truth):**
    *   **删除旧代码**: 彻底删除 `moemail_source/app/lib/jwt.ts` 和 `moemail_source/lib/edge-auth.ts` 这两个冲突的实现。
    *   **创建新模块**: 在 `moemail_source/app/lib/` 目录下创建一个新的、统一的凭证服务模块：`credential.ts`。
    *   **该模块将导出**:
        *   `generateAddressCredential(...)`: 用于创建符合架构 v1.2 规范的 JWT。
        *   `verifySignatureEdge(...)`: 一个轻量的、Edge-Compatible 的函数，仅用于中间件进行无状态验证。
        *   `verifyCredentialNode(...)`: 一个完整的、运行于 Node.js 的函数，包含所有无状态和有状态（撤销）的验证逻辑，供 API 端点调用。
        *   `revokeCredential(...)`: 用于将令牌的 `jti` 添加到数据库黑名单中。

2.  **环境变量标准化:**
    *   `JWT_ADDRESS_SECRET`: **唯一**用于JWT凭证签名的密钥。
    *   `AUTH_SECRET`: **仅**用于 `next-auth` 框架自身。
    *   将更新 `.env.local` 和 `.env.example` 文件以体现此标准。

3.  **中间件 (`middleware.ts`) 重构:**
    *   **简化逻辑**: 只调用 `verifySignatureEdge()` 进行验证。
    *   **完善 `matcher`**: 根据报告的分析，补充所有需要保护的 API 路径。
    *   **标准化错误响应**: 返回符合架构规范的 `401 Unauthorized` 错误信息。

4.  **API 端点加固:**
    *   所有受保护的 API 路由，在执行其核心功能前，**必须**首先调用 `verifyCredentialNode()` 函数来完成最终的、包含撤销检查的身份验证。

### **预期收益**

*   **架构清晰**: 彻底解决 Edge 与 Node.js 的环境冲突。
*   **安全可靠**: 通过分层防御和强制的撤销检查，消除已知的安全漏洞。
*   **高可维护性**: 单一代码来源和统一的规范将极大降低未来维护成本。
*   **高可测试性**: 清晰的职责划分使单元测试和集成测试更容易编写和维护。

* **DW 确认:** 此解决方案已清晰记录，决策过程可追溯，并符合文档标准。

## 3. 实施计划 (PLAN Mode Generation - Checklist Format)

*(此部分将在 PLAN 模式下进行填充)*

## 4. 当前执行步骤 (EXECUTE Mode - Updated when starting a step)

*(此部分将在 EXECUTE 模式下进行填充)*

## 5. 任务进展 (EXECUTE Mode - Appended after each step/node)

*(此部分将在 EXECUTE 模式下进行填充)*

## 6. 最终审查 (REVIEW Mode Population)

*(此部分将在 REVIEW 模式下进行填充)* 