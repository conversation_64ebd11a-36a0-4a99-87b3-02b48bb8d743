# Moemail 凭证系统重构任务

**任务创建时间**: `[2025-06-07 19:07:12 +08:00]`
**创建人**: AI (<PERSON><PERSON> Dasheng - PM 提议, DW 创建)
**关联协议**: RIPER-5 + Multi-Dimensional Thinking + Agent Execution Protocol (Refined v3.9)
**项目工作区路径**: `[PROJECT_ROOT]/project_document/`
**关联分析报告**: `issues_analysis_report_20250607.md`

---

## 0. 团队协作日志与关键决策点

(此部分将记录此重构任务期间的所有关键会议和决策)

---
**会议记录**
*   **日期与时间:** `[2025-06-07 19:07:12 +08:00]`
*   **会议类型:** 紧急问题评审与对策会议 (模拟)
*   **主持人:** PM
*   **记录员:** DW
*   **与会者:** PM, AR, LD, SE, TE, DW
*   **议程摘要:** 评审`augment`生成的问题分析报告，制定全面的修复和重构计划。
*   **核心决策:**
    1.  批准立即启动基于分析报告的重构计划。
    2.  暂停所有其他开发活动，优先解决架构和安全问题。
    3.  进入 `INNOVATE` 模式，设计统一的凭证系统解决方案。
    4.  创建此任务文件以跟踪整个重构过程。
*   **DW 确认:** 会议纪要完整并符合标准。
---

## 任务描述

根据 `issues_analysis_report_20250607.md` 的深度分析，本任务旨在对 `moemail` 项目的JWT凭证系统进行一次彻底的重构。目标是解决当前系统中存在的架构与实现不符、代码冲突、安全漏洞、配置混乱等多重问题，最终交付一个统一、健壮、安全且可维护的凭证系统。

## 项目概览 (将在 PLAN 阶段填充)

[目标、核心特性、成功指标等]

---
*以下各部分将由AI在协议执行期间动态填充。*
---

## 1. 分析 (RESEARCH Mode Population)

*本阶段的分析已经由 `issues_analysis_report_20250607.md` 完成，其核心结论是启动此重构任务的依据。*

*   **核心问题**: 架构漂移、代码实现冲突、中间件安全漏洞、配置不一致。
*   **根本原因**: 缺乏统一规范和代码审查，导致技术债累积。
*   **风险**: 系统不稳定、存在严重安全漏洞、开发和维护成本极高。
*   **DW 确认**: 分析阶段的输入清晰、完整，为后续工作提供了坚实的基础。

## 2. 提议的解决方案 (INNOVATE Mode Population)

*最后更新于: `[2025-06-07 19:16:11 +08:00]` (AR, LD, SE 共同设计, DW 整理)*

基于对 `issues_analysis_report_20250607.md` 的深度分析和团队的创新研讨，我们提出以下统一解决方案，旨在根除现有问题并建立一个健壮、可维护的凭证系统。

### **核心原则：分层认证网关 (Layered Authentication Gateway)**

我们将采用一种清晰的分层认证模式来彻底解决 Edge Runtime 与 Node.js Runtime 的架构冲突，并增强安全性。

1.  **第一层 - Edge 中间件 (Stateless Gatekeeper):**
    *   **职责**: 作为快速的"守门人"，仅执行**无状态 (Stateless)** 的JWT验证。
    *   **操作**: 只验证JWT的签名、签发者(`iss`)、受众(`aud`)和有效期(`exp`)。**不执行任何数据库查询。**
    *   **结果**: 快速拒绝格式错误或签名无效的令牌，并将看似合法的请求传递给下一层。

2.  **第二层 - Node.js API 端点 (Stateful Verification):**
    *   **职责**: 作为安全的核心防线，执行**有状态 (Stateful)** 的业务和安全验证。
    *   **操作**: 在处理业务逻辑前，必须调用完整的验证函数，对令牌进行**撤销检查 (Revocation Check)**，即查询数据库确认令牌的 `jti` 未被列入黑名单。
    *   **结果**: 确保即使是签名合法的令牌，如果已被撤销，也绝对无法访问到核心业务逻辑和数据。

### **具体技术方案**

1.  **代码实现统一 (Single Source of Truth):**
    *   **删除旧代码**: 彻底删除 `moemail_source/app/lib/jwt.ts` 和 `moemail_source/lib/edge-auth.ts` 这两个冲突的实现。
    *   **创建新模块**: 在 `moemail_source/app/lib/` 目录下创建一个新的、统一的凭证服务模块：`credential.ts`。
    *   **该模块将导出**:
        *   `generateAddressCredential(...)`: 用于创建符合架构 v1.2 规范的 JWT。
        *   `verifySignatureEdge(...)`: 一个轻量的、Edge-Compatible 的函数，仅用于中间件进行无状态验证。
        *   `verifyCredentialNode(...)`: 一个完整的、运行于 Node.js 的函数，包含所有无状态和有状态（撤销）的验证逻辑，供 API 端点调用。
        *   `revokeCredential(...)`: 用于将令牌的 `jti` 添加到数据库黑名单中。

2.  **环境变量标准化:**
    *   `JWT_ADDRESS_SECRET`: **唯一**用于JWT凭证签名的密钥。
    *   `AUTH_SECRET`: **仅**用于 `next-auth` 框架自身。
    *   将更新 `.env.local` 和 `.env.example` 文件以体现此标准。

3.  **中间件 (`middleware.ts`) 重构:**
    *   **简化逻辑**: 只调用 `verifySignatureEdge()` 进行验证。
    *   **完善 `matcher`**: 根据报告的分析，补充所有需要保护的 API 路径。
    *   **标准化错误响应**: 返回符合架构规范的 `401 Unauthorized` 错误信息。

4.  **API 端点加固:**
    *   所有受保护的 API 路由，在执行其核心功能前，**必须**首先调用 `verifyCredentialNode()` 函数来完成最终的、包含撤销检查的身份验证。

### **预期收益**

*   **架构清晰**: 彻底解决 Edge 与 Node.js 的环境冲突。
*   **安全可靠**: 通过分层防御和强制的撤销检查，消除已知的安全漏洞。
*   **高可维护性**: 单一代码来源和统一的规范将极大降低未来维护成本。
*   **高可测试性**: 清晰的职责划分使单元测试和集成测试更容易编写和维护。

* **DW 确认:** 此解决方案已清晰记录，决策过程可追溯，并符合文档标准。

## 3. 实施计划 (PLAN Mode Generation - Checklist Format)

*最后更新于: `[2025-06-07 19:35:48 +08:00]` (全员参与制定, DW 整理)*

本计划将"分层认证网关"方案分解为三个阶段，并采纳了用户关于**实施细节、分阶段执行、加强测试、保留回滚方案**的全部建议。

### **准备工作：风险控制与回滚方案**

1.  `[P1-PM-001]` **创建开发分支**:
    *   **Action**: 从最新的 `main` 或 `develop` 分支创建一个名为 `refactor/credential-system` 的新分支。所有重构工作将在此分支上进行。
    *   **回滚方案**: 如果重构过程中出现重大问题，可直接废弃此分支，切换回原分支。
    *   **验收标准**: 新分支已成功创建并推送到远程仓库。

### **阶段一：地基搭建 - 统一凭证服务 (预计2-3小时)**

**目标**: 建立代码的"单一事实来源"，清理技术债。

2.  `[P1-AR-002]` **清理冗余代码**:
    *   **Action**: 在执行任何创建操作前，先进行清理。使用全局搜索确认无任何地方引用后，删除以下文件：
        *   `moemail_source/app/lib/jwt.ts`
        *   `moemail_source/lib/edge-auth.ts`
    *   **风险**: 可能存在未知的间接引用。
    *   **回滚方案**: 从git历史中恢复被删除的文件。
    *   **验收标准**: 文件已删除，项目编译通过（此时会有因缺少模块导致的错误，是正常的）。

3.  `[P1-LD-003]` **创建统一的凭证服务模块**:
    *   **Action**: 在 `moemail_source/app/lib/` 目录下创建新文件 `credential.ts`。
    *   **实施细节**: 文件将包含以下核心功能的框架（初始为空或基础实现）：
        ```typescript
        // moemail_source/app/lib/credential.ts

        import { db } from '@/db';
        import { revoked_credentials } from '@/db/schema';
        import { eq } from 'drizzle-orm';
        import { SignJWT, jwtVerify } from 'jose';
        import { nanoid } from 'nanoid';

        const secret = new TextEncoder().encode(process.env.JWT_ADDRESS_SECRET!);

        /**
         * 生成符合 v1.2 规范的地址凭证
         */
        export async function generateAddressCredential(
            emailAddress: string,
            expiresIn: string | number | Date
        ): Promise<string> {
          // ... 实现将在这里填充
        }

        /**
         * [Edge-Compatible] 仅验证签名，用于中间件
         */
        export async function verifySignatureEdge(
            token: string
        ): Promise<boolean | any> {
          // ... 实现将在这里填充
        }

        /**
         * [Node.js] 完整验证凭证，包括撤销检查，用于 API 端点
         */
        export async function verifyCredentialNode(
            token: string,
            expectedAddress: string
        ): Promise<{ valid: boolean; payload?: any; error?: string }> {
          // ... 实现将在这里填充
        }

        /**
         * 撤销一个凭证
         */
        export async function revokeCredential(
            token: string
        ): Promise<{ success: boolean; error?: string }> {
          // ... 实现将在这里填充
        }
        ```
    *   **验收标准**: `credential.ts` 文件已创建，包含上述函数签名。

4.  `[P1-TE-004]` **编写单元测试**:
    *   **Action**: 创建 `moemail_source/app/lib/credential.test.ts`，为 `credential.ts` 中的每一个函数编写详尽的单元测试。
    *   **测试要点**:
        *   `generateAddressCredential`: 验证生成的 token 格式、`iss`、`aud`、`sub` 是否正确。
        *   `verify` 函数族: 测试有效 token、无效 token、过期 token、错误签名的场景。
        *   `revokeCredential`: 模拟数据库操作，验证 `jti` 是否被正确记录。
    *   **验收标准**: 单元测试覆盖率达到90%以上，所有测试用例通过。

### **阶段二：安全加固 - 重构中间件与API (预计2-3小时)**

**目标**: 应用新的凭证服务，修复安全漏洞。

5.  `[P2-LD-005]` **重构中间件 (`middleware.ts`)**:
    *   **Action**: 修改 `middleware.ts` 以使用新的凭证服务。
    *   **实施细节**:
        *   导入 `verifySignatureEdge` from `@/app/lib/credential`。
        *   在中间件逻辑中，调用 `verifySignatureEdge` 进行快速验证。
        *   根据分析报告，更新 `config.matcher` 列表，确保所有需要保护的路径都被包含。
        *   统一 `401` 错误响应的格式。
    *   **验收标准**: 中间件逻辑更新，编译通过。

6.  `[P2-SE-006]` **加固 API 端点**:
    *   **Action**: 逐一排查并重构所有受保护的API端点，强制执行有状态验证。
    *   **实施细节**:
        *   在需要身份验证的 API 路由处理函数（例如 `POST`, `GET` 等）的起始位置，导入并调用 `verifyCredentialNode`。
        *   将请求头中的 token 和 URL 中的地址作为参数传入。
        *   如果 `verifyCredentialNode` 返回 `valid: false`，则立即返回 `401` 或 `403` 错误。
    *   **验收标准**: 所有相关API端点都已加入 `verifyCredentialNode` 验证逻辑。

7.  `[P2-TE-007]` **编写集成测试**:
    *   **Action**: 创建或更新集成测试，以验证整个认证流程。
    *   **测试要点**:
        *   **成功场景**: 使用有效凭证成功访问受保护的 API。
        *   **失败场景**:
            *   使用无效/过期凭证访问 API，应返回 `401`。
            *   不提供凭证访问 API，应返回 `401`。
            *   **关键**: 使用已撤销的凭证访问 API，应返回 `401`。
            *   **关键**: 使用邮箱A的凭证访问邮箱B的API，应返回 `403`。
    *   **验收标准**: 所有集成测试用例通过。

### **阶段三：环境配置与最终验证 (预计1-2小时)**

**目标**: 确保配置统一，并进行最终回归验证。

8.  `[P3-AR-008]` **标准化环境变量**:
    *   **Action**: 统一项目中的环境变量。
    *   **实施细节**:
        *   确保所有凭证签名操作都使用 `process.env.JWT_ADDRESS_SECRET`。
        *   检查并更新 `.env.local` 和 `.env.example` 文件，删除旧的或混淆的变量，添加 `JWT_ADDRESS_SECRET` 的说明。
    *   **验收标准**: 环境变量使用统一，文档清晰。

9.  `[P3-TE-009]` **执行端到端 (E2E) 及回归测试**:
    *   **Action**: 运行所有现有的测试（单元、集成、E2E），确保重构没有破坏任何原有功能。
    *   **测试要点**: 模拟用户从登录、生成凭证、使用凭证、到撤销凭证的完整流程。
    *   **验收标准**: 所有测试通过，无功能回归。

10. `[P3-PM-010]` **代码审查与合并**:
    *   **Action**: 对 `refactor/credential-system` 分支发起 Pull Request，由团队进行最终审查。
    *   **审查要点**: 是否遵循了所有计划步骤？代码是否清晰？测试是否充分？
    *   **验收标准**: PR 被批准并合并到主分支。

* **DW 确认:** 清单完整、详细、可操作，且包含了风险控制和验证步骤，符合文档标准。

### **补充优化建议 (基于专家评审)**

*最后更新于: `[2025-06-07 20:15:32 +08:00]` (基于Augment专家评审建议, DW 整理)*

以下是在执行上述计划时可以进一步优化的技术细节，虽非必需但能显著提升系统的健壮性和可维护性：

#### **A. 错误处理标准化**

**建议在 `credential.ts` 中添加统一的错误类型定义**：

```typescript
// 统一的凭证错误类型
export interface CredentialError {
  code: 'INVALID_TOKEN' | 'TOKEN_EXPIRED' | 'TOKEN_REVOKED' | 'ADDRESS_MISMATCH' | 'SIGNATURE_INVALID' | 'MISSING_CLAIMS';
  message: string;
  details?: {
    jti?: string;
    address?: string;
    timestamp?: Date;
    [key: string]: any;
  };
}

// 标准化的验证结果
export interface VerificationResult {
  valid: boolean;
  payload?: any;
  error?: CredentialError;
}
```

**优势**:
- 统一的错误处理机制
- 便于前端显示用户友好的错误信息
- 有助于问题诊断和日志分析

#### **B. 性能监控与指标收集**

**建议添加性能监控功能**：

```typescript
// 性能指标收集
export interface PerformanceMetrics {
  jwtVerificationTime: number;    // JWT验证耗时(ms)
  revocationCheckTime: number;    // 撤销检查耗时(ms)
  totalVerificationTime: number; // 总验证耗时(ms)
  cacheHitRate?: number;         // 缓存命中率(如果实现缓存)
}

// 在验证函数中添加性能监控
export async function verifyCredentialNodeWithMetrics(
  token: string,
  expectedAddress: string
): Promise<VerificationResult & { metrics: PerformanceMetrics }> {
  const startTime = performance.now();
  // ... 验证逻辑
  const endTime = performance.now();

  return {
    // ... 验证结果
    metrics: {
      totalVerificationTime: endTime - startTime,
      // ... 其他指标
    }
  };
}
```

**优势**:
- 便于性能优化和瓶颈识别
- 为后续缓存策略提供数据支持
- 有助于设置合理的超时时间

#### **C. 安全审计日志系统**

**建议实现结构化的安全事件记录**：

```typescript
// 安全事件类型
export type SecurityEventType =
  | 'TOKEN_GENERATED'
  | 'TOKEN_VERIFIED_SUCCESS'
  | 'TOKEN_VERIFIED_FAILED'
  | 'TOKEN_REVOKED'
  | 'SUSPICIOUS_ACTIVITY';

// 安全事件记录
export interface SecurityEvent {
  type: SecurityEventType;
  timestamp: Date;
  address: string;
  jti?: string;
  clientIP?: string;
  userAgent?: string;
  errorCode?: string;
  metadata?: Record<string, any>;
}

// 安全日志记录函数
export async function logSecurityEvent(event: SecurityEvent): Promise<void> {
  // 可以记录到数据库、文件或外部日志服务
  console.log(`[SECURITY] ${event.type}:`, {
    timestamp: event.timestamp.toISOString(),
    address: event.address,
    jti: event.jti,
    ip: event.clientIP,
    error: event.errorCode
  });

  // 未来可扩展到：
  // - 数据库记录
  // - 外部SIEM系统
  // - 实时告警系统
}
```

**优势**:
- 满足安全合规要求
- 便于安全事件分析和调查
- 支持异常行为检测

#### **D. 缓存策略优化**

**建议为撤销检查添加缓存层**：

```typescript
// 简单的内存缓存实现
class RevocationCache {
  private cache = new Map<string, { revoked: boolean; expiry: number }>();
  private readonly TTL = 5 * 60 * 1000; // 5分钟缓存

  async isRevoked(jti: string): Promise<boolean | null> {
    const cached = this.cache.get(jti);
    if (cached && Date.now() < cached.expiry) {
      return cached.revoked;
    }
    return null; // 缓存未命中
  }

  setRevoked(jti: string, revoked: boolean): void {
    this.cache.set(jti, {
      revoked,
      expiry: Date.now() + this.TTL
    });
  }

  clear(): void {
    this.cache.clear();
  }
}
```

**优势**:
- 减少数据库查询压力
- 提升验证性能
- 降低系统延迟

#### **E. 配置管理增强**

**建议添加配置验证和默认值**：

```typescript
// 配置接口
export interface CredentialConfig {
  jwtSecret: string;
  defaultExpiryTime: string | number;
  maxTokenAge: number;
  enableRevocationCache: boolean;
  enableSecurityLogging: boolean;
  issuer: string;
  audience: string;
}

// 配置验证和默认值
export function getCredentialConfig(): CredentialConfig {
  const config: CredentialConfig = {
    jwtSecret: process.env.JWT_ADDRESS_SECRET!,
    defaultExpiryTime: process.env.JWT_DEFAULT_EXPIRY || '1h',
    maxTokenAge: parseInt(process.env.JWT_MAX_AGE || '86400'), // 24小时
    enableRevocationCache: process.env.ENABLE_REVOCATION_CACHE === 'true',
    enableSecurityLogging: process.env.ENABLE_SECURITY_LOGGING !== 'false',
    issuer: 'moemail:api',
    audience: 'moemail:mailbox:access'
  };

  // 配置验证
  if (!config.jwtSecret) {
    throw new Error('JWT_ADDRESS_SECRET environment variable is required');
  }

  return config;
}
```

**优势**:
- 集中的配置管理
- 环境变量验证
- 合理的默认值

#### **实施优先级建议**

1. **高优先级 (建议在阶段一实施)**:
   - 错误处理标准化 - 影响所有后续开发
   - 配置管理增强 - 确保环境配置正确

2. **中优先级 (建议在阶段二实施)**:
   - 安全审计日志 - 提升安全可观测性
   - 性能监控基础 - 为优化提供数据

3. **低优先级 (可在阶段三或后续迭代实施)**:
   - 缓存策略优化 - 性能优化
   - 高级监控指标 - 运维增强

**注意**: 这些优化建议都是**可选的**，不实施也不会影响核心重构计划的成功。团队可根据时间和资源情况选择性实施。

* **DW 确认:** 优化建议已记录，分优先级且不影响核心计划执行，符合文档标准。

## 4. 当前执行步骤 (EXECUTE Mode - Updated when starting a step)

*(此部分将在 EXECUTE 模式下进行填充)*

## 5. 任务进展 (EXECUTE Mode - Appended after each step/node)

*(此部分将在 EXECUTE 模式下进行填充)*

## 6. 最终审查 (REVIEW Mode Population)

*(此部分将在 REVIEW 模式下进行填充)* 