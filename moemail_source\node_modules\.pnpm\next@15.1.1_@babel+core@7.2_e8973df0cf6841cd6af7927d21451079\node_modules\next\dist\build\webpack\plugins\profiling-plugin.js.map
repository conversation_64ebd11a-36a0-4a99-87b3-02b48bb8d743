{"version": 3, "sources": ["../../../../src/build/webpack/plugins/profiling-plugin.ts"], "sourcesContent": ["import { NormalModule } from 'next/dist/compiled/webpack/webpack'\nimport type { Span } from '../../../trace'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport path from 'path'\n\nconst pluginName = 'ProfilingPlugin'\nexport const spans = new WeakMap<webpack.Compilation | webpack.Compiler, Span>()\nconst moduleSpansByCompilation = new WeakMap<\n  webpack.Compilation,\n  WeakMap<webpack.Module, Span>\n>()\nconst makeSpanByCompilation = new WeakMap<webpack.Compilation, Span>()\nconst sealSpanByCompilation = new WeakMap<webpack.Compilation, Span>()\nexport const webpackInvalidSpans = new WeakMap<any, Span>()\n\nconst TRACE_LABELS_SEAL = [\n  'module assets',\n  'create chunk assets',\n  'asset render',\n  'asset emit',\n  'store asset',\n]\n\nfunction inTraceLabelsSeal(label: string) {\n  return TRACE_LABELS_SEAL.some((l) => label.startsWith(l))\n}\n\nexport class ProfilingPlugin {\n  compiler: any\n  runWebpackSpan: Span\n  rootDir: string\n\n  constructor({\n    runWebpackSpan,\n    rootDir,\n  }: {\n    runWebpackSpan: Span\n    rootDir: string\n  }) {\n    this.runWebpackSpan = runWebpackSpan\n    this.rootDir = rootDir\n  }\n  apply(compiler: any) {\n    this.traceTopLevelHooks(compiler)\n    this.traceCompilationHooks(compiler)\n    this.compiler = compiler\n  }\n\n  traceHookPair(\n    spanName: string | (() => string),\n    startHook: any,\n    stopHook: any,\n    {\n      parentSpan,\n      attrs,\n      onStart,\n      onStop,\n    }: {\n      parentSpan?: (...params: any[]) => Span\n      attrs?: any\n      onStart?: (span: Span, ...params: any[]) => void\n      onStop?: (span: Span, ...params: any[]) => void\n    } = {}\n  ) {\n    let span: Span | undefined\n    startHook.tap(\n      { name: pluginName, stage: -Infinity },\n      (...params: any[]) => {\n        const name = typeof spanName === 'function' ? spanName() : spanName\n        const attributes = attrs ? attrs(...params) : attrs\n        span = parentSpan\n          ? parentSpan(...params).traceChild(name, attributes)\n          : this.runWebpackSpan.traceChild(name, attributes)\n\n        if (onStart) onStart(span, ...params)\n      }\n    )\n    stopHook.tap({ name: pluginName, stage: Infinity }, (...params: any[]) => {\n      // `stopHook` may be triggered when `startHook` has not in cases\n      // where `stopHook` is used as the terminating event for more\n      // than one pair of hooks.\n      if (!span) {\n        return\n      }\n\n      if (onStop) onStop(span, ...params)\n      span.stop()\n    })\n  }\n\n  traceTopLevelHooks(compiler: any) {\n    this.traceHookPair(\n      'webpack-compilation',\n      compiler.hooks.compilation,\n      compiler.hooks.afterCompile,\n      {\n        parentSpan: () =>\n          webpackInvalidSpans.get(compiler) || this.runWebpackSpan,\n        attrs: () => ({ name: compiler.name }),\n        onStart: (span, compilation) => {\n          spans.set(compilation, span)\n          spans.set(compiler, span)\n          moduleSpansByCompilation.set(compilation, new WeakMap())\n        },\n      }\n    )\n\n    if (compiler.options.mode === 'development') {\n      this.traceHookPair(\n        () => `webpack-invalidated-${compiler.name}`,\n        compiler.hooks.invalid,\n        compiler.hooks.done,\n        {\n          onStart: (span) => webpackInvalidSpans.set(compiler, span),\n          onStop: () => webpackInvalidSpans.delete(compiler),\n          attrs: (fileName: any) => ({\n            trigger: fileName\n              ? path.relative(this.rootDir, fileName).replaceAll(path.sep, '/')\n              : 'manual',\n          }),\n        }\n      )\n    }\n  }\n\n  traceCompilationHooks(compiler: any) {\n    this.traceHookPair('emit', compiler.hooks.emit, compiler.hooks.afterEmit, {\n      parentSpan: () =>\n        webpackInvalidSpans.get(compiler) || this.runWebpackSpan,\n    })\n\n    this.traceHookPair('make', compiler.hooks.make, compiler.hooks.finishMake, {\n      parentSpan: (compilation) => {\n        const compilationSpan = spans.get(compilation)\n        if (!compilationSpan) {\n          return webpackInvalidSpans.get(compiler) || this.runWebpackSpan\n        }\n\n        return compilationSpan\n      },\n      onStart: (span, compilation) => {\n        makeSpanByCompilation.set(compilation, span)\n      },\n      onStop: (_span, compilation) => {\n        makeSpanByCompilation.delete(compilation)\n      },\n    })\n\n    compiler.hooks.compilation.tap(\n      { name: pluginName, stage: -Infinity },\n      (compilation: any) => {\n        compilation.hooks.buildModule.tap(pluginName, (module: any) => {\n          const moduleType = (() => {\n            const r = module.userRequest\n            if (!r || r.endsWith('!')) {\n              return ''\n            } else {\n              const resource = r.split('!').pop()\n              const match = /^[^?]+\\.([^?]+)$/.exec(resource)\n              return match ? match[1] : ''\n            }\n          })()\n\n          const issuerModule = compilation?.moduleGraph?.getIssuer(module)\n\n          let span: Span\n\n          const moduleSpans = moduleSpansByCompilation.get(compilation)\n          const spanName = `build-module${moduleType ? `-${moduleType}` : ''}`\n          const issuerSpan: Span | undefined =\n            issuerModule && moduleSpans?.get(issuerModule)\n          if (issuerSpan) {\n            span = issuerSpan.traceChild(spanName)\n          } else {\n            let parentSpan: Span | undefined\n            for (const incomingConnection of compilation.moduleGraph.getIncomingConnections(\n              module\n            )) {\n              const entrySpan = spans.get(incomingConnection.dependency)\n              if (entrySpan) {\n                parentSpan = entrySpan\n                break\n              }\n            }\n\n            if (!parentSpan) {\n              const compilationSpan = spans.get(compilation)\n              if (!compilationSpan) {\n                return\n              }\n\n              parentSpan = compilationSpan\n            }\n            span = parentSpan.traceChild(spanName)\n          }\n          span.setAttribute('name', module.userRequest)\n          span.setAttribute('layer', module.layer)\n          moduleSpans!.set(module, span)\n        })\n\n        const moduleHooks = NormalModule.getCompilationHooks(compilation)\n        moduleHooks.readResource.for(undefined).intercept({\n          register(tapInfo: any) {\n            const fn = tapInfo.fn\n            tapInfo.fn = (loaderContext: any, callback: any) => {\n              fn(loaderContext, (err: any, result: any) => {\n                callback(err, result)\n              })\n            }\n            return tapInfo\n          },\n        })\n\n        moduleHooks.loader.tap(\n          pluginName,\n          (loaderContext: any, module: any) => {\n            const moduleSpan = moduleSpansByCompilation\n              .get(compilation)\n              ?.get(module)\n            loaderContext.currentTraceSpan = moduleSpan\n          }\n        )\n\n        compilation.hooks.succeedModule.tap(pluginName, (module: any) => {\n          moduleSpansByCompilation?.get(compilation)?.get(module)?.stop()\n        })\n        compilation.hooks.failedModule.tap(pluginName, (module: any) => {\n          moduleSpansByCompilation?.get(compilation)?.get(module)?.stop()\n        })\n\n        this.traceHookPair(\n          'seal',\n          compilation.hooks.seal,\n          compilation.hooks.afterSeal,\n          {\n            parentSpan: () => spans.get(compilation)!,\n            onStart(span) {\n              sealSpanByCompilation.set(compilation, span)\n            },\n            onStop() {\n              sealSpanByCompilation.delete(compilation)\n            },\n          }\n        )\n\n        compilation.hooks.addEntry.tap(pluginName, (entry: any) => {\n          const parentSpan =\n            makeSpanByCompilation.get(compilation) || spans.get(compilation)\n          if (!parentSpan) {\n            return\n          }\n          const addEntrySpan = parentSpan.traceChild('add-entry')\n          addEntrySpan.setAttribute('request', entry.request)\n          spans.set(entry, addEntrySpan)\n        })\n\n        compilation.hooks.succeedEntry.tap(pluginName, (entry: any) => {\n          spans.get(entry)?.stop()\n          spans.delete(entry)\n        })\n        compilation.hooks.failedEntry.tap(pluginName, (entry: any) => {\n          spans.get(entry)?.stop()\n          spans.delete(entry)\n        })\n\n        this.traceHookPair(\n          'chunk-graph',\n          compilation.hooks.beforeChunks,\n          compilation.hooks.afterChunks,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'optimize',\n          compilation.hooks.optimize,\n          compilation.hooks.reviveModules,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'optimize-modules',\n          compilation.hooks.optimizeModules,\n          compilation.hooks.afterOptimizeModules,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'optimize-chunks',\n          compilation.hooks.optimizeChunks,\n          compilation.hooks.afterOptimizeChunks,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'optimize-tree',\n          compilation.hooks.optimizeTree,\n          compilation.hooks.afterOptimizeTree,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'optimize-chunk-modules',\n          compilation.hooks.optimizeChunkModules,\n          compilation.hooks.afterOptimizeChunkModules,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'module-hash',\n          compilation.hooks.beforeModuleHash,\n          compilation.hooks.afterModuleHash,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'code-generation',\n          compilation.hooks.beforeCodeGeneration,\n          compilation.hooks.afterCodeGeneration,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'hash',\n          compilation.hooks.beforeHash,\n          compilation.hooks.afterHash,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'code-generation-jobs',\n          compilation.hooks.afterHash,\n          compilation.hooks.beforeModuleAssets,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n\n        const logs = new Map()\n        const originalTime = compilation.logger.time\n        const originalTimeEnd = compilation.logger.timeEnd\n\n        compilation.logger.time = (label: string) => {\n          if (!inTraceLabelsSeal(label)) {\n            return originalTime.call(compilation.logger, label)\n          }\n          const span = sealSpanByCompilation.get(compilation)\n          if (span) {\n            logs.set(label, span.traceChild(label.replace(/ /g, '-')))\n          }\n          return originalTime.call(compilation.logger, label)\n        }\n        compilation.logger.timeEnd = (label: string) => {\n          if (!inTraceLabelsSeal(label)) {\n            return originalTimeEnd.call(compilation.logger, label)\n          }\n\n          const span = logs.get(label)\n          if (span) {\n            span.stop()\n            logs.delete(label)\n          }\n          return originalTimeEnd.call(compilation.logger, label)\n        }\n      }\n    )\n  }\n}\n"], "names": ["Profiling<PERSON><PERSON><PERSON>", "spans", "webpackInvalidSpans", "pluginName", "WeakMap", "moduleSpansByCompilation", "makeSpanByCompilation", "sealSpanByCompilation", "TRACE_LABELS_SEAL", "inTraceLabelsSeal", "label", "some", "l", "startsWith", "constructor", "runWebpackSpan", "rootDir", "apply", "compiler", "traceTopLevelHooks", "traceCompilationHooks", "traceHookPair", "spanName", "startHook", "stopHook", "parentSpan", "attrs", "onStart", "onStop", "span", "tap", "name", "stage", "Infinity", "params", "attributes", "<PERSON><PERSON><PERSON><PERSON>", "stop", "hooks", "compilation", "afterCompile", "get", "set", "options", "mode", "invalid", "done", "delete", "fileName", "trigger", "path", "relative", "replaceAll", "sep", "emit", "afterEmit", "make", "finishMake", "compilationSpan", "_span", "buildModule", "module", "moduleType", "r", "userRequest", "endsWith", "resource", "split", "pop", "match", "exec", "issuerModule", "moduleGraph", "get<PERSON><PERSON><PERSON>", "moduleSpans", "issuerSpan", "incomingConnection", "getIncomingConnections", "entrySpan", "dependency", "setAttribute", "layer", "moduleHooks", "NormalModule", "getCompilationHooks", "readResource", "for", "undefined", "intercept", "register", "tapInfo", "fn", "loaderContext", "callback", "err", "result", "loader", "moduleSpan", "currentTraceSpan", "succeedModule", "failedModule", "seal", "afterSeal", "addEntry", "entry", "addEntrySpan", "request", "<PERSON><PERSON><PERSON><PERSON>", "failedEntry", "beforeChunks", "after<PERSON><PERSON><PERSON>", "optimize", "reviveModules", "optimizeModules", "afterOptimizeModules", "optimizeChunks", "afterOptimizeChunks", "optimizeTree", "afterOptimizeTree", "optimizeChunkModules", "afterOptimizeChunkModules", "beforeModuleHash", "afterModuleHash", "beforeCodeGeneration", "afterCodeGeneration", "beforeHash", "afterHash", "beforeModuleAssets", "logs", "Map", "originalTime", "logger", "time", "originalTimeEnd", "timeEnd", "call", "replace"], "mappings": ";;;;;;;;;;;;;;;;IA2BaA,eAAe;eAAfA;;IArBAC,KAAK;eAALA;;IAOAC,mBAAmB;eAAnBA;;;yBAbgB;6DAGZ;;;;;;AAEjB,MAAMC,aAAa;AACZ,MAAMF,QAAQ,IAAIG;AACzB,MAAMC,2BAA2B,IAAID;AAIrC,MAAME,wBAAwB,IAAIF;AAClC,MAAMG,wBAAwB,IAAIH;AAC3B,MAAMF,sBAAsB,IAAIE;AAEvC,MAAMI,oBAAoB;IACxB;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,kBAAkBC,KAAa;IACtC,OAAOF,kBAAkBG,IAAI,CAAC,CAACC,IAAMF,MAAMG,UAAU,CAACD;AACxD;AAEO,MAAMZ;IAKXc,YAAY,EACVC,cAAc,EACdC,OAAO,EAIR,CAAE;QACD,IAAI,CAACD,cAAc,GAAGA;QACtB,IAAI,CAACC,OAAO,GAAGA;IACjB;IACAC,MAAMC,QAAa,EAAE;QACnB,IAAI,CAACC,kBAAkB,CAACD;QACxB,IAAI,CAACE,qBAAqB,CAACF;QAC3B,IAAI,CAACA,QAAQ,GAAGA;IAClB;IAEAG,cACEC,QAAiC,EACjCC,SAAc,EACdC,QAAa,EACb,EACEC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EAMP,GAAG,CAAC,CAAC,EACN;QACA,IAAIC;QACJN,UAAUO,GAAG,CACX;YAAEC,MAAM5B;YAAY6B,OAAO,CAACC;QAAS,GACrC,CAAC,GAAGC;YACF,MAAMH,OAAO,OAAOT,aAAa,aAAaA,aAAaA;YAC3D,MAAMa,aAAaT,QAAQA,SAASQ,UAAUR;YAC9CG,OAAOJ,aACHA,cAAcS,QAAQE,UAAU,CAACL,MAAMI,cACvC,IAAI,CAACpB,cAAc,CAACqB,UAAU,CAACL,MAAMI;YAEzC,IAAIR,SAASA,QAAQE,SAASK;QAChC;QAEFV,SAASM,GAAG,CAAC;YAAEC,MAAM5B;YAAY6B,OAAOC;QAAS,GAAG,CAAC,GAAGC;YACtD,gEAAgE;YAChE,6DAA6D;YAC7D,0BAA0B;YAC1B,IAAI,CAACL,MAAM;gBACT;YACF;YAEA,IAAID,QAAQA,OAAOC,SAASK;YAC5BL,KAAKQ,IAAI;QACX;IACF;IAEAlB,mBAAmBD,QAAa,EAAE;QAChC,IAAI,CAACG,aAAa,CAChB,uBACAH,SAASoB,KAAK,CAACC,WAAW,EAC1BrB,SAASoB,KAAK,CAACE,YAAY,EAC3B;YACEf,YAAY,IACVvB,oBAAoBuC,GAAG,CAACvB,aAAa,IAAI,CAACH,cAAc;YAC1DW,OAAO,IAAO,CAAA;oBAAEK,MAAMb,SAASa,IAAI;gBAAC,CAAA;YACpCJ,SAAS,CAACE,MAAMU;gBACdtC,MAAMyC,GAAG,CAACH,aAAaV;gBACvB5B,MAAMyC,GAAG,CAACxB,UAAUW;gBACpBxB,yBAAyBqC,GAAG,CAACH,aAAa,IAAInC;YAChD;QACF;QAGF,IAAIc,SAASyB,OAAO,CAACC,IAAI,KAAK,eAAe;YAC3C,IAAI,CAACvB,aAAa,CAChB,IAAM,CAAC,oBAAoB,EAAEH,SAASa,IAAI,EAAE,EAC5Cb,SAASoB,KAAK,CAACO,OAAO,EACtB3B,SAASoB,KAAK,CAACQ,IAAI,EACnB;gBACEnB,SAAS,CAACE,OAAS3B,oBAAoBwC,GAAG,CAACxB,UAAUW;gBACrDD,QAAQ,IAAM1B,oBAAoB6C,MAAM,CAAC7B;gBACzCQ,OAAO,CAACsB,WAAmB,CAAA;wBACzBC,SAASD,WACLE,aAAI,CAACC,QAAQ,CAAC,IAAI,CAACnC,OAAO,EAAEgC,UAAUI,UAAU,CAACF,aAAI,CAACG,GAAG,EAAE,OAC3D;oBACN,CAAA;YACF;QAEJ;IACF;IAEAjC,sBAAsBF,QAAa,EAAE;QACnC,IAAI,CAACG,aAAa,CAAC,QAAQH,SAASoB,KAAK,CAACgB,IAAI,EAAEpC,SAASoB,KAAK,CAACiB,SAAS,EAAE;YACxE9B,YAAY,IACVvB,oBAAoBuC,GAAG,CAACvB,aAAa,IAAI,CAACH,cAAc;QAC5D;QAEA,IAAI,CAACM,aAAa,CAAC,QAAQH,SAASoB,KAAK,CAACkB,IAAI,EAAEtC,SAASoB,KAAK,CAACmB,UAAU,EAAE;YACzEhC,YAAY,CAACc;gBACX,MAAMmB,kBAAkBzD,MAAMwC,GAAG,CAACF;gBAClC,IAAI,CAACmB,iBAAiB;oBACpB,OAAOxD,oBAAoBuC,GAAG,CAACvB,aAAa,IAAI,CAACH,cAAc;gBACjE;gBAEA,OAAO2C;YACT;YACA/B,SAAS,CAACE,MAAMU;gBACdjC,sBAAsBoC,GAAG,CAACH,aAAaV;YACzC;YACAD,QAAQ,CAAC+B,OAAOpB;gBACdjC,sBAAsByC,MAAM,CAACR;YAC/B;QACF;QAEArB,SAASoB,KAAK,CAACC,WAAW,CAACT,GAAG,CAC5B;YAAEC,MAAM5B;YAAY6B,OAAO,CAACC;QAAS,GACrC,CAACM;YACCA,YAAYD,KAAK,CAACsB,WAAW,CAAC9B,GAAG,CAAC3B,YAAY,CAAC0D;oBAYxBtB;gBAXrB,MAAMuB,aAAa,AAAC,CAAA;oBAClB,MAAMC,IAAIF,QAAOG,WAAW;oBAC5B,IAAI,CAACD,KAAKA,EAAEE,QAAQ,CAAC,MAAM;wBACzB,OAAO;oBACT,OAAO;wBACL,MAAMC,WAAWH,EAAEI,KAAK,CAAC,KAAKC,GAAG;wBACjC,MAAMC,QAAQ,mBAAmBC,IAAI,CAACJ;wBACtC,OAAOG,QAAQA,KAAK,CAAC,EAAE,GAAG;oBAC5B;gBACF,CAAA;gBAEA,MAAME,eAAehC,gCAAAA,2BAAAA,YAAaiC,WAAW,qBAAxBjC,yBAA0BkC,SAAS,CAACZ;gBAEzD,IAAIhC;gBAEJ,MAAM6C,cAAcrE,yBAAyBoC,GAAG,CAACF;gBACjD,MAAMjB,WAAW,CAAC,YAAY,EAAEwC,aAAa,CAAC,CAAC,EAAEA,YAAY,GAAG,IAAI;gBACpE,MAAMa,aACJJ,iBAAgBG,+BAAAA,YAAajC,GAAG,CAAC8B;gBACnC,IAAII,YAAY;oBACd9C,OAAO8C,WAAWvC,UAAU,CAACd;gBAC/B,OAAO;oBACL,IAAIG;oBACJ,KAAK,MAAMmD,sBAAsBrC,YAAYiC,WAAW,CAACK,sBAAsB,CAC7EhB,SACC;wBACD,MAAMiB,YAAY7E,MAAMwC,GAAG,CAACmC,mBAAmBG,UAAU;wBACzD,IAAID,WAAW;4BACbrD,aAAaqD;4BACb;wBACF;oBACF;oBAEA,IAAI,CAACrD,YAAY;wBACf,MAAMiC,kBAAkBzD,MAAMwC,GAAG,CAACF;wBAClC,IAAI,CAACmB,iBAAiB;4BACpB;wBACF;wBAEAjC,aAAaiC;oBACf;oBACA7B,OAAOJ,WAAWW,UAAU,CAACd;gBAC/B;gBACAO,KAAKmD,YAAY,CAAC,QAAQnB,QAAOG,WAAW;gBAC5CnC,KAAKmD,YAAY,CAAC,SAASnB,QAAOoB,KAAK;gBACvCP,YAAahC,GAAG,CAACmB,SAAQhC;YAC3B;YAEA,MAAMqD,cAAcC,qBAAY,CAACC,mBAAmB,CAAC7C;YACrD2C,YAAYG,YAAY,CAACC,GAAG,CAACC,WAAWC,SAAS,CAAC;gBAChDC,UAASC,OAAY;oBACnB,MAAMC,KAAKD,QAAQC,EAAE;oBACrBD,QAAQC,EAAE,GAAG,CAACC,eAAoBC;wBAChCF,GAAGC,eAAe,CAACE,KAAUC;4BAC3BF,SAASC,KAAKC;wBAChB;oBACF;oBACA,OAAOL;gBACT;YACF;YAEAR,YAAYc,MAAM,CAAClE,GAAG,CACpB3B,YACA,CAACyF,eAAoB/B;oBACAxD;gBAAnB,MAAM4F,cAAa5F,gCAAAA,yBAChBoC,GAAG,CAACF,iCADYlC,8BAEfoC,GAAG,CAACoB;gBACR+B,cAAcM,gBAAgB,GAAGD;YACnC;YAGF1D,YAAYD,KAAK,CAAC6D,aAAa,CAACrE,GAAG,CAAC3B,YAAY,CAAC0D;oBAC/CxD,mCAAAA;gBAAAA,6CAAAA,gCAAAA,yBAA0BoC,GAAG,CAACF,kCAA9BlC,oCAAAA,8BAA4CoC,GAAG,CAACoB,6BAAhDxD,kCAAyDgC,IAAI;YAC/D;YACAE,YAAYD,KAAK,CAAC8D,YAAY,CAACtE,GAAG,CAAC3B,YAAY,CAAC0D;oBAC9CxD,mCAAAA;gBAAAA,6CAAAA,gCAAAA,yBAA0BoC,GAAG,CAACF,kCAA9BlC,oCAAAA,8BAA4CoC,GAAG,CAACoB,6BAAhDxD,kCAAyDgC,IAAI;YAC/D;YAEA,IAAI,CAAChB,aAAa,CAChB,QACAkB,YAAYD,KAAK,CAAC+D,IAAI,EACtB9D,YAAYD,KAAK,CAACgE,SAAS,EAC3B;gBACE7E,YAAY,IAAMxB,MAAMwC,GAAG,CAACF;gBAC5BZ,SAAQE,IAAI;oBACVtB,sBAAsBmC,GAAG,CAACH,aAAaV;gBACzC;gBACAD;oBACErB,sBAAsBwC,MAAM,CAACR;gBAC/B;YACF;YAGFA,YAAYD,KAAK,CAACiE,QAAQ,CAACzE,GAAG,CAAC3B,YAAY,CAACqG;gBAC1C,MAAM/E,aACJnB,sBAAsBmC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;gBACtD,IAAI,CAACd,YAAY;oBACf;gBACF;gBACA,MAAMgF,eAAehF,WAAWW,UAAU,CAAC;gBAC3CqE,aAAazB,YAAY,CAAC,WAAWwB,MAAME,OAAO;gBAClDzG,MAAMyC,GAAG,CAAC8D,OAAOC;YACnB;YAEAlE,YAAYD,KAAK,CAACqE,YAAY,CAAC7E,GAAG,CAAC3B,YAAY,CAACqG;oBAC9CvG;iBAAAA,aAAAA,MAAMwC,GAAG,CAAC+D,2BAAVvG,WAAkBoC,IAAI;gBACtBpC,MAAM8C,MAAM,CAACyD;YACf;YACAjE,YAAYD,KAAK,CAACsE,WAAW,CAAC9E,GAAG,CAAC3B,YAAY,CAACqG;oBAC7CvG;iBAAAA,aAAAA,MAAMwC,GAAG,CAAC+D,2BAAVvG,WAAkBoC,IAAI;gBACtBpC,MAAM8C,MAAM,CAACyD;YACf;YAEA,IAAI,CAACnF,aAAa,CAChB,eACAkB,YAAYD,KAAK,CAACuE,YAAY,EAC9BtE,YAAYD,KAAK,CAACwE,WAAW,EAC7B;gBACErF,YAAY,IACVlB,sBAAsBkC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,YACAkB,YAAYD,KAAK,CAACyE,QAAQ,EAC1BxE,YAAYD,KAAK,CAAC0E,aAAa,EAC/B;gBACEvF,YAAY,IACVlB,sBAAsBkC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,oBACAkB,YAAYD,KAAK,CAAC2E,eAAe,EACjC1E,YAAYD,KAAK,CAAC4E,oBAAoB,EACtC;gBACEzF,YAAY,IACVlB,sBAAsBkC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,mBACAkB,YAAYD,KAAK,CAAC6E,cAAc,EAChC5E,YAAYD,KAAK,CAAC8E,mBAAmB,EACrC;gBACE3F,YAAY,IACVlB,sBAAsBkC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,iBACAkB,YAAYD,KAAK,CAAC+E,YAAY,EAC9B9E,YAAYD,KAAK,CAACgF,iBAAiB,EACnC;gBACE7F,YAAY,IACVlB,sBAAsBkC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,0BACAkB,YAAYD,KAAK,CAACiF,oBAAoB,EACtChF,YAAYD,KAAK,CAACkF,yBAAyB,EAC3C;gBACE/F,YAAY,IACVlB,sBAAsBkC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,eACAkB,YAAYD,KAAK,CAACmF,gBAAgB,EAClClF,YAAYD,KAAK,CAACoF,eAAe,EACjC;gBACEjG,YAAY,IACVlB,sBAAsBkC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,mBACAkB,YAAYD,KAAK,CAACqF,oBAAoB,EACtCpF,YAAYD,KAAK,CAACsF,mBAAmB,EACrC;gBACEnG,YAAY,IACVlB,sBAAsBkC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,QACAkB,YAAYD,KAAK,CAACuF,UAAU,EAC5BtF,YAAYD,KAAK,CAACwF,SAAS,EAC3B;gBACErG,YAAY,IACVlB,sBAAsBkC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,wBACAkB,YAAYD,KAAK,CAACwF,SAAS,EAC3BvF,YAAYD,KAAK,CAACyF,kBAAkB,EACpC;gBACEtG,YAAY,IACVlB,sBAAsBkC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAGF,MAAMyF,OAAO,IAAIC;YACjB,MAAMC,eAAe3F,YAAY4F,MAAM,CAACC,IAAI;YAC5C,MAAMC,kBAAkB9F,YAAY4F,MAAM,CAACG,OAAO;YAElD/F,YAAY4F,MAAM,CAACC,IAAI,GAAG,CAAC1H;gBACzB,IAAI,CAACD,kBAAkBC,QAAQ;oBAC7B,OAAOwH,aAAaK,IAAI,CAAChG,YAAY4F,MAAM,EAAEzH;gBAC/C;gBACA,MAAMmB,OAAOtB,sBAAsBkC,GAAG,CAACF;gBACvC,IAAIV,MAAM;oBACRmG,KAAKtF,GAAG,CAAChC,OAAOmB,KAAKO,UAAU,CAAC1B,MAAM8H,OAAO,CAAC,MAAM;gBACtD;gBACA,OAAON,aAAaK,IAAI,CAAChG,YAAY4F,MAAM,EAAEzH;YAC/C;YACA6B,YAAY4F,MAAM,CAACG,OAAO,GAAG,CAAC5H;gBAC5B,IAAI,CAACD,kBAAkBC,QAAQ;oBAC7B,OAAO2H,gBAAgBE,IAAI,CAAChG,YAAY4F,MAAM,EAAEzH;gBAClD;gBAEA,MAAMmB,OAAOmG,KAAKvF,GAAG,CAAC/B;gBACtB,IAAImB,MAAM;oBACRA,KAAKQ,IAAI;oBACT2F,KAAKjF,MAAM,CAACrC;gBACd;gBACA,OAAO2H,gBAAgBE,IAAI,CAAChG,YAAY4F,MAAM,EAAEzH;YAClD;QACF;IAEJ;AACF"}