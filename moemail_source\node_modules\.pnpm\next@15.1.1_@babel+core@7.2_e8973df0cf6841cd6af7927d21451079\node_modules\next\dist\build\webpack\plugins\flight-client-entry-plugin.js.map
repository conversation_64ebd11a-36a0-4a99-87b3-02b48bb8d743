{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-client-entry-plugin.ts"], "sourcesContent": ["import type {\n  CssImports,\n  ClientComponentImports,\n} from '../loaders/next-flight-client-entry-loader'\n\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { stringify } from 'querystring'\nimport path from 'path'\nimport { sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  getInvalidator,\n  getEntries,\n  EntryTypes,\n  getEntryKey,\n} from '../../../server/dev/on-demand-entry-handler'\nimport { WEBPACK_LAYERS } from '../../../lib/constants'\nimport {\n  APP_CLIENT_INTERNALS,\n  BARREL_OPTIMIZATION_PREFIX,\n  COMPILER_NAMES,\n  DEFAULT_RUNTIME_WEBPACK,\n  EDGE_RUNTIME_WEBPACK,\n  SERVER_REFERENCE_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../../../shared/lib/constants'\nimport {\n  getActionsFromBuildInfo,\n  isClientComponentEntryModule,\n  isCSSMod,\n  regexCSS,\n} from '../loaders/utils'\nimport {\n  traverseModules,\n  forEachEntryModule,\n  formatBarrelOptimizedResource,\n  getModuleReferencesInOrder,\n} from '../utils'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\nimport { getProxiedPluginState } from '../../build-context'\nimport { PAGE_TYPES } from '../../../lib/page-types'\nimport { getModuleBuildInfo } from '../loaders/get-module-build-info'\nimport { getAssumedSourceType } from '../loaders/next-flight-loader'\nimport { isAppRouteRoute } from '../../../lib/is-app-route-route'\n\ninterface Options {\n  dev: boolean\n  appDir: string\n  isEdgeServer: boolean\n  encryptionKey: string\n}\n\nconst PLUGIN_NAME = 'FlightClientEntryPlugin'\n\ntype Actions = {\n  [actionId: string]: {\n    workers: {\n      [name: string]: { moduleId: string | number; async: boolean }\n    }\n    // Record which layer the action is in (rsc or sc_action), in the specific entry.\n    layer: {\n      [name: string]: string\n    }\n  }\n}\n\ntype ActionIdNamePair = [id: string, name: string]\n\nexport type ActionManifest = {\n  // Assign a unique encryption key during production build.\n  encryptionKey: string\n  node: Actions\n  edge: Actions\n}\n\nexport interface ModuleInfo {\n  moduleId: string | number\n  async: boolean\n}\n\nconst pluginState = getProxiedPluginState({\n  // A map to track \"action\" -> \"list of bundles\".\n  serverActions: {} as ActionManifest['node'],\n  edgeServerActions: {} as ActionManifest['edge'],\n\n  serverActionModules: {} as {\n    [workerName: string]: { server?: ModuleInfo; client?: ModuleInfo }\n  },\n\n  edgeServerActionModules: {} as {\n    [workerName: string]: { server?: ModuleInfo; client?: ModuleInfo }\n  },\n\n  ssrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n  edgeSsrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n\n  rscModules: {} as { [rscModuleId: string]: ModuleInfo },\n  edgeRscModules: {} as { [rscModuleId: string]: ModuleInfo },\n\n  injectedClientEntries: {} as Record<string, string>,\n})\n\nfunction deduplicateCSSImportsForEntry(mergedCSSimports: CssImports) {\n  // If multiple entry module connections are having the same CSS import,\n  // we only need to have one module to keep track of that CSS import.\n  // It is based on the fact that if a page or a layout is rendered in the\n  // given entry, all its parent layouts are always rendered too.\n  // This can avoid duplicate CSS imports in the generated CSS manifest,\n  // for example, if a page and its parent layout are both using the same\n  // CSS import, we only need to have the layout to keep track of that CSS\n  // import.\n  // To achieve this, we need to first collect all the CSS imports from\n  // every connection, and deduplicate them in the order of layers from\n  // top to bottom. The implementation can be generally described as:\n  // - Sort by number of `/` in the request path (the more `/`, the deeper)\n  // - When in the same depth, sort by the filename (template < layout < page and others)\n\n  // Sort the connections as described above.\n  const sortedCSSImports = Object.entries(mergedCSSimports).sort((a, b) => {\n    const [aPath] = a\n    const [bPath] = b\n\n    const aDepth = aPath.split('/').length\n    const bDepth = bPath.split('/').length\n\n    if (aDepth !== bDepth) {\n      return aDepth - bDepth\n    }\n\n    const aName = path.parse(aPath).name\n    const bName = path.parse(bPath).name\n\n    const indexA = ['template', 'layout'].indexOf(aName)\n    const indexB = ['template', 'layout'].indexOf(bName)\n\n    if (indexA === -1) return 1\n    if (indexB === -1) return -1\n    return indexA - indexB\n  })\n\n  const dedupedCSSImports: CssImports = {}\n  const trackedCSSImports = new Set<string>()\n  for (const [entryName, cssImports] of sortedCSSImports) {\n    for (const cssImport of cssImports) {\n      if (trackedCSSImports.has(cssImport)) continue\n\n      // Only track CSS imports that are in files that can inherit CSS.\n      const filename = path.parse(entryName).name\n      if (['template', 'layout'].includes(filename)) {\n        trackedCSSImports.add(cssImport)\n      }\n\n      if (!dedupedCSSImports[entryName]) {\n        dedupedCSSImports[entryName] = []\n      }\n      dedupedCSSImports[entryName].push(cssImport)\n    }\n  }\n\n  return dedupedCSSImports\n}\n\nexport class FlightClientEntryPlugin {\n  dev: boolean\n  appDir: string\n  encryptionKey: string\n  isEdgeServer: boolean\n  assetPrefix: string\n  webpackRuntime: string\n\n  constructor(options: Options) {\n    this.dev = options.dev\n    this.appDir = options.appDir\n    this.isEdgeServer = options.isEdgeServer\n    this.assetPrefix = !this.dev && !this.isEdgeServer ? '../' : ''\n    this.encryptionKey = options.encryptionKey\n    this.webpackRuntime = this.isEdgeServer\n      ? EDGE_RUNTIME_WEBPACK\n      : DEFAULT_RUNTIME_WEBPACK\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(\n      PLUGIN_NAME,\n      (compilation, { normalModuleFactory }) => {\n        compilation.dependencyFactories.set(\n          webpack.dependencies.ModuleDependency,\n          normalModuleFactory\n        )\n        compilation.dependencyTemplates.set(\n          webpack.dependencies.ModuleDependency,\n          new webpack.dependencies.NullDependency.Template()\n        )\n      }\n    )\n\n    compiler.hooks.finishMake.tapPromise(PLUGIN_NAME, (compilation) =>\n      this.createClientEntries(compiler, compilation)\n    )\n\n    compiler.hooks.afterCompile.tap(PLUGIN_NAME, (compilation) => {\n      const recordModule = (modId: string, mod: any) => {\n        // Match Resource is undefined unless an import is using the inline match resource syntax\n        // https://webpack.js.org/api/loaders/#inline-matchresource\n        const modPath = mod.matchResource || mod.resourceResolveData?.path\n        const modQuery = mod.resourceResolveData?.query || ''\n        // query is already part of mod.resource\n        // so it's only necessary to add it for matchResource or mod.resourceResolveData\n        const modResource = modPath\n          ? modPath.startsWith(BARREL_OPTIMIZATION_PREFIX)\n            ? formatBarrelOptimizedResource(mod.resource, modPath)\n            : modPath + modQuery\n          : mod.resource\n\n        if (typeof modId !== 'undefined' && modResource) {\n          if (mod.layer === WEBPACK_LAYERS.reactServerComponents) {\n            const key = path\n              .relative(compiler.context, modResource)\n              .replace(/\\/next\\/dist\\/esm\\//, '/next/dist/')\n\n            const moduleInfo: ModuleInfo = {\n              moduleId: modId,\n              async: compilation.moduleGraph.isAsync(mod),\n            }\n\n            if (this.isEdgeServer) {\n              pluginState.edgeRscModules[key] = moduleInfo\n            } else {\n              pluginState.rscModules[key] = moduleInfo\n            }\n          }\n        }\n\n        if (mod.layer !== WEBPACK_LAYERS.serverSideRendering) {\n          return\n        }\n\n        // Check mod resource to exclude the empty resource module like virtual module created by next-flight-client-entry-loader\n        if (typeof modId !== 'undefined' && modResource) {\n          // Note that this isn't that reliable as webpack is still possible to assign\n          // additional queries to make sure there's no conflict even using the `named`\n          // module ID strategy.\n          let ssrNamedModuleId = path.relative(compiler.context, modResource)\n\n          if (!ssrNamedModuleId.startsWith('.')) {\n            // TODO use getModuleId instead\n            ssrNamedModuleId = `./${normalizePathSep(ssrNamedModuleId)}`\n          }\n\n          const moduleInfo: ModuleInfo = {\n            moduleId: modId,\n            async: compilation.moduleGraph.isAsync(mod),\n          }\n\n          if (this.isEdgeServer) {\n            pluginState.edgeSsrModules[\n              ssrNamedModuleId.replace(/\\/next\\/dist\\/esm\\//, '/next/dist/')\n            ] = moduleInfo\n          } else {\n            pluginState.ssrModules[ssrNamedModuleId] = moduleInfo\n          }\n        }\n      }\n\n      traverseModules(compilation, (mod, _chunk, _chunkGroup, modId) => {\n        if (modId) recordModule(modId, mod)\n      })\n    })\n\n    compiler.hooks.make.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.processAssets.tapPromise(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_HASH,\n        },\n        (assets) => this.createActionAssets(compilation, assets)\n      )\n    })\n  }\n\n  async createClientEntries(\n    compiler: webpack.Compiler,\n    compilation: webpack.Compilation\n  ) {\n    const addClientEntryAndSSRModulesList: Array<\n      ReturnType<typeof this.injectClientEntryAndSSRModules>\n    > = []\n    const createdSSRDependenciesForEntry: Record<\n      string,\n      ReturnType<typeof this.injectClientEntryAndSSRModules>[3][]\n    > = {}\n\n    const addActionEntryList: Array<ReturnType<typeof this.injectActionEntry>> =\n      []\n    const actionMapsPerEntry: Record<\n      string,\n      Map<string, ActionIdNamePair[]>\n    > = {}\n    const createdActionIds = new Set<string>()\n\n    // For each SC server compilation entry, we need to create its corresponding\n    // client component entry.\n    forEachEntryModule(compilation, ({ name, entryModule }) => {\n      const internalClientComponentEntryImports: ClientComponentImports = {}\n      const actionEntryImports = new Map<string, ActionIdNamePair[]>()\n      const clientEntriesToInject = []\n      const mergedCSSimports: CssImports = {}\n\n      for (const connection of getModuleReferencesInOrder(\n        entryModule,\n        compilation.moduleGraph\n      )) {\n        // Entry can be any user defined entry files such as layout, page, error, loading, etc.\n        const entryRequest = (\n          connection.dependency as unknown as webpack.NormalModule\n        ).request\n\n        const { clientComponentImports, actionImports, cssImports } =\n          this.collectComponentInfoFromServerEntryDependency({\n            entryRequest,\n            compilation,\n            resolvedModule: connection.resolvedModule,\n          })\n\n        actionImports.forEach(([dep, actions]) =>\n          actionEntryImports.set(dep, actions)\n        )\n\n        const isAbsoluteRequest = path.isAbsolute(entryRequest)\n\n        // Next.js internals are put into a separate entry.\n        if (!isAbsoluteRequest) {\n          Object.keys(clientComponentImports).forEach(\n            (value) => (internalClientComponentEntryImports[value] = new Set())\n          )\n          continue\n        }\n\n        // TODO-APP: Enable these lines. This ensures no entrypoint is created for layout/page when there are no client components.\n        // Currently disabled because it causes test failures in CI.\n        // if (clientImports.length === 0 && actionImports.length === 0) {\n        //   continue\n        // }\n\n        const relativeRequest = isAbsoluteRequest\n          ? path.relative(compilation.options.context!, entryRequest)\n          : entryRequest\n\n        // Replace file suffix as `.js` will be added.\n        const bundlePath = normalizePathSep(\n          relativeRequest.replace(/\\.[^.\\\\/]+$/, '').replace(/^src[\\\\/]/, '')\n        )\n\n        Object.assign(mergedCSSimports, cssImports)\n        clientEntriesToInject.push({\n          compiler,\n          compilation,\n          entryName: name,\n          clientComponentImports,\n          bundlePath,\n          absolutePagePath: entryRequest,\n        })\n\n        // The webpack implementation of writing the client reference manifest relies on all entrypoints writing a page.js even when there is no client components in the page.\n        // It needs the file in order to write the reference manifest for the path in the `.next/server` folder.\n        // TODO-APP: This could be better handled, however Turbopack does not have the same problem as we resolve client components in a single graph.\n        if (\n          name === `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}` &&\n          bundlePath === 'app/not-found'\n        ) {\n          clientEntriesToInject.push({\n            compiler,\n            compilation,\n            entryName: name,\n            clientComponentImports: {},\n            bundlePath: `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}`,\n            absolutePagePath: entryRequest,\n          })\n        }\n      }\n\n      // Make sure CSS imports are deduplicated before injecting the client entry\n      // and SSR modules.\n      const dedupedCSSImports = deduplicateCSSImportsForEntry(mergedCSSimports)\n      for (const clientEntryToInject of clientEntriesToInject) {\n        const injected = this.injectClientEntryAndSSRModules({\n          ...clientEntryToInject,\n          clientImports: {\n            ...clientEntryToInject.clientComponentImports,\n            ...(\n              dedupedCSSImports[clientEntryToInject.absolutePagePath] || []\n            ).reduce<ClientComponentImports>((res, curr) => {\n              res[curr] = new Set()\n              return res\n            }, {}),\n          },\n        })\n\n        // Track all created SSR dependencies for each entry from the server layer.\n        if (!createdSSRDependenciesForEntry[clientEntryToInject.entryName]) {\n          createdSSRDependenciesForEntry[clientEntryToInject.entryName] = []\n        }\n        createdSSRDependenciesForEntry[clientEntryToInject.entryName].push(\n          injected[3]\n        )\n\n        addClientEntryAndSSRModulesList.push(injected)\n      }\n\n      if (!isAppRouteRoute(name)) {\n        // Create internal app\n        addClientEntryAndSSRModulesList.push(\n          this.injectClientEntryAndSSRModules({\n            compiler,\n            compilation,\n            entryName: name,\n            clientImports: { ...internalClientComponentEntryImports },\n            bundlePath: APP_CLIENT_INTERNALS,\n          })\n        )\n      }\n\n      if (actionEntryImports.size > 0) {\n        if (!actionMapsPerEntry[name]) {\n          actionMapsPerEntry[name] = new Map()\n        }\n        actionMapsPerEntry[name] = new Map([\n          ...actionMapsPerEntry[name],\n          ...actionEntryImports,\n        ])\n      }\n    })\n\n    for (const [name, actionEntryImports] of Object.entries(\n      actionMapsPerEntry\n    )) {\n      addActionEntryList.push(\n        this.injectActionEntry({\n          compiler,\n          compilation,\n          actions: actionEntryImports,\n          entryName: name,\n          bundlePath: name,\n          createdActionIds,\n        })\n      )\n    }\n\n    // Invalidate in development to trigger recompilation\n    const invalidator = getInvalidator(compiler.outputPath)\n    // Check if any of the entry injections need an invalidation\n    if (\n      invalidator &&\n      addClientEntryAndSSRModulesList.some(\n        ([shouldInvalidate]) => shouldInvalidate === true\n      )\n    ) {\n      invalidator.invalidate([COMPILER_NAMES.client])\n    }\n\n    // Client compiler is invalidated before awaiting the compilation of the SSR\n    // and RSC client component entries so that the client compiler is running\n    // in parallel to the server compiler.\n    await Promise.all(\n      addClientEntryAndSSRModulesList.flatMap((addClientEntryAndSSRModules) => [\n        addClientEntryAndSSRModules[1],\n        addClientEntryAndSSRModules[2],\n      ])\n    )\n\n    // Wait for action entries to be added.\n    await Promise.all(addActionEntryList)\n\n    const addedClientActionEntryList: Promise<any>[] = []\n    const actionMapsPerClientEntry: Record<\n      string,\n      Map<string, ActionIdNamePair[]>\n    > = {}\n\n    // We need to create extra action entries that are created from the\n    // client layer.\n    // Start from each entry's created SSR dependency from our previous step.\n    for (const [name, ssrEntryDependencies] of Object.entries(\n      createdSSRDependenciesForEntry\n    )) {\n      // Collect from all entries, e.g. layout.js, page.js, loading.js, ...\n      // add aggregate them.\n      const actionEntryImports = this.collectClientActionsFromDependencies({\n        compilation,\n        dependencies: ssrEntryDependencies,\n      })\n\n      if (actionEntryImports.size > 0) {\n        if (!actionMapsPerClientEntry[name]) {\n          actionMapsPerClientEntry[name] = new Map()\n        }\n        actionMapsPerClientEntry[name] = new Map([\n          ...actionMapsPerClientEntry[name],\n          ...actionEntryImports,\n        ])\n      }\n    }\n\n    for (const [entryName, actionEntryImports] of Object.entries(\n      actionMapsPerClientEntry\n    )) {\n      // If an action method is already created in the server layer, we don't\n      // need to create it again in the action layer.\n      // This is to avoid duplicate action instances and make sure the module\n      // state is shared.\n      let remainingClientImportedActions = false\n      const remainingActionEntryImports = new Map<string, ActionIdNamePair[]>()\n      for (const [dep, actions] of actionEntryImports) {\n        const remainingActionNames = []\n        for (const action of actions) {\n          // `action` is a [id, name] pair.\n          if (!createdActionIds.has(entryName + '@' + action[0])) {\n            remainingActionNames.push(action)\n          }\n        }\n        if (remainingActionNames.length > 0) {\n          remainingActionEntryImports.set(dep, remainingActionNames)\n          remainingClientImportedActions = true\n        }\n      }\n\n      if (remainingClientImportedActions) {\n        addedClientActionEntryList.push(\n          this.injectActionEntry({\n            compiler,\n            compilation,\n            actions: remainingActionEntryImports,\n            entryName,\n            bundlePath: entryName,\n            fromClient: true,\n            createdActionIds,\n          })\n        )\n      }\n    }\n\n    await Promise.all(addedClientActionEntryList)\n  }\n\n  collectClientActionsFromDependencies({\n    compilation,\n    dependencies,\n  }: {\n    compilation: webpack.Compilation\n    dependencies: ReturnType<typeof webpack.EntryPlugin.createDependency>[]\n  }) {\n    // action file path -> action names\n    const collectedActions = new Map<string, ActionIdNamePair[]>()\n\n    // Keep track of checked modules to avoid infinite loops with recursive imports.\n    const visitedModule = new Set<string>()\n    const visitedEntry = new Set<string>()\n\n    const collectActions = ({\n      entryRequest,\n      resolvedModule,\n    }: {\n      entryRequest: string\n      resolvedModule: any\n    }) => {\n      const collectActionsInDep = (mod: webpack.NormalModule): void => {\n        if (!mod) return\n\n        const modResource = getModuleResource(mod)\n\n        if (!modResource) return\n\n        if (visitedModule.has(modResource)) return\n        visitedModule.add(modResource)\n\n        const actions = getActionsFromBuildInfo(mod)\n        if (actions) {\n          collectedActions.set(modResource, Object.entries(actions))\n        }\n\n        // Collect used exported actions transversely.\n        getModuleReferencesInOrder(mod, compilation.moduleGraph).forEach(\n          (connection: any) => {\n            collectActionsInDep(\n              connection.resolvedModule as webpack.NormalModule\n            )\n          }\n        )\n      }\n\n      // Don't traverse the module graph anymore once hitting the action layer.\n      if (\n        entryRequest &&\n        !entryRequest.includes('next-flight-action-entry-loader')\n      ) {\n        // Traverse the module graph to find all client components.\n        collectActionsInDep(resolvedModule)\n      }\n    }\n\n    for (const entryDependency of dependencies) {\n      const ssrEntryModule =\n        compilation.moduleGraph.getResolvedModule(entryDependency)!\n      for (const connection of getModuleReferencesInOrder(\n        ssrEntryModule,\n        compilation.moduleGraph\n      )) {\n        const depModule = connection.dependency\n        const request = (depModule as unknown as webpack.NormalModule).request\n\n        // It is possible that the same entry is added multiple times in the\n        // connection graph. We can just skip these to speed up the process.\n        if (visitedEntry.has(request)) continue\n        visitedEntry.add(request)\n\n        collectActions({\n          entryRequest: request,\n          resolvedModule: connection.resolvedModule,\n        })\n      }\n    }\n\n    return collectedActions\n  }\n\n  collectComponentInfoFromServerEntryDependency({\n    entryRequest,\n    compilation,\n    resolvedModule,\n  }: {\n    entryRequest: string\n    compilation: webpack.Compilation\n    resolvedModule: any /* Dependency */\n  }): {\n    cssImports: CssImports\n    clientComponentImports: ClientComponentImports\n    actionImports: [string, ActionIdNamePair[]][]\n  } {\n    // Keep track of checked modules to avoid infinite loops with recursive imports.\n    const visitedOfClientComponentsTraverse = new Set()\n\n    // Info to collect.\n    const clientComponentImports: ClientComponentImports = {}\n    const actionImports: [string, ActionIdNamePair[]][] = []\n    const CSSImports = new Set<string>()\n\n    const filterClientComponents = (\n      mod: webpack.NormalModule,\n      importedIdentifiers: string[]\n    ): void => {\n      if (!mod) return\n\n      const modResource = getModuleResource(mod)\n\n      if (!modResource) return\n      if (visitedOfClientComponentsTraverse.has(modResource)) {\n        if (clientComponentImports[modResource]) {\n          addClientImport(\n            mod,\n            modResource,\n            clientComponentImports,\n            importedIdentifiers,\n            false\n          )\n        }\n        return\n      }\n      visitedOfClientComponentsTraverse.add(modResource)\n\n      const actions = getActionsFromBuildInfo(mod)\n      if (actions) {\n        actionImports.push([modResource, Object.entries(actions)])\n      }\n\n      if (isCSSMod(mod)) {\n        const sideEffectFree =\n          mod.factoryMeta && (mod.factoryMeta as any).sideEffectFree\n\n        if (sideEffectFree) {\n          const unused = !compilation.moduleGraph\n            .getExportsInfo(mod)\n            .isModuleUsed(this.webpackRuntime)\n\n          if (unused) return\n        }\n\n        CSSImports.add(modResource)\n      } else if (isClientComponentEntryModule(mod)) {\n        if (!clientComponentImports[modResource]) {\n          clientComponentImports[modResource] = new Set()\n        }\n        addClientImport(\n          mod,\n          modResource,\n          clientComponentImports,\n          importedIdentifiers,\n          true\n        )\n\n        return\n      }\n\n      getModuleReferencesInOrder(mod, compilation.moduleGraph).forEach(\n        (connection: any) => {\n          let dependencyIds: string[] = []\n\n          // `ids` are the identifiers that are imported from the dependency,\n          // if it's present, it's an array of strings.\n          if (connection.dependency?.ids) {\n            dependencyIds.push(...connection.dependency.ids)\n          } else {\n            dependencyIds = ['*']\n          }\n\n          filterClientComponents(connection.resolvedModule, dependencyIds)\n        }\n      )\n    }\n\n    // Traverse the module graph to find all client components.\n    filterClientComponents(resolvedModule, [])\n\n    return {\n      clientComponentImports,\n      cssImports: CSSImports.size\n        ? {\n            [entryRequest]: Array.from(CSSImports),\n          }\n        : {},\n      actionImports,\n    }\n  }\n\n  injectClientEntryAndSSRModules({\n    compiler,\n    compilation,\n    entryName,\n    clientImports,\n    bundlePath,\n    absolutePagePath,\n  }: {\n    compiler: webpack.Compiler\n    compilation: webpack.Compilation\n    entryName: string\n    clientImports: ClientComponentImports\n    bundlePath: string\n    absolutePagePath?: string\n  }): [\n    shouldInvalidate: boolean,\n    addSSREntryPromise: Promise<void>,\n    addRSCEntryPromise: Promise<void>,\n    ssrDep: ReturnType<typeof webpack.EntryPlugin.createDependency>,\n  ] {\n    let shouldInvalidate = false\n\n    const modules = Object.keys(clientImports)\n      .sort((a, b) => (regexCSS.test(b) ? 1 : a.localeCompare(b)))\n      .map((clientImportPath) => ({\n        request: clientImportPath,\n        ids: [...clientImports[clientImportPath]],\n      }))\n\n    // For the client entry, we always use the CJS build of Next.js. If the\n    // server is using the ESM build (when using the Edge runtime), we need to\n    // replace them.\n    const clientBrowserLoader = `next-flight-client-entry-loader?${stringify({\n      modules: (this.isEdgeServer\n        ? modules.map(({ request, ids }) => ({\n            request: request.replace(\n              /[\\\\/]next[\\\\/]dist[\\\\/]esm[\\\\/]/,\n              '/next/dist/'.replace(/\\//g, path.sep)\n            ),\n            ids,\n          }))\n        : modules\n      ).map((x) => JSON.stringify(x)),\n      server: false,\n    })}!`\n\n    const clientServerLoader = `next-flight-client-entry-loader?${stringify({\n      modules: modules.map((x) => JSON.stringify(x)),\n      server: true,\n    })}!`\n\n    // Add for the client compilation\n    // Inject the entry to the client compiler.\n    if (this.dev) {\n      const entries = getEntries(compiler.outputPath)\n      const pageKey = getEntryKey(\n        COMPILER_NAMES.client,\n        PAGE_TYPES.APP,\n        bundlePath\n      )\n\n      if (!entries[pageKey]) {\n        entries[pageKey] = {\n          type: EntryTypes.CHILD_ENTRY,\n          parentEntries: new Set([entryName]),\n          absoluteEntryFilePath: absolutePagePath,\n          bundlePath,\n          request: clientBrowserLoader,\n          dispose: false,\n          lastActiveTime: Date.now(),\n        }\n        shouldInvalidate = true\n      } else {\n        const entryData = entries[pageKey]\n        // New version of the client loader\n        if (entryData.request !== clientBrowserLoader) {\n          entryData.request = clientBrowserLoader\n          shouldInvalidate = true\n        }\n        if (entryData.type === EntryTypes.CHILD_ENTRY) {\n          entryData.parentEntries.add(entryName)\n        }\n        entryData.dispose = false\n        entryData.lastActiveTime = Date.now()\n      }\n    } else {\n      pluginState.injectedClientEntries[bundlePath] = clientBrowserLoader\n    }\n\n    const clientComponentSSREntryDep = webpack.EntryPlugin.createDependency(\n      clientServerLoader,\n      { name: bundlePath }\n    )\n\n    const clientComponentRSCEntryDep = webpack.EntryPlugin.createDependency(\n      clientServerLoader,\n      { name: bundlePath }\n    )\n\n    return [\n      shouldInvalidate,\n      // Add the entries to the server compiler for the SSR and RSC layers. The\n      // promises are awaited later using `Promise.all` in order to parallelize\n      // adding the entries.\n      this.addEntry(compilation, compiler.context, clientComponentSSREntryDep, {\n        name: entryName,\n        layer: WEBPACK_LAYERS.serverSideRendering,\n      }),\n      this.addEntry(compilation, compiler.context, clientComponentRSCEntryDep, {\n        name: entryName,\n        layer: WEBPACK_LAYERS.reactServerComponents,\n      }),\n      clientComponentSSREntryDep,\n    ]\n  }\n\n  injectActionEntry({\n    compiler,\n    compilation,\n    actions,\n    entryName,\n    bundlePath,\n    fromClient,\n    createdActionIds,\n  }: {\n    compiler: webpack.Compiler\n    compilation: webpack.Compilation\n    actions: Map<string, ActionIdNamePair[]>\n    entryName: string\n    bundlePath: string\n    createdActionIds: Set<string>\n    fromClient?: boolean\n  }) {\n    const actionsArray = Array.from(actions.entries())\n    for (const [, actionsFromModule] of actions) {\n      for (const [id] of actionsFromModule) {\n        createdActionIds.add(entryName + '@' + id)\n      }\n    }\n\n    if (actionsArray.length === 0) {\n      return Promise.resolve()\n    }\n\n    const actionLoader = `next-flight-action-entry-loader?${stringify({\n      actions: JSON.stringify(actionsArray),\n      __client_imported__: fromClient,\n    })}!`\n\n    const currentCompilerServerActions = this.isEdgeServer\n      ? pluginState.edgeServerActions\n      : pluginState.serverActions\n\n    for (const [, actionsFromModule] of actionsArray) {\n      for (const [id] of actionsFromModule) {\n        if (typeof currentCompilerServerActions[id] === 'undefined') {\n          currentCompilerServerActions[id] = {\n            workers: {},\n            layer: {},\n          }\n        }\n        currentCompilerServerActions[id].workers[bundlePath] = {\n          moduleId: '', // TODO: What's the meaning of this?\n          async: false,\n        }\n\n        currentCompilerServerActions[id].layer[bundlePath] = fromClient\n          ? WEBPACK_LAYERS.actionBrowser\n          : WEBPACK_LAYERS.reactServerComponents\n      }\n    }\n\n    // Inject the entry to the server compiler\n    const actionEntryDep = webpack.EntryPlugin.createDependency(actionLoader, {\n      name: bundlePath,\n    })\n\n    return this.addEntry(\n      compilation,\n      // Reuse compilation context.\n      compiler.context,\n      actionEntryDep,\n      {\n        name: entryName,\n        layer: fromClient\n          ? WEBPACK_LAYERS.actionBrowser\n          : WEBPACK_LAYERS.reactServerComponents,\n      }\n    )\n  }\n\n  addEntry(\n    compilation: any,\n    context: string,\n    dependency: webpack.Dependency,\n    options: webpack.EntryOptions\n  ): Promise<any> /* Promise<module> */ {\n    return new Promise((resolve, reject) => {\n      const entry = compilation.entries.get(options.name)\n      entry.includeDependencies.push(dependency)\n      compilation.hooks.addEntry.call(entry, options)\n      compilation.addModuleTree(\n        {\n          context,\n          dependency,\n          contextInfo: { issuerLayer: options.layer },\n        },\n        (err: Error | undefined, module: any) => {\n          if (err) {\n            compilation.hooks.failedEntry.call(dependency, options, err)\n            return reject(err)\n          }\n\n          compilation.hooks.succeedEntry.call(dependency, options, module)\n\n          compilation.moduleGraph\n            .getExportsInfo(module)\n            .setUsedInUnknownWay(\n              this.isEdgeServer ? EDGE_RUNTIME_WEBPACK : DEFAULT_RUNTIME_WEBPACK\n            )\n\n          return resolve(module)\n        }\n      )\n    })\n  }\n\n  async createActionAssets(\n    compilation: webpack.Compilation,\n    assets: webpack.Compilation['assets']\n  ) {\n    const serverActions: ActionManifest['node'] = {}\n    const edgeServerActions: ActionManifest['edge'] = {}\n\n    traverseModules(compilation, (mod, _chunk, chunkGroup, modId) => {\n      // Go through all action entries and record the module ID for each entry.\n      if (\n        chunkGroup.name &&\n        mod.request &&\n        modId &&\n        /next-flight-action-entry-loader/.test(mod.request)\n      ) {\n        const fromClient = /&__client_imported__=true/.test(mod.request)\n\n        const mapping = this.isEdgeServer\n          ? pluginState.edgeServerActionModules\n          : pluginState.serverActionModules\n\n        if (!mapping[chunkGroup.name]) {\n          mapping[chunkGroup.name] = {}\n        }\n        mapping[chunkGroup.name][fromClient ? 'client' : 'server'] = {\n          moduleId: modId,\n          async: compilation.moduleGraph.isAsync(mod),\n        }\n      }\n    })\n\n    for (let id in pluginState.serverActions) {\n      const action = pluginState.serverActions[id]\n      for (let name in action.workers) {\n        const modId =\n          pluginState.serverActionModules[name][\n            action.layer[name] === WEBPACK_LAYERS.actionBrowser\n              ? 'client'\n              : 'server'\n          ]\n        action.workers[name] = modId!\n      }\n      serverActions[id] = action\n    }\n\n    for (let id in pluginState.edgeServerActions) {\n      const action = pluginState.edgeServerActions[id]\n      for (let name in action.workers) {\n        const modId =\n          pluginState.edgeServerActionModules[name][\n            action.layer[name] === WEBPACK_LAYERS.actionBrowser\n              ? 'client'\n              : 'server'\n          ]\n        action.workers[name] = modId!\n      }\n      edgeServerActions[id] = action\n    }\n\n    const serverManifest = {\n      node: serverActions,\n      edge: edgeServerActions,\n      encryptionKey: this.encryptionKey,\n    }\n    const edgeServerManifest = {\n      ...serverManifest,\n      encryptionKey: 'process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY',\n    }\n\n    const json = JSON.stringify(serverManifest, null, this.dev ? 2 : undefined)\n    const edgeJson = JSON.stringify(\n      edgeServerManifest,\n      null,\n      this.dev ? 2 : undefined\n    )\n\n    assets[`${this.assetPrefix}${SERVER_REFERENCE_MANIFEST}.js`] =\n      new sources.RawSource(\n        `self.__RSC_SERVER_MANIFEST=${JSON.stringify(edgeJson)}`\n      ) as unknown as webpack.sources.RawSource\n    assets[`${this.assetPrefix}${SERVER_REFERENCE_MANIFEST}.json`] =\n      new sources.RawSource(json) as unknown as webpack.sources.RawSource\n  }\n}\n\nfunction addClientImport(\n  mod: webpack.NormalModule,\n  modRequest: string,\n  clientComponentImports: ClientComponentImports,\n  importedIdentifiers: string[],\n  isFirstVisitModule: boolean\n) {\n  const clientEntryType = getModuleBuildInfo(mod).rsc?.clientEntryType\n  const isCjsModule = clientEntryType === 'cjs'\n  const assumedSourceType = getAssumedSourceType(\n    mod,\n    isCjsModule ? 'commonjs' : 'auto'\n  )\n\n  const clientImportsSet = clientComponentImports[modRequest]\n\n  if (importedIdentifiers[0] === '*') {\n    // If there's collected import path with named import identifiers,\n    // or there's nothing in collected imports are empty.\n    // we should include the whole module.\n    if (!isFirstVisitModule && [...clientImportsSet][0] !== '*') {\n      clientComponentImports[modRequest] = new Set(['*'])\n    }\n  } else {\n    const isAutoModuleSourceType = assumedSourceType === 'auto'\n    if (isAutoModuleSourceType) {\n      clientComponentImports[modRequest] = new Set(['*'])\n    } else {\n      // If it's not analyzed as named ESM exports, e.g. if it's mixing `export *` with named exports,\n      // We'll include all modules since it's not able to do tree-shaking.\n      for (const name of importedIdentifiers) {\n        // For cjs module default import, we include the whole module since\n        const isCjsDefaultImport = isCjsModule && name === 'default'\n\n        // Always include __esModule along with cjs module default export,\n        // to make sure it work with client module proxy from React.\n        if (isCjsDefaultImport) {\n          clientComponentImports[modRequest].add('__esModule')\n        }\n\n        clientComponentImports[modRequest].add(name)\n      }\n    }\n  }\n}\n\nfunction getModuleResource(mod: webpack.NormalModule): string {\n  const modPath: string = mod.resourceResolveData?.path || ''\n  const modQuery = mod.resourceResolveData?.query || ''\n  // We have to always use the resolved request here to make sure the\n  // server and client are using the same module path (required by RSC), as\n  // the server compiler and client compiler have different resolve configs.\n  let modResource: string = modPath + modQuery\n\n  // Context modules don't have a resource path, we use the identifier instead.\n  if (mod.constructor.name === 'ContextModule') {\n    modResource = mod.identifier()\n  }\n\n  // For the barrel optimization, we need to use the match resource instead\n  // because there will be 2 modules for the same file (same resource path)\n  // but they're different modules and can't be deduped via `visitedModule`.\n  // The first module is a virtual re-export module created by the loader.\n  if (mod.matchResource?.startsWith(BARREL_OPTIMIZATION_PREFIX)) {\n    modResource = mod.matchResource + ':' + modResource\n  }\n  return modResource\n}\n"], "names": ["FlightClientEntryPlugin", "PLUGIN_NAME", "pluginState", "getProxiedPluginState", "serverActions", "edgeServerActions", "serverActionModules", "edgeServerActionModules", "ssrModules", "edgeSsrModules", "rscModules", "edgeRscModules", "injectedClientEntries", "deduplicateCSSImportsForEntry", "mergedCSSimports", "sortedCSSImports", "Object", "entries", "sort", "a", "b", "a<PERSON><PERSON>", "bPath", "a<PERSON><PERSON><PERSON>", "split", "length", "b<PERSON><PERSON><PERSON>", "aName", "path", "parse", "name", "bName", "indexA", "indexOf", "indexB", "dedupedCSSImports", "trackedCSSImports", "Set", "entryName", "cssImports", "cssImport", "has", "filename", "includes", "add", "push", "constructor", "options", "dev", "appDir", "isEdgeServer", "assetPrefix", "<PERSON><PERSON><PERSON>", "webpackRuntime", "EDGE_RUNTIME_WEBPACK", "DEFAULT_RUNTIME_WEBPACK", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "dependencyFactories", "set", "webpack", "dependencies", "ModuleDependency", "dependencyTemplates", "NullDependency", "Template", "finishMake", "tapPromise", "createClientEntries", "afterCompile", "recordModule", "modId", "mod", "modPath", "matchResource", "resourceResolveData", "mod<PERSON><PERSON><PERSON>", "query", "modResource", "startsWith", "BARREL_OPTIMIZATION_PREFIX", "formatBarrelOptimizedResource", "resource", "layer", "WEBPACK_LAYERS", "reactServerComponents", "key", "relative", "context", "replace", "moduleInfo", "moduleId", "async", "moduleGraph", "isAsync", "serverSideRendering", "ssrNamedModuleId", "normalizePathSep", "traverseModules", "_chunk", "_chunkGroup", "make", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "assets", "createActionAssets", "addClientEntryAndSSRModulesList", "createdSSRDependenciesForEntry", "addActionEntryList", "actionMapsPerEntry", "createdActionIds", "forEachEntryModule", "entryModule", "internalClientComponentEntryImports", "actionEntryImports", "Map", "clientEntriesToInject", "connection", "getModuleReferencesInOrder", "entryRequest", "dependency", "request", "clientComponentImports", "actionImports", "collectComponentInfoFromServerEntryDependency", "resolvedModule", "for<PERSON>ach", "dep", "actions", "isAbsoluteRequest", "isAbsolute", "keys", "value", "relativeRequest", "bundlePath", "assign", "absolutePagePath", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "clientEntryToInject", "injected", "injectClientEntryAndSSRModules", "clientImports", "reduce", "res", "curr", "isAppRouteRoute", "APP_CLIENT_INTERNALS", "size", "injectActionEntry", "invalidator", "getInvalidator", "outputPath", "some", "shouldInvalidate", "invalidate", "COMPILER_NAMES", "client", "Promise", "all", "flatMap", "addClientEntryAndSSRModules", "addedClientActionEntryList", "actionMapsPerClientEntry", "ssrEntryDependencies", "collectClientActionsFromDependencies", "remainingClientImportedActions", "remainingActionEntryImports", "remainingActionNames", "action", "fromClient", "collectedActions", "visitedModule", "visitedEntry", "collectActions", "collectActionsInDep", "getModuleResource", "getActionsFromBuildInfo", "entryDependency", "ssrEntryModule", "getResolvedModule", "depModule", "visitedOfClientComponentsTraverse", "CSSImports", "filterClientComponents", "importedIdentifiers", "addClientImport", "isCSSMod", "sideEffectFree", "factoryMeta", "unused", "getExportsInfo", "isModuleUsed", "isClientComponentEntryModule", "dependencyIds", "ids", "Array", "from", "modules", "regexCSS", "test", "localeCompare", "map", "clientImportPath", "clientBrowserLoader", "stringify", "sep", "x", "JSON", "server", "clientServerLoader", "getEntries", "page<PERSON><PERSON>", "getEntry<PERSON>ey", "PAGE_TYPES", "APP", "type", "EntryTypes", "CHILD_ENTRY", "parentEntries", "absoluteEntryFilePath", "dispose", "lastActiveTime", "Date", "now", "entryData", "clientComponentSSREntryDep", "EntryPlugin", "createDependency", "clientComponentRSCEntryDep", "addEntry", "actionsArray", "actionsFromModule", "id", "resolve", "actionLoader", "__client_imported__", "currentCompilerServerActions", "workers", "<PERSON><PERSON><PERSON><PERSON>", "actionEntryDep", "reject", "entry", "get", "includeDependencies", "call", "addModuleTree", "contextInfo", "issuer<PERSON><PERSON>er", "err", "module", "failedEntry", "<PERSON><PERSON><PERSON><PERSON>", "setUsedInUnknownWay", "chunkGroup", "mapping", "serverManifest", "node", "edge", "edgeServerManifest", "json", "undefined", "edgeJson", "SERVER_REFERENCE_MANIFEST", "sources", "RawSource", "modRequest", "isFirstVisitModule", "getModuleBuildInfo", "clientEntryType", "rsc", "isCjsModule", "assumedSourceType", "getAssumedSourceType", "clientImportsSet", "isAutoModuleSourceType", "isCjsDefaultImport", "identifier"], "mappings": ";;;;+BAiKaA;;;eAAAA;;;yBA5JW;6BACE;6DACT;sCAOV;2BACwB;4BASxB;uBAMA;wBAMA;kCAC0B;8BACK;2BACX;oCACQ;kCACE;iCACL;;;;;;AAShC,MAAMC,cAAc;AA4BpB,MAAMC,cAAcC,IAAAA,mCAAqB,EAAC;IACxC,gDAAgD;IAChDC,eAAe,CAAC;IAChBC,mBAAmB,CAAC;IAEpBC,qBAAqB,CAAC;IAItBC,yBAAyB,CAAC;IAI1BC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,uBAAuB,CAAC;AAC1B;AAEA,SAASC,8BAA8BC,gBAA4B;IACjE,uEAAuE;IACvE,oEAAoE;IACpE,wEAAwE;IACxE,+DAA+D;IAC/D,sEAAsE;IACtE,uEAAuE;IACvE,wEAAwE;IACxE,UAAU;IACV,qEAAqE;IACrE,qEAAqE;IACrE,mEAAmE;IACnE,yEAAyE;IACzE,uFAAuF;IAEvF,2CAA2C;IAC3C,MAAMC,mBAAmBC,OAAOC,OAAO,CAACH,kBAAkBI,IAAI,CAAC,CAACC,GAAGC;QACjE,MAAM,CAACC,MAAM,GAAGF;QAChB,MAAM,CAACG,MAAM,GAAGF;QAEhB,MAAMG,SAASF,MAAMG,KAAK,CAAC,KAAKC,MAAM;QACtC,MAAMC,SAASJ,MAAME,KAAK,CAAC,KAAKC,MAAM;QAEtC,IAAIF,WAAWG,QAAQ;YACrB,OAAOH,SAASG;QAClB;QAEA,MAAMC,QAAQC,aAAI,CAACC,KAAK,CAACR,OAAOS,IAAI;QACpC,MAAMC,QAAQH,aAAI,CAACC,KAAK,CAACP,OAAOQ,IAAI;QAEpC,MAAME,SAAS;YAAC;YAAY;SAAS,CAACC,OAAO,CAACN;QAC9C,MAAMO,SAAS;YAAC;YAAY;SAAS,CAACD,OAAO,CAACF;QAE9C,IAAIC,WAAW,CAAC,GAAG,OAAO;QAC1B,IAAIE,WAAW,CAAC,GAAG,OAAO,CAAC;QAC3B,OAAOF,SAASE;IAClB;IAEA,MAAMC,oBAAgC,CAAC;IACvC,MAAMC,oBAAoB,IAAIC;IAC9B,KAAK,MAAM,CAACC,WAAWC,WAAW,IAAIxB,iBAAkB;QACtD,KAAK,MAAMyB,aAAaD,WAAY;YAClC,IAAIH,kBAAkBK,GAAG,CAACD,YAAY;YAEtC,iEAAiE;YACjE,MAAME,WAAWd,aAAI,CAACC,KAAK,CAACS,WAAWR,IAAI;YAC3C,IAAI;gBAAC;gBAAY;aAAS,CAACa,QAAQ,CAACD,WAAW;gBAC7CN,kBAAkBQ,GAAG,CAACJ;YACxB;YAEA,IAAI,CAACL,iBAAiB,CAACG,UAAU,EAAE;gBACjCH,iBAAiB,CAACG,UAAU,GAAG,EAAE;YACnC;YACAH,iBAAiB,CAACG,UAAU,CAACO,IAAI,CAACL;QACpC;IACF;IAEA,OAAOL;AACT;AAEO,MAAMnC;IAQX8C,YAAYC,OAAgB,CAAE;QAC5B,IAAI,CAACC,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,YAAY,GAAGH,QAAQG,YAAY;QACxC,IAAI,CAACC,WAAW,GAAG,CAAC,IAAI,CAACH,GAAG,IAAI,CAAC,IAAI,CAACE,YAAY,GAAG,QAAQ;QAC7D,IAAI,CAACE,aAAa,GAAGL,QAAQK,aAAa;QAC1C,IAAI,CAACC,cAAc,GAAG,IAAI,CAACH,YAAY,GACnCI,gCAAoB,GACpBC,mCAAuB;IAC7B;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5B3D,aACA,CAAC0D,aAAa,EAAEE,mBAAmB,EAAE;YACnCF,YAAYG,mBAAmB,CAACC,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrCL;YAEFF,YAAYQ,mBAAmB,CAACJ,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrC,IAAIF,gBAAO,CAACC,YAAY,CAACG,cAAc,CAACC,QAAQ;QAEpD;QAGFZ,SAASC,KAAK,CAACY,UAAU,CAACC,UAAU,CAACtE,aAAa,CAAC0D,cACjD,IAAI,CAACa,mBAAmB,CAACf,UAAUE;QAGrCF,SAASC,KAAK,CAACe,YAAY,CAACb,GAAG,CAAC3D,aAAa,CAAC0D;YAC5C,MAAMe,eAAe,CAACC,OAAeC;oBAGEA,0BACpBA;gBAHjB,yFAAyF;gBACzF,2DAA2D;gBAC3D,MAAMC,UAAUD,IAAIE,aAAa,MAAIF,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyBhD,IAAI;gBAClE,MAAMoD,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;gBACnD,wCAAwC;gBACxC,gFAAgF;gBAChF,MAAMC,cAAcL,UAChBA,QAAQM,UAAU,CAACC,sCAA0B,IAC3CC,IAAAA,qCAA6B,EAACT,IAAIU,QAAQ,EAAET,WAC5CA,UAAUG,WACZJ,IAAIU,QAAQ;gBAEhB,IAAI,OAAOX,UAAU,eAAeO,aAAa;oBAC/C,IAAIN,IAAIW,KAAK,KAAKC,yBAAc,CAACC,qBAAqB,EAAE;wBACtD,MAAMC,MAAM9D,aAAI,CACb+D,QAAQ,CAAClC,SAASmC,OAAO,EAAEV,aAC3BW,OAAO,CAAC,uBAAuB;wBAElC,MAAMC,aAAyB;4BAC7BC,UAAUpB;4BACVqB,OAAOrC,YAAYsC,WAAW,CAACC,OAAO,CAACtB;wBACzC;wBAEA,IAAI,IAAI,CAAC1B,YAAY,EAAE;4BACrBhD,YAAYS,cAAc,CAAC+E,IAAI,GAAGI;wBACpC,OAAO;4BACL5F,YAAYQ,UAAU,CAACgF,IAAI,GAAGI;wBAChC;oBACF;gBACF;gBAEA,IAAIlB,IAAIW,KAAK,KAAKC,yBAAc,CAACW,mBAAmB,EAAE;oBACpD;gBACF;gBAEA,yHAAyH;gBACzH,IAAI,OAAOxB,UAAU,eAAeO,aAAa;oBAC/C,4EAA4E;oBAC5E,6EAA6E;oBAC7E,sBAAsB;oBACtB,IAAIkB,mBAAmBxE,aAAI,CAAC+D,QAAQ,CAAClC,SAASmC,OAAO,EAAEV;oBAEvD,IAAI,CAACkB,iBAAiBjB,UAAU,CAAC,MAAM;wBACrC,+BAA+B;wBAC/BiB,mBAAmB,CAAC,EAAE,EAAEC,IAAAA,kCAAgB,EAACD,mBAAmB;oBAC9D;oBAEA,MAAMN,aAAyB;wBAC7BC,UAAUpB;wBACVqB,OAAOrC,YAAYsC,WAAW,CAACC,OAAO,CAACtB;oBACzC;oBAEA,IAAI,IAAI,CAAC1B,YAAY,EAAE;wBACrBhD,YAAYO,cAAc,CACxB2F,iBAAiBP,OAAO,CAAC,uBAAuB,eACjD,GAAGC;oBACN,OAAO;wBACL5F,YAAYM,UAAU,CAAC4F,iBAAiB,GAAGN;oBAC7C;gBACF;YACF;YAEAQ,IAAAA,uBAAe,EAAC3C,aAAa,CAACiB,KAAK2B,QAAQC,aAAa7B;gBACtD,IAAIA,OAAOD,aAAaC,OAAOC;YACjC;QACF;QAEAnB,SAASC,KAAK,CAAC+C,IAAI,CAAC7C,GAAG,CAAC3D,aAAa,CAAC0D;YACpCA,YAAYD,KAAK,CAACgD,aAAa,CAACnC,UAAU,CACxC;gBACEzC,MAAM7B;gBACN0G,OAAO3C,gBAAO,CAAC4C,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACC,SAAW,IAAI,CAACC,kBAAkB,CAACpD,aAAamD;QAErD;IACF;IAEA,MAAMtC,oBACJf,QAA0B,EAC1BE,WAAgC,EAChC;QACA,MAAMqD,kCAEF,EAAE;QACN,MAAMC,iCAGF,CAAC;QAEL,MAAMC,qBACJ,EAAE;QACJ,MAAMC,qBAGF,CAAC;QACL,MAAMC,mBAAmB,IAAI/E;QAE7B,4EAA4E;QAC5E,0BAA0B;QAC1BgF,IAAAA,0BAAkB,EAAC1D,aAAa,CAAC,EAAE7B,IAAI,EAAEwF,WAAW,EAAE;YACpD,MAAMC,sCAA8D,CAAC;YACrE,MAAMC,qBAAqB,IAAIC;YAC/B,MAAMC,wBAAwB,EAAE;YAChC,MAAM5G,mBAA+B,CAAC;YAEtC,KAAK,MAAM6G,cAAcC,IAAAA,kCAA0B,EACjDN,aACA3D,YAAYsC,WAAW,EACtB;gBACD,uFAAuF;gBACvF,MAAM4B,eAAe,AACnBF,WAAWG,UAAU,CACrBC,OAAO;gBAET,MAAM,EAAEC,sBAAsB,EAAEC,aAAa,EAAE1F,UAAU,EAAE,GACzD,IAAI,CAAC2F,6CAA6C,CAAC;oBACjDL;oBACAlE;oBACAwE,gBAAgBR,WAAWQ,cAAc;gBAC3C;gBAEFF,cAAcG,OAAO,CAAC,CAAC,CAACC,KAAKC,QAAQ,GACnCd,mBAAmBzD,GAAG,CAACsE,KAAKC;gBAG9B,MAAMC,oBAAoB3G,aAAI,CAAC4G,UAAU,CAACX;gBAE1C,mDAAmD;gBACnD,IAAI,CAACU,mBAAmB;oBACtBvH,OAAOyH,IAAI,CAACT,wBAAwBI,OAAO,CACzC,CAACM,QAAWnB,mCAAmC,CAACmB,MAAM,GAAG,IAAIrG;oBAE/D;gBACF;gBAEA,2HAA2H;gBAC3H,4DAA4D;gBAC5D,kEAAkE;gBAClE,aAAa;gBACb,IAAI;gBAEJ,MAAMsG,kBAAkBJ,oBACpB3G,aAAI,CAAC+D,QAAQ,CAAChC,YAAYZ,OAAO,CAAC6C,OAAO,EAAGiC,gBAC5CA;gBAEJ,8CAA8C;gBAC9C,MAAMe,aAAavC,IAAAA,kCAAgB,EACjCsC,gBAAgB9C,OAAO,CAAC,eAAe,IAAIA,OAAO,CAAC,aAAa;gBAGlE7E,OAAO6H,MAAM,CAAC/H,kBAAkByB;gBAChCmF,sBAAsB7E,IAAI,CAAC;oBACzBY;oBACAE;oBACArB,WAAWR;oBACXkG;oBACAY;oBACAE,kBAAkBjB;gBACpB;gBAEA,uKAAuK;gBACvK,wGAAwG;gBACxG,8IAA8I;gBAC9I,IACE/F,SAAS,CAAC,GAAG,EAAEiH,4CAAgC,EAAE,IACjDH,eAAe,iBACf;oBACAlB,sBAAsB7E,IAAI,CAAC;wBACzBY;wBACAE;wBACArB,WAAWR;wBACXkG,wBAAwB,CAAC;wBACzBY,YAAY,CAAC,GAAG,EAAEG,4CAAgC,EAAE;wBACpDD,kBAAkBjB;oBACpB;gBACF;YACF;YAEA,2EAA2E;YAC3E,mBAAmB;YACnB,MAAM1F,oBAAoBtB,8BAA8BC;YACxD,KAAK,MAAMkI,uBAAuBtB,sBAAuB;gBACvD,MAAMuB,WAAW,IAAI,CAACC,8BAA8B,CAAC;oBACnD,GAAGF,mBAAmB;oBACtBG,eAAe;wBACb,GAAGH,oBAAoBhB,sBAAsB;wBAC7C,GAAG,AACD7F,CAAAA,iBAAiB,CAAC6G,oBAAoBF,gBAAgB,CAAC,IAAI,EAAE,AAAD,EAC5DM,MAAM,CAAyB,CAACC,KAAKC;4BACrCD,GAAG,CAACC,KAAK,GAAG,IAAIjH;4BAChB,OAAOgH;wBACT,GAAG,CAAC,EAAE;oBACR;gBACF;gBAEA,2EAA2E;gBAC3E,IAAI,CAACpC,8BAA8B,CAAC+B,oBAAoB1G,SAAS,CAAC,EAAE;oBAClE2E,8BAA8B,CAAC+B,oBAAoB1G,SAAS,CAAC,GAAG,EAAE;gBACpE;gBACA2E,8BAA8B,CAAC+B,oBAAoB1G,SAAS,CAAC,CAACO,IAAI,CAChEoG,QAAQ,CAAC,EAAE;gBAGbjC,gCAAgCnE,IAAI,CAACoG;YACvC;YAEA,IAAI,CAACM,IAAAA,gCAAe,EAACzH,OAAO;gBAC1B,sBAAsB;gBACtBkF,gCAAgCnE,IAAI,CAClC,IAAI,CAACqG,8BAA8B,CAAC;oBAClCzF;oBACAE;oBACArB,WAAWR;oBACXqH,eAAe;wBAAE,GAAG5B,mCAAmC;oBAAC;oBACxDqB,YAAYY,gCAAoB;gBAClC;YAEJ;YAEA,IAAIhC,mBAAmBiC,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACtC,kBAAkB,CAACrF,KAAK,EAAE;oBAC7BqF,kBAAkB,CAACrF,KAAK,GAAG,IAAI2F;gBACjC;gBACAN,kBAAkB,CAACrF,KAAK,GAAG,IAAI2F,IAAI;uBAC9BN,kBAAkB,CAACrF,KAAK;uBACxB0F;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAAC1F,MAAM0F,mBAAmB,IAAIxG,OAAOC,OAAO,CACrDkG,oBACC;YACDD,mBAAmBrE,IAAI,CACrB,IAAI,CAAC6G,iBAAiB,CAAC;gBACrBjG;gBACAE;gBACA2E,SAASd;gBACTlF,WAAWR;gBACX8G,YAAY9G;gBACZsF;YACF;QAEJ;QAEA,qDAAqD;QACrD,MAAMuC,cAAcC,IAAAA,oCAAc,EAACnG,SAASoG,UAAU;QACtD,4DAA4D;QAC5D,IACEF,eACA3C,gCAAgC8C,IAAI,CAClC,CAAC,CAACC,iBAAiB,GAAKA,qBAAqB,OAE/C;YACAJ,YAAYK,UAAU,CAAC;gBAACC,0BAAc,CAACC,MAAM;aAAC;QAChD;QAEA,4EAA4E;QAC5E,0EAA0E;QAC1E,sCAAsC;QACtC,MAAMC,QAAQC,GAAG,CACfpD,gCAAgCqD,OAAO,CAAC,CAACC,8BAAgC;gBACvEA,2BAA2B,CAAC,EAAE;gBAC9BA,2BAA2B,CAAC,EAAE;aAC/B;QAGH,uCAAuC;QACvC,MAAMH,QAAQC,GAAG,CAAClD;QAElB,MAAMqD,6BAA6C,EAAE;QACrD,MAAMC,2BAGF,CAAC;QAEL,mEAAmE;QACnE,gBAAgB;QAChB,yEAAyE;QACzE,KAAK,MAAM,CAAC1I,MAAM2I,qBAAqB,IAAIzJ,OAAOC,OAAO,CACvDgG,gCACC;YACD,qEAAqE;YACrE,sBAAsB;YACtB,MAAMO,qBAAqB,IAAI,CAACkD,oCAAoC,CAAC;gBACnE/G;gBACAM,cAAcwG;YAChB;YAEA,IAAIjD,mBAAmBiC,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACe,wBAAwB,CAAC1I,KAAK,EAAE;oBACnC0I,wBAAwB,CAAC1I,KAAK,GAAG,IAAI2F;gBACvC;gBACA+C,wBAAwB,CAAC1I,KAAK,GAAG,IAAI2F,IAAI;uBACpC+C,wBAAwB,CAAC1I,KAAK;uBAC9B0F;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAAClF,WAAWkF,mBAAmB,IAAIxG,OAAOC,OAAO,CAC1DuJ,0BACC;YACD,uEAAuE;YACvE,+CAA+C;YAC/C,uEAAuE;YACvE,mBAAmB;YACnB,IAAIG,iCAAiC;YACrC,MAAMC,8BAA8B,IAAInD;YACxC,KAAK,MAAM,CAACY,KAAKC,QAAQ,IAAId,mBAAoB;gBAC/C,MAAMqD,uBAAuB,EAAE;gBAC/B,KAAK,MAAMC,UAAUxC,QAAS;oBAC5B,iCAAiC;oBACjC,IAAI,CAAClB,iBAAiB3E,GAAG,CAACH,YAAY,MAAMwI,MAAM,CAAC,EAAE,GAAG;wBACtDD,qBAAqBhI,IAAI,CAACiI;oBAC5B;gBACF;gBACA,IAAID,qBAAqBpJ,MAAM,GAAG,GAAG;oBACnCmJ,4BAA4B7G,GAAG,CAACsE,KAAKwC;oBACrCF,iCAAiC;gBACnC;YACF;YAEA,IAAIA,gCAAgC;gBAClCJ,2BAA2B1H,IAAI,CAC7B,IAAI,CAAC6G,iBAAiB,CAAC;oBACrBjG;oBACAE;oBACA2E,SAASsC;oBACTtI;oBACAsG,YAAYtG;oBACZyI,YAAY;oBACZ3D;gBACF;YAEJ;QACF;QAEA,MAAM+C,QAAQC,GAAG,CAACG;IACpB;IAEAG,qCAAqC,EACnC/G,WAAW,EACXM,YAAY,EAIb,EAAE;QACD,mCAAmC;QACnC,MAAM+G,mBAAmB,IAAIvD;QAE7B,gFAAgF;QAChF,MAAMwD,gBAAgB,IAAI5I;QAC1B,MAAM6I,eAAe,IAAI7I;QAEzB,MAAM8I,iBAAiB,CAAC,EACtBtD,YAAY,EACZM,cAAc,EAIf;YACC,MAAMiD,sBAAsB,CAACxG;gBAC3B,IAAI,CAACA,KAAK;gBAEV,MAAMM,cAAcmG,kBAAkBzG;gBAEtC,IAAI,CAACM,aAAa;gBAElB,IAAI+F,cAAcxI,GAAG,CAACyC,cAAc;gBACpC+F,cAAcrI,GAAG,CAACsC;gBAElB,MAAMoD,UAAUgD,IAAAA,8BAAuB,EAAC1G;gBACxC,IAAI0D,SAAS;oBACX0C,iBAAiBjH,GAAG,CAACmB,aAAalE,OAAOC,OAAO,CAACqH;gBACnD;gBAEA,8CAA8C;gBAC9CV,IAAAA,kCAA0B,EAAChD,KAAKjB,YAAYsC,WAAW,EAAEmC,OAAO,CAC9D,CAACT;oBACCyD,oBACEzD,WAAWQ,cAAc;gBAE7B;YAEJ;YAEA,yEAAyE;YACzE,IACEN,gBACA,CAACA,aAAalF,QAAQ,CAAC,oCACvB;gBACA,2DAA2D;gBAC3DyI,oBAAoBjD;YACtB;QACF;QAEA,KAAK,MAAMoD,mBAAmBtH,aAAc;YAC1C,MAAMuH,iBACJ7H,YAAYsC,WAAW,CAACwF,iBAAiB,CAACF;YAC5C,KAAK,MAAM5D,cAAcC,IAAAA,kCAA0B,EACjD4D,gBACA7H,YAAYsC,WAAW,EACtB;gBACD,MAAMyF,YAAY/D,WAAWG,UAAU;gBACvC,MAAMC,UAAU,AAAC2D,UAA8C3D,OAAO;gBAEtE,oEAAoE;gBACpE,oEAAoE;gBACpE,IAAImD,aAAazI,GAAG,CAACsF,UAAU;gBAC/BmD,aAAatI,GAAG,CAACmF;gBAEjBoD,eAAe;oBACbtD,cAAcE;oBACdI,gBAAgBR,WAAWQ,cAAc;gBAC3C;YACF;QACF;QAEA,OAAO6C;IACT;IAEA9C,8CAA8C,EAC5CL,YAAY,EACZlE,WAAW,EACXwE,cAAc,EAKf,EAIC;QACA,gFAAgF;QAChF,MAAMwD,oCAAoC,IAAItJ;QAE9C,mBAAmB;QACnB,MAAM2F,yBAAiD,CAAC;QACxD,MAAMC,gBAAgD,EAAE;QACxD,MAAM2D,aAAa,IAAIvJ;QAEvB,MAAMwJ,yBAAyB,CAC7BjH,KACAkH;YAEA,IAAI,CAAClH,KAAK;YAEV,MAAMM,cAAcmG,kBAAkBzG;YAEtC,IAAI,CAACM,aAAa;YAClB,IAAIyG,kCAAkClJ,GAAG,CAACyC,cAAc;gBACtD,IAAI8C,sBAAsB,CAAC9C,YAAY,EAAE;oBACvC6G,gBACEnH,KACAM,aACA8C,wBACA8D,qBACA;gBAEJ;gBACA;YACF;YACAH,kCAAkC/I,GAAG,CAACsC;YAEtC,MAAMoD,UAAUgD,IAAAA,8BAAuB,EAAC1G;YACxC,IAAI0D,SAAS;gBACXL,cAAcpF,IAAI,CAAC;oBAACqC;oBAAalE,OAAOC,OAAO,CAACqH;iBAAS;YAC3D;YAEA,IAAI0D,IAAAA,eAAQ,EAACpH,MAAM;gBACjB,MAAMqH,iBACJrH,IAAIsH,WAAW,IAAI,AAACtH,IAAIsH,WAAW,CAASD,cAAc;gBAE5D,IAAIA,gBAAgB;oBAClB,MAAME,SAAS,CAACxI,YAAYsC,WAAW,CACpCmG,cAAc,CAACxH,KACfyH,YAAY,CAAC,IAAI,CAAChJ,cAAc;oBAEnC,IAAI8I,QAAQ;gBACd;gBAEAP,WAAWhJ,GAAG,CAACsC;YACjB,OAAO,IAAIoH,IAAAA,mCAA4B,EAAC1H,MAAM;gBAC5C,IAAI,CAACoD,sBAAsB,CAAC9C,YAAY,EAAE;oBACxC8C,sBAAsB,CAAC9C,YAAY,GAAG,IAAI7C;gBAC5C;gBACA0J,gBACEnH,KACAM,aACA8C,wBACA8D,qBACA;gBAGF;YACF;YAEAlE,IAAAA,kCAA0B,EAAChD,KAAKjB,YAAYsC,WAAW,EAAEmC,OAAO,CAC9D,CAACT;oBAKKA;gBAJJ,IAAI4E,gBAA0B,EAAE;gBAEhC,mEAAmE;gBACnE,6CAA6C;gBAC7C,KAAI5E,yBAAAA,WAAWG,UAAU,qBAArBH,uBAAuB6E,GAAG,EAAE;oBAC9BD,cAAc1J,IAAI,IAAI8E,WAAWG,UAAU,CAAC0E,GAAG;gBACjD,OAAO;oBACLD,gBAAgB;wBAAC;qBAAI;gBACvB;gBAEAV,uBAAuBlE,WAAWQ,cAAc,EAAEoE;YACpD;QAEJ;QAEA,2DAA2D;QAC3DV,uBAAuB1D,gBAAgB,EAAE;QAEzC,OAAO;YACLH;YACAzF,YAAYqJ,WAAWnC,IAAI,GACvB;gBACE,CAAC5B,aAAa,EAAE4E,MAAMC,IAAI,CAACd;YAC7B,IACA,CAAC;YACL3D;QACF;IACF;IAEAiB,+BAA+B,EAC7BzF,QAAQ,EACRE,WAAW,EACXrB,SAAS,EACT6G,aAAa,EACbP,UAAU,EACVE,gBAAgB,EAQjB,EAKC;QACA,IAAIiB,mBAAmB;QAEvB,MAAM4C,UAAU3L,OAAOyH,IAAI,CAACU,eACzBjI,IAAI,CAAC,CAACC,GAAGC,IAAOwL,eAAQ,CAACC,IAAI,CAACzL,KAAK,IAAID,EAAE2L,aAAa,CAAC1L,IACvD2L,GAAG,CAAC,CAACC,mBAAsB,CAAA;gBAC1BjF,SAASiF;gBACTR,KAAK;uBAAIrD,aAAa,CAAC6D,iBAAiB;iBAAC;YAC3C,CAAA;QAEF,uEAAuE;QACvE,0EAA0E;QAC1E,gBAAgB;QAChB,MAAMC,sBAAsB,CAAC,gCAAgC,EAAEC,IAAAA,sBAAS,EAAC;YACvEP,SAAS,AAAC,CAAA,IAAI,CAACzJ,YAAY,GACvByJ,QAAQI,GAAG,CAAC,CAAC,EAAEhF,OAAO,EAAEyE,GAAG,EAAE,GAAM,CAAA;oBACjCzE,SAASA,QAAQlC,OAAO,CACtB,mCACA,cAAcA,OAAO,CAAC,OAAOjE,aAAI,CAACuL,GAAG;oBAEvCX;gBACF,CAAA,KACAG,OAAM,EACRI,GAAG,CAAC,CAACK,IAAMC,KAAKH,SAAS,CAACE;YAC5BE,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,MAAMC,qBAAqB,CAAC,gCAAgC,EAAEL,IAAAA,sBAAS,EAAC;YACtEP,SAASA,QAAQI,GAAG,CAAC,CAACK,IAAMC,KAAKH,SAAS,CAACE;YAC3CE,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,iCAAiC;QACjC,2CAA2C;QAC3C,IAAI,IAAI,CAACtK,GAAG,EAAE;YACZ,MAAM/B,UAAUuM,IAAAA,gCAAU,EAAC/J,SAASoG,UAAU;YAC9C,MAAM4D,UAAUC,IAAAA,iCAAW,EACzBzD,0BAAc,CAACC,MAAM,EACrByD,qBAAU,CAACC,GAAG,EACdhF;YAGF,IAAI,CAAC3H,OAAO,CAACwM,QAAQ,EAAE;gBACrBxM,OAAO,CAACwM,QAAQ,GAAG;oBACjBI,MAAMC,gCAAU,CAACC,WAAW;oBAC5BC,eAAe,IAAI3L,IAAI;wBAACC;qBAAU;oBAClC2L,uBAAuBnF;oBACvBF;oBACAb,SAASkF;oBACTiB,SAAS;oBACTC,gBAAgBC,KAAKC,GAAG;gBAC1B;gBACAtE,mBAAmB;YACrB,OAAO;gBACL,MAAMuE,YAAYrN,OAAO,CAACwM,QAAQ;gBAClC,mCAAmC;gBACnC,IAAIa,UAAUvG,OAAO,KAAKkF,qBAAqB;oBAC7CqB,UAAUvG,OAAO,GAAGkF;oBACpBlD,mBAAmB;gBACrB;gBACA,IAAIuE,UAAUT,IAAI,KAAKC,gCAAU,CAACC,WAAW,EAAE;oBAC7CO,UAAUN,aAAa,CAACpL,GAAG,CAACN;gBAC9B;gBACAgM,UAAUJ,OAAO,GAAG;gBACpBI,UAAUH,cAAc,GAAGC,KAAKC,GAAG;YACrC;QACF,OAAO;YACLnO,YAAYU,qBAAqB,CAACgI,WAAW,GAAGqE;QAClD;QAEA,MAAMsB,6BAA6BvK,gBAAO,CAACwK,WAAW,CAACC,gBAAgB,CACrElB,oBACA;YAAEzL,MAAM8G;QAAW;QAGrB,MAAM8F,6BAA6B1K,gBAAO,CAACwK,WAAW,CAACC,gBAAgB,CACrElB,oBACA;YAAEzL,MAAM8G;QAAW;QAGrB,OAAO;YACLmB;YACA,yEAAyE;YACzE,yEAAyE;YACzE,sBAAsB;YACtB,IAAI,CAAC4E,QAAQ,CAAChL,aAAaF,SAASmC,OAAO,EAAE2I,4BAA4B;gBACvEzM,MAAMQ;gBACNiD,OAAOC,yBAAc,CAACW,mBAAmB;YAC3C;YACA,IAAI,CAACwI,QAAQ,CAAChL,aAAaF,SAASmC,OAAO,EAAE8I,4BAA4B;gBACvE5M,MAAMQ;gBACNiD,OAAOC,yBAAc,CAACC,qBAAqB;YAC7C;YACA8I;SACD;IACH;IAEA7E,kBAAkB,EAChBjG,QAAQ,EACRE,WAAW,EACX2E,OAAO,EACPhG,SAAS,EACTsG,UAAU,EACVmC,UAAU,EACV3D,gBAAgB,EASjB,EAAE;QACD,MAAMwH,eAAenC,MAAMC,IAAI,CAACpE,QAAQrH,OAAO;QAC/C,KAAK,MAAM,GAAG4N,kBAAkB,IAAIvG,QAAS;YAC3C,KAAK,MAAM,CAACwG,GAAG,IAAID,kBAAmB;gBACpCzH,iBAAiBxE,GAAG,CAACN,YAAY,MAAMwM;YACzC;QACF;QAEA,IAAIF,aAAanN,MAAM,KAAK,GAAG;YAC7B,OAAO0I,QAAQ4E,OAAO;QACxB;QAEA,MAAMC,eAAe,CAAC,gCAAgC,EAAE9B,IAAAA,sBAAS,EAAC;YAChE5E,SAAS+E,KAAKH,SAAS,CAAC0B;YACxBK,qBAAqBlE;QACvB,GAAG,CAAC,CAAC;QAEL,MAAMmE,+BAA+B,IAAI,CAAChM,YAAY,GAClDhD,YAAYG,iBAAiB,GAC7BH,YAAYE,aAAa;QAE7B,KAAK,MAAM,GAAGyO,kBAAkB,IAAID,aAAc;YAChD,KAAK,MAAM,CAACE,GAAG,IAAID,kBAAmB;gBACpC,IAAI,OAAOK,4BAA4B,CAACJ,GAAG,KAAK,aAAa;oBAC3DI,4BAA4B,CAACJ,GAAG,GAAG;wBACjCK,SAAS,CAAC;wBACV5J,OAAO,CAAC;oBACV;gBACF;gBACA2J,4BAA4B,CAACJ,GAAG,CAACK,OAAO,CAACvG,WAAW,GAAG;oBACrD7C,UAAU;oBACVC,OAAO;gBACT;gBAEAkJ,4BAA4B,CAACJ,GAAG,CAACvJ,KAAK,CAACqD,WAAW,GAAGmC,aACjDvF,yBAAc,CAAC4J,aAAa,GAC5B5J,yBAAc,CAACC,qBAAqB;YAC1C;QACF;QAEA,0CAA0C;QAC1C,MAAM4J,iBAAiBrL,gBAAO,CAACwK,WAAW,CAACC,gBAAgB,CAACO,cAAc;YACxElN,MAAM8G;QACR;QAEA,OAAO,IAAI,CAAC+F,QAAQ,CAClBhL,aACA,6BAA6B;QAC7BF,SAASmC,OAAO,EAChByJ,gBACA;YACEvN,MAAMQ;YACNiD,OAAOwF,aACHvF,yBAAc,CAAC4J,aAAa,GAC5B5J,yBAAc,CAACC,qBAAqB;QAC1C;IAEJ;IAEAkJ,SACEhL,WAAgB,EAChBiC,OAAe,EACfkC,UAA8B,EAC9B/E,OAA6B,EACf,mBAAmB,GAAG;QACpC,OAAO,IAAIoH,QAAQ,CAAC4E,SAASO;YAC3B,MAAMC,QAAQ5L,YAAY1C,OAAO,CAACuO,GAAG,CAACzM,QAAQjB,IAAI;YAClDyN,MAAME,mBAAmB,CAAC5M,IAAI,CAACiF;YAC/BnE,YAAYD,KAAK,CAACiL,QAAQ,CAACe,IAAI,CAACH,OAAOxM;YACvCY,YAAYgM,aAAa,CACvB;gBACE/J;gBACAkC;gBACA8H,aAAa;oBAAEC,aAAa9M,QAAQwC,KAAK;gBAAC;YAC5C,GACA,CAACuK,KAAwBC;gBACvB,IAAID,KAAK;oBACPnM,YAAYD,KAAK,CAACsM,WAAW,CAACN,IAAI,CAAC5H,YAAY/E,SAAS+M;oBACxD,OAAOR,OAAOQ;gBAChB;gBAEAnM,YAAYD,KAAK,CAACuM,YAAY,CAACP,IAAI,CAAC5H,YAAY/E,SAASgN;gBAEzDpM,YAAYsC,WAAW,CACpBmG,cAAc,CAAC2D,QACfG,mBAAmB,CAClB,IAAI,CAAChN,YAAY,GAAGI,gCAAoB,GAAGC,mCAAuB;gBAGtE,OAAOwL,QAAQgB;YACjB;QAEJ;IACF;IAEA,MAAMhJ,mBACJpD,WAAgC,EAChCmD,MAAqC,EACrC;QACA,MAAM1G,gBAAwC,CAAC;QAC/C,MAAMC,oBAA4C,CAAC;QAEnDiG,IAAAA,uBAAe,EAAC3C,aAAa,CAACiB,KAAK2B,QAAQ4J,YAAYxL;YACrD,yEAAyE;YACzE,IACEwL,WAAWrO,IAAI,IACf8C,IAAImD,OAAO,IACXpD,SACA,kCAAkCkI,IAAI,CAACjI,IAAImD,OAAO,GAClD;gBACA,MAAMgD,aAAa,4BAA4B8B,IAAI,CAACjI,IAAImD,OAAO;gBAE/D,MAAMqI,UAAU,IAAI,CAAClN,YAAY,GAC7BhD,YAAYK,uBAAuB,GACnCL,YAAYI,mBAAmB;gBAEnC,IAAI,CAAC8P,OAAO,CAACD,WAAWrO,IAAI,CAAC,EAAE;oBAC7BsO,OAAO,CAACD,WAAWrO,IAAI,CAAC,GAAG,CAAC;gBAC9B;gBACAsO,OAAO,CAACD,WAAWrO,IAAI,CAAC,CAACiJ,aAAa,WAAW,SAAS,GAAG;oBAC3DhF,UAAUpB;oBACVqB,OAAOrC,YAAYsC,WAAW,CAACC,OAAO,CAACtB;gBACzC;YACF;QACF;QAEA,IAAK,IAAIkK,MAAM5O,YAAYE,aAAa,CAAE;YACxC,MAAM0K,SAAS5K,YAAYE,aAAa,CAAC0O,GAAG;YAC5C,IAAK,IAAIhN,QAAQgJ,OAAOqE,OAAO,CAAE;gBAC/B,MAAMxK,QACJzE,YAAYI,mBAAmB,CAACwB,KAAK,CACnCgJ,OAAOvF,KAAK,CAACzD,KAAK,KAAK0D,yBAAc,CAAC4J,aAAa,GAC/C,WACA,SACL;gBACHtE,OAAOqE,OAAO,CAACrN,KAAK,GAAG6C;YACzB;YACAvE,aAAa,CAAC0O,GAAG,GAAGhE;QACtB;QAEA,IAAK,IAAIgE,MAAM5O,YAAYG,iBAAiB,CAAE;YAC5C,MAAMyK,SAAS5K,YAAYG,iBAAiB,CAACyO,GAAG;YAChD,IAAK,IAAIhN,QAAQgJ,OAAOqE,OAAO,CAAE;gBAC/B,MAAMxK,QACJzE,YAAYK,uBAAuB,CAACuB,KAAK,CACvCgJ,OAAOvF,KAAK,CAACzD,KAAK,KAAK0D,yBAAc,CAAC4J,aAAa,GAC/C,WACA,SACL;gBACHtE,OAAOqE,OAAO,CAACrN,KAAK,GAAG6C;YACzB;YACAtE,iBAAiB,CAACyO,GAAG,GAAGhE;QAC1B;QAEA,MAAMuF,iBAAiB;YACrBC,MAAMlQ;YACNmQ,MAAMlQ;YACN+C,eAAe,IAAI,CAACA,aAAa;QACnC;QACA,MAAMoN,qBAAqB;YACzB,GAAGH,cAAc;YACjBjN,eAAe;QACjB;QAEA,MAAMqN,OAAOpD,KAAKH,SAAS,CAACmD,gBAAgB,MAAM,IAAI,CAACrN,GAAG,GAAG,IAAI0N;QACjE,MAAMC,WAAWtD,KAAKH,SAAS,CAC7BsD,oBACA,MACA,IAAI,CAACxN,GAAG,GAAG,IAAI0N;QAGjB5J,MAAM,CAAC,GAAG,IAAI,CAAC3D,WAAW,GAAGyN,qCAAyB,CAAC,GAAG,CAAC,CAAC,GAC1D,IAAIC,gBAAO,CAACC,SAAS,CACnB,CAAC,2BAA2B,EAAEzD,KAAKH,SAAS,CAACyD,WAAW;QAE5D7J,MAAM,CAAC,GAAG,IAAI,CAAC3D,WAAW,GAAGyN,qCAAyB,CAAC,KAAK,CAAC,CAAC,GAC5D,IAAIC,gBAAO,CAACC,SAAS,CAACL;IAC1B;AACF;AAEA,SAAS1E,gBACPnH,GAAyB,EACzBmM,UAAkB,EAClB/I,sBAA8C,EAC9C8D,mBAA6B,EAC7BkF,kBAA2B;QAEHC;IAAxB,MAAMC,mBAAkBD,0BAAAA,IAAAA,sCAAkB,EAACrM,KAAKuM,GAAG,qBAA3BF,wBAA6BC,eAAe;IACpE,MAAME,cAAcF,oBAAoB;IACxC,MAAMG,oBAAoBC,IAAAA,sCAAoB,EAC5C1M,KACAwM,cAAc,aAAa;IAG7B,MAAMG,mBAAmBvJ,sBAAsB,CAAC+I,WAAW;IAE3D,IAAIjF,mBAAmB,CAAC,EAAE,KAAK,KAAK;QAClC,kEAAkE;QAClE,qDAAqD;QACrD,sCAAsC;QACtC,IAAI,CAACkF,sBAAsB;eAAIO;SAAiB,CAAC,EAAE,KAAK,KAAK;YAC3DvJ,sBAAsB,CAAC+I,WAAW,GAAG,IAAI1O,IAAI;gBAAC;aAAI;QACpD;IACF,OAAO;QACL,MAAMmP,yBAAyBH,sBAAsB;QACrD,IAAIG,wBAAwB;YAC1BxJ,sBAAsB,CAAC+I,WAAW,GAAG,IAAI1O,IAAI;gBAAC;aAAI;QACpD,OAAO;YACL,gGAAgG;YAChG,oEAAoE;YACpE,KAAK,MAAMP,QAAQgK,oBAAqB;gBACtC,mEAAmE;gBACnE,MAAM2F,qBAAqBL,eAAetP,SAAS;gBAEnD,kEAAkE;gBAClE,4DAA4D;gBAC5D,IAAI2P,oBAAoB;oBACtBzJ,sBAAsB,CAAC+I,WAAW,CAACnO,GAAG,CAAC;gBACzC;gBAEAoF,sBAAsB,CAAC+I,WAAW,CAACnO,GAAG,CAACd;YACzC;QACF;IACF;AACF;AAEA,SAASuJ,kBAAkBzG,GAAyB;QAC1BA,0BACPA,2BAebA;IAhBJ,MAAMC,UAAkBD,EAAAA,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyBhD,IAAI,KAAI;IACzD,MAAMoD,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;IACnD,mEAAmE;IACnE,yEAAyE;IACzE,0EAA0E;IAC1E,IAAIC,cAAsBL,UAAUG;IAEpC,6EAA6E;IAC7E,IAAIJ,IAAI9B,WAAW,CAAChB,IAAI,KAAK,iBAAiB;QAC5CoD,cAAcN,IAAI8M,UAAU;IAC9B;IAEA,yEAAyE;IACzE,yEAAyE;IACzE,0EAA0E;IAC1E,wEAAwE;IACxE,KAAI9M,qBAAAA,IAAIE,aAAa,qBAAjBF,mBAAmBO,UAAU,CAACC,sCAA0B,GAAG;QAC7DF,cAAcN,IAAIE,aAAa,GAAG,MAAMI;IAC1C;IACA,OAAOA;AACT"}