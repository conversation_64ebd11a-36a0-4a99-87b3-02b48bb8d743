(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[545],{5521:e=>{"use strict";e.exports=require("node:async_hooks")},5356:e=>{"use strict";e.exports=require("node:buffer")},6869:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>R,default:()=>b});var s,a={};r.r(a),r.d(a,{GET:()=>_,runtime:()=>w});var i={};r.r(i),r.d(i,{patchFetch:()=>E,routeModule:()=>h,serverHooks:()=>x,workAsyncStorage:()=>I,workUnitAsyncStorage:()=>q});var n=r(793),o=r(6590),l=r(3439),c=r(4651),d=r(6292),u=r(8498),m=r(1639),p=r(615),f=r(9230),g=r(789),y=r(9066);let w="edge";async function _(){let e=await (0,m.j2)();if(!e?.user?.id)return Response.json({error:"未授权"},{status:401});let t=(0,p.d)(),r=await t.query.roles.findFirst({where:(0,y.eq)(f.roles.name,g.gg.EMPEROR),with:{userRoles:!0}});if(r&&r.userRoles.length>0)return Response.json({error:"已存在皇帝, 谋反将被处死"},{status:400});try{let s=await t.query.userRoles.findFirst({where:(0,y.eq)(f.userRoles.userId,e.user.id),with:{role:!0}});if(s?.role.name===g.gg.EMPEROR)return Response.json({message:"你已经是皇帝了"});let a=r?.id;if(!a){let[e]=await t.insert(f.roles).values({name:g.gg.EMPEROR,description:"皇帝（网站所有者）"}).returning({id:f.roles.id});a=e.id}return await (0,m.iz)(t,e.user.id,a),Response.json({message:"登基成功，你已成为皇帝"})}catch(e){return console.error("Failed to initialize emperor:",e),Response.json({error:"登基称帝失败"},{status:500})}}let h=new c.AppRouteRouteModule({definition:{kind:d.A.APP_ROUTE,page:"/api/roles/init-emperor/route",pathname:"/api/roles/init-emperor",filename:"route",bundlePath:"app/api/roles/init-emperor/route"},resolvedPagePath:"F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\roles\\init-emperor\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:I,workUnitAsyncStorage:q,serverHooks:x}=h;function E(){return(0,u.V5)({workAsyncStorage:I,workUnitAsyncStorage:q})}let v=null==(s=self.__RSC_MANIFEST)?void 0:s["/api/roles/init-emperor/route"],A=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);v&&A&&(0,o.fQ)({page:"/api/roles/init-emperor/route",clientReferenceManifest:v,serverActionsManifest:A,serverModuleMap:(0,n.e)({serverActionsManifest:A})});let R=i,b=l.s.wrap(h,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\Mail\\moemail_source",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\Mail\\moemail_source"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\Mail\\moemail_source\\next.config.ts",configFileName:"next.config.ts"}})},7032:()=>{},6760:()=>{},1514:(e,t,r)=>{"use strict";r.d(t,{F:()=>i}),r(615),r(9230),r(658);var s=r(1639),a=r(144);let i=async()=>{let e=(await (0,a.b3)()).get("X-User-Id");if(e)return e;let t=await (0,s.j2)();return t?.user.id}},1639:(e,t,r)=>{"use strict";r.d(t,{fG:()=>E,LO:()=>v,iz:()=>I,j2:()=>A,Yj:()=>x,uG:()=>q,kz:()=>N});var s=r(3757),a=r(7031),i=r(1049),n=r(615),o=r(9230),l=r(9066),c=r(888),d=r(789),u=r(236),m=r(2596),p=r(2342),f=r(5356).Buffer;let g=["#2196F3","#009688","#9C27B0","#F44336","#673AB7","#3F51B5","#4CAF50","#FF5722","#795548","#607D8B"];var y=r(1514);let w={[d.gg.EMPEROR]:"皇帝（网站所有者）",[d.gg.DUKE]:"公爵（超级用户）",[d.gg.KNIGHT]:"骑士（高级用户）",[d.gg.CIVILIAN]:"平民（普通用户）"},_=async()=>{let e=await (0,c.getRequestContext)().env.SITE_CONFIG.get("DEFAULT_ROLE");return e===d.gg.DUKE||e===d.gg.KNIGHT||e===d.gg.CIVILIAN?e:d.gg.CIVILIAN};async function h(e,t){let r=await e.query.roles.findFirst({where:(0,l.eq)(o.roles.name,t)});if(!r){let[s]=await e.insert(o.roles).values({name:t,description:w[t]}).returning();r=s}return r}async function I(e,t,r){await e.delete(o.userRoles).where((0,l.eq)(o.userRoles.userId,t)),await e.insert(o.userRoles).values({userId:t,roleId:r})}async function q(e){let t=(0,n.d)();return(await t.query.userRoles.findMany({where:(0,l.eq)(o.userRoles.userId,e),with:{role:!0}}))[0].role.name}async function x(e){let t=await (0,y.F)();if(!t)return!1;let r=(0,n.d)(),s=(await r.query.userRoles.findMany({where:(0,l.eq)(o.userRoles.userId,t),with:{role:!0}})).map(e=>e.role.name);return(0,d._m)(s,e)}let{handlers:{GET:E,POST:v},auth:A,signIn:R,signOut:b}=(0,s.Ay)(()=>({secret:process.env.AUTH_SECRET,adapter:(0,i._)((0,n.d)(),{usersTable:o.users,accountsTable:o.accounts}),providers:[(0,a.A)({clientId:process.env.AUTH_GITHUB_ID,clientSecret:process.env.AUTH_GITHUB_SECRET}),(0,u.A)({name:"Credentials",credentials:{username:{label:"用户名",type:"text",placeholder:"请输入用户名"},password:{label:"密码",type:"password",placeholder:"请输入密码"}},async authorize(e){if(!e)throw Error("请输入用户名和密码");let{username:t,password:r}=e;try{p.Q.parse({username:t,password:r})}catch(e){throw Error("输入格式不正确")}let s=(0,n.d)(),a=await s.query.users.findFirst({where:(0,l.eq)(o.users.username,t)});if(!a||!await (0,m.b)(r,a.password))throw Error("用户名或密码错误");return{...a,password:void 0}}})],events:{async signIn({user:e}){if(e.id)try{let t=(0,n.d)();if(await t.query.userRoles.findFirst({where:(0,l.eq)(o.userRoles.userId,e.id)}))return;let r=await _(),s=await h(t,r);await I(t,e.id,s.id)}catch(e){console.error("Error assigning role:",e)}}},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.name=t.name||t.username,e.username=t.username,e.image=t.image||function(e){let t=e[0].toUpperCase(),r=Array.from(e).reduce((e,t)=>e+t.charCodeAt(0),0)%g.length,s=g[r],a=`
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
      <rect width="100" height="100" fill="${s}"/>
      <text 
        x="50%" 
        y="50%" 
        fill="white" 
        font-family="system-ui, -apple-system, sans-serif" 
        font-size="45" 
        font-weight="500"
        text-anchor="middle"
        alignment-baseline="central"
        dominant-baseline="central"
        style="text-transform: uppercase"
      >
        ${t}
      </text>
    </svg>
  `.trim(),i=new TextEncoder().encode(a),n=f.from(i).toString("base64");return`data:image/svg+xml;base64,${n}`}(e.name)),e),async session({session:e,token:t}){if(t&&e.user){e.user.id=t.id,e.user.name=t.name,e.user.username=t.username,e.user.image=t.image;let r=(0,n.d)(),s=await r.query.userRoles.findMany({where:(0,l.eq)(o.userRoles.userId,e.user.id),with:{role:!0}});if(!s.length){let t=await _(),a=await h(r,t);await I(r,e.user.id,a.id),s=[{userId:e.user.id,roleId:a.id,createdAt:new Date,role:a}]}e.user.roles=s.map(e=>({name:e.role.name}))}return e}},session:{strategy:"jwt"}}));async function N(e,t){let r=(0,n.d)();if(await r.query.users.findFirst({where:(0,l.eq)(o.users.username,e)}))throw Error("用户名已存在");let s=await (0,m.E)(t),[a]=await r.insert(o.users).values({username:e,password:s}).returning();return a}},615:(e,t,r)=>{"use strict";r.d(t,{d:()=>n});var s=r(888),a=r(7384),i=r(9230);let n=()=>(0,a.f)((0,s.getRequestContext)().env.DB,{schema:i})},789:(e,t,r)=>{"use strict";r.d(t,{Jj:()=>a,_m:()=>n,gg:()=>s});let s={EMPEROR:"emperor",DUKE:"duke",KNIGHT:"knight",CIVILIAN:"civilian"},a={MANAGE_EMAIL:"manage_email",MANAGE_WEBHOOK:"manage_webhook",PROMOTE_USER:"promote_user",MANAGE_CONFIG:"manage_config",MANAGE_API_KEY:"manage_api_key"},i={[s.EMPEROR]:Object.values(a),[s.DUKE]:[a.MANAGE_EMAIL,a.MANAGE_WEBHOOK,a.MANAGE_API_KEY],[s.KNIGHT]:[a.MANAGE_EMAIL,a.MANAGE_WEBHOOK],[s.CIVILIAN]:[]};function n(e,t){return e.some(e=>i[e]?.includes(t))}},9230:(e,t,r)=>{"use strict";r.r(t),r.d(t,{accounts:()=>d,apiKeys:()=>y,apiKeysRelations:()=>_,emails:()=>u,messages:()=>m,revoked_credentials:()=>w,roles:()=>f,rolesRelations:()=>q,userRoles:()=>g,userRolesRelations:()=>h,users:()=>c,usersRelations:()=>I,webhooks:()=>p});var s=r(7243),a=r(1939),i=r(3268),n=r(3797),o=r(1870),l=r(652);let c=(0,s.D)("user",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,a.Qq)("name"),email:(0,a.Qq)("email").unique(),emailVerified:(0,i.nd)("emailVerified",{mode:"timestamp_ms"}),image:(0,a.Qq)("image"),username:(0,a.Qq)("username").unique(),password:(0,a.Qq)("password")}),d=(0,s.D)("account",{userId:(0,a.Qq)("userId").notNull().references(()=>c.id,{onDelete:"cascade"}),type:(0,a.Qq)("type").$type().notNull(),provider:(0,a.Qq)("provider").notNull(),providerAccountId:(0,a.Qq)("providerAccountId").notNull(),refresh_token:(0,a.Qq)("refresh_token"),access_token:(0,a.Qq)("access_token"),expires_at:(0,i.nd)("expires_at"),token_type:(0,a.Qq)("token_type"),scope:(0,a.Qq)("scope"),id_token:(0,a.Qq)("id_token"),session_state:(0,a.Qq)("session_state")},e=>({compoundKey:(0,n.ie)({columns:[e.provider,e.providerAccountId]})})),u=(0,s.D)("email",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),address:(0,a.Qq)("address").notNull().unique(),userId:(0,a.Qq)("userId").references(()=>c.id,{onDelete:"cascade"}),createdAt:(0,i.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),expiresAt:(0,i.nd)("expires_at",{mode:"timestamp_ms"}).notNull()},e=>({expiresAtIdx:(0,o.Pe)("email_expires_at_idx").on(e.expiresAt)})),m=(0,s.D)("message",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),emailId:(0,a.Qq)("emailId").notNull().references(()=>u.id,{onDelete:"cascade"}),fromAddress:(0,a.Qq)("from_address").notNull(),subject:(0,a.Qq)("subject").notNull(),content:(0,a.Qq)("content").notNull(),html:(0,a.Qq)("html"),receivedAt:(0,i.nd)("received_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)},e=>({emailIdIdx:(0,o.Pe)("message_email_id_idx").on(e.emailId)})),p=(0,s.D)("webhook",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,a.Qq)("user_id").notNull().references(()=>c.id,{onDelete:"cascade"}),url:(0,a.Qq)("url").notNull(),enabled:(0,i.nd)("enabled",{mode:"boolean"}).notNull().default(!0),createdAt:(0,i.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),updatedAt:(0,i.nd)("updated_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)}),f=(0,s.D)("role",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,a.Qq)("name").notNull(),description:(0,a.Qq)("description"),createdAt:(0,i.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),updatedAt:(0,i.nd)("updated_at",{mode:"timestamp"}).$defaultFn(()=>new Date)}),g=(0,s.D)("user_role",{userId:(0,a.Qq)("user_id").notNull().references(()=>c.id,{onDelete:"cascade"}),roleId:(0,a.Qq)("role_id").notNull().references(()=>f.id,{onDelete:"cascade"}),createdAt:(0,i.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date)},e=>({pk:(0,n.ie)({columns:[e.userId,e.roleId]})})),y=(0,s.D)("api_keys",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,a.Qq)("user_id").notNull().references(()=>c.id),name:(0,a.Qq)("name").notNull(),key:(0,a.Qq)("key").notNull().unique(),createdAt:(0,i.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),expiresAt:(0,i.nd)("expires_at",{mode:"timestamp"}),enabled:(0,i.nd)("enabled",{mode:"boolean"}).notNull().default(!0)},e=>({nameUserIdUnique:(0,o.GL)("name_user_id_unique").on(e.name,e.userId)})),w=(0,s.D)("revoked_credential",{jti:(0,a.Qq)("jti").primaryKey(),expiresAt:(0,i.nd)("expires_at",{mode:"timestamp_ms"}).notNull()},e=>({expiresAtIdx:(0,o.Pe)("revoked_expires_at_idx").on(e.expiresAt)})),_=(0,l.K1)(y,({one:e})=>({user:e(c,{fields:[y.userId],references:[c.id]})})),h=(0,l.K1)(g,({one:e})=>({user:e(c,{fields:[g.userId],references:[c.id]}),role:e(f,{fields:[g.roleId],references:[f.id]})})),I=(0,l.K1)(c,({many:e})=>({userRoles:e(g),apiKeys:e(y)})),q=(0,l.K1)(f,({many:e})=>({userRoles:e(g)}))},2596:(e,t,r)=>{"use strict";r.d(t,{E:()=>n,b:()=>o,cn:()=>i});var s=r(8649),a=r(5588);function i(...e){return(0,a.QP)((0,s.$)(e))}async function n(e){let t=new TextEncoder,r=process.env.AUTH_SECRET||"",s=t.encode(e+r);return btoa(String.fromCharCode(...new Uint8Array(await crypto.subtle.digest("SHA-256",s))))}async function o(e,t){return await n(e)===t}},2342:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a});var s=r(9267);let a=s.z.object({username:s.z.string().min(1,"用户名不能为空").max(20,"用户名不能超过20个字符").regex(/^[a-zA-Z0-9_-]+$/,"用户名只能包含字母、数字、下划线和横杠").refine(e=>!e.includes("@"),"用户名不能是邮箱格式"),password:s.z.string().min(8,"密码长度必须大于等于8位")})}},e=>{var t=t=>e(e.s=t);e.O(0,[810,702,267,658,51,92],()=>t(6869));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/roles/init-emperor/route"]=r}]);
//# sourceMappingURL=route.js.map