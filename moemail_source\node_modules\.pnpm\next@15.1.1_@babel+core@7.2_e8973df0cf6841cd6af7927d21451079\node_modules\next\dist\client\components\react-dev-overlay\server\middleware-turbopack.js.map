{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/middleware-turbopack.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport {\n  badRequest,\n  getOriginalCodeFrame,\n  internalServerError,\n  json,\n  jsonString,\n  noContent,\n  type OriginalStackFrameResponse,\n} from './shared'\n\nimport fs, { constants as FS } from 'fs/promises'\nimport path from 'path'\nimport url from 'url'\nimport { launchEditor } from '../internal/helpers/launchEditor'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport { SourceMapConsumer } from 'next/dist/compiled/source-map08'\nimport type { Project, TurbopackStackFrame } from '../../../../build/swc/types'\nimport { getSourceMapFromFile } from '../internal/helpers/get-source-map-from-file'\nimport { findSourceMap, type SourceMapPayload } from 'node:module'\n\nfunction shouldIgnorePath(modulePath: string): boolean {\n  return (\n    modulePath.includes('node_modules') ||\n    // Only relevant for when Next.js is symlinked e.g. in the Next.js monorepo\n    modulePath.includes('next/dist')\n  )\n}\n\ntype IgnorableStackFrame = StackFrame & { ignored: boolean }\n\nconst currentSourcesByFile: Map<string, Promise<string | null>> = new Map()\nexport async function batchedTraceSource(\n  project: Project,\n  frame: TurbopackStackFrame\n): Promise<{ frame: IgnorableStackFrame; source: string | null } | undefined> {\n  const file = frame.file\n    ? // TODO(veil): Why are the frames sent encoded?\n      decodeURIComponent(frame.file)\n    : undefined\n  if (!file) return\n\n  const sourceFrame = await project.traceSource(frame)\n  if (!sourceFrame) {\n    return {\n      frame: {\n        file,\n        lineNumber: frame.line ?? 0,\n        column: frame.column ?? 0,\n        methodName: frame.methodName ?? '<unknown>',\n        ignored: shouldIgnorePath(frame.file),\n        arguments: [],\n      },\n      source: null,\n    }\n  }\n\n  let source = null\n  // Don't look up source for node_modules or internals. These can often be large bundled files.\n  const ignored =\n    shouldIgnorePath(sourceFrame.file) ||\n    // isInternal means resource starts with turbopack://[turbopack]\n    !!sourceFrame.isInternal\n  if (sourceFrame && sourceFrame.file && !ignored) {\n    let sourcePromise = currentSourcesByFile.get(sourceFrame.file)\n    if (!sourcePromise) {\n      sourcePromise = project.getSourceForAsset(sourceFrame.file)\n      currentSourcesByFile.set(sourceFrame.file, sourcePromise)\n      setTimeout(() => {\n        // Cache file reads for 100ms, as frames will often reference the same\n        // files and can be large.\n        currentSourcesByFile.delete(sourceFrame.file!)\n      }, 100)\n    }\n    source = await sourcePromise\n  }\n\n  // TODO: get ignoredList from turbopack source map\n  const ignorableFrame = {\n    file: sourceFrame.file,\n    lineNumber: sourceFrame.line ?? 0,\n    column: sourceFrame.column ?? 0,\n    methodName: sourceFrame.methodName ?? frame.methodName ?? '<unknown>',\n    ignored,\n    arguments: [],\n  }\n\n  return {\n    frame: ignorableFrame,\n    source,\n  }\n}\n\nfunction createStackFrame(searchParams: URLSearchParams) {\n  const fileParam = searchParams.get('file')\n\n  if (!fileParam) {\n    return undefined\n  }\n\n  // rsc://React/Server/file://<filename>?42 => file://<filename>\n  const file = fileParam\n    .replace(/^rsc:\\/\\/React\\/[^/]+\\//, '')\n    .replace(/\\?\\d+$/, '')\n\n  return {\n    file,\n    methodName: searchParams.get('methodName') ?? '<unknown>',\n    line: parseInt(searchParams.get('lineNumber') ?? '0', 10) || 0,\n    column: parseInt(searchParams.get('column') ?? '0', 10) || 0,\n    isServer: searchParams.get('isServer') === 'true',\n  } satisfies TurbopackStackFrame\n}\n\n/**\n * https://tc39.es/source-map/#index-map\n */\ninterface IndexSourceMapSection {\n  offset: {\n    line: number\n    column: number\n  }\n  map: ModernRawSourceMap\n}\n\n// TODO(veil): Upstream types\ninterface IndexSourceMap {\n  version: number\n  file: string\n  sections: IndexSourceMapSection[]\n}\n\ninterface ModernRawSourceMap extends SourceMapPayload {\n  ignoreList?: number[]\n}\n\ntype ModernSourceMapPayload = ModernRawSourceMap | IndexSourceMap\n\n/**\n * Finds the sourcemap payload applicable to a given frame.\n * Equal to the input unless an Index Source Map is used.\n */\nfunction findApplicableSourceMapPayload(\n  frame: TurbopackStackFrame,\n  payload: ModernSourceMapPayload\n): ModernRawSourceMap | undefined {\n  if ('sections' in payload) {\n    const frameLine = frame.line ?? 0\n    const frameColumn = frame.column ?? 0\n    // Sections must not overlap and must be sorted: https://tc39.es/source-map/#section-object\n    // Therefore the last section that has an offset less than or equal to the frame is the applicable one.\n    // TODO(veil): Binary search\n    let section: IndexSourceMapSection | undefined = payload.sections[0]\n    for (\n      let i = 0;\n      i < payload.sections.length &&\n      payload.sections[i].offset.line <= frameLine &&\n      payload.sections[i].offset.column <= frameColumn;\n      i++\n    ) {\n      section = payload.sections[i]\n    }\n\n    return section === undefined ? undefined : section.map\n  } else {\n    return payload\n  }\n}\n\nasync function nativeTraceSource(\n  frame: TurbopackStackFrame\n): Promise<{ frame: IgnorableStackFrame; source: string | null } | undefined> {\n  const sourceMap = findSourceMap(\n    // TODO(veil): Why are the frames sent encoded?\n    decodeURIComponent(frame.file)\n  )\n  if (sourceMap !== undefined) {\n    const traced = await SourceMapConsumer.with(\n      sourceMap.payload,\n      null,\n      async (consumer) => {\n        const originalPosition = consumer.originalPositionFor({\n          line: frame.line ?? 1,\n          column: frame.column ?? 1,\n        })\n\n        if (originalPosition.source === null) {\n          return null\n        }\n\n        const sourceContent: string | null =\n          consumer.sourceContentFor(\n            originalPosition.source,\n            /* returnNullOnMissing */ true\n          ) ?? null\n\n        return { originalPosition, sourceContent }\n      }\n    )\n\n    if (traced !== null) {\n      const { originalPosition, sourceContent } = traced\n      const applicableSourceMap = findApplicableSourceMapPayload(\n        frame,\n        sourceMap.payload\n      )\n\n      // TODO(veil): Upstream a method to sourcemap consumer that immediately says if a frame is ignored or not.\n      let ignored = false\n      if (applicableSourceMap === undefined) {\n        console.error(\n          'No applicable source map found in sections for frame',\n          frame\n        )\n      } else {\n        // TODO: O(n^2). Consider moving `ignoreList` into a Set\n        const sourceIndex = applicableSourceMap.sources.indexOf(\n          originalPosition.source!\n        )\n        ignored = applicableSourceMap.ignoreList?.includes(sourceIndex) ?? false\n      }\n\n      const originalStackFrame: IgnorableStackFrame = {\n        methodName:\n          originalPosition.name ||\n          // default is not a valid identifier in JS so webpack uses a custom variable when it's an unnamed default export\n          // Resolve it back to `default` for the method name if the source position didn't have the method.\n          frame.methodName\n            ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n            ?.replace('__webpack_exports__.', '') ||\n          '<unknown>',\n        column: (originalPosition.column ?? 0) + 1,\n        file: originalPosition.source?.startsWith('file://')\n          ? path.relative(\n              process.cwd(),\n              url.fileURLToPath(originalPosition.source)\n            )\n          : originalPosition.source,\n        lineNumber: originalPosition.line ?? 0,\n        // TODO: c&p from async createOriginalStackFrame but why not frame.arguments?\n        arguments: [],\n        ignored,\n      }\n\n      return {\n        frame: originalStackFrame,\n        source: sourceContent,\n      }\n    }\n  }\n\n  return undefined\n}\n\nasync function createOriginalStackFrame(\n  project: Project,\n  frame: TurbopackStackFrame\n): Promise<OriginalStackFrameResponse | null> {\n  const traced =\n    (await nativeTraceSource(frame)) ??\n    // TODO(veil): When would the bundler know more than native?\n    // If it's faster, try the bundler first and fall back to native later.\n    (await batchedTraceSource(project, frame))\n  if (!traced) {\n    return null\n  }\n\n  return {\n    originalStackFrame: traced.frame,\n    originalCodeFrame: getOriginalCodeFrame(traced.frame, traced.source),\n  }\n}\n\nexport function getOverlayMiddleware(project: Project) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(req.url!, 'http://n')\n\n    if (pathname === '/__nextjs_original-stack-frame') {\n      const frame = createStackFrame(searchParams)\n\n      if (!frame) return badRequest(res)\n\n      let originalStackFrame: OriginalStackFrameResponse | null\n      try {\n        originalStackFrame = await createOriginalStackFrame(project, frame)\n      } catch (e: any) {\n        return internalServerError(res, e.message)\n      }\n\n      if (!originalStackFrame) {\n        res.statusCode = 404\n        res.end('Unable to resolve sourcemap')\n        return\n      }\n\n      return json(res, originalStackFrame)\n    } else if (pathname === '/__nextjs_launch-editor') {\n      const frame = createStackFrame(searchParams)\n\n      if (!frame) return badRequest(res)\n\n      const fileExists = await fs.access(frame.file, FS.F_OK).then(\n        () => true,\n        () => false\n      )\n      if (!fileExists) return noContent(res)\n\n      try {\n        launchEditor(frame.file, frame.line ?? 1, frame.column ?? 1)\n      } catch (err) {\n        console.log('Failed to launch editor:', err)\n        return internalServerError(res)\n      }\n\n      noContent(res)\n    }\n\n    return next()\n  }\n}\n\nexport function getSourceMapMiddleware(project: Project) {\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(req.url!, 'http://n')\n\n    if (pathname !== '/__nextjs_source-map') {\n      return next()\n    }\n\n    let filename = searchParams.get('filename')\n\n    if (!filename) {\n      return badRequest(res)\n    }\n\n    // TODO(veil): Always try the native version first.\n    // Externals could also be files that aren't bundled via Webpack.\n    if (\n      filename.startsWith('webpack://') ||\n      filename.startsWith('webpack-internal:///')\n    ) {\n      const sourceMap = findSourceMap(filename)\n\n      if (sourceMap) {\n        return json(res, sourceMap.payload)\n      }\n\n      return noContent(res)\n    }\n\n    try {\n      // Turbopack chunk filenames might be URL-encoded.\n      filename = decodeURI(filename)\n\n      if (path.isAbsolute(filename)) {\n        filename = url.pathToFileURL(filename).href\n      }\n\n      const sourceMapString = await project.getSourceMap(filename)\n\n      if (sourceMapString) {\n        return jsonString(res, sourceMapString)\n      }\n\n      if (filename.startsWith('file:')) {\n        const sourceMap = await getSourceMapFromFile(filename)\n\n        if (sourceMap) {\n          return json(res, sourceMap)\n        }\n      }\n    } catch (error) {\n      console.error('Failed to get source map:', error)\n    }\n\n    noContent(res)\n  }\n}\n"], "names": ["batchedTraceSource", "getOverlayMiddleware", "getSourceMapMiddleware", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modulePath", "includes", "currentSourcesByFile", "Map", "project", "frame", "file", "decodeURIComponent", "undefined", "sourceFrame", "traceSource", "lineNumber", "line", "column", "methodName", "ignored", "arguments", "source", "isInternal", "sourcePromise", "get", "getSourceForAsset", "set", "setTimeout", "delete", "ignorableFrame", "createStackFrame", "searchParams", "fileParam", "replace", "parseInt", "isServer", "findApplicableSourceMapPayload", "payload", "frameLine", "frameColumn", "section", "sections", "i", "length", "offset", "map", "nativeTraceSource", "sourceMap", "findSourceMap", "traced", "SourceMapConsumer", "with", "consumer", "originalPosition", "originalPositionFor", "sourceContent", "sourceContentFor", "applicableSourceMap", "console", "error", "sourceIndex", "sources", "indexOf", "ignoreList", "originalStackFrame", "name", "startsWith", "path", "relative", "process", "cwd", "url", "fileURLToPath", "createOriginalStackFrame", "originalCodeFrame", "getOriginalCodeFrame", "req", "res", "next", "pathname", "URL", "badRequest", "e", "internalServerError", "message", "statusCode", "end", "json", "fileExists", "fs", "access", "FS", "F_OK", "then", "noContent", "launchEditor", "err", "log", "filename", "decodeURI", "isAbsolute", "pathToFileURL", "href", "sourceMapString", "getSourceMap", "jsonString", "getSourceMapFromFile"], "mappings": ";;;;;;;;;;;;;;;;IAgCsBA,kBAAkB;eAAlBA;;IAiPNC,oBAAoB;eAApBA;;IAoDAC,sBAAsB;eAAtBA;;;;;wBA5TT;oEAE6B;+DACnB;8DACD;8BACa;6BAEK;sCAEG;4BACgB;AAErD,SAASC,iBAAiBC,UAAkB;IAC1C,OACEA,WAAWC,QAAQ,CAAC,mBACpB,2EAA2E;IAC3ED,WAAWC,QAAQ,CAAC;AAExB;AAIA,MAAMC,uBAA4D,IAAIC;AAC/D,eAAeP,mBACpBQ,OAAgB,EAChBC,KAA0B;IAE1B,MAAMC,OAAOD,MAAMC,IAAI,GAEnBC,mBAAmBF,MAAMC,IAAI,IAC7BE;IACJ,IAAI,CAACF,MAAM;IAEX,MAAMG,cAAc,MAAML,QAAQM,WAAW,CAACL;IAC9C,IAAI,CAACI,aAAa;YAIAJ,aACJA,eACIA;QALhB,OAAO;YACLA,OAAO;gBACLC;gBACAK,YAAYN,CAAAA,cAAAA,MAAMO,IAAI,YAAVP,cAAc;gBAC1BQ,QAAQR,CAAAA,gBAAAA,MAAMQ,MAAM,YAAZR,gBAAgB;gBACxBS,YAAYT,CAAAA,oBAAAA,MAAMS,UAAU,YAAhBT,oBAAoB;gBAChCU,SAAShB,iBAAiBM,MAAMC,IAAI;gBACpCU,WAAW,EAAE;YACf;YACAC,QAAQ;QACV;IACF;IAEA,IAAIA,SAAS;IACb,8FAA8F;IAC9F,MAAMF,UACJhB,iBAAiBU,YAAYH,IAAI,KACjC,gEAAgE;IAChE,CAAC,CAACG,YAAYS,UAAU;IAC1B,IAAIT,eAAeA,YAAYH,IAAI,IAAI,CAACS,SAAS;QAC/C,IAAII,gBAAgBjB,qBAAqBkB,GAAG,CAACX,YAAYH,IAAI;QAC7D,IAAI,CAACa,eAAe;YAClBA,gBAAgBf,QAAQiB,iBAAiB,CAACZ,YAAYH,IAAI;YAC1DJ,qBAAqBoB,GAAG,CAACb,YAAYH,IAAI,EAAEa;YAC3CI,WAAW;gBACT,sEAAsE;gBACtE,0BAA0B;gBAC1BrB,qBAAqBsB,MAAM,CAACf,YAAYH,IAAI;YAC9C,GAAG;QACL;QACAW,SAAS,MAAME;IACjB;QAKcV,mBACJA,qBACIA,yBAAAA;IALd,kDAAkD;IAClD,MAAMgB,iBAAiB;QACrBnB,MAAMG,YAAYH,IAAI;QACtBK,YAAYF,CAAAA,oBAAAA,YAAYG,IAAI,YAAhBH,oBAAoB;QAChCI,QAAQJ,CAAAA,sBAAAA,YAAYI,MAAM,YAAlBJ,sBAAsB;QAC9BK,YAAYL,CAAAA,OAAAA,CAAAA,0BAAAA,YAAYK,UAAU,YAAtBL,0BAA0BJ,MAAMS,UAAU,YAA1CL,OAA8C;QAC1DM;QACAC,WAAW,EAAE;IACf;IAEA,OAAO;QACLX,OAAOoB;QACPR;IACF;AACF;AAEA,SAASS,iBAAiBC,YAA6B;IACrD,MAAMC,YAAYD,aAAaP,GAAG,CAAC;IAEnC,IAAI,CAACQ,WAAW;QACd,OAAOpB;IACT;IAEA,+DAA+D;IAC/D,MAAMF,OAAOsB,UACVC,OAAO,CAAC,2BAA2B,IACnCA,OAAO,CAAC,UAAU;QAIPF,mBACGA,oBACEA;IAJnB,OAAO;QACLrB;QACAQ,YAAYa,CAAAA,oBAAAA,aAAaP,GAAG,CAAC,yBAAjBO,oBAAkC;QAC9Cf,MAAMkB,SAASH,CAAAA,qBAAAA,aAAaP,GAAG,CAAC,yBAAjBO,qBAAkC,KAAK,OAAO;QAC7Dd,QAAQiB,SAASH,CAAAA,qBAAAA,aAAaP,GAAG,CAAC,qBAAjBO,qBAA8B,KAAK,OAAO;QAC3DI,UAAUJ,aAAaP,GAAG,CAAC,gBAAgB;IAC7C;AACF;AA0BA;;;CAGC,GACD,SAASY,+BACP3B,KAA0B,EAC1B4B,OAA+B;IAE/B,IAAI,cAAcA,SAAS;YACP5B;QAAlB,MAAM6B,YAAY7B,CAAAA,cAAAA,MAAMO,IAAI,YAAVP,cAAc;YACZA;QAApB,MAAM8B,cAAc9B,CAAAA,gBAAAA,MAAMQ,MAAM,YAAZR,gBAAgB;QACpC,2FAA2F;QAC3F,uGAAuG;QACvG,4BAA4B;QAC5B,IAAI+B,UAA6CH,QAAQI,QAAQ,CAAC,EAAE;QACpE,IACE,IAAIC,IAAI,GACRA,IAAIL,QAAQI,QAAQ,CAACE,MAAM,IAC3BN,QAAQI,QAAQ,CAACC,EAAE,CAACE,MAAM,CAAC5B,IAAI,IAAIsB,aACnCD,QAAQI,QAAQ,CAACC,EAAE,CAACE,MAAM,CAAC3B,MAAM,IAAIsB,aACrCG,IACA;YACAF,UAAUH,QAAQI,QAAQ,CAACC,EAAE;QAC/B;QAEA,OAAOF,YAAY5B,YAAYA,YAAY4B,QAAQK,GAAG;IACxD,OAAO;QACL,OAAOR;IACT;AACF;AAEA,eAAeS,kBACbrC,KAA0B;IAE1B,MAAMsC,YAAYC,IAAAA,yBAAa,EAC7B,+CAA+C;IAC/CrC,mBAAmBF,MAAMC,IAAI;IAE/B,IAAIqC,cAAcnC,WAAW;QAC3B,MAAMqC,SAAS,MAAMC,8BAAiB,CAACC,IAAI,CACzCJ,UAAUV,OAAO,EACjB,MACA,OAAOe;gBAEG3C,aACEA;YAFV,MAAM4C,mBAAmBD,SAASE,mBAAmB,CAAC;gBACpDtC,MAAMP,CAAAA,cAAAA,MAAMO,IAAI,YAAVP,cAAc;gBACpBQ,QAAQR,CAAAA,gBAAAA,MAAMQ,MAAM,YAAZR,gBAAgB;YAC1B;YAEA,IAAI4C,iBAAiBhC,MAAM,KAAK,MAAM;gBACpC,OAAO;YACT;gBAGE+B;YADF,MAAMG,gBACJH,CAAAA,6BAAAA,SAASI,gBAAgB,CACvBH,iBAAiBhC,MAAM,EACvB,uBAAuB,GAAG,iBAF5B+B,6BAGK;YAEP,OAAO;gBAAEC;gBAAkBE;YAAc;QAC3C;QAGF,IAAIN,WAAW,MAAM;gBAyBf,gHAAgH;YAChH,kGAAkG;YAClGxC,2BAAAA,mBAKI4C;YA/BR,MAAM,EAAEA,gBAAgB,EAAEE,aAAa,EAAE,GAAGN;YAC5C,MAAMQ,sBAAsBrB,+BAC1B3B,OACAsC,UAAUV,OAAO;YAGnB,0GAA0G;YAC1G,IAAIlB,UAAU;YACd,IAAIsC,wBAAwB7C,WAAW;gBACrC8C,QAAQC,KAAK,CACX,wDACAlD;YAEJ,OAAO;oBAKKgD;gBAJV,wDAAwD;gBACxD,MAAMG,cAAcH,oBAAoBI,OAAO,CAACC,OAAO,CACrDT,iBAAiBhC,MAAM;oBAEfoC;gBAAVtC,UAAUsC,CAAAA,4CAAAA,kCAAAA,oBAAoBM,UAAU,qBAA9BN,gCAAgCpD,QAAQ,CAACuD,wBAAzCH,2CAAyD;YACrE;gBAWWJ,0BAOGA;YAhBd,MAAMW,qBAA0C;gBAC9C9C,YACEmC,iBAAiBY,IAAI,MAGrBxD,oBAAAA,MAAMS,UAAU,sBAAhBT,4BAAAA,kBACIwB,OAAO,CAAC,8BAA8B,+BAD1CxB,0BAEIwB,OAAO,CAAC,wBAAwB,QACpC;gBACFhB,QAAQ,AAACoC,CAAAA,CAAAA,2BAAAA,iBAAiBpC,MAAM,YAAvBoC,2BAA2B,CAAA,IAAK;gBACzC3C,MAAM2C,EAAAA,2BAAAA,iBAAiBhC,MAAM,qBAAvBgC,yBAAyBa,UAAU,CAAC,cACtCC,aAAI,CAACC,QAAQ,CACXC,QAAQC,GAAG,IACXC,YAAG,CAACC,aAAa,CAACnB,iBAAiBhC,MAAM,KAE3CgC,iBAAiBhC,MAAM;gBAC3BN,YAAYsC,CAAAA,yBAAAA,iBAAiBrC,IAAI,YAArBqC,yBAAyB;gBACrC,6EAA6E;gBAC7EjC,WAAW,EAAE;gBACbD;YACF;YAEA,OAAO;gBACLV,OAAOuD;gBACP3C,QAAQkC;YACV;QACF;IACF;IAEA,OAAO3C;AACT;AAEA,eAAe6D,yBACbjE,OAAgB,EAChBC,KAA0B;QAGvB;IADH,MAAMwC,SACJ,CAAC,OAAA,MAAMH,kBAAkBrC,kBAAxB,OACD,4DAA4D;IAC5D,uEAAuE;IACtE,MAAMT,mBAAmBQ,SAASC;IACrC,IAAI,CAACwC,QAAQ;QACX,OAAO;IACT;IAEA,OAAO;QACLe,oBAAoBf,OAAOxC,KAAK;QAChCiE,mBAAmBC,IAAAA,4BAAoB,EAAC1B,OAAOxC,KAAK,EAAEwC,OAAO5B,MAAM;IACrE;AACF;AAEO,SAASpB,qBAAqBO,OAAgB;IACnD,OAAO,eACLoE,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEhD,YAAY,EAAE,GAAG,IAAIiD,IAAIJ,IAAIL,GAAG,EAAG;QAErD,IAAIQ,aAAa,kCAAkC;YACjD,MAAMtE,QAAQqB,iBAAiBC;YAE/B,IAAI,CAACtB,OAAO,OAAOwE,IAAAA,kBAAU,EAACJ;YAE9B,IAAIb;YACJ,IAAI;gBACFA,qBAAqB,MAAMS,yBAAyBjE,SAASC;YAC/D,EAAE,OAAOyE,GAAQ;gBACf,OAAOC,IAAAA,2BAAmB,EAACN,KAAKK,EAAEE,OAAO;YAC3C;YAEA,IAAI,CAACpB,oBAAoB;gBACvBa,IAAIQ,UAAU,GAAG;gBACjBR,IAAIS,GAAG,CAAC;gBACR;YACF;YAEA,OAAOC,IAAAA,YAAI,EAACV,KAAKb;QACnB,OAAO,IAAIe,aAAa,2BAA2B;YACjD,MAAMtE,QAAQqB,iBAAiBC;YAE/B,IAAI,CAACtB,OAAO,OAAOwE,IAAAA,kBAAU,EAACJ;YAE9B,MAAMW,aAAa,MAAMC,iBAAE,CAACC,MAAM,CAACjF,MAAMC,IAAI,EAAEiF,mBAAE,CAACC,IAAI,EAAEC,IAAI,CAC1D,IAAM,MACN,IAAM;YAER,IAAI,CAACL,YAAY,OAAOM,IAAAA,iBAAS,EAACjB;YAElC,IAAI;oBACuBpE,aAAiBA;gBAA1CsF,IAAAA,0BAAY,EAACtF,MAAMC,IAAI,EAAED,CAAAA,cAAAA,MAAMO,IAAI,YAAVP,cAAc,GAAGA,CAAAA,gBAAAA,MAAMQ,MAAM,YAAZR,gBAAgB;YAC5D,EAAE,OAAOuF,KAAK;gBACZtC,QAAQuC,GAAG,CAAC,4BAA4BD;gBACxC,OAAOb,IAAAA,2BAAmB,EAACN;YAC7B;YAEAiB,IAAAA,iBAAS,EAACjB;QACZ;QAEA,OAAOC;IACT;AACF;AAEO,SAAS5E,uBAAuBM,OAAgB;IACrD,OAAO,eACLoE,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEhD,YAAY,EAAE,GAAG,IAAIiD,IAAIJ,IAAIL,GAAG,EAAG;QAErD,IAAIQ,aAAa,wBAAwB;YACvC,OAAOD;QACT;QAEA,IAAIoB,WAAWnE,aAAaP,GAAG,CAAC;QAEhC,IAAI,CAAC0E,UAAU;YACb,OAAOjB,IAAAA,kBAAU,EAACJ;QACpB;QAEA,mDAAmD;QACnD,iEAAiE;QACjE,IACEqB,SAAShC,UAAU,CAAC,iBACpBgC,SAAShC,UAAU,CAAC,yBACpB;YACA,MAAMnB,YAAYC,IAAAA,yBAAa,EAACkD;YAEhC,IAAInD,WAAW;gBACb,OAAOwC,IAAAA,YAAI,EAACV,KAAK9B,UAAUV,OAAO;YACpC;YAEA,OAAOyD,IAAAA,iBAAS,EAACjB;QACnB;QAEA,IAAI;YACF,kDAAkD;YAClDqB,WAAWC,UAAUD;YAErB,IAAI/B,aAAI,CAACiC,UAAU,CAACF,WAAW;gBAC7BA,WAAW3B,YAAG,CAAC8B,aAAa,CAACH,UAAUI,IAAI;YAC7C;YAEA,MAAMC,kBAAkB,MAAM/F,QAAQgG,YAAY,CAACN;YAEnD,IAAIK,iBAAiB;gBACnB,OAAOE,IAAAA,kBAAU,EAAC5B,KAAK0B;YACzB;YAEA,IAAIL,SAAShC,UAAU,CAAC,UAAU;gBAChC,MAAMnB,YAAY,MAAM2D,IAAAA,0CAAoB,EAACR;gBAE7C,IAAInD,WAAW;oBACb,OAAOwC,IAAAA,YAAI,EAACV,KAAK9B;gBACnB;YACF;QACF,EAAE,OAAOY,OAAO;YACdD,QAAQC,KAAK,CAAC,6BAA6BA;QAC7C;QAEAmC,IAAAA,iBAAS,EAACjB;IACZ;AACF"}