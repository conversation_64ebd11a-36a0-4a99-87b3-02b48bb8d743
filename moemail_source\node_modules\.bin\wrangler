#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/wrangler@3.114.9_@cloudflare+workers-types@4.20250607.0/node_modules/wrangler/bin/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/wrangler@3.114.9_@cloudflare+workers-types@4.20250607.0/node_modules/wrangler/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/wrangler@3.114.9_@cloudflare+workers-types@4.20250607.0/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/wrangler@3.114.9_@cloudflare+workers-types@4.20250607.0/node_modules/wrangler/bin/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/wrangler@3.114.9_@cloudflare+workers-types@4.20250607.0/node_modules/wrangler/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/wrangler@3.114.9_@cloudflare+workers-types@4.20250607.0/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/wrangler@3.114.9_@cloudflare+workers-types@4.20250607.0/node_modules/wrangler/bin/wrangler.js" "$@"
else
  exec node  "$basedir/../.pnpm/wrangler@3.114.9_@cloudflare+workers-types@4.20250607.0/node_modules/wrangler/bin/wrangler.js" "$@"
fi
