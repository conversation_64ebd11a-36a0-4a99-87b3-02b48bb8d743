{"version": 3, "file": "edge-chunks/559.js", "mappings": "6EAAA,sCAA4I,CAE5I,sCAA2J,CAE3J,sCAA2I,CAE3I,sCAA+H,iBCN/H,sCAA4I,CAE5I,sCAA2J,CAE3J,sCAA2I,CAE3I,sCAA+H,iBCN/H,sCAAiO,CAEjO,sCAAkO,CAElO,sCAAqO,CAErO,sCAAqO,CAErO,sCAA2P,CAE3P,sCAAoO,CAEpO,sCAAmP,CAEnP,sCAA4P,CAE5P,sCAAmO,CAEnO,sCAAiP,CAEjP,sCAAmP,CAEnP,sCAAmP,iBCtBnP,sCAAiO,CAEjO,sCAAkO,CAElO,sCAAqO,CAErO,sCAAqO,CAErO,sCAA2P,CAE3P,sCAAoO,CAEpO,sCAAmP,CAEnP,sCAA4P,CAE5P,sCAAmO,CAEnO,sCAAiP,CAEjP,qCAAmP,CAEnP,sCAAmP,mGCX5O,SAASA,IACd,MACE,UAACC,MAAAA,CAAIC,UAAU,kCACb,UAACC,EAAAA,EAAeA,CAAAA,UACd,WAACC,EAAAA,EAAOA,CAAAA,WACN,UAACC,EAAAA,EAAcA,CAAAA,CAACC,OAAO,aACrB,WAACC,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,UACRC,KAAK,OACLP,UAAU,sFACVQ,QAAS,IAAMC,OAAOC,IAAI,CAAC,wCAAyC,oBAEpE,UAACC,EAAAA,CAAMA,CAAAA,CACLX,UAAU,2EAEZ,UAACY,OAAAA,CAAKZ,UAAU,mBAAU,iBAG9B,UAACa,EAAAA,EAAcA,CAAAA,UACb,UAACd,MAAAA,CAAIC,UAAU,mBACb,UAACc,IAAAA,UAAE,sBAOjB,oFCjCO,SAASC,EAAc,UAAEC,CAAQ,CAAE,GAAGC,EAA2B,EACtE,MAAO,UAACC,EAAAA,CAAkBA,CAAAA,CAAE,GAAGD,CAAK,UAAGD,GACzC,8GCFA,IAAMG,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CACxB,oNACA,CACEC,SAAU,CACRf,QAAS,CACPgB,QAAS,gEACTC,YAAa,+EACbC,QAAS,4FACTC,UAAW,yEACXC,MAAO,+CACPC,KAAM,iDACR,EACApB,KAAM,CACJe,QAAS,gBACTM,GAAI,8BACJC,GAAI,uBACJC,KAAM,SACR,CACF,EACAC,gBAAiB,CACfzB,QAAS,UACTC,KAAM,SACR,CACF,GASIF,EAAS2B,EAAAA,UAAgB,CAC7B,CAAC,WAAEhC,CAAS,SAAEM,CAAO,MAAEC,CAAI,SAAEH,GAAU,CAAK,CAAE,GAAGa,EAAO,CAAEgB,KACxD,IAAMC,EAAO9B,EAAU+B,EAAAA,EAAIA,CAAG,SAC9B,MACE,UAACD,EAAAA,CACClC,UAAWoC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACjB,EAAe,SAAEb,EAASC,iBAAMP,CAAU,IACxDiC,IAAKA,EACJ,GAAGhB,CAAK,EAGf,GAEFZ,EAAOgC,WAAW,CAAG,6HCzCrB,IAAMC,EAAgBC,EAAAA,EAAwB,CAExCC,EAAgBR,EAAAA,UAAgB,CAGpC,CAAC,WAAEhC,CAAS,CAAE,GAAGiB,EAAO,CAAEgB,IAC1B,UAACM,EAAAA,EAAwB,EACvBN,IAAKA,EACLjC,UAAWoC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,oIACApC,GAED,GAAGiB,CAAK,IAGbuB,EAAcH,WAAW,CAAGE,EAAAA,EAAwB,CAACF,WAAW,CAEhE,IAAMI,EAAgBrB,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CACvB,4lBACA,CACEC,SAAU,CACRf,QAAS,CACPgB,QAAS,uCACTC,YACE,iFACJ,CACF,EACAQ,gBAAiB,CACfzB,QAAS,SACX,CACF,GAGIoC,EAAQV,EAAAA,UAAgB,CAI5B,CAAC,WAAEhC,CAAS,SAAEM,CAAO,CAAE,GAAGW,EAAO,CAAEgB,IAEjC,UAACM,EAAAA,EAAoB,EACnBN,IAAKA,EACLjC,UAAWoC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACK,EAAc,SAAEnC,CAAQ,GAAIN,GACzC,GAAGiB,CAAK,GAIfyB,GAAML,WAAW,CAAGE,EAAAA,EAAoB,CAACF,WAAW,CAEpD,IAAMM,EAAaX,EAAAA,UAAgB,CAGjC,CAAC,WAAEhC,CAAS,CAAE,GAAGiB,EAAO,CAAEgB,IAC1B,UAACM,EAAAA,EAAqB,EACpBN,IAAKA,EACLjC,UAAWoC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,wVACApC,GAEF4C,cAAY,GACX,GAAG3B,CAAK,UAET,UAAC4B,EAAAA,CAACA,CAAAA,CAAC7C,UAAU,eAGjB2C,EAAWN,WAAW,CAAGE,EAAAA,EAAqB,CAACF,WAAW,CAE1D,IAAMS,EAAad,EAAAA,UAAgB,CAGjC,CAAC,WAAEhC,CAAS,CAAE,GAAGiB,EAAO,CAAEgB,IAC1B,UAACM,EAAAA,EAAqB,EACpBN,IAAKA,EACLjC,UAAWoC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,wCAAyCpC,GACtD,GAAGiB,CAAK,IAGb6B,EAAWT,WAAW,CAAGE,EAAAA,EAAqB,CAACF,WAAW,CAE1D,IAAMU,EAAmBf,EAAAA,UAAgB,CAGvC,CAAC,WAAEhC,CAAS,CAAE,GAAGiB,EAAO,CAAEgB,IAC1B,UAACM,EAAAA,EAA2B,EAC1BN,IAAKA,EACLjC,UAAWoC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qBAAsBpC,GACnC,GAAGiB,CAAK,IAGb8B,EAAiBV,WAAW,CAAGE,EAAAA,EAA2B,CAACF,WAAW,cC5E/D,SAASW,IACd,GAAM,QAAEC,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GAE3B,MACE,WAACZ,EAAaA,WAAAA,EACJa,GAAG,CAAC,SAAU,CACpBC,IAAE,OACFC,CAAK,aACLC,CAAW,QACXC,CAAM,CACN,GAAGtC,EAOJ,EACC,MACE,WAACyB,EAAKA,CAAW,EAAXA,CAAczB,CAAK,WACvB,WAAClB,MAAAA,CAAIC,UAAU,uBACZqD,GAAS,UAACP,EAAUA,QAAAA,EAAEO,IACtBC,GACC,UAACP,EAAgBA,UAAEO,IAAFP,GAGpBQ,EACD,UAACZ,EAAUA,CAAAA,KARDS,EAQCT,GAIjB,UAACH,EAAaA,CAAAA,KAGpB,KAHoBA,mHC7CpB,IAAMvC,EAAkBuD,EAAAA,EAAyB,CAE3CtD,EAAUsD,EAAAA,EAAqB,CAE/BrD,EAAiBqD,EAAAA,EAAwB,CAEzC3C,EAAiBmB,EAAAA,UAAgB,CAGrC,CAAC,WAAEhC,CAAS,YAAEyD,EAAa,CAAC,CAAE,GAAGxC,EAAO,CAAEgB,IAC1C,UAACuB,EAAAA,EAAwB,EACvBvB,IAAKA,EACLwB,WAAYA,EACZzD,UAAWoC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,oXACApC,GAED,GAAGiB,CAAK,GAGbJ,GAAewB,WAAW,CAAGmB,EAAAA,EAAwB,CAACnB,WAAW,6DCCjE,IAAIqB,EAAQ,EA6BNC,EAAgB,IAAIC,IAEpBC,EAAmB,IACvB,GAAIF,EAAcG,GAAG,CAACC,GACpB,OAD8B,IAI1BC,EAAUC,WAAW,KACzBN,EAAcO,MAAM,CAACH,GACrBI,EAAS,CACPC,KAAM,eACNL,QAASA,CACX,EACF,EA7DyB,CA6DtBM,IAEHV,EAAcW,GAAG,CAACP,EAASC,EAC7B,EAEaO,EAAU,CAACC,EAAcjB,KACpC,OAAQA,EAAOa,IAAI,EACjB,IAAK,YACH,MAAO,CACL,GAAGI,CAAK,CACRvB,OAAQ,CAACM,EAAOkB,KAAK,IAAKD,EAAMvB,MAAM,CAAC,CAACyB,KAAK,CAAC,EAxElC,CAwEqCC,CACnD,CAEF,KAAK,eACH,MAAO,CACL,GAAGH,CAAK,CACRvB,OAAQuB,EAAMvB,MAAM,CAACE,GAAG,CAAEyB,GACxBA,EAAExB,EAAE,GAAKG,EAAOkB,KAAK,CAACrB,EAAE,CAAG,CAAE,GAAGwB,CAAC,CAAE,GAAGrB,EAAOkB,KAAK,EAAKG,EAE3D,CAEF,KAAK,gBAAiB,CACpB,GAAM,SAAEb,CAAO,CAAE,CAAGR,EAYpB,OARIQ,EACFF,EAAiBE,GAEjBS,EAHW,MAGC,CAACK,OAAO,CAAC,IACnBhB,EAAiBY,EAAMrB,EAAE,CAC3B,GAGK,CACL,GAAGoB,CAAK,CACRvB,OAAQuB,EAAMvB,MAAM,CAACE,GAAG,CAAC,GACvByB,EAAExB,EAAE,GAAKW,GAAWA,KAAYe,MAC5B,CACE,GAAGF,CAAC,CACJlE,MAAM,CACR,EACAkE,EAER,CACF,CACA,IAAK,eACH,QAAuBE,IAAnBvB,EAAOQ,KAAuB,EAAhB,CAChB,MAAO,CACL,GAAGS,CAAK,CACRvB,OAAQ,EAAE,EAGd,MAAO,CACL,GAAGuB,CAAK,CACRvB,OAAQuB,EAAMvB,MAAM,CAAC8B,MAAM,CAAC,GAAOH,EAAExB,EAAE,GAAKG,EAAOQ,OAAO,CAC5D,CACJ,CACF,EAAC,EAEgD,EAAE,CAE/CiB,EAAqB,CAAE/B,OAAQ,EAAE,EAErC,SAASkB,EAASZ,CAAc,EAC9ByB,EAAcT,EAAQS,EAAazB,GACnC0B,EAAUJ,OAAO,CAAC,IAChBK,EAASF,EACX,EACF,CAIA,SAASP,EAAM,CAAE,GAAGxD,EAAc,EAChC,IAAMmC,EAhHCM,CADPA,EAASA,IAAQ,EAAKyB,OAAOC,SAAAA,EAChBC,QAAQ,GAuHfC,EAAU,IAAMnB,EAAS,CAAEC,KAAM,gBAAiBL,QAASX,CAAG,GAcpE,OAZAe,EAAS,CACPC,KAAM,YACNK,MAAO,CACL,GAAGxD,CAAK,IACRmC,EACA1C,MAAM,EACN6E,aAAc,IACP7E,GAAM4E,GACb,CACF,CACF,GAEO,CACLlC,GAAIA,UACJkC,EACAE,OAtBa,GACbrB,EAAS,CACPC,KAAM,eACNK,MAAO,CAAE,GAAGxD,CAAK,IAAEmC,CAAG,CACxB,EAmBF,CACF,CAEA,SAASF,IACP,GAAM,CAACsB,EAAOiB,EAAS,CAAGzD,EAAAA,QAAc,CAAQgD,GAYhD,OAVAhD,EAAAA,SAAe,CAAC,KACdiD,EAAUS,IAAI,CAACD,GACR,KACL,IAAME,EAAQV,EAAUW,OAAO,CAACH,GAC5BE,EAAQ,CAAC,GAAG,EACJE,MAAM,CAACF,EAAO,EAE5B,GACC,CAACnB,EAAM,EAEH,CACL,GAAGA,CAAK,CACRC,QACAa,QAAS,GAAsBnB,EAAS,CAAEC,KAAM,wBAAiBL,CAAQ,EAC3E,CACF,yEC1LO,SAAS3B,EAAG,GAAG0D,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,gFCDO,SAASG,EAAU,UAAEjF,CAAQ,CAAiC,EACnE,MACE,UAACkF,EAAAA,EAAeA,CAAAA,UACblF,GAGP,g6CCDO,IAAMmF,EAAqB,CAChC9C,MAAO,sBACPC,YAAa,gDACb8C,SAAU,iGAiBVC,QAAS,CAAC,CAAEC,KAAM,gBAAiB,EAAE,CACrCC,QAAS,iBACTC,UAAW,iBACXC,OAAQ,CACNd,OAAO,EACPe,OAAQ,GACRC,UAAW,CACThB,OAAO,EACPe,QAAQ,CACV,CACF,EACAE,UAAW,CACTxC,KAAM,UACNyC,OAAQ,QACRC,IAAK,sBACLzD,MAAO,sBACPC,YAAa,gDACbyD,SAAU,SACZ,EACAC,QAAS,CACPC,KAAM,sBACN5D,MAAO,sBACPC,YAAa,+CACf,EACA4D,SAAU,iBACVC,MAAO,CACL,CAAEC,IAAK,mBAAoBN,IAAK,yBAA0B,EAC3D,EACF,EAEiC,CAChCO,WAAY,UACZC,MAAO,eACPC,aAAc,EACdC,aAAc,EACdC,cAAc,CAChB,EAAC,SAEuBC,EAAW,CACjC1G,UAAQ,CAGT,EACC,MACE,WAAC2G,OAAAA,CAAKC,KAAK,KAAKC,wBAAwB,cACtC,WAACC,OAAAA,WACC,UAACC,OAAAA,CAAKzB,KAAK,mBAAmB0B,QAAQ,YACtC,UAACD,OAAAA,CAAKzB,KAAK,+BAA+B0B,QAAQ,QAClD,UAACD,OAAAA,CAAKzB,KAAK,wCAAwC0B,QAAQ,YAC3D,UAACD,OAAAA,CAAKzB,KAAK,6BAA6B0B,QAAQ,YAChD,UAACD,OAAAA,CAAKzB,KAAK,mBAAmB0B,QAAQ,iBACtC,UAACD,OAAAA,CAAKzB,KAAK,yBAAyB0B,QAAQ,WAE9C,UAACC,OAAAA,CACCjI,UAAWoC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX8F,IAAAA,QAAAA,CAAa,qCAEb,gCACA,2CAGF,WAACnH,EAAAA,aAAaA,CAAAA,CACZoH,UAAU,QACVC,aAAa,SACbC,YAAY,IACZC,2BAA2B,EAC3BC,WAAW,4BAEX,UAACtC,EAAAA,SAASA,CAAAA,UACPjF,IAEH,UAACgC,EAAAA,OAAOA,CAAAA,CAAAA,GACR,UAAClD,EAAAA,SAASA,CAAAA,CAAAA,UAKpB,6FC3DO,IAAM0I,EAAY,UAEvB,IAAMC,EAASC,CADK,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,EAAAA,EACNC,GAAG,CAAC,aAE/B,GAAIH,EAAQ,OAAOA,EAEnB,IAAMI,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAE1B,OAAOD,GAASE,KAAK3F,EACvB,EAAC,oOCxDD,IAAM4F,EAAS,CACb,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACD,eCGD,IAAMC,EAA0C,CAC9C,CAACC,EAAAA,EAAKA,CAACC,OAAO,CAAC,CAAE,YACjB,CAACD,EAAAA,EAAKA,CAACE,IAAI,CAAC,CAAE,WACd,CAACF,EAAAA,EAAKA,CAACG,MAAM,CAAC,CAAE,WAChB,CAACH,EAAAA,EAAKA,CAACI,QAAQ,CAAC,CAAE,UACpB,EAEMC,EAAiB,UACrB,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACC,WAAW,CAACf,GAAG,CAAC,uBAElE,IACkBM,EAAAA,EAAKA,CAACE,IAAI,EAC1BI,IAAgBN,EAAAA,EAAKA,CAACG,MAAM,EAC5BG,IAAgBN,EAAAA,EAAKA,CAACI,QAAQ,CAEvBE,CADP,CAIKN,EAAAA,EAAKA,CAACI,QAAQ,EAGvB,eAAeM,EAAiBC,CAAM,CAAEC,CAAc,EACpD,IAAIC,EAAO,MAAMF,EAAGG,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC,CACxCC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,KAAKA,CAAC3D,IAAI,CAAEwD,EACxB,GAEA,GAAI,CAACC,EAAM,CACT,GAAM,CAACM,EAAQ,CAAG,MAAMR,EAAGS,MAAM,CAACL,EAAAA,KAAKA,EACpCM,MAAM,CAAC,CACNjE,KAAMwD,EACNxG,YAAa2F,CAAiB,CAACa,EAAS,GAEzCU,SAAS,GACZT,EAAOM,CACT,CAEA,OAAON,CACT,CAEO,eAAeU,EAAiBZ,CAAM,CAAEpB,CAAc,CAAEiC,CAAc,EAC3E,MAAMb,EAAG3F,MAAM,CAACyG,EAAAA,SAASA,EACtBR,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACO,EAAAA,SAASA,CAAClC,MAAM,CAAEA,IAE9B,MAAMoB,EAAGS,MAAM,CAACK,EAAAA,SAASA,EACtBJ,MAAM,CAAC,QACN9B,SACAiC,CACF,EACJ,CAEO,eAAeE,EAAYnC,CAAc,EAC9C,IAAMoB,EAAKgB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,MAAOC,CAJiB,MAAMjB,EAAGG,KAAK,CAACW,SAAS,CAACI,QAAQ,CAAC,CACxDZ,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACO,EAAAA,SAASA,CAAClC,MAAM,CAAEA,GAC5BuC,KAAM,CAAEjB,MAAM,CAAK,CACrB,GACsB,CAAC,EAAE,CAACA,IAAI,CAACzD,IAAI,CAG9B,eAAe2E,EAAgBC,CAAsB,EAC1D,IAAMzC,EAAS,MAAMD,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE9B,GAAI,CAACC,EAAQ,OAAO,EAEpB,IAAMoB,EAAKgB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMbM,EAAgBL,CALE,MAAMjB,EAAGG,KAAK,CAACW,SAAS,CAACI,QAAQ,CAAC,CACxDZ,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACO,EAAAA,SAASA,CAAClC,MAAM,CAAEA,GAC5BuC,KAAM,CAAEjB,MAAM,CAAK,CACrB,IAEsC5G,GAAG,CAACiI,GAAMA,EAAGrB,IAAI,CAACzD,IAAI,EAC5D,MAAO+E,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAACF,EAAyBD,EAChD,CAEO,GAAM,CACXI,SAAU,KAAEC,CAAG,MAAEC,CAAI,CAAE,MACvB1C,CAAI,QACJ2C,CAAM,SACNC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,IAAO,EAClBC,OAAQC,QAAQnC,GAAG,CAACoC,WAAW,CAC/BC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACnB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAAI,CAClCoB,WAAYC,EAAAA,KAAKA,CACjBC,cAAeC,EAAAA,QAAQA,GAEzBC,UAAW,CACTC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC,CACLC,SAAUV,QAAQnC,GAAG,CAAC8C,cAAc,CACpCC,aAAcZ,QAAQnC,GAAG,CAACgD,kBAAkB,GAE9CC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CAClBrG,KAAM,cACNsG,YAAa,CACXC,SAAU,CAAEC,MAAO,MAAO1I,KAAM,OAAQ2I,YAAa,QAAS,EAC9DC,SAAU,CAAEF,MAAO,KAAM1I,KAAM,WAAY2I,YAAa,OAAQ,CAClE,EACA,MAAME,UAAUL,CAAW,EACzB,GAAI,CAACA,EACH,MAAM,KADU,CACA,aAGlB,GAAM,UAAEC,CAAQ,UAAEG,CAAQ,CAAE,CAAGJ,EAE/B,GAAI,CACFM,EAAAA,CAAUA,CAACC,KAAK,CAAC,UAAEN,WAAUG,CAAS,EAExC,CAAE,MAAOI,EAAO,CACd,MAAM,MAAU,UAClB,CAEA,IAAMvD,EAAKgB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEb9B,EAAO,MAAMc,EAAGG,KAAK,CAACkC,KAAK,CAAChC,SAAS,CAAC,CAC1CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC8B,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAEA,GAAI,CAAC9D,GAKD,CADY,EAJL,IAIWsE,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAACL,EAAoBjE,EAAKiE,QAAQ,EAHrE,MAAM,MAAU,YAQlB,MAAO,CACL,GAAGjE,CAAI,CACPiE,cAAUlI,CACZ,CACF,CACF,GACD,CACDwI,OAAQ,CACN,MAAM7B,OAAO,MAAE1C,CAAI,CAAE,EACnB,GAAKA,CAAD,CAAM3F,EAAE,CAEZ,CAFc,EAEV,CACF,IAAMyG,EAAKgB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,GAJqB,CAIjB0C,KAJuB1D,EAAGG,KAAK,CAACW,SAAS,CAACT,SAAS,CAAC,CACtDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACO,EAAAA,SAASA,CAAClC,MAAM,CAAEM,EAAK3F,EAAE,CACrC,GAEkB,OAElB,IAAMoG,EAAc,MAAMD,IACpBQ,EAAO,MAAMH,EAAiBC,EAAIL,EACxC,OAAMiB,EAAiBZ,EAAId,EAAK3F,EAAE,CAAE2G,EAAK3G,EAAE,CAC7C,CAAE,MAAOgK,EAAO,CACdI,QAAQJ,KAAK,CAAC,wBAAyBA,EACzC,CACF,CACF,EACAK,UAAW,CACHC,IAAN,MAAU,OAAEC,CAAK,MAAE5E,CAAI,CAAE,IACnBA,IACF4E,EADQ,EACA,CAAG5E,EAAK3F,EAAE,CAClBuK,EAAMrH,IAAI,CAAGyC,EAAKzC,IAAI,EAAIyC,EAAK8D,QAAQ,CACvCc,EAAMd,QAAQ,CAAG9D,EAAK8D,QAAQ,CAC9Bc,EAAMC,KAAK,CAAG7E,EAAK6E,KAAK,ED/JzB,SAASC,CAA8B,EAC5C,IAAMC,CC8J6CD,CD9JnCvH,CAAI,CAAC,EAAE,CAACyH,WAAW,GAE7BC,EAAaC,MAAMC,IAAI,CAAC5H,GAAM6H,MAAM,CACxC,CAACC,EAAKC,IAASD,EAAMC,EAAKC,UAAU,CAAC,GAAI,GACvCtF,EAAOuF,MAAM,CAEXC,EAAkBxF,CAAM,CAACgF,EAAW,CAEpCS,EAAM,CAAC;;2CAE4B,EAAED,EAAgB;;;;;;;;;;;;;QAarD,EAAEV,QAAQ;;;EAGhB,CAAC,CAACY,IAAI,GAGAC,EADU,IAAIC,cACEC,MAAM,CAACJ,GACvBK,EAASC,EAAOb,IAADa,CAAMJ,GAAOtJ,QAAQ,CAAC,UAE3C,MAAO,CAAC,0BAA0B,EAAEyJ,EAAAA,CAAQ,EC8HQnB,EAAMrH,IAAI,GAEnDqH,GAET,MAAM9E,QAAQ,CAAEA,SAAO,OAAE8E,CAAK,CAAE,EAC9B,GAAIA,GAAS9E,EAAQE,IAAI,CAAE,CACzBF,EAAQE,IAAI,CAAC3F,EAAE,CAAGuK,EAAMvK,EAAE,CAC1ByF,EAAQE,IAAI,CAACzC,IAAI,CAAGqH,EAAMrH,IAAI,CAC9BuC,EAAQE,IAAI,CAAC8D,QAAQ,CAAGc,EAAMd,QAAQ,CACtChE,EAAQE,IAAI,CAAC6E,KAAK,CAAGD,EAAMC,KAAK,CAEhC,IAAM/D,EAAKgB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACfC,EAAkB,MAAMjB,EAAGG,KAAK,CAACW,SAAS,CAACI,QAAQ,CAAC,CACtDZ,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACO,EAAAA,SAASA,CAAClC,MAAM,CAAEI,EAAQE,IAAI,CAAC3F,EAAE,EAC3C4H,KAAM,CAAEjB,MAAM,CAAK,CACrB,GAEA,GAAI,CAACe,EAAgByD,MAAM,CAAE,CAC3B,IAAM/E,EAAc,MAAMD,IACpBQ,EAAO,MAAMH,EAAiBC,EAAIL,EACxC,OAAMiB,EAAiBZ,EAAIhB,EAAQE,IAAI,CAAC3F,EAAE,CAAE2G,EAAK3G,EAAE,EACnD0H,EAAkB,CAAC,CACjBrC,OAAQI,EAAQE,IAAI,CAAC3F,EAAE,CACvBsH,OAAQX,EAAK3G,EAAE,CACf4L,UAAW,IAAIC,KACflF,KAAMA,CACR,EAAE,CAGJlB,EAAQE,IAAI,CAACkB,KAAK,CAAGa,EAAgB3H,GAAG,CAACiI,GAAO,OACxCA,EAAGrB,IAAI,CAACzD,IAAI,CACpB,EACF,CAEA,OAAOuC,CACT,CACF,EACAA,QAAS,CACPqG,SAAU,KACZ,EACF,GAEO,CAFJ,cAEmBC,EAAStC,CAAgB,CAAEG,CAAgB,EAC/D,IAAMnD,EAAKgB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,GAJiB,CAIbuE,KAJmBvF,EAAGG,GAIZ,EAJiB,CAACkC,KAAK,CAAChC,SAAS,CAAC,CAC9CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC8B,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAGE,MAAM,MAAU,UAGlB,IAAMwC,EAAiB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACtC,GAEpC,CAACjE,EAAK,CAAG,MAAMc,EAAGS,MAAM,CAAC4B,EAAAA,KAAKA,EACjC3B,MAAM,CAAC,UACNsC,EACAG,SAAUqC,CACZ,GACC7E,SAAS,GAEZ,OAAOzB,CACT,gFCvOO,IAAM8B,EAAW,IAAM0E,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAAC9F,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAAC8F,EAAE,CAAE,CAAEC,MAAMA,CAAAA,CAAC,GAAE,gCAAHA,gCCJnE,IAAMvG,EAAQ,CACnBC,QAAS,UACTC,KAAM,OACNC,OAAQ,SACRC,SAAU,UACZ,EAAW,EAIgB,CACzBoG,aAAc,eACdC,eAAgB,iBAChBC,aAAc,eACdC,cAAe,gBACfC,eAAgB,gBAClB,EAIaC,EAA+C,CAC1D,CAAC7G,EAAMC,OAAO,CAAC,CAAE6G,OAAOzF,MAAM,CAAC0F,GAC/B,CAAC/G,EAAME,IAAI,CAAC,CAAE,CACZ6G,EAAYP,YAAY,CACxBO,EAAYN,cAAc,CAC1BM,EAAYH,cAAc,CAC3B,CACD,CAAC5G,EAAMG,MAAM,CAAC,CAAE,CACd4G,EAAYP,YAAY,CACxBO,EAAYN,cAAc,CAC3B,CACD,CAACzG,EAAMI,QAAQ,CAAC,CAAE,EAAE,EACX,SAEK+B,EAAcV,CAAiB,CAAEO,CAAsB,EACrE,OAAOP,EAAUuF,IAAI,CAACnG,GAAQgG,CAAgB,CAAChG,EAAK,EAAEoG,SAASjF,GACjE,kVC9BO,IAAMgB,EAAQkE,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvChN,GAAIiN,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MACNC,UAAU,GACVC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IACrCnK,KAAM+J,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXK,MAAOL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASM,MAAM,GAC3BC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,gBAAiB,CAAEC,KAAM,cAAe,GAC/DlD,MAAOyC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZxD,SAAUwD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYM,MAAM,GACjC3D,SAAUqD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACjB,GAAE,EACsBD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CACjC,UACA,CACE3H,OAAQ4H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UACVU,OAAO,GACPC,UAAU,CAAC,IAAM9E,EAAM9I,EAAE,CAAE,CAAE6N,SAAU,SAAU,GACpD7M,KAAMiM,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQa,KAAK,GAAuBH,OAAO,GACtDI,SAAUd,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYU,OAAO,GAClCK,kBAAmBf,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,qBAAqBU,OAAO,GACpDM,cAAehB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBACpBiB,aAAcjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACnBkB,WAAYV,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cACpBW,WAAYnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,cACjBoB,MAAOpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZqB,SAAUrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YACfsB,cAAetB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACtB,EACA,GAAc,EACZuB,KADY,OACCtB,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CACtBuB,QAAS,CAACC,EAAQX,QAAQ,CAAEW,EAAQV,iBAAiB,CAAC,GAE1D,GAGWW,CAFZ,CAEqB3B,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,QAAS,CACzChN,GAAIiN,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DuB,QAAS3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGJ,MAAM,GACzClI,OAAQ4H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UAAUW,UAAU,CAAC,IAAM9E,EAAM9I,EAAE,CAAE,CAAE6N,SAAU,SAAU,GACxEjC,UAAW6B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAItB,MACxBgD,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbmB,GADa,UACCvM,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBwM,EAAE,CAACC,EAAMH,SAAS,EAChE,GAEaI,CAFV,CAEqBjC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7ChN,GAAIiN,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D6B,QAASjC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACXU,OAAO,GACPC,UAAU,CAAC,IAAMe,EAAO3O,EAAE,CAAE,CAAE6N,SAAU,SAAU,GACrDsB,YAAalC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBAAgBU,OAAO,GACzCyB,QAASnC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC/I,QAASqI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChCpJ,KAAM0I,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXoC,WAAY5B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cAAe,CAAEC,KAAM,cAAe,GACvDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAItB,KAC1B,EAAG,GAAY,EACbyD,GADa,QACD/M,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBwM,EAAE,CAACC,EAAME,OAAO,EAC5D,GAEaK,CAFV,CAEqBvC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7ChN,GAAIiN,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DhI,OAAQ4H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACVU,OAAO,GACPC,UAAU,CAAC,IAAM9E,EAAM9I,EAAE,CAAE,CAAE6N,SAAU,SAAU,GACpDnK,IAAKuJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GACxB6B,QAAS/B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGzP,OAAO,EAAC,GACnE0N,UAAW6B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAItB,MACxB4D,UAAWhC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAItB,KAC1B,GAAE,EAEmBmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvChN,GAAIiN,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DnK,KAAM+J,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BzN,YAAa+M,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,eAClBrB,UAAW6B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAItB,MAC7E4D,UAAWhC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAItB,KAC/E,GAAG,EAEsBmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,YAAa,CAChD3H,OAAQ4H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM9E,EAAM9I,EAAE,CAAE,CAAE6N,SAAU,SAAU,GACnFvG,OAAQ2F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM/G,EAAM7G,EAAE,CAAE,CAAE6N,SAAU,SAAU,GACnFjC,UAAW6B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAItB,KAC/E,EAAG,GAAY,EACb6D,GADa,CACTxC,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CAAEuB,QAAS,CAACO,EAAM3J,MAAM,CAAE2J,EAAM1H,MAAM,CAAC,GACxD,GAEaqI,CAFT,CAEmB3C,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,WAAY,CAC7ChN,GAAIiN,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DhI,OAAQ4H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM9E,EAAM9I,EAAE,EAC3DkD,KAAM+J,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BiC,IAAK3C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GAAGJ,MAAM,GACjC3B,UAAW6B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAItB,MAC7EgD,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrD8B,QAAS/B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGzP,OAAO,EAAC,EACrE,EAAG,GAAY,EACb2R,GADa,cACKC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,uBAAuBf,EAAE,CAACC,EAAM9L,IAAI,CAAE8L,EAAM3J,MAAM,EAClF,GAEa0K,CAFT,CAE+B/C,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,qBAAsB,CACnEgD,IAAK/C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOC,UAAU,GAC3B2B,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbmB,GADa,UACCvM,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,0BAA0BwM,EAAE,CAACC,EAAMH,SAAS,EAClE,GAEaoB,CAFT,CAE4BC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACP,EAAS,CAAC,KAAEQ,CAAG,CAAE,GAAM,EAC/DxK,KAAMwK,EAAIrH,EAAO,CACfsH,OAAQ,CAACT,EAAQtK,MAAM,CAAC,CACxBuI,WAAY,CAAC9E,EAAM9I,EAAE,CAAC,GAE1B,GAEaqQ,CAFT,CAE8BH,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAC3I,EAAW,CAAC,KAAE4I,CAAG,CAAE,GAAM,EACnExK,KAAMwK,EAAIrH,EAAO,CACfsH,OAAQ,CAAC7I,EAAUlC,MAAM,CAAC,CAC1BuI,WAAY,CAAC9E,EAAM9I,EAAE,CAAC,GAExB2G,KAAMwJ,EAAItJ,EAAO,CACfuJ,OAAQ,CAAC7I,EAAUD,MAAM,CAAC,CAC1BsG,WAAY,CAAC/G,EAAM7G,EAAE,CACvB,GACF,GAEasQ,CAFT,CAE0BJ,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACpH,EAAO,CAAC,MAAEyH,CAAI,CAAE,GAAM,EAC5DhJ,UAAWgJ,EAAKhJ,GAChBoI,QAASY,EAAKZ,GAChB,GAEaa,CAFT,CAE0BN,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACrJ,EAAO,CAAC,MAAE0J,CAAI,CAAE,GAAM,EAC5DhJ,UAAWgJ,EAAKhJ,GAClB,IAAI,sFR5IG,SAASvI,EAAG,GAAG0D,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,CAEO,eAAewJ,EAAatC,CAAgB,EACjD,IAAM6G,EAAU,IAAIjF,YACdkF,EAAOjI,QAAQnC,GAAG,CAACoC,WAAW,EAAI,GAClCiI,EAAOF,EAAQhF,MAAM,CAAC7B,EAAW8G,GAEvC,OAAOE,KAAKC,OAAOC,YAAY,IAAI,IAAIC,WAD1B,MAAM3D,OAAO4D,MAAM,CAACC,MAAM,CAAC,UAAWN,KAErD,CAEO,eAAe1G,EAAgBL,CAAgB,CAAEqC,CAAsB,EAE5E,OADa,MAAMC,EAAatC,KAChBqC,CAClB,8DShBO,IAAMnC,EAAaoH,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACjCzH,SAAUyH,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,WACPC,GAAG,CAAC,GAAI,gBACRC,KAAK,CAAC,mBAAoB,uBAC1BC,MAAM,CAACC,GAAO,CAACA,EAAIxE,QAAQ,CAAC,KAAM,cACrCnD,SAAUsH,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,eACZ,GAAE,wdCRA,MAAe,SAIjB,EAHuB,kCAKvB,KAJqB,OAAmB,mCAIxC,EACA,CAAK", "sources": ["webpack://_N_E/?0346", "webpack://_N_E/?a635", "webpack://_N_E/?a36e", "webpack://_N_E/?ef96", "webpack://_N_E/./app/components/float-menu.tsx", "webpack://_N_E/./app/components/theme/theme-provider.tsx", "webpack://_N_E/./app/components/ui/button.tsx", "webpack://_N_E/./app/components/ui/toast.tsx", "webpack://_N_E/./app/components/ui/toaster.tsx", "webpack://_N_E/./app/components/ui/tooltip.tsx", "webpack://_N_E/./app/components/ui/use-toast.ts", "webpack://_N_E/./app/lib/utils.ts", "webpack://_N_E/./app/providers.tsx", "webpack://_N_E/./app/layout.tsx", "webpack://_N_E/./app/lib/apiKey.ts", "webpack://_N_E/./app/lib/avatar.ts", "webpack://_N_E/./app/lib/auth.ts", "webpack://_N_E/./app/lib/db.ts", "webpack://_N_E/./app/lib/permissions.ts", "webpack://_N_E/./app/lib/schema.ts", "webpack://_N_E/./app/lib/validation.ts", "webpack://_N_E/./app/favicon.ico", "webpack://_N_E/./app/globals.css"], "sourcesContent": ["import(/* webpackMode: \"eager\", webpackExports: [\"FloatMenu\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\float-menu.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThemeProvider\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\theme\\\\theme-provider.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Toaster\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\ui\\\\toaster.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Providers\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\providers.tsx\");\n", "import(/* webpackMode: \"eager\", webpackExports: [\"FloatMenu\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\float-menu.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThemeProvider\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\theme\\\\theme-provider.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Toaster\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\ui\\\\toaster.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Providers\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\providers.tsx\");\n", "import(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\app-router.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\client-page.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\client-segment.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\error-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\http-access-fallback\\\\error-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\layout-router.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\render-from-template-context.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\router-reducer\\\\fetch-server-response.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\lib\\\\metadata\\\\metadata-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\shared\\\\lib\\\\app-router-context.shared-runtime.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\shared\\\\lib\\\\hooks-client-context.shared-runtime.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\shared\\\\lib\\\\server-inserted-html.shared-runtime.js\");\n", "import(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\app-router.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\client-page.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\client-segment.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\error-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\http-access-fallback\\\\error-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\layout-router.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\render-from-template-context.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\router-reducer\\\\fetch-server-response.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\lib\\\\metadata\\\\metadata-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\shared\\\\lib\\\\app-router-context.shared-runtime.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\shared\\\\lib\\\\hooks-client-context.shared-runtime.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\node_modules\\\\.pnpm\\\\next@15.1.1_@babel+core@7.2_20a4cfb4ac86d164275142bb55425a5b\\\\node_modules\\\\next\\\\dist\\\\esm\\\\shared\\\\lib\\\\server-inserted-html.shared-runtime.js\");\n", "\"use client\"\r\n\r\nimport { G<PERSON><PERSON> } from \"lucide-react\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\n\r\nexport function FloatMenu() {\r\n  return (\r\n    <div className=\"fixed bottom-6 right-6\">\r\n      <TooltipProvider>\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"icon\"\r\n              className=\"bg-white dark:bg-background rounded-full shadow-lg group relative border-primary/20\"\r\n              onClick={() => window.open(\"https://github.com/beilunyang/moemail\", \"_blank\")}\r\n            >\r\n              <Github \r\n                className=\"w-4 h-4 transition-all duration-300 text-primary group-hover:scale-110\"\r\n              />\r\n              <span className=\"sr-only\">获取网站源代码</span>\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <div className=\"text-sm\">\r\n              <p>获取网站源代码</p>\r\n            </div>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n      </TooltipProvider>\r\n    </div>\r\n  )\r\n} ", "\"use client\"\r\n\r\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\r\nimport { type ThemeProviderProps } from \"next-themes/dist/types\"\r\n\r\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\r\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\r\n} ", "import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline: \"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants } ", "import * as React from \"react\"\r\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\nimport { ToastAction } from \"./toast-action\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ToastProvider = ToastPrimitives.Provider\r\n\r\nconst ToastViewport = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Viewport\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\r\n\r\nconst toastVariants = cva(\r\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"border bg-background text-foreground\",\r\n        destructive:\r\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Toast = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\r\n    VariantProps<typeof toastVariants>\r\n>(({ className, variant, ...props }, ref) => {\r\n  return (\r\n    <ToastPrimitives.Root\r\n      ref={ref}\r\n      className={cn(toastVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nToast.displayName = ToastPrimitives.Root.displayName\r\n\r\nconst ToastClose = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Close>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Close\r\n    ref={ref}\r\n    className={cn(\r\n      \"absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\r\n      className\r\n    )}\r\n    toast-close=\"\"\r\n    {...props}\r\n  >\r\n    <X className=\"h-4 w-4\" />\r\n  </ToastPrimitives.Close>\r\n))\r\nToastClose.displayName = ToastPrimitives.Close.displayName\r\n\r\nconst ToastTitle = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Title>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Title\r\n    ref={ref}\r\n    className={cn(\"text-sm font-semibold [&+div]:text-xs\", className)}\r\n    {...props}\r\n  />\r\n))\r\nToastTitle.displayName = ToastPrimitives.Title.displayName\r\n\r\nconst ToastDescription = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Description>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm opacity-90\", className)}\r\n    {...props}\r\n  />\r\n))\r\nToastDescription.displayName = ToastPrimitives.Description.displayName\r\n\r\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\r\n\r\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\r\n\r\nexport {\r\n  type ToastProps,\r\n  type ToastActionElement,\r\n  ToastProvider,\r\n  ToastViewport,\r\n  Toast,\r\n  ToastTitle,\r\n  ToastDescription,\r\n  ToastClose,\r\n} ", "\"use client\"\r\n\r\nimport {\r\n  Toast,\r\n  ToastClose,\r\n  ToastDescription,\r\n  ToastProvider,\r\n  ToastTitle,\r\n  ToastViewport,\r\n} from \"./toast\"\r\nimport { useToast } from \"./use-toast\"\r\n\r\nexport interface ToastProps {\r\n  id: string\r\n  title?: string\r\n  description?: string\r\n  action?: React.ReactNode\r\n  variant?: \"default\" | \"destructive\"\r\n}\r\n\r\nexport function Toaster() {\r\n  const { toasts } = useToast()\r\n\r\n  return (\r\n    <ToastProvider>\r\n      {toasts.map(function ({ \r\n        id, \r\n        title, \r\n        description, \r\n        action, \r\n        ...props \r\n      }: {\r\n        id: string;\r\n        title?: React.ReactNode;\r\n        description?: React.ReactNode;\r\n        action?: React.ReactNode;\r\n        [key: string]: any;\r\n      }) {\r\n        return (\r\n          <Toast key={id} {...props}>\r\n            <div className=\"grid gap-1\">\r\n              {title && <ToastTitle>{title}</ToastTitle>}\r\n              {description && (\r\n                <ToastDescription>{description}</ToastDescription>\r\n              )}\r\n            </div>\r\n            {action}\r\n            <ToastClose />\r\n          </Toast>\r\n        )\r\n      })}\r\n      <ToastViewport />\r\n    </ToastProvider>\r\n  )\r\n} ", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider\r\n\r\nconst Tooltip = TooltipPrimitive.Root\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Content\r\n    ref={ref}\r\n    sideOffset={sideOffset}\r\n    className={cn(\r\n      \"z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } ", "import * as React from \"react\"\r\n\r\nimport type {\r\n  ToastActionElement,\r\n  ToastProps,\r\n} from \"@/components/ui/toast\"\r\n\r\nconst TOAST_LIMIT = 1\r\nconst TOAST_REMOVE_DELAY = 1000000\r\n\r\ntype ToasterToast = ToastProps & {\r\n  id: string\r\n  title?: React.ReactNode\r\n  description?: React.ReactNode\r\n  action?: ToastActionElement\r\n}\r\n\r\nexport const actionTypes = {\r\n  ADD_TOAST: \"ADD_TOAST\",\r\n  UPDATE_TOAST: \"UPDATE_TOAST\",\r\n  DISMISS_TOAST: \"DISMISS_TOAST\",\r\n  REMOVE_TOAST: \"REMOVE_TOAST\",\r\n} as const\r\n\r\n\r\nexport type ActionType = typeof actionTypes\r\n\r\nlet count = 0\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_VALUE\r\n  return count.toString()\r\n}\r\n\r\ntype Action =\r\n  | {\r\n      type: ActionType[\"ADD_TOAST\"]\r\n      toast: ToasterToast\r\n    }\r\n  | {\r\n      type: ActionType[\"UPDATE_TOAST\"]\r\n      toast: Partial<ToasterToast>\r\n    }\r\n  | {\r\n      type: ActionType[\"DISMISS_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n  | {\r\n      type: ActionType[\"REMOVE_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n\r\ninterface State {\r\n  toasts: ToasterToast[]\r\n}\r\n\r\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\r\n\r\nconst addToRemoveQueue = (toastId: string) => {\r\n  if (toastTimeouts.has(toastId)) {\r\n    return\r\n  }\r\n\r\n  const timeout = setTimeout(() => {\r\n    toastTimeouts.delete(toastId)\r\n    dispatch({\r\n      type: \"REMOVE_TOAST\",\r\n      toastId: toastId,\r\n    })\r\n  }, TOAST_REMOVE_DELAY)\r\n\r\n  toastTimeouts.set(toastId, timeout)\r\n}\r\n\r\nexport const reducer = (state: State, action: Action): State => {\r\n  switch (action.type) {\r\n    case \"ADD_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      }\r\n\r\n    case \"UPDATE_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\r\n        ),\r\n      }\r\n\r\n    case \"DISMISS_TOAST\": {\r\n      const { toastId } = action\r\n\r\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\r\n      // but I'll keep it here for simplicity\r\n      if (toastId) {\r\n        addToRemoveQueue(toastId)\r\n      } else {\r\n        state.toasts.forEach((toast) => {\r\n          addToRemoveQueue(toast.id)\r\n        })\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === toastId || toastId === undefined\r\n            ? {\r\n                ...t,\r\n                open: false,\r\n              }\r\n            : t\r\n        ),\r\n      }\r\n    }\r\n    case \"REMOVE_TOAST\":\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        }\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\r\n      }\r\n  }\r\n}\r\n\r\nconst listeners: Array<(state: State) => void> = []\r\n\r\nlet memoryState: State = { toasts: [] }\r\n\r\nfunction dispatch(action: Action) {\r\n  memoryState = reducer(memoryState, action)\r\n  listeners.forEach((listener) => {\r\n    listener(memoryState)\r\n  })\r\n}\r\n\r\ntype Toast = Omit<ToasterToast, \"id\">\r\n\r\nfunction toast({ ...props }: Toast) {\r\n  const id = genId()\r\n\r\n  const update = (props: ToasterToast) =>\r\n    dispatch({\r\n      type: \"UPDATE_TOAST\",\r\n      toast: { ...props, id },\r\n    })\r\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\r\n\r\n  dispatch({\r\n    type: \"ADD_TOAST\",\r\n    toast: {\r\n      ...props,\r\n      id,\r\n      open: true,\r\n      onOpenChange: (open) => {\r\n        if (!open) dismiss()\r\n      },\r\n    },\r\n  })\r\n\r\n  return {\r\n    id: id,\r\n    dismiss,\r\n    update,\r\n  }\r\n}\r\n\r\nfunction useToast() {\r\n  const [state, setState] = React.useState<State>(memoryState)\r\n\r\n  React.useEffect(() => {\r\n    listeners.push(setState)\r\n    return () => {\r\n      const index = listeners.indexOf(setState)\r\n      if (index > -1) {\r\n        listeners.splice(index, 1)\r\n      }\r\n    }\r\n  }, [state])\r\n\r\n  return {\r\n    ...state,\r\n    toast,\r\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\r\n  }\r\n}\r\n\r\nexport { useToast, toast } ", "import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport async function hashPassword(password: string): Promise<string> {\r\n  const encoder = new TextEncoder()\r\n  const salt = process.env.AUTH_SECRET || ''\r\n  const data = encoder.encode(password + salt)\r\n  const hash = await crypto.subtle.digest('SHA-256', data)\r\n  return btoa(String.fromCharCode(...new Uint8Array(hash)))\r\n}\r\n\r\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\r\n  const hash = await hashPassword(password)\r\n  return hash === hashedPassword\r\n}", "\"use client\"\r\n\r\nimport { SessionProvider } from \"next-auth/react\"\r\n\r\nexport function Providers({ children }: { children: React.ReactNode }) {\r\n  return (\r\n    <SessionProvider>\r\n      {children}\r\n    </SessionProvider>\r\n  )\r\n} ", "import { ThemeProvider } from \"@/components/theme/theme-provider\"\r\nimport { Toaster } from \"@/components/ui/toaster\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport type { Metadata, Viewport } from \"next\"\r\nimport { zpix } from \"./fonts\"\r\nimport \"./globals.css\"\r\nimport { Providers } from \"./providers\"\r\nimport { FloatMenu } from \"@/components/float-menu\"\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"MoeMail - 萌萌哒临时邮箱服务\",\r\n  description: \"安全、快速、一次性的临时邮箱地址，保护您的隐私，远离垃圾邮件。支持即时收件，到期自动失效。\",\r\n  keywords: [\r\n    \"临时邮箱\",\r\n    \"一次性邮箱\",\r\n    \"匿名邮箱\",\r\n    \"隐私保护\",\r\n    \"垃圾邮件过滤\",\r\n    \"即时收件\",\r\n    \"自动过期\",\r\n    \"安全邮箱\",\r\n    \"注册验证\",\r\n    \"临时账号\",\r\n    \"萌系邮箱\",\r\n    \"电子邮件\",\r\n    \"隐私安全\",\r\n    \"邮件服务\",\r\n    \"MoeMail\"\r\n  ].join(\", \"),\r\n  authors: [{ name: \"SoftMoe Studio\" }],\r\n  creator: \"SoftMoe Studio\",\r\n  publisher: \"SoftMoe Studio\",\r\n  robots: {\r\n    index: true,\r\n    follow: true,\r\n    googleBot: {\r\n      index: true,\r\n      follow: true,\r\n    },\r\n  },\r\n  openGraph: {\r\n    type: \"website\",\r\n    locale: \"zh_CN\",\r\n    url: \"https://moemail.app\",\r\n    title: \"MoeMail - 萌萌哒临时邮箱服务\",\r\n    description: \"安全、快速、一次性的临时邮箱地址，保护您的隐私，远离垃圾邮件。支持即时收件，到期自动失效。\",\r\n    siteName: \"MoeMail\",\r\n  },\r\n  twitter: {\r\n    card: \"summary_large_image\",\r\n    title: \"MoeMail - 萌萌哒临时邮箱服务\",\r\n    description: \"安全、快速、一次性的临时邮箱地址，保护您的隐私，远离垃圾邮件。支持即时收件，到期自动失效。\",\r\n  },\r\n  manifest: '/manifest.json',\r\n  icons: [\r\n    { rel: 'apple-touch-icon', url: '/icons/icon-192x192.png' },\r\n  ],\r\n}\r\n\r\nexport const viewport: Viewport = {\r\n  themeColor: '#826DD9',\r\n  width: 'device-width',\r\n  initialScale: 1,\r\n  maximumScale: 1,\r\n  userScalable: false,\r\n}\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  return (\r\n    <html lang=\"zh\" suppressHydrationWarning>\r\n      <head>\r\n        <meta name=\"application-name\" content=\"MoeMail\" />\r\n        <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\r\n        <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\" />\r\n        <meta name=\"apple-mobile-web-app-title\" content=\"MoeMail\" />\r\n        <meta name=\"format-detection\" content=\"telephone=no\" />\r\n        <meta name=\"mobile-web-app-capable\" content=\"yes\" />\r\n      </head>\r\n      <body \r\n        className={cn(\r\n          zpix.variable,\r\n          \"font-zpix min-h-screen antialiased\",\r\n          \"bg-background text-foreground\",\r\n          \"transition-colors duration-300\"\r\n        )}\r\n      >\r\n        <ThemeProvider\r\n          attribute=\"class\"\r\n          defaultTheme=\"system\"\r\n          enableSystem\r\n          disableTransitionOnChange={false}\r\n          storageKey=\"temp-mail-theme\"\r\n        >\r\n          <Providers>\r\n            {children}\r\n          </Providers>\r\n          <Toaster />\r\n          <FloatMenu />\r\n        </ThemeProvider>\r\n      </body>\r\n    </html>\r\n  )\r\n}\r\n", "import { createDb } from \"./db\"\r\nimport { api<PERSON><PERSON>s } from \"./schema\"\r\nimport { eq, and, gt } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport type { User } from \"next-auth\"\r\nimport { auth } from \"./auth\"\r\nimport { headers } from \"next/headers\"\r\n\r\nasync function getUserByApiKey(key: string): Promise<User | null> {\r\n  const db = createDb()\r\n  const apiKey = await db.query.apiKeys.findFirst({\r\n    where: and(\r\n      eq(apiKeys.key, key),\r\n      eq(apiKeys.enabled, true),\r\n      gt(apiKeys.expiresAt, new Date())\r\n    ),\r\n    with: {\r\n      user: true\r\n    }\r\n  })\r\n\r\n  if (!apiKey) return null\r\n\r\n  return apiKey.user\r\n}\r\n\r\nexport async function handleApiKeyAuth(apiKey: string, pathname: string) {\r\n  if (!pathname.startsWith('/api/emails') && !pathname.startsWith('/api/config')) {\r\n    return NextResponse.json(\r\n      { error: \"无权限查看\" },\r\n      { status: 403 }\r\n    )\r\n  }\r\n\r\n  const user = await getUserByApiKey(apiKey)\r\n  if (!user?.id) {\r\n    return NextResponse.json(\r\n      { error: \"无效的 API Key\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  const response = NextResponse.next()\r\n  response.headers.set(\"X-User-Id\", user.id)\r\n  return response\r\n}\r\n\r\nexport const getUserId = async () => {\r\n  const headersList = await headers()\r\n  const userId = headersList.get(\"X-User-Id\")\r\n  \r\n  if (userId) return userId\r\n\r\n  const session = await auth()\r\n\r\n  return session?.user.id\r\n}\r\n", "const COLORS = [\r\n  '#2196F3', // 蓝色\r\n  '#009688', // 青色\r\n  '#9C27B0', // 紫色\r\n  '#F44336', // 红色\r\n  '#673AB7', // 深紫色\r\n  '#3F51B5', // 靛蓝\r\n  '#4CAF50', // 绿色\r\n  '#FF5722', // 深橙\r\n  '#795548', // 棕色\r\n  '#607D8B', // 蓝灰\r\n];\r\n\r\nexport function generateAvatarUrl(name: string): string {\r\n  const initial = name[0].toUpperCase();\r\n  \r\n  const colorIndex = Array.from(name).reduce(\r\n    (acc, char) => acc + char.charCodeAt(0), 0\r\n  ) % COLORS.length;\r\n  \r\n  const backgroundColor = COLORS[colorIndex];\r\n  \r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\" width=\"100\" height=\"100\">\r\n      <rect width=\"100\" height=\"100\" fill=\"${backgroundColor}\"/>\r\n      <text \r\n        x=\"50%\" \r\n        y=\"50%\" \r\n        fill=\"white\" \r\n        font-family=\"system-ui, -apple-system, sans-serif\" \r\n        font-size=\"45\" \r\n        font-weight=\"500\"\r\n        text-anchor=\"middle\"\r\n        alignment-baseline=\"central\"\r\n        dominant-baseline=\"central\"\r\n        style=\"text-transform: uppercase\"\r\n      >\r\n        ${initial}\r\n      </text>\r\n    </svg>\r\n  `.trim();\r\n\r\n  const encoder = new TextEncoder();\r\n  const bytes = encoder.encode(svg);\r\n  const base64 = Buffer.from(bytes).toString('base64');\r\n  \r\n  return `data:image/svg+xml;base64,${base64}`;\r\n} ", "import NextAuth from \"next-auth\"\r\nimport GitHub from \"next-auth/providers/github\"\r\nimport { Dr<PERSON>zleAdapter } from \"@auth/drizzle-adapter\"\r\nimport { createDb, Db } from \"./db\"\r\nimport { accounts, users, roles, userRoles } from \"./schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { Permission, hasPermission, ROLES, Role } from \"./permissions\"\r\nimport CredentialsProvider from \"next-auth/providers/credentials\"\r\nimport { hashPassword, comparePassword } from \"@/lib/utils\"\r\nimport { authSchema } from \"@/lib/validation\"\r\nimport { generateAvatarUrl } from \"./avatar\"\r\nimport { getUserId } from \"./apiKey\"\r\n\r\nconst ROLE_DESCRIPTIONS: Record<Role, string> = {\r\n  [ROLES.EMPEROR]: \"皇帝（网站所有者）\",\r\n  [ROLES.DUKE]: \"公爵（超级用户）\",\r\n  [ROLES.KNIGHT]: \"骑士（高级用户）\",\r\n  [ROLES.CIVILIAN]: \"平民（普通用户）\",\r\n}\r\n\r\nconst getDefaultRole = async (): Promise<Role> => {\r\n  const defaultRole = await getRequestContext().env.SITE_CONFIG.get(\"DEFAULT_ROLE\")\r\n\r\n  if (\r\n    defaultRole === ROLES.DUKE ||\r\n    defaultRole === ROLES.KNIGHT ||\r\n    defaultRole === ROLES.CIVILIAN\r\n  ) {\r\n    return defaultRole as Role\r\n  }\r\n  \r\n  return ROLES.CIVILIAN\r\n}\r\n\r\nasync function findOrCreateRole(db: Db, roleName: Role) {\r\n  let role = await db.query.roles.findFirst({\r\n    where: eq(roles.name, roleName),\r\n  })\r\n\r\n  if (!role) {\r\n    const [newRole] = await db.insert(roles)\r\n      .values({\r\n        name: roleName,\r\n        description: ROLE_DESCRIPTIONS[roleName],\r\n      })\r\n      .returning()\r\n    role = newRole\r\n  }\r\n\r\n  return role\r\n}\r\n\r\nexport async function assignRoleToUser(db: Db, userId: string, roleId: string) {\r\n  await db.delete(userRoles)\r\n    .where(eq(userRoles.userId, userId))\r\n\r\n  await db.insert(userRoles)\r\n    .values({\r\n      userId,\r\n      roleId,\r\n    })\r\n}\r\n\r\nexport async function getUserRole(userId: string) {\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n  return userRoleRecords[0].role.name\r\n}\r\n\r\nexport async function checkPermission(permission: Permission) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) return false\r\n\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n\r\n  const userRoleNames = userRoleRecords.map(ur => ur.role.name)\r\n  return hasPermission(userRoleNames as Role[], permission)\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  auth,\r\n  signIn,\r\n  signOut\r\n} = NextAuth(() => ({\r\n  secret: process.env.AUTH_SECRET,\r\n  adapter: DrizzleAdapter(createDb(), {\r\n    usersTable: users,\r\n    accountsTable: accounts,\r\n  }),\r\n  providers: [\r\n    GitHub({\r\n      clientId: process.env.AUTH_GITHUB_ID,\r\n      clientSecret: process.env.AUTH_GITHUB_SECRET,\r\n    }),\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        username: { label: \"用户名\", type: \"text\", placeholder: \"请输入用户名\" },\r\n        password: { label: \"密码\", type: \"password\", placeholder: \"请输入密码\" },\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials) {\r\n          throw new Error(\"请输入用户名和密码\")\r\n        }\r\n\r\n        const { username, password } = credentials\r\n\r\n        try {\r\n          authSchema.parse({ username, password })\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        } catch (error) {\r\n          throw new Error(\"输入格式不正确\")\r\n        }\r\n\r\n        const db = createDb()\r\n\r\n        const user = await db.query.users.findFirst({\r\n          where: eq(users.username, username as string),\r\n        })\r\n\r\n        if (!user) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        const isValid = await comparePassword(password as string, user.password as string)\r\n        if (!isValid) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        return {\r\n          ...user,\r\n          password: undefined,\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  events: {\r\n    async signIn({ user }) {\r\n      if (!user.id) return\r\n\r\n      try {\r\n        const db = createDb()\r\n        const existingRole = await db.query.userRoles.findFirst({\r\n          where: eq(userRoles.userId, user.id),\r\n        })\r\n\r\n        if (existingRole) return\r\n\r\n        const defaultRole = await getDefaultRole()\r\n        const role = await findOrCreateRole(db, defaultRole)\r\n        await assignRoleToUser(db, user.id, role.id)\r\n      } catch (error) {\r\n        console.error('Error assigning role:', error)\r\n      }\r\n    },\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id\r\n        token.name = user.name || user.username\r\n        token.username = user.username\r\n        token.image = user.image || generateAvatarUrl(token.name as string)\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token && session.user) {\r\n        session.user.id = token.id as string\r\n        session.user.name = token.name as string\r\n        session.user.username = token.username as string\r\n        session.user.image = token.image as string\r\n\r\n        const db = createDb()\r\n        let userRoleRecords = await db.query.userRoles.findMany({\r\n          where: eq(userRoles.userId, session.user.id),\r\n          with: { role: true },\r\n        })\r\n  \r\n        if (!userRoleRecords.length) {\r\n          const defaultRole = await getDefaultRole()\r\n          const role = await findOrCreateRole(db, defaultRole)\r\n          await assignRoleToUser(db, session.user.id, role.id)\r\n          userRoleRecords = [{\r\n            userId: session.user.id,\r\n            roleId: role.id,\r\n            createdAt: new Date(),\r\n            role: role\r\n          }]\r\n        }\r\n  \r\n        session.user.roles = userRoleRecords.map(ur => ({\r\n          name: ur.role.name,\r\n        }))\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: \"jwt\",\r\n  },\r\n}))\r\n\r\nexport async function register(username: string, password: string) {\r\n  const db = createDb()\r\n  \r\n  const existing = await db.query.users.findFirst({\r\n    where: eq(users.username, username)\r\n  })\r\n\r\n  if (existing) {\r\n    throw new Error(\"用户名已存在\")\r\n  }\r\n\r\n  const hashedPassword = await hashPassword(password)\r\n  \r\n  const [user] = await db.insert(users)\r\n    .values({\r\n      username,\r\n      password: hashedPassword,\r\n    })\r\n    .returning()\r\n\r\n  return user\r\n}\r\n", "import { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { drizzle } from \"drizzle-orm/d1\"\r\nimport * as schema from \"./schema\"\r\n\r\nexport const createDb = () => drizzle(getRequestContext().env.DB, { schema })\r\n\r\nexport type Db = ReturnType<typeof createDb>\r\n", "export const ROLES = {\r\n  EMPEROR: 'emperor',\r\n  <PERSON>U<PERSON>: 'duke',\r\n  <PERSON><PERSON><PERSON><PERSON>: 'knight',\r\n  CIVILIAN: 'civilian',\r\n} as const;\r\n\r\nexport type Role = typeof ROLES[keyof typeof ROLES];\r\n\r\nexport const PERMISSIONS = {\r\n  MANAGE_EMAIL: 'manage_email',\r\n  <PERSON><PERSON>GE_WEBHOOK: 'manage_webhook',\r\n  PROMOTE_USER: 'promote_user',\r\n  MANAGE_CONFIG: 'manage_config',\r\n  MANAGE_API_KEY: 'manage_api_key',\r\n} as const;\r\n\r\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\r\n\r\nexport const ROLE_PERMISSIONS: Record<Role, Permission[]> = {\r\n  [ROLES.EMPEROR]: Object.values(PERMISSIONS),\r\n  [ROLES.DUKE]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_API_KEY,\r\n  ],\r\n  [ROLES.KNIGHT]: [\r\n    PERMISSIONS.<PERSON>NAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n  ],\r\n  [ROLES.CIVILIAN]: [],\r\n} as const;\r\n\r\nexport function hasPermission(userRoles: Role[], permission: Permission): boolean {\r\n  return userRoles.some(role => ROLE_PERMISSIONS[role]?.includes(permission));\r\n} ", "import { integer, sqliteTable, text, primaryKey, uniqueIndex, index } from \"drizzle-orm/sqlite-core\"\r\nimport type { AdapterAccountType } from \"next-auth/adapters\"\r\nimport { relations } from 'drizzle-orm';\r\n\r\n// https://authjs.dev/getting-started/adapters/drizzle\r\nexport const users = sqliteTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: integer(\"emailVerified\", { mode: \"timestamp_ms\" }),\r\n  image: text(\"image\"),\r\n  username: text(\"username\").unique(),\r\n  password: text(\"password\"),\r\n})\r\nexport const accounts = sqliteTable(\r\n  \"account\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => users.id, { onDelete: \"cascade\" }),\r\n    type: text(\"type\").$type<AdapterAccountType>().notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n  },\r\n  (account) => ({\r\n    compoundKey: primaryKey({\r\n      columns: [account.provider, account.providerAccountId],\r\n    }),\r\n  })\r\n)\r\n\r\nexport const emails = sqliteTable(\"email\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  address: text(\"address\").notNull().unique(),\r\n  userId: text(\"userId\").references(() => users.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp_ms\" }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"email_expires_at_idx\").on(table.expiresAt),\r\n}))\r\n\r\nexport const messages = sqliteTable(\"message\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  emailId: text(\"emailId\")\r\n    .notNull()\r\n    .references(() => emails.id, { onDelete: \"cascade\" }),\r\n  fromAddress: text(\"from_address\").notNull(),\r\n  subject: text(\"subject\").notNull(),\r\n  content: text(\"content\").notNull(),\r\n  html: text(\"html\"),\r\n  receivedAt: integer(\"received_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  emailIdIdx: index(\"message_email_id_idx\").on(table.emailId),\r\n}))\r\n\r\nexport const webhooks = sqliteTable('webhook', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id')\r\n    .notNull()\r\n    .references(() => users.id, { onDelete: \"cascade\" }),\r\n  url: text('url').notNull(),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n})\r\n\r\nexport const roles = sqliteTable(\"role\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\").notNull(),\r\n  description: text(\"description\"),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n});\r\n\r\nexport const userRoles = sqliteTable(\"user_role\", {\r\n  userId: text(\"user_id\").notNull().references(() => users.id, { onDelete: \"cascade\" }),\r\n  roleId: text(\"role_id\").notNull().references(() => roles.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  pk: primaryKey({ columns: [table.userId, table.roleId] }),\r\n}));\r\n\r\nexport const apiKeys = sqliteTable('api_keys', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id),\r\n  name: text('name').notNull(),\r\n  key: text('key').notNull().unique(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n}, (table) => ({\r\n  nameUserIdUnique: uniqueIndex('name_user_id_unique').on(table.name, table.userId)\r\n}));\r\n\r\nexport const revoked_credentials = sqliteTable('revoked_credential', {\r\n  jti: text('jti').primaryKey(),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp_ms' }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"revoked_expires_at_idx\").on(table.expiresAt),\r\n}));\r\n\r\nexport const apiKeysRelations = relations(apiKeys, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [apiKeys.userId],\r\n    references: [users.id],\r\n  }),\r\n}));\r\n\r\nexport const userRolesRelations = relations(userRoles, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userRoles.userId],\r\n    references: [users.id],\r\n  }),\r\n  role: one(roles, {\r\n    fields: [userRoles.roleId],\r\n    references: [roles.id],\r\n  }),\r\n}));\r\n\r\nexport const usersRelations = relations(users, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n  apiKeys: many(apiKeys),\r\n}));\r\n\r\nexport const rolesRelations = relations(roles, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n}));", "import { z } from \"zod\"\r\n\r\nexport const authSchema = z.object({\r\n  username: z.string()\r\n    .min(1, \"用户名不能为空\")\r\n    .max(20, \"用户名不能超过20个字符\")\r\n    .regex(/^[a-zA-Z0-9_-]+$/, \"用户名只能包含字母、数字、下划线和横杠\")\r\n    .refine(val => !val.includes('@'), \"用户名不能是邮箱格式\"),\r\n  password: z.string()\r\n    .min(8, \"密码长度必须大于等于8位\")\r\n})\r\n\r\nexport type AuthSchema = z.infer<typeof authSchema>", "  import { fillMetadataSegment } from 'next/dist/lib/metadata/get-metadata-route'\n\n  export default async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = fillMetadataSegment(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  }"], "names": ["FloatMenu", "div", "className", "TooltipProvider", "<PERSON><PERSON><PERSON>", "TooltipTrigger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "size", "onClick", "window", "open", "<PERSON><PERSON><PERSON>", "span", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "p", "ThemeProvider", "children", "props", "NextThemesProvider", "buttonVariants", "cva", "variants", "default", "destructive", "outline", "secondary", "ghost", "link", "sm", "lg", "icon", "defaultVariants", "React", "ref", "Comp", "Slot", "cn", "displayName", "ToastProvider", "ToastPrimitives", "ToastViewport", "toastVariants", "Toast", "ToastClose", "toast-close", "X", "ToastTitle", "ToastDescription", "Toaster", "toasts", "useToast", "map", "id", "title", "description", "action", "TooltipPrimitive", "sideOffset", "count", "toastTimeouts", "Map", "addToRemoveQueue", "has", "toastId", "timeout", "setTimeout", "delete", "dispatch", "type", "TOAST_REMOVE_DELAY", "set", "reducer", "state", "toast", "slice", "TOAST_LIMIT", "t", "for<PERSON>ach", "undefined", "filter", "memoryState", "listeners", "listener", "Number", "MAX_VALUE", "toString", "dismiss", "onOpenChange", "update", "setState", "push", "index", "indexOf", "splice", "inputs", "twMerge", "clsx", "Providers", "Session<PERSON>rov<PERSON>", "metadata", "keywords", "authors", "name", "creator", "publisher", "robots", "follow", "googleBot", "openGraph", "locale", "url", "siteName", "twitter", "card", "manifest", "icons", "rel", "themeColor", "width", "initialScale", "maximumScale", "userScalable", "RootLayout", "html", "lang", "suppressHydrationWarning", "head", "meta", "content", "body", "zpix", "attribute", "defaultTheme", "enableSystem", "disableTransitionOnChange", "storageKey", "getUserId", "userId", "headersList", "headers", "get", "session", "auth", "user", "COLORS", "ROLE_DESCRIPTIONS", "ROLES", "EMPEROR", "DUKE", "KNIGHT", "CIVILIAN", "getDefaultRole", "defaultRole", "getRequestContext", "env", "SITE_CONFIG", "findOrCreateRole", "db", "<PERSON><PERSON><PERSON>", "role", "query", "roles", "<PERSON><PERSON><PERSON><PERSON>", "where", "eq", "newRole", "insert", "values", "returning", "assignRoleToUser", "roleId", "userRoles", "getUserRole", "createDb", "userRoleRecords", "find<PERSON>any", "with", "checkPermission", "permission", "userRoleNames", "ur", "hasPermission", "handlers", "GET", "POST", "signIn", "signOut", "NextAuth", "secret", "process", "AUTH_SECRET", "adapter", "DrizzleAdapter", "usersTable", "users", "accountsTable", "accounts", "providers", "GitHub", "clientId", "AUTH_GITHUB_ID", "clientSecret", "AUTH_GITHUB_SECRET", "CredentialsProvider", "credentials", "username", "label", "placeholder", "password", "authorize", "authSchema", "parse", "error", "comparePassword", "events", "existingRole", "console", "callbacks", "jwt", "token", "image", "generateAvatarUrl", "initial", "toUpperCase", "colorIndex", "Array", "from", "reduce", "acc", "char", "charCodeAt", "length", "backgroundColor", "svg", "trim", "bytes", "TextEncoder", "encode", "base64", "<PERSON><PERSON><PERSON>", "createdAt", "Date", "strategy", "register", "existing", "hashedPassword", "hashPassword", "drizzle", "DB", "schema", "MANAGE_EMAIL", "MANAGE_WEBHOOK", "PROMOTE_USER", "MANAGE_CONFIG", "MANAGE_API_KEY", "ROLE_PERMISSIONS", "Object", "PERMISSIONS", "some", "includes", "sqliteTable", "text", "<PERSON><PERSON><PERSON>", "$defaultFn", "crypto", "randomUUID", "email", "unique", "emailVerified", "integer", "mode", "notNull", "references", "onDelete", "$type", "provider", "providerAccountId", "refresh_token", "access_token", "expires_at", "token_type", "scope", "id_token", "session_state", "<PERSON><PERSON><PERSON>", "columns", "account", "emails", "address", "expiresAt", "expiresAtIdx", "on", "table", "messages", "emailId", "fromAddress", "subject", "receivedAt", "emailIdIdx", "webhooks", "enabled", "updatedAt", "pk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "nameUserIdUnique", "uniqueIndex", "revoked_credentials", "jti", "apiKeysRelations", "relations", "one", "fields", "userRolesRelations", "usersRelations", "many", "rolesRelations", "encoder", "salt", "data", "btoa", "String", "fromCharCode", "Uint8Array", "subtle", "digest", "z", "min", "max", "regex", "refine", "val"], "sourceRoot": "", "ignoreList": []}