{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "sourcesContent": ["import type {\n  I18NDoma<PERSON>,\n  NextConfigComplete,\n} from '../../../server/config-shared'\nimport type { MiddlewareMatcher } from '../../analysis/get-page-static-info'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { needsExperimentalReact } from '../../../lib/needs-experimental-react'\nimport { checkIsAppPPREnabled } from '../../../server/lib/experimental/ppr'\n\nfunction errorIfEnvConflicted(config: NextConfigComplete, key: string) {\n  const isPrivateKey = /^(?:NODE_.+)|^(?:__.+)$/i.test(key)\n  const hasNextRuntimeKey = key === 'NEXT_RUNTIME'\n\n  if (isPrivateKey || hasNextRuntimeKey) {\n    throw new Error(\n      `The key \"${key}\" under \"env\" in ${config.configFileName} is not allowed. https://nextjs.org/docs/messages/env-key-not-allowed`\n    )\n  }\n}\n\ntype BloomFilter = ReturnType<\n  import('../../../shared/lib/bloom-filter').BloomFilter['export']\n>\n\nexport interface DefineEnvPluginOptions {\n  isTurbopack: boolean\n  clientRouterFilters?: {\n    staticFilter: BloomFilter\n    dynamicFilter: BloomFilter\n  }\n  config: NextConfigComplete\n  dev: boolean\n  distDir: string\n  fetchCacheKeyPrefix: string | undefined\n  hasRewrites: boolean\n  isClient: boolean\n  isEdgeServer: boolean\n  isNodeOrEdgeCompilation: boolean\n  isNodeServer: boolean\n  middlewareMatchers: MiddlewareMatcher[] | undefined\n}\n\ninterface DefineEnv {\n  [key: string]:\n    | string\n    | string[]\n    | boolean\n    | MiddlewareMatcher[]\n    | BloomFilter\n    | Partial<NextConfigComplete['images']>\n    | I18NDomains\n}\n\ninterface SerializedDefineEnv {\n  [key: string]: string\n}\n\n/**\n * Collects all environment variables that are using the `NEXT_PUBLIC_` prefix.\n */\nexport function getNextPublicEnvironmentVariables(): DefineEnv {\n  const defineEnv: DefineEnv = {}\n  for (const key in process.env) {\n    if (key.startsWith('NEXT_PUBLIC_')) {\n      const value = process.env[key]\n      if (value != null) {\n        defineEnv[`process.env.${key}`] = value\n      }\n    }\n  }\n  return defineEnv\n}\n\n/**\n * Collects the `env` config value from the Next.js config.\n */\nexport function getNextConfigEnv(config: NextConfigComplete): DefineEnv {\n  // Refactored code below to use for-of\n  const defineEnv: DefineEnv = {}\n  const env = config.env\n  for (const key in env) {\n    const value = env[key]\n    if (value != null) {\n      errorIfEnvConflicted(config, key)\n      defineEnv[`process.env.${key}`] = value\n    }\n  }\n  return defineEnv\n}\n\n/**\n * Serializes the DefineEnv config so that it can be inserted into the code by Webpack/Turbopack, JSON stringifies each value.\n */\nfunction serializeDefineEnv(defineEnv: DefineEnv): SerializedDefineEnv {\n  const defineEnvStringified: SerializedDefineEnv = {}\n  for (const key in defineEnv) {\n    const value = defineEnv[key]\n    defineEnvStringified[key] = JSON.stringify(value)\n  }\n\n  return defineEnvStringified\n}\n\nfunction getImageConfig(\n  config: NextConfigComplete,\n  dev: boolean\n): { 'process.env.__NEXT_IMAGE_OPTS': Partial<NextConfigComplete['images']> } {\n  return {\n    'process.env.__NEXT_IMAGE_OPTS': {\n      deviceSizes: config.images.deviceSizes,\n      imageSizes: config.images.imageSizes,\n      path: config.images.path,\n      loader: config.images.loader,\n      dangerouslyAllowSVG: config.images.dangerouslyAllowSVG,\n      unoptimized: config?.images?.unoptimized,\n      ...(dev\n        ? {\n            // pass domains in development to allow validating on the client\n            domains: config.images.domains,\n            remotePatterns: config.images?.remotePatterns,\n            localPatterns: config.images?.localPatterns,\n            output: config.output,\n          }\n        : {}),\n    },\n  }\n}\n\nexport function getDefineEnv({\n  isTurbopack,\n  clientRouterFilters,\n  config,\n  dev,\n  distDir,\n  fetchCacheKeyPrefix,\n  hasRewrites,\n  isClient,\n  isEdgeServer,\n  isNodeOrEdgeCompilation,\n  isNodeServer,\n  middlewareMatchers,\n}: DefineEnvPluginOptions): SerializedDefineEnv {\n  const nextPublicEnv = getNextPublicEnvironmentVariables()\n  const nextConfigEnv = getNextConfigEnv(config)\n\n  const isPPREnabled = checkIsAppPPREnabled(config.experimental.ppr)\n  const isDynamicIOEnabled = !!config.experimental.dynamicIO\n\n  const defineEnv: DefineEnv = {\n    // internal field to identify the plugin config\n    __NEXT_DEFINE_ENV: true,\n\n    ...nextPublicEnv,\n    ...nextConfigEnv,\n    ...(!isEdgeServer\n      ? {}\n      : {\n          EdgeRuntime:\n            /**\n             * Cloud providers can set this environment variable to allow users\n             * and library authors to have different implementations based on\n             * the runtime they are running with, if it's not using `edge-runtime`\n             */\n            process.env.NEXT_EDGE_RUNTIME_PROVIDER ?? 'edge-runtime',\n\n          // process should be only { env: {...} } for edge runtime.\n          // For ignore avoid warn on `process.emit` usage but directly omit it.\n          'process.emit': false,\n        }),\n    'process.turbopack': isTurbopack,\n    'process.env.TURBOPACK': isTurbopack,\n    // TODO: enforce `NODE_ENV` on `process.env`, and add a test:\n    'process.env.NODE_ENV':\n      dev || config.experimental.allowDevelopmentBuild\n        ? 'development'\n        : 'production',\n    'process.env.NEXT_RUNTIME': isEdgeServer\n      ? 'edge'\n      : isNodeServer\n        ? 'nodejs'\n        : '',\n    'process.env.NEXT_MINIMAL': '',\n    'process.env.__NEXT_APP_NAV_FAIL_HANDLING': Boolean(\n      config.experimental.appNavFailHandling\n    ),\n    'process.env.__NEXT_APP_ISR_INDICATOR': Boolean(\n      config.devIndicators.appIsrStatus\n    ),\n    'process.env.__NEXT_PPR': isPPREnabled,\n    'process.env.__NEXT_DYNAMIC_IO': isDynamicIOEnabled,\n    'process.env.NEXT_DEPLOYMENT_ID': config.deploymentId || false,\n    'process.env.__NEXT_FETCH_CACHE_KEY_PREFIX': fetchCacheKeyPrefix ?? '',\n    ...(isTurbopack\n      ? {}\n      : {\n          'process.env.__NEXT_MIDDLEWARE_MATCHERS': middlewareMatchers ?? [],\n        }),\n    'process.env.__NEXT_MANUAL_CLIENT_BASE_PATH':\n      config.experimental.manualClientBasePath ?? false,\n    'process.env.__NEXT_CLIENT_ROUTER_DYNAMIC_STALETIME': JSON.stringify(\n      isNaN(Number(config.experimental.staleTimes?.dynamic))\n        ? 0\n        : config.experimental.staleTimes?.dynamic\n    ),\n    'process.env.__NEXT_CLIENT_ROUTER_STATIC_STALETIME': JSON.stringify(\n      isNaN(Number(config.experimental.staleTimes?.static))\n        ? 5 * 60 // 5 minutes\n        : config.experimental.staleTimes?.static\n    ),\n    'process.env.__NEXT_CLIENT_ROUTER_FILTER_ENABLED':\n      config.experimental.clientRouterFilter ?? true,\n    'process.env.__NEXT_CLIENT_ROUTER_S_FILTER':\n      clientRouterFilters?.staticFilter ?? false,\n    'process.env.__NEXT_CLIENT_ROUTER_D_FILTER':\n      clientRouterFilters?.dynamicFilter ?? false,\n    'process.env.__NEXT_CLIENT_SEGMENT_CACHE': Boolean(\n      config.experimental.clientSegmentCache\n    ),\n    'process.env.__NEXT_OPTIMISTIC_CLIENT_CACHE':\n      config.experimental.optimisticClientCache ?? true,\n    'process.env.__NEXT_MIDDLEWARE_PREFETCH':\n      config.experimental.middlewarePrefetch ?? 'flexible',\n    'process.env.__NEXT_CROSS_ORIGIN': config.crossOrigin,\n    'process.browser': isClient,\n    'process.env.__NEXT_TEST_MODE': process.env.__NEXT_TEST_MODE ?? false,\n    // This is used in client/dev-error-overlay/hot-dev-client.js to replace the dist directory\n    ...(dev && (isClient ?? isEdgeServer)\n      ? {\n          'process.env.__NEXT_DIST_DIR': distDir,\n        }\n      : {}),\n    'process.env.__NEXT_TRAILING_SLASH': config.trailingSlash,\n    'process.env.__NEXT_BUILD_INDICATOR':\n      config.devIndicators.buildActivity ?? true,\n    'process.env.__NEXT_BUILD_INDICATOR_POSITION':\n      config.devIndicators.buildActivityPosition ?? 'bottom-right',\n    'process.env.__NEXT_STRICT_MODE':\n      config.reactStrictMode === null ? false : config.reactStrictMode,\n    'process.env.__NEXT_STRICT_MODE_APP':\n      // When next.config.js does not have reactStrictMode it's enabled by default.\n      config.reactStrictMode === null ? true : config.reactStrictMode,\n    'process.env.__NEXT_OPTIMIZE_CSS':\n      (config.experimental.optimizeCss && !dev) ?? false,\n    'process.env.__NEXT_SCRIPT_WORKERS':\n      (config.experimental.nextScriptWorkers && !dev) ?? false,\n    'process.env.__NEXT_SCROLL_RESTORATION':\n      config.experimental.scrollRestoration ?? false,\n    ...getImageConfig(config, dev),\n    'process.env.__NEXT_ROUTER_BASEPATH': config.basePath,\n    'process.env.__NEXT_STRICT_NEXT_HEAD':\n      config.experimental.strictNextHead ?? true,\n    'process.env.__NEXT_HAS_REWRITES': hasRewrites,\n    'process.env.__NEXT_CONFIG_OUTPUT': config.output,\n    'process.env.__NEXT_I18N_SUPPORT': !!config.i18n,\n    'process.env.__NEXT_I18N_DOMAINS': config.i18n?.domains ?? false,\n    'process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE':\n      config.skipMiddlewareUrlNormalize,\n    'process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE':\n      config.experimental.externalMiddlewareRewritesResolve ?? false,\n    'process.env.__NEXT_MANUAL_TRAILING_SLASH':\n      config.skipTrailingSlashRedirect,\n    'process.env.__NEXT_HAS_WEB_VITALS_ATTRIBUTION':\n      (config.experimental.webVitalsAttribution &&\n        config.experimental.webVitalsAttribution.length > 0) ??\n      false,\n    'process.env.__NEXT_WEB_VITALS_ATTRIBUTION':\n      config.experimental.webVitalsAttribution ?? false,\n    'process.env.__NEXT_LINK_NO_TOUCH_START':\n      config.experimental.linkNoTouchStart ?? false,\n    'process.env.__NEXT_ASSET_PREFIX': config.assetPrefix,\n    'process.env.__NEXT_DISABLE_SYNC_DYNAMIC_API_WARNINGS':\n      // Internal only so untyped to avoid discovery\n      (config.experimental as any).internal_disableSyncDynamicAPIWarnings ??\n      false,\n    'process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS':\n      !!config.experimental.authInterrupts,\n    ...(isNodeOrEdgeCompilation\n      ? {\n          // Fix bad-actors in the npm ecosystem (e.g. `node-formidable`)\n          // This is typically found in unmaintained modules from the\n          // pre-webpack era (common in server-side code)\n          'global.GENTLY': false,\n        }\n      : undefined),\n    ...(isNodeOrEdgeCompilation\n      ? {\n          'process.env.__NEXT_EXPERIMENTAL_REACT':\n            needsExperimentalReact(config),\n        }\n      : undefined),\n  }\n\n  const userDefines = config.compiler?.define ?? {}\n  for (const key in userDefines) {\n    if (defineEnv.hasOwnProperty(key)) {\n      throw new Error(\n        `The \\`compiler.define\\` option is configured to replace the \\`${key}\\` variable. This variable is either part of a Next.js built-in or is already configured via the \\`env\\` option.`\n      )\n    }\n    defineEnv[key] = userDefines[key]\n  }\n\n  return serializeDefineEnv(defineEnv)\n}\n\nexport function getDefineEnvPlugin(options: DefineEnvPluginOptions) {\n  return new webpack.DefinePlugin(getDefineEnv(options))\n}\n"], "names": ["getDefineEnv", "getDefineEnvPlugin", "getNextConfigEnv", "getNextPublicEnvironmentVariables", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "defineEnv", "process", "env", "startsWith", "value", "serializeDefineEnv", "defineEnvStringified", "JSON", "stringify", "getImageConfig", "dev", "deviceSizes", "images", "imageSizes", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "localPatterns", "output", "isTurbopack", "clientRouterFilters", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "nextPublicEnv", "nextConfigEnv", "isPPREnabled", "checkIsAppPPREnabled", "experimental", "ppr", "isDynamicIOEnabled", "dynamicIO", "__NEXT_DEFINE_ENV", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "allowDevelopmentBuild", "Boolean", "appNavFailHandling", "devIndicators", "appIsrStatus", "deploymentId", "manualClientBasePath", "isNaN", "Number", "staleTimes", "dynamic", "static", "clientRouterFilter", "staticFilter", "dynamicFilter", "clientSegmentCache", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "buildActivity", "buildActivityPosition", "reactStrictMode", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "basePath", "strictNextHead", "i18n", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "linkNoTouchStart", "assetPrefix", "internal_disableSyncDynamicAPIWarnings", "authInterrupts", "undefined", "needsExperimentalReact", "userDefines", "compiler", "define", "hasOwnProperty", "options", "webpack", "DefinePlugin"], "mappings": ";;;;;;;;;;;;;;;;;IAgIgBA,YAAY;eAAZA;;IAiLAC,kBAAkB;eAAlBA;;IArOAC,gBAAgB;eAAhBA;;IAhBAC,iCAAiC;eAAjCA;;;yBAvDQ;wCACe;qBACF;AAErC,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC;IAEnI;AACF;AA0CO,SAASR;IACd,MAAMS,YAAuB,CAAC;IAC9B,IAAK,MAAMN,OAAOO,QAAQC,GAAG,CAAE;QAC7B,IAAIR,IAAIS,UAAU,CAAC,iBAAiB;YAClC,MAAMC,QAAQH,QAAQC,GAAG,CAACR,IAAI;YAC9B,IAAIU,SAAS,MAAM;gBACjBJ,SAAS,CAAC,CAAC,YAAY,EAAEN,KAAK,CAAC,GAAGU;YACpC;QACF;IACF;IACA,OAAOJ;AACT;AAKO,SAASV,iBAAiBG,MAA0B;IACzD,sCAAsC;IACtC,MAAMO,YAAuB,CAAC;IAC9B,MAAME,MAAMT,OAAOS,GAAG;IACtB,IAAK,MAAMR,OAAOQ,IAAK;QACrB,MAAME,QAAQF,GAAG,CAACR,IAAI;QACtB,IAAIU,SAAS,MAAM;YACjBZ,qBAAqBC,QAAQC;YAC7BM,SAAS,CAAC,CAAC,YAAY,EAAEN,KAAK,CAAC,GAAGU;QACpC;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,SAASK,mBAAmBL,SAAoB;IAC9C,MAAMM,uBAA4C,CAAC;IACnD,IAAK,MAAMZ,OAAOM,UAAW;QAC3B,MAAMI,QAAQJ,SAAS,CAACN,IAAI;QAC5BY,oBAAoB,CAACZ,IAAI,GAAGa,KAAKC,SAAS,CAACJ;IAC7C;IAEA,OAAOE;AACT;AAEA,SAASG,eACPhB,MAA0B,EAC1BiB,GAAY;QASKjB,gBAKSA,iBACDA;IAbzB,OAAO;QACL,iCAAiC;YAC/BkB,aAAalB,OAAOmB,MAAM,CAACD,WAAW;YACtCE,YAAYpB,OAAOmB,MAAM,CAACC,UAAU;YACpCC,MAAMrB,OAAOmB,MAAM,CAACE,IAAI;YACxBC,QAAQtB,OAAOmB,MAAM,CAACG,MAAM;YAC5BC,qBAAqBvB,OAAOmB,MAAM,CAACI,mBAAmB;YACtDC,WAAW,EAAExB,2BAAAA,iBAAAA,OAAQmB,MAAM,qBAAdnB,eAAgBwB,WAAW;YACxC,GAAIP,MACA;gBACE,gEAAgE;gBAChEQ,SAASzB,OAAOmB,MAAM,CAACM,OAAO;gBAC9BC,cAAc,GAAE1B,kBAAAA,OAAOmB,MAAM,qBAAbnB,gBAAe0B,cAAc;gBAC7CC,aAAa,GAAE3B,kBAAAA,OAAOmB,MAAM,qBAAbnB,gBAAe2B,aAAa;gBAC3CC,QAAQ5B,OAAO4B,MAAM;YACvB,IACA,CAAC,CAAC;QACR;IACF;AACF;AAEO,SAASjC,aAAa,EAC3BkC,WAAW,EACXC,mBAAmB,EACnB9B,MAAM,EACNiB,GAAG,EACHc,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EACK;QA2DNtC,iCAETA,kCAGSA,kCAETA,kCA+C6BA,cAsCjBA;IAtJpB,MAAMuC,gBAAgBzC;IACtB,MAAM0C,gBAAgB3C,iBAAiBG;IAEvC,MAAMyC,eAAeC,IAAAA,yBAAoB,EAAC1C,OAAO2C,YAAY,CAACC,GAAG;IACjE,MAAMC,qBAAqB,CAAC,CAAC7C,OAAO2C,YAAY,CAACG,SAAS;IAE1D,MAAMvC,YAAuB;QAC3B,+CAA+C;QAC/CwC,mBAAmB;QAEnB,GAAGR,aAAa;QAChB,GAAGC,aAAa;QAChB,GAAI,CAACL,eACD,CAAC,IACD;YACEa,aACE;;;;aAIC,GACDxC,QAAQC,GAAG,CAACwC,0BAA0B,IAAI;YAE5C,0DAA0D;YAC1D,sEAAsE;YACtE,gBAAgB;QAClB,CAAC;QACL,qBAAqBpB;QACrB,yBAAyBA;QACzB,6DAA6D;QAC7D,wBACEZ,OAAOjB,OAAO2C,YAAY,CAACO,qBAAqB,GAC5C,gBACA;QACN,4BAA4Bf,eACxB,SACAE,eACE,WACA;QACN,4BAA4B;QAC5B,4CAA4Cc,QAC1CnD,OAAO2C,YAAY,CAACS,kBAAkB;QAExC,wCAAwCD,QACtCnD,OAAOqD,aAAa,CAACC,YAAY;QAEnC,0BAA0Bb;QAC1B,iCAAiCI;QACjC,kCAAkC7C,OAAOuD,YAAY,IAAI;QACzD,6CAA6CvB,uBAAuB;QACpE,GAAIH,cACA,CAAC,IACD;YACE,0CAA0CS,sBAAsB,EAAE;QACpE,CAAC;QACL,8CACEtC,OAAO2C,YAAY,CAACa,oBAAoB,IAAI;QAC9C,sDAAsD1C,KAAKC,SAAS,CAClE0C,MAAMC,QAAO1D,kCAAAA,OAAO2C,YAAY,CAACgB,UAAU,qBAA9B3D,gCAAgC4D,OAAO,KAChD,KACA5D,mCAAAA,OAAO2C,YAAY,CAACgB,UAAU,qBAA9B3D,iCAAgC4D,OAAO;QAE7C,qDAAqD9C,KAAKC,SAAS,CACjE0C,MAAMC,QAAO1D,mCAAAA,OAAO2C,YAAY,CAACgB,UAAU,qBAA9B3D,iCAAgC6D,MAAM,KAC/C,IAAI,GAAG,YAAY;YACnB7D,mCAAAA,OAAO2C,YAAY,CAACgB,UAAU,qBAA9B3D,iCAAgC6D,MAAM;QAE5C,mDACE7D,OAAO2C,YAAY,CAACmB,kBAAkB,IAAI;QAC5C,6CACEhC,CAAAA,uCAAAA,oBAAqBiC,YAAY,KAAI;QACvC,6CACEjC,CAAAA,uCAAAA,oBAAqBkC,aAAa,KAAI;QACxC,2CAA2Cb,QACzCnD,OAAO2C,YAAY,CAACsB,kBAAkB;QAExC,8CACEjE,OAAO2C,YAAY,CAACuB,qBAAqB,IAAI;QAC/C,0CACElE,OAAO2C,YAAY,CAACwB,kBAAkB,IAAI;QAC5C,mCAAmCnE,OAAOoE,WAAW;QACrD,mBAAmBlC;QACnB,gCAAgC1B,QAAQC,GAAG,CAAC4D,gBAAgB,IAAI;QAChE,2FAA2F;QAC3F,GAAIpD,OAAQiB,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+BJ;QACjC,IACA,CAAC,CAAC;QACN,qCAAqC/B,OAAOsE,aAAa;QACzD,sCACEtE,OAAOqD,aAAa,CAACkB,aAAa,IAAI;QACxC,+CACEvE,OAAOqD,aAAa,CAACmB,qBAAqB,IAAI;QAChD,kCACExE,OAAOyE,eAAe,KAAK,OAAO,QAAQzE,OAAOyE,eAAe;QAClE,sCACE,6EAA6E;QAC7EzE,OAAOyE,eAAe,KAAK,OAAO,OAAOzE,OAAOyE,eAAe;QACjE,mCACE,AAACzE,CAAAA,OAAO2C,YAAY,CAAC+B,WAAW,IAAI,CAACzD,GAAE,KAAM;QAC/C,qCACE,AAACjB,CAAAA,OAAO2C,YAAY,CAACgC,iBAAiB,IAAI,CAAC1D,GAAE,KAAM;QACrD,yCACEjB,OAAO2C,YAAY,CAACiC,iBAAiB,IAAI;QAC3C,GAAG5D,eAAehB,QAAQiB,IAAI;QAC9B,sCAAsCjB,OAAO6E,QAAQ;QACrD,uCACE7E,OAAO2C,YAAY,CAACmC,cAAc,IAAI;QACxC,mCAAmC7C;QACnC,oCAAoCjC,OAAO4B,MAAM;QACjD,mCAAmC,CAAC,CAAC5B,OAAO+E,IAAI;QAChD,mCAAmC/E,EAAAA,eAAAA,OAAO+E,IAAI,qBAAX/E,aAAayB,OAAO,KAAI;QAC3D,kDACEzB,OAAOgF,0BAA0B;QACnC,0DACEhF,OAAO2C,YAAY,CAACsC,iCAAiC,IAAI;QAC3D,4CACEjF,OAAOkF,yBAAyB;QAClC,iDACE,AAAClF,CAAAA,OAAO2C,YAAY,CAACwC,oBAAoB,IACvCnF,OAAO2C,YAAY,CAACwC,oBAAoB,CAACC,MAAM,GAAG,CAAA,KACpD;QACF,6CACEpF,OAAO2C,YAAY,CAACwC,oBAAoB,IAAI;QAC9C,0CACEnF,OAAO2C,YAAY,CAAC0C,gBAAgB,IAAI;QAC1C,mCAAmCrF,OAAOsF,WAAW;QACrD,wDAEE,AADA,8CAA8C;QAC7CtF,OAAO2C,YAAY,CAAS4C,sCAAsC,IACnE;QACF,mDACE,CAAC,CAACvF,OAAO2C,YAAY,CAAC6C,cAAc;QACtC,GAAIpD,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiB;QACnB,IACAqD,SAAS;QACb,GAAIrD,0BACA;YACE,yCACEsD,IAAAA,8CAAsB,EAAC1F;QAC3B,IACAyF,SAAS;IACf;IAEA,MAAME,cAAc3F,EAAAA,mBAAAA,OAAO4F,QAAQ,qBAAf5F,iBAAiB6F,MAAM,KAAI,CAAC;IAChD,IAAK,MAAM5F,OAAO0F,YAAa;QAC7B,IAAIpF,UAAUuF,cAAc,CAAC7F,MAAM;YACjC,MAAM,IAAII,MACR,CAAC,8DAA8D,EAAEJ,IAAI,gHAAgH,CAAC;QAE1L;QACAM,SAAS,CAACN,IAAI,GAAG0F,WAAW,CAAC1F,IAAI;IACnC;IAEA,OAAOW,mBAAmBL;AAC5B;AAEO,SAASX,mBAAmBmG,OAA+B;IAChE,OAAO,IAAIC,gBAAO,CAACC,YAAY,CAACtG,aAAaoG;AAC/C"}