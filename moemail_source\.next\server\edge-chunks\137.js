"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[137],{3390:(e,t,r)=>{let a;r.d(t,{P:()=>G});let s=new TextEncoder,i=new TextDecoder;function o(e){let t=e;return("string"==typeof t&&(t=s.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class n extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class c extends n{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class d extends n{static code="ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class y extends n{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class p extends n{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}let h=(e,t)=>{let r=`SHA-${e.slice(-3)}`;switch(e){case"HS256":case"HS384":case"HS512":return{hash:r,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:r,name:"RSA-PSS",saltLength:parseInt(e.slice(-3),10)>>3};case"RS256":case"RS384":case"RS512":return{hash:r,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:r,name:"ECDSA",namedCurve:t.namedCurve};case"Ed25519":case"EdDSA":return{name:"Ed25519"};default:throw new c(`alg ${e} is not supported either by JOSE or your javascript runtime`)}},l=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}};function u(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function f(e,t){return e.name===t}function S(e){return parseInt(e.name.slice(4),10)}function m(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let w=(e,...t)=>m("Key must be ",e,...t);function g(e,t,...r){return m(`Key for the ${e} algorithm must be `,t,...r)}let E=async(e,t,r)=>{if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError(w(t,"CryptoKey","KeyObject","JSON Web Key"));return crypto.subtle.importKey("raw",t,{hash:`SHA-${e.slice(-3)}`,name:"HMAC"},!1,[r])}return!function(e,t,r){switch(t){case"HS256":case"HS384":case"HS512":{if(!f(e.algorithm,"HMAC"))throw u("HMAC");let r=parseInt(t.slice(2),10);if(S(e.algorithm.hash)!==r)throw u(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!f(e.algorithm,"RSASSA-PKCS1-v1_5"))throw u("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if(S(e.algorithm.hash)!==r)throw u(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!f(e.algorithm,"RSA-PSS"))throw u("RSA-PSS");let r=parseInt(t.slice(2),10);if(S(e.algorithm.hash)!==r)throw u(`SHA-${r}`,"algorithm.hash");break}case"Ed25519":case"EdDSA":if(!f(e.algorithm,"Ed25519"))throw u("Ed25519");break;case"ES256":case"ES384":case"ES512":{if(!f(e.algorithm,"ECDSA"))throw u("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw u(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}(function(e,t){if(t&&!e.usages.includes(t))throw TypeError(`CryptoKey does not support this operation, its usages must include ${t}.`)})(e,r)}(t,e,r),t},b=async(e,t,r)=>{let a=await E(e,t,"sign");return l(e,a),new Uint8Array(await crypto.subtle.sign(h(e,a.algorithm),a,r))},A=(...e)=>{let t;let r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0};function H(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function K(e){return e?.[Symbol.toStringTag]==="KeyObject"}let P=e=>H(e)||K(e),C=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function v(e){return C(e)&&"string"==typeof e.kty}let T=e=>e?.[Symbol.toStringTag],k=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let a;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):a=r;break;case e.startsWith("PBES2"):a="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):a=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):a="wrapKey";break;case"decrypt"===r:a=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(a&&t.key_ops?.includes?.(a)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${a}" when present`)}return!0},R=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(v(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&k(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!P(t))throw TypeError(g(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${T(t)} instances for symmetric algorithms must be of type "secret"`)}},O=(e,t,r)=>{if(v(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&k(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&k(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!P(t))throw TypeError(g(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${T(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${T(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${T(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${T(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${T(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},W=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?R(e,t,r):O(e,t,r)},j=(e,t,r,a,s)=>{let i;if(void 0!==s.crit&&a?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!a||void 0===a.crit)return new Set;if(!Array.isArray(a.crit)||0===a.crit.length||a.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(i=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,a.crit)){if(!i.has(o))throw new c(`Extension Header Parameter "${o}" is not recognized`);if(void 0===s[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(i.get(o)&&void 0===a[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(a.crit)},D=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new c('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new c('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new c('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new c('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),a={...e};return delete a.alg,delete a.use,crypto.subtle.importKey("jwk",a,t,e.ext??!e.d,e.key_ops??r)},$=async(e,t,r,s=!1)=>{let i=(a||=new WeakMap).get(e);if(i?.[r])return i[r];let o=await D({...t,alg:r});return s&&Object.freeze(e),i?i[r]=o:a.set(e,{[r]:o}),o},_=(e,t)=>{let r;let s=(a||=new WeakMap).get(e);if(s?.[t])return s[t];let i="public"===e.type,o=!!i;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,o,i?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,o,[i?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let a;switch(t){case"RSA-OAEP":a="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":a="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":a="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":a="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:a},o,i?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:a},o,[i?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let a=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!a)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===a&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:a},o,[i?"verify":"sign"])),"ES384"===t&&"P-384"===a&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:a},o,[i?"verify":"sign"])),"ES512"===t&&"P-521"===a&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:a},o,[i?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:a},o,i?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return s?s[t]=r:a.set(e,{[t]:r}),r},I=async(e,t)=>{if(e instanceof Uint8Array||H(e))return e;if(K(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return _(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return $(e,r,t)}if(v(e))return e.k?function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:i.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=i.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64(e);let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}(e.k):$(e,e,t,!0);throw Error("unreachable")};class J{#e;#t;#r;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this.#e=e}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setUnprotectedHeader(e){if(this.#r)throw TypeError("setUnprotectedHeader can only be called once");return this.#r=e,this}async sign(e,t){let r;if(!this.#t&&!this.#r)throw new d("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!A(this.#t,this.#r))throw new d("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let a={...this.#t,...this.#r},n=j(d,new Map([["b64",!0]]),t?.crit,this.#t,a),c=!0;if(n.has("b64")&&"boolean"!=typeof(c=this.#t.b64))throw new d('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:y}=a;if("string"!=typeof y||!y)throw new d('JWS "alg" (Algorithm) Header Parameter missing or invalid');W(y,e,"sign");let p=this.#e;c&&(p=s.encode(o(p)));let h=function(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let a of e)t.set(a,r),r+=a.length;return t}(r=this.#t?s.encode(o(JSON.stringify(this.#t))):s.encode(""),s.encode("."),p),l=await I(e,y),u={signature:o(await b(y,l,h)),payload:""};return c&&(u.payload=i.decode(p)),this.#r&&(u.header=this.#r),this.#t&&(u.protected=i.decode(r)),u}}class U{#a;constructor(e){this.#a=new J(e)}setProtectedHeader(e){return this.#a.setProtectedHeader(e),this}async sign(e,t){let r=await this.#a.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}let M=e=>Math.floor(e.getTime()/1e3),N=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,x=e=>{let t;let r=N.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let a=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(a);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*a);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*a);break;case"day":case"days":case"d":t=Math.round(86400*a);break;case"week":case"weeks":case"w":t=Math.round(604800*a);break;default:t=Math.round(0x1e187e0*a)}return"-"===r[1]||"ago"===r[4]?-t:t};function B(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}class L{#e;constructor(e){if(!C(e))throw TypeError("JWT Claims Set MUST be an object");this.#e=structuredClone(e)}data(){return s.encode(JSON.stringify(this.#e))}get iss(){return this.#e.iss}set iss(e){this.#e.iss=e}get sub(){return this.#e.sub}set sub(e){this.#e.sub=e}get aud(){return this.#e.aud}set aud(e){this.#e.aud=e}set jti(e){this.#e.jti=e}set nbf(e){"number"==typeof e?this.#e.nbf=B("setNotBefore",e):e instanceof Date?this.#e.nbf=B("setNotBefore",M(e)):this.#e.nbf=M(new Date)+x(e)}set exp(e){"number"==typeof e?this.#e.exp=B("setExpirationTime",e):e instanceof Date?this.#e.exp=B("setExpirationTime",M(e)):this.#e.exp=M(new Date)+x(e)}set iat(e){void 0===e?this.#e.iat=M(new Date):e instanceof Date?this.#e.iat=B("setIssuedAt",M(e)):"string"==typeof e?this.#e.iat=B("setIssuedAt",M(new Date)+x(e)):this.#e.iat=B("setIssuedAt",e)}}class G{#t;#s;constructor(e={}){this.#s=new L(e)}setIssuer(e){return this.#s.iss=e,this}setSubject(e){return this.#s.sub=e,this}setAudience(e){return this.#s.aud=e,this}setJti(e){return this.#s.jti=e,this}setNotBefore(e){return this.#s.nbf=e,this}setExpirationTime(e){return this.#s.exp=e,this}setIssuedAt(e){return this.#s.iat=e,this}setProtectedHeader(e){return this.#t=e,this}async sign(e,t){let r=new U(this.#s.data());if(r.setProtectedHeader(this.#t),Array.isArray(this.#t?.crit)&&this.#t.crit.includes("b64")&&!1===this.#t.b64)throw new y("JWTs MUST NOT use unencoded payload");return r.sign(e,t)}}},3922:(e,t,r)=>{r.d(t,{Ak:()=>a});let a=(e=21)=>{let t="",r=crypto.getRandomValues(new Uint8Array(e|=0));for(;e--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&r[e]];return t}}}]);
//# sourceMappingURL=137.js.map