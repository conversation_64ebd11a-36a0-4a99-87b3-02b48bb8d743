import { jwtVerify } from "jose";

const secretKey = process.env.AUTH_SECRET;
if (!secretKey) {
  throw new Error("AUTH_SECRET must be set in environment variables");
}
const secret = new TextEncoder().encode(secretKey);

const issuer = "moemail";
const audience = "moemail:credential";

export interface AddressCredentialPayload {
  sub: string; // The email address
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
}

export async function verifyAddressCredential(
  token: string
): Promise<AddressCredentialPayload | null> {
  try {
    const { payload } = await jwtVerify<AddressCredentialPayload>(token, secret, {
      issuer,
      audience,
    });
    return payload;
  } catch (error) {
    // This will catch errors like invalid signature, expired token, etc.
    return null;
  }
} 