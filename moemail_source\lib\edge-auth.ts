import { SignJWT, jwtVerify } from "jose";
import { createDb } from "@/lib/db";
import { revoked_credentials } from "@/lib/schema";
import { eq } from "drizzle-orm";

const secretKey = process.env.AUTH_SECRET;
if (!secretKey) {
  throw new Error("AUTH_SECRET is not set");
}
const secret = new TextEncoder().encode(secretKey);

const issuer = "moemail";
const audience = "moemail:credential";

export interface AddressCredentialPayload {
  sub: string; // The email address
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
}

export async function generateAddressCredential(
  address: string,
  expiresInSeconds: number = 3600 // 1 hour
) {
  const now = Math.floor(Date.now() / 1000);
  const jwt = new SignJWT({})
    .setProtectedHeader({ alg: "HS256" })
    .setSubject(address)
    .setIssuer(issuer)
    .setAudience(audience)
    .setIssuedAt(now);

  if (expiresInSeconds > 0) {
    jwt.setExpirationTime(now + expiresInSeconds);
  }

  const token = await jwt.sign(secret);
  return token;
}

export async function verifyAddressCredential(
  token: string
): Promise<AddressCredentialPayload | null> {
  try {
    const { payload } = await jwtVerify<AddressCredentialPayload>(token, secret, {
      issuer,
      audience,
    });

    // In Edge environment, we cannot perform DB lookups reliably yet.
    // For now, we will rely on the JWT signature for verification.
    // Revocation checks will be handled at the API route level (Node.js runtime).
    // const db = createDb();
    // const isRevoked = await db.query.revoked_credentials.findFirst({
    //     where: eq(revoked_credentials.jti, token)
    // });
    // if (isRevoked) {
    //     return null;
    // }

    return payload;
  } catch (error) {
    return null;
  }
}

export async function revokeAddressCredential(token: string) {
    const db = createDb();
    const payload = await verifyAddressCredential(token);
    if (!payload || !payload.exp) {
        // Cannot revoke a token that is invalid or has no expiry
        return;
    }

    await db.insert(revoked_credentials).values({
        jti: token,
        expiresAt: new Date(payload.exp * 1000)
    });
} 