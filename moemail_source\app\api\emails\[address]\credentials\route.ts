import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { createDb } from "@/lib/db";
import { emails } from "@/lib/schema";
import { eq, and } from "drizzle-orm";
import { generateAddressCredential } from "@/lib/jwt";

export const runtime = "edge";

export async function POST(
  request: Request,
  { params }: { params: { address: string } }
) {
  const session = await auth();
  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const db = createDb();
  const emailAddress = decodeURIComponent(params.address);

  try {
    const email = await db.query.emails.findFirst({
      where: and(
        eq(emails.address, emailAddress),
        eq(emails.userId, session.user.id)
      ),
    });

    if (!email) {
      return NextResponse.json(
        { error: "Email not found or access denied" },
        { status: 404 }
      );
    }

    const { expiresIn = 3600 } = (await request.json().catch(() => ({}))) as {
      expiresIn?: number;
    };

    const expiresAt = expiresIn === 0 ? 0 : expiresIn * 1000;
    
    const credential = await generateAddressCredential(email.address, expiresIn);

    const expiresAtDate = expiresIn === 0 
      ? new Date('9999-01-01T00:00:00.000Z')
      : new Date(Date.now() + expiresAt);

    return NextResponse.json({
      credential,
      expiresAt: expiresAtDate.toISOString(),
    });
  } catch (error) {
    console.error("Failed to generate credential:", error);
    return NextResponse.json(
      { error: "Failed to generate credential" },
      { status: 500 }
    );
  }
} 