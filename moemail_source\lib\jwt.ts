import { SignJWT } from "jose";
import { createDb } from "@/lib/db";
import { revoked_credentials } from "@/lib/schema";
import { eq } from "drizzle-orm";
import { verifyAddressCredential as edgeVerify } from "@/lib/edge-auth"; // Import for revocation check

const secret = new TextEncoder().encode(process.env.AUTH_SECRET);
const issuer = "moemail";
const audience = "moemail:credential";

export async function generateAddressCredential(
  address: string,
  expiresInSeconds: number = 3600 // 1 hour
) {
  const now = Math.floor(Date.now() / 1000);
  const jwt = new SignJWT({})
    .setProtectedHeader({ alg: "HS256" })
    .setSubject(address)
    .setIssuer(issuer)
    .setAudience(audience)
    .setIssuedAt(now);

  if (expiresInSeconds > 0) {
    jwt.setExpirationTime(now + expiresInSeconds);
  }

  const token = await jwt.sign(secret);
  return token;
}

export async function revokeAddressCredential(token: string) {
    const db = createDb();
    // We use the edge-compatible verify function here, but in Node.js context, 
    // it won't perform the DB check, which is fine since we are about to write to DB.
    const payload = await edgeVerify(token);
    if (!payload || !payload.exp) {
        // Cannot revoke a token that is invalid or has no expiry
        return;
    }

    // Additionally, we should check if it's already revoked to avoid duplicates.
    const isAlreadyRevoked = await db.query.revoked_credentials.findFirst({
        where: eq(revoked_credentials.jti, token),
    });

    if (isAlreadyRevoked) {
        return;
    }

    await db.insert(revoked_credentials).values({
        jti: token,
        expiresAt: new Date(payload.exp * 1000)
    });
} 