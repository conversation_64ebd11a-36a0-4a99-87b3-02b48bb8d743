#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="F:\CODE\Project\Mail\moemail_source\node_modules\.pnpm\terser@5.41.0\node_modules\terser\bin\node_modules;F:\CODE\Project\Mail\moemail_source\node_modules\.pnpm\terser@5.41.0\node_modules\terser\node_modules;F:\CODE\Project\Mail\moemail_source\node_modules\.pnpm\terser@5.41.0\node_modules;F:\CODE\Project\Mail\moemail_source\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/terser@5.41.0/node_modules/terser/bin/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/terser@5.41.0/node_modules/terser/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/terser@5.41.0/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../.pnpm/terser@5.41.0/node_modules/terser/bin/terser" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../.pnpm/terser@5.41.0/node_modules/terser/bin/terser" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../.pnpm/terser@5.41.0/node_modules/terser/bin/terser" $args
  } else {
    & "node$exe"  "$basedir/../.pnpm/terser@5.41.0/node_modules/terser/bin/terser" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
