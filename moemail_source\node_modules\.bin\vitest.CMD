@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=F:\CODE\Project\Mail\moemail_source\node_modules\.pnpm\vitest@3.2.2_@edge-runtime+_3dc4917ec1ded1b0e4a023a5f2859cd4\node_modules\vitest\node_modules;F:\CODE\Project\Mail\moemail_source\node_modules\.pnpm\vitest@3.2.2_@edge-runtime+_3dc4917ec1ded1b0e4a023a5f2859cd4\node_modules;F:\CODE\Project\Mail\moemail_source\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=F:\CODE\Project\Mail\moemail_source\node_modules\.pnpm\vitest@3.2.2_@edge-runtime+_3dc4917ec1ded1b0e4a023a5f2859cd4\node_modules\vitest\node_modules;F:\CODE\Project\Mail\moemail_source\node_modules\.pnpm\vitest@3.2.2_@edge-runtime+_3dc4917ec1ded1b0e4a023a5f2859cd4\node_modules;F:\CODE\Project\Mail\moemail_source\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\vitest\vitest.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\vitest\vitest.mjs" %*
)
