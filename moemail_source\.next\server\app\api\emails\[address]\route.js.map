{"version": 3, "file": "app/api/emails/[address]/route.js", "mappings": "qFAAA,6DCAA,mHGAA,+TFMO,IAAMA,EAAU,OAAM,eAEPC,EACpBC,CAAgB,CAChB,QAAEC,CAAM,CAAuC,EAE/C,IAAMC,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE9B,GAAI,CACF,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACb,IAAEC,CAAE,CAAE,CAAG,MAAML,EAQrB,GAAI,CAACM,MAPeH,CAOR,CAPWI,KAAK,CAACC,MAAM,CAACC,SAAS,CAAC,CAC5CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACH,EAAE,CAAEA,GACdO,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACP,MAAM,CAAEA,GAEtB,GAGE,OAAOY,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,aAAc,EACvB,CAAEC,OAAQ,GAAI,GASlB,OANA,MAAMb,EAAGc,MAAM,CAACC,EAAAA,QAAQA,EACrBR,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACM,EAAAA,QAAQA,CAACC,OAAO,CAAEd,IAE9B,MAAMF,EAAGc,MAAM,CAACT,EAAAA,MAAMA,EACnBE,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACH,EAAE,CAAEA,IAEhBQ,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEM,SAAS,CAAK,EAC3C,CAAE,MAAOL,EAAO,CAEd,OADAM,QAAQN,KAAK,CAAC,0BAA2BA,GAClCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,QAAS,EAClB,CAAEC,OAAQ,GAAI,EAElB,CACF,CAIO,eAAeM,EACpBvB,CAAgB,CAChB,QAAEC,CAAM,CAAuC,EAE/C,GAAM,cAAEuB,CAAY,CAAE,CAAG,IAAIC,IAAIzB,EAAQ0B,GAAG,EACtCC,EAAYH,EAAaI,GAAG,CAAC,UAEnC,GAAI,CACF,IAAMxB,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACb,IAAEC,CAAE,CAAE,CAAG,MAAML,EAEfC,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAS9B,GAAI,CAACI,MAPeH,CAOR,CAPWI,KAAK,CAACC,MAAM,CAACC,SAAS,CAAC,CAC5CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACH,EAAE,CAAEA,GACdO,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACP,MAAM,CAAEA,GAEtB,GAGE,OAAOY,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,OAAQ,EACjB,CAAEC,OAAQ,GAAI,GAIlB,IAAMY,EAAiBhB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACM,EAAAA,QAAQA,CAACC,OAAO,CAAEd,GAEtCwB,EAAc,MAAM1B,EAAG2B,MAAM,CAAC,CAAEC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAW,CAAC,QAAQ,CAAC,GAC/DC,IAAI,CAACf,EAAAA,QAAQA,EACbR,KAAK,CAACkB,GACHM,EAAaC,OAAON,CAAW,CAAC,EAAE,CAACE,KAAK,EAExCK,EAAa,CAACR,EAAe,CAEnC,GAAIF,EAAW,CACb,GAAM,WAAEW,CAAS,IAAEhC,CAAE,CAAE,CAAGiC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACZ,GACvCU,EAAWG,IAAI,CACb,CACAC,EAAAA,EAAAA,EAAAA,CAAEA,CACAC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACvB,EAAAA,QAAQA,CAACwB,SAFwB,CAEd,CAAE,IAAIC,KAAKN,IACjC1B,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACM,EAAAA,QAAQA,CAACwB,UAAU,CAAE,IAAIC,KAAKN,IACjCI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACvB,EAAAA,QAAQA,CAACb,EAAE,CAAEA,KAIxB,CAEA,IAAMuC,EAAU,MAAMzC,EAAGI,KAAK,CAACW,QAAQ,CAAC2B,QAAQ,CAAC,CAC/CnC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,IAAIyB,GACdU,QAAS,CAAC5B,EAAU,MAAE6B,CAAI,CAAE,GAAK,CAC/BA,EAAK7B,EAASwB,UAAU,EACxBK,EAAK7B,EAASb,EAAE,EACjB,CACD2C,MAAOC,EACT,GAEMC,EAAUN,EAAQO,GAHH,GAGS,GAAGF,CAC3BG,EAAaF,EACfG,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CACVT,CAAO,CAACK,GAAc,CAACP,QAAH,EAAa,CAACY,OAAO,GACzCV,CAAO,CAACK,GAAc,CAAC5C,EAAE,EAE3B,IAFsB,CAGpBkD,EAAcL,EAAUN,EAAQY,KAAK,CAAC,EApE9B,CAoEiCP,GAAaL,EAE5D,OAAO/B,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBI,SAAUqC,EAAYE,GAAG,CAACC,GAAQ,EAChCrD,CADgC,EAC5BqD,EAAIrD,EAAE,CACVsD,aAAcD,EAAIE,WAAW,CAC7BC,QAASH,EAAIG,OAAO,CACpBC,YAAaJ,EAAIhB,UAAU,CAACY,OAAO,GACrC,cACAF,EACAW,MAAO7B,CACT,EACF,CAAE,MAAOnB,EAAO,CAEd,OADAM,QAAQN,KAAK,CAAC,4BAA6BA,GACpCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,0BAA2B,EACpC,CAAEC,OAAQ,GAAI,EAElB,CACF,CC9HA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,mCACA,iCACA,iBACA,2CACA,CAAK,CACL,kGACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,4EACA,GAFA,2BAEA,4BACA,MACI,QAA8B,EAClC,mCACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,uIAiBnxJ,CAAC,CAAC,EAAC,kHCwBI,IAAMd,EAAY,UAEvB,IAAMD,EAAS+D,CADK,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,EAAAA,EACNtC,GAAG,CAAC,aAE/B,GAAI1B,EAAQ,OAAOA,EAEnB,IAAMiE,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAE1B,OAAOD,GAASE,KAAK/D,EACvB,EAAC,2NCxDD,IAAMgE,EAAS,CACb,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACD,eCGD,IAAMC,EAA0C,CAC9C,CAACC,EAAAA,EAAKA,CAACC,OAAO,CAAC,CAAE,YACjB,CAACD,EAAAA,EAAKA,CAACE,IAAI,CAAC,CAAE,WACd,CAACF,EAAAA,EAAKA,CAACG,MAAM,CAAC,CAAE,WAChB,CAACH,EAAAA,EAAKA,CAACI,QAAQ,CAAC,CAAE,UACpB,EAEMC,EAAiB,UACrB,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACC,WAAW,CAACrD,GAAG,CAAC,uBAGhEkD,IAAgBN,EAAAA,EAAKA,CAACE,IAAI,EAC1BI,IAAgBN,EAAAA,EAAKA,CAACG,MAAM,EAC5BG,IAAgBN,EAAAA,EAAKA,CAACI,QAAQ,CAEvBE,CADP,CAIKN,EAAAA,EAAKA,CAACI,QAAQ,EAGvB,eAAeM,EAAiB9E,CAAM,CAAE+E,CAAc,EACpD,IAAIC,EAAO,MAAMhF,EAAGI,KAAK,CAAC6E,KAAK,CAAC3E,SAAS,CAAC,CACxCC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACwE,EAAAA,KAAKA,CAACC,IAAI,CAAEH,EACxB,GAEA,GAAI,CAACC,EAAM,CACT,GAAM,CAACG,EAAQ,CAAG,MAAMnF,EAAGoF,MAAM,CAACH,EAAAA,KAAKA,EACpCI,MAAM,CAAC,CACNH,KAAMH,EACNO,YAAanB,CAAiB,CAACY,EAAS,GAEzCQ,SAAS,GACZP,EAAOG,CACT,CAEA,OAAOH,CACT,CAEO,eAAeQ,EAAiBxF,CAAM,CAAEF,CAAc,CAAE2F,CAAc,EAC3E,MAAMzF,EAAGc,MAAM,CAAC4E,EAAAA,SAASA,EACtBnF,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACiF,EAAAA,SAASA,CAAC5F,MAAM,CAAEA,IAE9B,MAAME,EAAGoF,MAAM,CAACM,EAAAA,SAASA,EACtBL,MAAM,CAAC,QACNvF,EACA2F,QACF,EACJ,CAWO,eAAeE,EAAgBC,CAAsB,EAC1D,IAAM9F,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE9B,GAAI,CAACD,EAAQ,OAAO,EAEpB,IAAME,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMb4F,EAAgBC,CALE,MAAM9F,EAAGI,KAAK,CAACsF,SAAS,CAAChD,QAAQ,CAAC,CACxDnC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACiF,EAAAA,SAASA,CAAC5F,MAAM,CAAEA,GAC5BiG,KAAM,CAAEf,KAAM,EAAK,CACrB,IAEsC1B,GAAG,CAAC0C,GAAMA,EAAGhB,IAAI,CAACE,IAAI,EAC5D,MAAOe,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAACJ,EAAyBD,EAChD,CAEO,GAAM,CACXM,SAAU,KAAE/E,CAAG,MAAEgF,CAAI,CAAE,MACvBnC,CAAI,QACJoC,CAAM,SACNC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,IAAO,EAClBC,OAAQC,QAAQ5B,GAAG,CAAC6B,WAAW,CAC/BC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAAC1G,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAAI,CAClC2G,WAAYC,EAAAA,KAAKA,CACjBC,cAAeC,EAAAA,QAAQA,GAEzBC,UAAW,CACTC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC,CACLC,SAAUV,QAAQ5B,GAAG,CAACuC,cAAc,CACpCC,aAAcZ,QAAQ5B,GAAG,CAACyC,kBAC5B,GACAC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CAClBpC,KAAM,cACNqC,YAAa,CACXC,SAAU,CAAEC,MAAO,MAAOC,KAAM,OAAQC,YAAa,QAAS,EAC9DC,SAAU,CAAEH,MAAO,KAAMC,KAAM,WAAYC,YAAa,OAAQ,CAClE,EACA,MAAME,UAAUN,CAAW,EACzB,GAAI,CAACA,EACH,MAAM,KADU,CACA,aAGlB,GAAM,UAAEC,CAAQ,UAAEI,CAAQ,CAAE,CAAGL,EAE/B,GAAI,CACFO,EAAAA,CAAUA,CAACC,KAAK,CAAC,UAAEP,WAAUI,CAAS,EAExC,CAAE,MAAOhH,EAAO,CACd,MAAM,MAAU,UAClB,CAEA,IAAMZ,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEbgE,EAAO,MAAMjE,EAAGI,KAAK,CAACyG,KAAK,CAACvG,SAAS,CAAC,CAC1CC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACoG,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAEA,GAAI,CAACvD,GAKD,CADY,EAJL,IAIW+D,CAAAA,EACR,EADQA,CAAAA,CAAeA,CAACJ,EAAoB3D,EAAK2D,QAAQ,EAHrE,MAAUK,MAAM,YAQlB,MAAO,CACL,GAAGhE,CAAI,CACP2D,cAAUM,CACZ,CACF,CACF,GACD,CACDC,OAAQ,CACN,MAAM/B,OAAO,MAAEnC,CAAI,CAAE,EACnB,GAAKA,CAAD,CAAM/D,EAAE,CAEZ,CAFc,EAEV,CACF,IAAMF,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,GAJqB,CAIjBmI,KAJuBpI,EAAGI,KAAK,CAACsF,SAAS,CAACpF,SAAS,CAAC,CACtDC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACiF,EAAAA,SAASA,CAAC5F,MAAM,CAAEmE,EAAK/D,EAAE,CACrC,GAEkB,OAElB,IAAMwE,EAAc,MAAMD,IACpBO,EAAO,MAAMF,EAAiB9E,EAAI0E,EACxC,OAAMc,EAAiBxF,EAAIiE,EAAK/D,EAAE,CAAE8E,EAAK9E,EAAE,CAC7C,CAAE,MAAOU,EAAO,CACdM,QAAQN,KAAK,CAAC,wBAAyBA,EACzC,CACF,CACF,EACAyH,UAAW,CACHC,IAAN,MAAU,OAAEC,CAAK,MAAEtE,CAAI,CAAE,IACnBA,IACFsE,EADQ,EACA,CAAGtE,EAAK/D,EAAE,CAClBqI,EAAMrD,IAAI,CAAGjB,EAAKiB,IAAI,EAAIjB,EAAKuD,QAAQ,CACvCe,EAAMf,QAAQ,CAAGvD,EAAKuD,QAAQ,CAC9Be,EAAMC,KAAK,CAAGvE,EAAKuE,KAAK,ED/JzB,SAASC,CAA8B,EAC5C,IAAMC,CC8J6CD,CD9JnCvD,CAAI,CAAC,EAAE,CAACyD,WAAW,GAE7BC,EAAaC,MAAM/G,IAAI,CAACoD,GAAM4D,MAAM,CACxC,CAACC,EAAKC,IAASD,EAAMC,EAAKC,UAAU,CAAC,GAAI,GACvC/E,EAAOlB,MAAM,CAEXkG,EAAkBhF,CAAM,CAAC0E,EAAW,CAEpCO,EAAM,CAAC;;2CAE4B,EAAED,EAAgB;;;;;;;;;;;;;QAarD,EAAER,QAAQ;;;EAGhB,CAAC,CAACU,IAAI,GAGAC,EADU,IAAIC,cACEC,MAAM,CAACJ,GACvBK,EAASC,EAAO3H,IAAD2H,CAAMJ,GAAOK,QAAQ,CAAC,UAE3C,MAAO,CAAC,0BAA0B,EAAEF,EAAAA,CAAQ,EC8HQjB,EAAMrD,IAAI,GAEnDqD,GAET,MAAMxE,QAAQ,CAAEA,SAAO,OAAEwE,CAAK,CAAE,EAC9B,GAAIA,GAASxE,EAAQE,IAAI,CAAE,CACzBF,EAAQE,IAAI,CAAC/D,EAAE,CAAGqI,EAAMrI,EAAE,CAC1B6D,EAAQE,IAAI,CAACiB,IAAI,CAAGqD,EAAMrD,IAAI,CAC9BnB,EAAQE,IAAI,CAACuD,QAAQ,CAAGe,EAAMf,QAAQ,CACtCzD,EAAQE,IAAI,CAACuE,KAAK,CAAGD,EAAMC,KAAK,CAEhC,IAAMxI,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACf6F,EAAkB,MAAM9F,EAAGI,KAAK,CAACsF,SAAS,CAAChD,QAAQ,CAAC,CACtDnC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACiF,EAAAA,SAASA,CAAC5F,MAAM,CAAEiE,EAAQE,IAAI,CAAC/D,EAAE,EAC3C6F,KAAM,CAAEf,MAAM,CAAK,CACrB,GAEA,GAAI,CAACc,EAAgB9C,MAAM,CAAE,CAC3B,IAAM0B,EAAc,MAAMD,IACpBO,EAAO,MAAMF,EAAiB9E,EAAI0E,EACxC,OAAMc,EAAiBxF,EAAI+D,EAAQE,IAAI,CAAC/D,EAAE,CAAE8E,EAAK9E,EAAE,EACnD4F,EAAkB,CAAC,CACjBhG,OAAQiE,EAAQE,IAAI,CAAC/D,EAAE,CACvBuF,OAAQT,EAAK9E,EAAE,CACfyJ,UAAW,IAAInH,KACfwC,KAAMA,CACR,EAAE,CAGJjB,EAAQE,IAAI,CAACgB,KAAK,CAAGa,EAAgBxC,GAAG,CAAC0C,GAAO,OACxCA,EAAGhB,IAAI,CAACE,IAAI,CACpB,EACF,CAEA,OAAOnB,CACT,CACF,EACAA,QAAS,CACP6F,SAAU,KACZ,EACF,GAEO,CAFJ,cAEmBC,EAASrC,CAAgB,CAAEI,CAAgB,EAC/D,IAAM5H,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,GAJiB,CAIb6J,KAJmB9J,EAAGI,GAIZ,EAJiB,CAACyG,KAAK,CAACvG,SAAS,CAAC,CAC9CC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACoG,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAGE,MAAM,MAAU,UAGlB,IAAMuC,EAAiB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACpC,GAEpC,CAAC3D,EAAK,CAAG,MAAMjE,EAAGoF,MAAM,CAACyB,EAAAA,KAAKA,EACjCxB,MAAM,CAAC,UACNmC,EACAI,SAAUmC,CACZ,GACCxE,SAAS,GAEZ,OAAOtB,CACT,6ECtOO,SAASf,EAAahB,CAAiB,CAAEhC,CAAU,EAExD,OAAOuJ,EAAO3H,IAAD2H,CAAMQ,KAAKC,SAAS,CADR,WAAEhI,KAAWhC,CAAG,IACAwJ,QAAQ,CAAC,SACpD,CAEO,SAASvH,EAAagI,CAAc,EAEzC,OAAOC,KADWrC,KAAK,CAAC0B,EAAO3H,IAAI,CAACqI,EAAQ,UAAUT,QAAQ,GAEhE,gFCTO,IAAMzJ,EAAW,IAAMoK,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAAC1F,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAAC0F,EAAE,CAAE,CAAEC,MAAMA,CAAAA,CAAC,GAAE,gCAAHA,gCCJnE,IAAMnG,EAAQ,CACnBC,QAAS,UACTC,KAAM,OACNC,OAAQ,SACRC,SAAU,UACZ,EAAW,EAIgB,CACzBgG,aAAc,eACdC,eAAgB,iBAChBC,aAAc,eACdC,cAAe,gBACfC,eAAgB,gBAClB,EAAW,EAIiD,CAC1D,CAACxG,EAAMC,OAAO,CAAC,CAAEwG,OAAOxF,MAAM,CAACyF,GAC/B,CAAC1G,EAAME,IAAI,CAAC,CAAE,CACZwG,EAAYN,YAAY,CACxBM,EAAYL,cAAc,CAC1BK,EAAYF,cAAc,CAC3B,CACD,CAACxG,EAAMG,MAAM,CAAC,CAAE,CACduG,EAAYN,YAAY,CACxBM,EAAYL,cAAc,CAC3B,CACD,CAACrG,EAAMI,QAAQ,CAAC,CAAE,EAAE,EACX,SAEKyB,EAAcP,CAAiB,CAAEE,CAAsB,EACrE,OAAOF,EAAUqF,IAAI,CAAC/F,GAAQgG,CAAgB,CAAChG,EAAK,EAAEiG,SAASrF,GACjE,kVC9BO,IAAMiB,EAAQqE,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvChL,GAAIiL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MACNC,UAAU,GACVC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IACrCrG,KAAMiG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXhL,MAAOgL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASK,MAAM,GAC3BC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,gBAAiB,CAAEC,KAAM,cAAe,GAC/DnD,MAAO2C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZ3D,SAAU2D,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYK,MAAM,GACjC5D,SAAUuD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACjB,GAAE,EACsBD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CACjC,UACA,CACEpL,OAAQqL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UACVS,OAAO,GACPC,UAAU,CAAC,IAAMhF,EAAM3G,EAAE,CAAE,CAAE4L,SAAU,SAAU,GACpDpE,KAAMyD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQY,KAAK,GAAuBH,OAAO,GACtDI,SAAUb,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYS,OAAO,GAClCK,kBAAmBd,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,qBAAqBS,OAAO,GACpDM,cAAef,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBACpBgB,aAAchB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACnBiB,WAAYV,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cACpBW,WAAYlB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,cACjBmB,MAAOnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZoB,SAAUpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YACfqB,cAAerB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACtB,EACA,GAAc,EACZsB,KADY,OACCrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CACtBsB,QAAS,CAACC,EAAQX,QAAQ,CAAEW,EAAQV,iBAAiB,CAAC,GAE1D,GAGW5L,CAFZ,CAEqB6K,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,QAAS,CACzChL,GAAIiL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DqB,QAASzB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAAGJ,MAAM,GACzC1L,OAAQqL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UAAUU,UAAU,CAAC,IAAMhF,EAAM3G,EAAE,CAAE,CAAE4L,SAAU,SAAU,GACxEnC,UAAW+B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPP,UAAU,CAAC,IAAM,IAAI7I,MACxBqK,UAAWnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbkB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAMJ,SAAS,EAChE,GAEa9L,CAFV,CAEqBmK,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7ChL,GAAIiL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DvK,QAASmK,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACXS,OAAO,GACPC,UAAU,CAAC,IAAMxL,EAAOH,EAAE,CAAE,CAAE4L,SAAU,SAAU,GACrDrI,YAAa0H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBAAgBS,OAAO,GACzClI,QAASyH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAChCsB,QAAS/B,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAChCuB,KAAMhC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACX5I,WAAYmJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cAAe,CAAEC,KAAM,cAAe,GACvDC,OAAO,GACPP,UAAU,CAAC,IAAM,IAAI7I,KAC1B,EAAG,GAAY,EACb4K,GADa,QACDL,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAMjM,OAAO,EAC5D,GAEaqM,CAFV,CAEqBnC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7ChL,GAAIiL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DzL,OAAQqL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACVS,OAAO,GACPC,UAAU,CAAC,IAAMhF,EAAM3G,EAAE,CAAE,CAAE4L,SAAU,SAAU,GACpDxK,IAAK6J,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOS,OAAO,GACxB0B,QAAS5B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAG2B,OAAO,EAAC,GACnE5D,UAAW+B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPP,UAAU,CAAC,IAAM,IAAI7I,MACxBgL,UAAW9B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPP,UAAU,CAAC,IAAM,IAAI7I,KAC1B,GAAE,EAEmB0I,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvChL,GAAIiL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DrG,KAAMiG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQS,OAAO,GAC1BtG,YAAa6F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,eAClBxB,UAAW+B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAI7I,MAC7EgL,UAAW9B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAI7I,KAC/E,GAAG,EAEsB0I,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,YAAa,CAChDpL,OAAQqL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAAGC,UAAU,CAAC,IAAMhF,EAAM3G,EAAE,CAAE,CAAE4L,SAAU,SAAU,GACnFrG,OAAQ0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAAGC,UAAU,CAAC,IAAM5G,EAAM/E,EAAE,CAAE,CAAE4L,SAAU,SAAU,GACnFnC,UAAW+B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAI7I,KAC/E,EAAG,GAAY,EACbiL,GADa,CACTrC,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CAAEsB,QAAS,CAACO,EAAMnN,MAAM,CAAEmN,EAAMxH,MAAM,CAAC,GACxD,GAEaiI,CAFT,CAEmBxC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,WAAY,CAC7ChL,GAAIiL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DzL,OAAQqL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAAGC,UAAU,CAAC,IAAMhF,EAAM3G,EAAE,EAC3DgF,KAAMiG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQS,OAAO,GAC1B+B,IAAKxC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOS,OAAO,GAAGJ,MAAM,GACjC7B,UAAW+B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAI7I,MAC7EqK,UAAWnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrD2B,QAAS5B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAG2B,OAAO,EAAC,EACrE,EAAG,GAAY,EACbK,GADa,cACKC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,uBAAuBb,EAAE,CAACC,EAAM/H,IAAI,CAAE+H,EAAMnN,MAAM,EAClF,GAEagO,CAFT,CAE+B5C,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,qBAAsB,CACnE6C,IAAK5C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOC,UAAU,GAC3ByB,UAAWnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbkB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,0BAA0BC,EAAE,CAACC,EAAMJ,SAAS,EAClE,GAEamB,CAFT,CAE4BC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACP,EAAS,CAAC,KAAEQ,CAAG,CAAE,GAAM,EAC/DjK,KAAMiK,EAAIrH,EAAO,CACfsH,OAAQ,CAACT,EAAQ5N,MAAM,CAAC,CACxB+L,WAAY,CAAChF,EAAM3G,EAAE,CAAC,GAE1B,GAEakO,CAFT,CAE8BH,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACvI,EAAW,CAAC,KAAEwI,CAAG,CAAE,GAAM,EACnEjK,KAAMiK,EAAIrH,EAAO,CACfsH,OAAQ,CAACzI,EAAU5F,MAAM,CAAC,CAC1B+L,WAAY,CAAChF,EAAM3G,EAAE,CACvB,GACA8E,KAAMkJ,EAAIjJ,EAAO,CACfkJ,OAAQ,CAACzI,EAAUD,MAAM,CAAC,CAC1BoG,WAAY,CAAC5G,EAAM/E,EAAE,CAAC,GAE1B,GAEamO,CAFT,CAE0BJ,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACpH,EAAO,CAAC,MAAEyH,CAAI,CAAE,GAAM,EAC5D5I,UAAW4I,EAAK5I,GAChBgI,QAASY,EAAKZ,GAChB,GAEaa,CAFT,CAE0BN,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAChJ,EAAO,CAAC,MAAEqJ,CAAI,CAAE,GAAM,EAC5D5I,UAAW4I,EAAK5I,GAClB,IAAI,sFC5IG,SAAS8I,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,CAEO,eAAezE,EAAapC,CAAgB,EACjD,IAAMgH,EAAU,IAAItF,YACduF,EAAOrI,QAAQ5B,GAAG,CAAC6B,WAAW,EAAI,GAClC2D,EAAOwE,EAAQrF,MAAM,CAAC3B,EAAWiH,GAEvC,OAAOC,KAAKC,OAAOC,YAAY,IAAI,IAAIC,WAD1B,MAAM3D,OAAO4D,MAAM,CAACC,MAAM,CAAC,UAAW/E,KAErD,CAEO,eAAepC,EAAgBJ,CAAgB,CAAEmC,CAAsB,EAE5E,OADa,MAAMC,EAAapC,KAChBmC,CAClB,8DChBO,IAAMjC,EAAasH,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACjC5H,SAAU4H,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,WACPC,GAAG,CAAC,GAAI,gBACRC,KAAK,CAAC,mBAAoB,uBAC1BC,MAAM,CAACC,GAAO,CAACA,EAAIxE,QAAQ,CAAC,KAAM,cACrCrD,SAAUwH,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,eACZ,GAAE", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/emails/[address]/route.ts", "webpack://_N_E/./app/api/emails/[address]/route.ts?3386", "webpack://_N_E/?35a3", "webpack://_N_E/./app/lib/apiKey.ts", "webpack://_N_E/./app/lib/avatar.ts", "webpack://_N_E/./app/lib/auth.ts", "webpack://_N_E/./app/lib/cursor.ts", "webpack://_N_E/./app/lib/db.ts", "webpack://_N_E/./app/lib/permissions.ts", "webpack://_N_E/./app/lib/schema.ts", "webpack://_N_E/./app/lib/utils.ts", "webpack://_N_E/./app/lib/validation.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { NextResponse } from \"next/server\"\r\nimport { createDb } from \"@/lib/db\"\r\nimport { emails, messages } from \"@/lib/schema\"\r\nimport { eq, and, lt, or, sql } from \"drizzle-orm\"\r\nimport { encodeCursor, decodeCursor } from \"@/lib/cursor\"\r\nimport { getUserId } from \"@/lib/apiKey\"\r\nexport const runtime = \"edge\"\r\n\r\nexport async function DELETE(\r\n  request: Request,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  const userId = await getUserId()\r\n\r\n  try {\r\n    const db = createDb()\r\n    const { id } = await params\r\n    const email = await db.query.emails.findFirst({\r\n      where: and(\r\n        eq(emails.id, id),\r\n        eq(emails.userId, userId!)\r\n      )\r\n    })\r\n\r\n    if (!email) {\r\n      return NextResponse.json(\r\n        { error: \"邮箱不存在或无权限删除\" },\r\n        { status: 403 }\r\n      )\r\n    }\r\n    await db.delete(messages)\r\n      .where(eq(messages.emailId, id))\r\n\r\n    await db.delete(emails)\r\n      .where(eq(emails.id, id))\r\n\r\n    return NextResponse.json({ success: true })\r\n  } catch (error) {\r\n    console.error('Failed to delete email:', error)\r\n    return NextResponse.json(\r\n      { error: \"删除邮箱失败\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} \r\n\r\nconst PAGE_SIZE = 20\r\n\r\nexport async function GET(\r\n  request: Request,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  const { searchParams } = new URL(request.url)\r\n  const cursorStr = searchParams.get('cursor')\r\n\r\n  try {\r\n    const db = createDb()\r\n    const { id } = await params\r\n\r\n    const userId = await getUserId()\r\n\r\n    const email = await db.query.emails.findFirst({\r\n      where: and(\r\n        eq(emails.id, id),\r\n        eq(emails.userId, userId!)\r\n      )\r\n    })\r\n\r\n    if (!email) {\r\n      return NextResponse.json(\r\n        { error: \"无权限查看\" },\r\n        { status: 403 }\r\n      )\r\n    }\r\n\r\n    const baseConditions = eq(messages.emailId, id)\r\n\r\n    const totalResult = await db.select({ count: sql<number>`count(*)` })\r\n      .from(messages)\r\n      .where(baseConditions)\r\n    const totalCount = Number(totalResult[0].count)\r\n\r\n    const conditions = [baseConditions]\r\n\r\n    if (cursorStr) {\r\n      const { timestamp, id } = decodeCursor(cursorStr)\r\n      conditions.push(\r\n        // @ts-expect-error \"ignore the error\"\r\n        or(\r\n          lt(messages.receivedAt, new Date(timestamp)),\r\n          and(\r\n            eq(messages.receivedAt, new Date(timestamp)),\r\n            lt(messages.id, id)\r\n          )\r\n        )\r\n      )\r\n    }\r\n\r\n    const results = await db.query.messages.findMany({\r\n      where: and(...conditions),\r\n      orderBy: (messages, { desc }) => [\r\n        desc(messages.receivedAt),\r\n        desc(messages.id)\r\n      ],\r\n      limit: PAGE_SIZE + 1\r\n    })\r\n    \r\n    const hasMore = results.length > PAGE_SIZE\r\n    const nextCursor = hasMore \r\n      ? encodeCursor(\r\n          results[PAGE_SIZE - 1].receivedAt.getTime(),\r\n          results[PAGE_SIZE - 1].id\r\n        )\r\n      : null\r\n    const messageList = hasMore ? results.slice(0, PAGE_SIZE) : results\r\n\r\n    return NextResponse.json({ \r\n      messages: messageList.map(msg => ({\r\n        id: msg.id,\r\n        from_address: msg.fromAddress,\r\n        subject: msg.subject,\r\n        received_at: msg.receivedAt.getTime()\r\n      })),\r\n      nextCursor,\r\n      total: totalCount\r\n    })\r\n  } catch (error) {\r\n    console.error('Failed to fetch messages:', error)\r\n    return NextResponse.json(\r\n      { error: \"Failed to fetch messages\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\emails\\\\[address]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/emails/[address]/route\",\n        pathname: \"/api/emails/[address]\",\n        filename: \"route\",\n        bundlePath: \"app/api/emails/[address]/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\emails\\\\[address]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Femails%2F%5Baddress%5D%2Froute&page=%2Fapi%2Femails%2F%5Baddress%5D%2Froute&pagePath=private-next-app-dir%2Fapi%2Femails%2F%5Baddress%5D%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fapi%2Femails%2F%5Baddress%5D%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/emails/[address]/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/emails/[address]/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/emails/[address]/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { createDb } from \"./db\"\r\nimport { api<PERSON><PERSON>s } from \"./schema\"\r\nimport { eq, and, gt } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport type { User } from \"next-auth\"\r\nimport { auth } from \"./auth\"\r\nimport { headers } from \"next/headers\"\r\n\r\nasync function getUserByApiKey(key: string): Promise<User | null> {\r\n  const db = createDb()\r\n  const apiKey = await db.query.apiKeys.findFirst({\r\n    where: and(\r\n      eq(apiKeys.key, key),\r\n      eq(apiKeys.enabled, true),\r\n      gt(apiKeys.expiresAt, new Date())\r\n    ),\r\n    with: {\r\n      user: true\r\n    }\r\n  })\r\n\r\n  if (!apiKey) return null\r\n\r\n  return apiKey.user\r\n}\r\n\r\nexport async function handleApiKeyAuth(apiKey: string, pathname: string) {\r\n  if (!pathname.startsWith('/api/emails') && !pathname.startsWith('/api/config')) {\r\n    return NextResponse.json(\r\n      { error: \"无权限查看\" },\r\n      { status: 403 }\r\n    )\r\n  }\r\n\r\n  const user = await getUserByApiKey(apiKey)\r\n  if (!user?.id) {\r\n    return NextResponse.json(\r\n      { error: \"无效的 API Key\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  const response = NextResponse.next()\r\n  response.headers.set(\"X-User-Id\", user.id)\r\n  return response\r\n}\r\n\r\nexport const getUserId = async () => {\r\n  const headersList = await headers()\r\n  const userId = headersList.get(\"X-User-Id\")\r\n  \r\n  if (userId) return userId\r\n\r\n  const session = await auth()\r\n\r\n  return session?.user.id\r\n}\r\n", "const COLORS = [\r\n  '#2196F3', // 蓝色\r\n  '#009688', // 青色\r\n  '#9C27B0', // 紫色\r\n  '#F44336', // 红色\r\n  '#673AB7', // 深紫色\r\n  '#3F51B5', // 靛蓝\r\n  '#4CAF50', // 绿色\r\n  '#FF5722', // 深橙\r\n  '#795548', // 棕色\r\n  '#607D8B', // 蓝灰\r\n];\r\n\r\nexport function generateAvatarUrl(name: string): string {\r\n  const initial = name[0].toUpperCase();\r\n  \r\n  const colorIndex = Array.from(name).reduce(\r\n    (acc, char) => acc + char.charCodeAt(0), 0\r\n  ) % COLORS.length;\r\n  \r\n  const backgroundColor = COLORS[colorIndex];\r\n  \r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\" width=\"100\" height=\"100\">\r\n      <rect width=\"100\" height=\"100\" fill=\"${backgroundColor}\"/>\r\n      <text \r\n        x=\"50%\" \r\n        y=\"50%\" \r\n        fill=\"white\" \r\n        font-family=\"system-ui, -apple-system, sans-serif\" \r\n        font-size=\"45\" \r\n        font-weight=\"500\"\r\n        text-anchor=\"middle\"\r\n        alignment-baseline=\"central\"\r\n        dominant-baseline=\"central\"\r\n        style=\"text-transform: uppercase\"\r\n      >\r\n        ${initial}\r\n      </text>\r\n    </svg>\r\n  `.trim();\r\n\r\n  const encoder = new TextEncoder();\r\n  const bytes = encoder.encode(svg);\r\n  const base64 = Buffer.from(bytes).toString('base64');\r\n  \r\n  return `data:image/svg+xml;base64,${base64}`;\r\n} ", "import NextAuth from \"next-auth\"\r\nimport GitHub from \"next-auth/providers/github\"\r\nimport { Dr<PERSON>zleAdapter } from \"@auth/drizzle-adapter\"\r\nimport { createDb, Db } from \"./db\"\r\nimport { accounts, users, roles, userRoles } from \"./schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { Permission, hasPermission, ROLES, Role } from \"./permissions\"\r\nimport CredentialsProvider from \"next-auth/providers/credentials\"\r\nimport { hashPassword, comparePassword } from \"@/lib/utils\"\r\nimport { authSchema } from \"@/lib/validation\"\r\nimport { generateAvatarUrl } from \"./avatar\"\r\nimport { getUserId } from \"./apiKey\"\r\n\r\nconst ROLE_DESCRIPTIONS: Record<Role, string> = {\r\n  [ROLES.EMPEROR]: \"皇帝（网站所有者）\",\r\n  [ROLES.DUKE]: \"公爵（超级用户）\",\r\n  [ROLES.KNIGHT]: \"骑士（高级用户）\",\r\n  [ROLES.CIVILIAN]: \"平民（普通用户）\",\r\n}\r\n\r\nconst getDefaultRole = async (): Promise<Role> => {\r\n  const defaultRole = await getRequestContext().env.SITE_CONFIG.get(\"DEFAULT_ROLE\")\r\n\r\n  if (\r\n    defaultRole === ROLES.DUKE ||\r\n    defaultRole === ROLES.KNIGHT ||\r\n    defaultRole === ROLES.CIVILIAN\r\n  ) {\r\n    return defaultRole as Role\r\n  }\r\n  \r\n  return ROLES.CIVILIAN\r\n}\r\n\r\nasync function findOrCreateRole(db: Db, roleName: Role) {\r\n  let role = await db.query.roles.findFirst({\r\n    where: eq(roles.name, roleName),\r\n  })\r\n\r\n  if (!role) {\r\n    const [newRole] = await db.insert(roles)\r\n      .values({\r\n        name: roleName,\r\n        description: ROLE_DESCRIPTIONS[roleName],\r\n      })\r\n      .returning()\r\n    role = newRole\r\n  }\r\n\r\n  return role\r\n}\r\n\r\nexport async function assignRoleToUser(db: Db, userId: string, roleId: string) {\r\n  await db.delete(userRoles)\r\n    .where(eq(userRoles.userId, userId))\r\n\r\n  await db.insert(userRoles)\r\n    .values({\r\n      userId,\r\n      roleId,\r\n    })\r\n}\r\n\r\nexport async function getUserRole(userId: string) {\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n  return userRoleRecords[0].role.name\r\n}\r\n\r\nexport async function checkPermission(permission: Permission) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) return false\r\n\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n\r\n  const userRoleNames = userRoleRecords.map(ur => ur.role.name)\r\n  return hasPermission(userRoleNames as Role[], permission)\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  auth,\r\n  signIn,\r\n  signOut\r\n} = NextAuth(() => ({\r\n  secret: process.env.AUTH_SECRET,\r\n  adapter: DrizzleAdapter(createDb(), {\r\n    usersTable: users,\r\n    accountsTable: accounts,\r\n  }),\r\n  providers: [\r\n    GitHub({\r\n      clientId: process.env.AUTH_GITHUB_ID,\r\n      clientSecret: process.env.AUTH_GITHUB_SECRET,\r\n    }),\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        username: { label: \"用户名\", type: \"text\", placeholder: \"请输入用户名\" },\r\n        password: { label: \"密码\", type: \"password\", placeholder: \"请输入密码\" },\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials) {\r\n          throw new Error(\"请输入用户名和密码\")\r\n        }\r\n\r\n        const { username, password } = credentials\r\n\r\n        try {\r\n          authSchema.parse({ username, password })\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        } catch (error) {\r\n          throw new Error(\"输入格式不正确\")\r\n        }\r\n\r\n        const db = createDb()\r\n\r\n        const user = await db.query.users.findFirst({\r\n          where: eq(users.username, username as string),\r\n        })\r\n\r\n        if (!user) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        const isValid = await comparePassword(password as string, user.password as string)\r\n        if (!isValid) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        return {\r\n          ...user,\r\n          password: undefined,\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  events: {\r\n    async signIn({ user }) {\r\n      if (!user.id) return\r\n\r\n      try {\r\n        const db = createDb()\r\n        const existingRole = await db.query.userRoles.findFirst({\r\n          where: eq(userRoles.userId, user.id),\r\n        })\r\n\r\n        if (existingRole) return\r\n\r\n        const defaultRole = await getDefaultRole()\r\n        const role = await findOrCreateRole(db, defaultRole)\r\n        await assignRoleToUser(db, user.id, role.id)\r\n      } catch (error) {\r\n        console.error('Error assigning role:', error)\r\n      }\r\n    },\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id\r\n        token.name = user.name || user.username\r\n        token.username = user.username\r\n        token.image = user.image || generateAvatarUrl(token.name as string)\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token && session.user) {\r\n        session.user.id = token.id as string\r\n        session.user.name = token.name as string\r\n        session.user.username = token.username as string\r\n        session.user.image = token.image as string\r\n\r\n        const db = createDb()\r\n        let userRoleRecords = await db.query.userRoles.findMany({\r\n          where: eq(userRoles.userId, session.user.id),\r\n          with: { role: true },\r\n        })\r\n  \r\n        if (!userRoleRecords.length) {\r\n          const defaultRole = await getDefaultRole()\r\n          const role = await findOrCreateRole(db, defaultRole)\r\n          await assignRoleToUser(db, session.user.id, role.id)\r\n          userRoleRecords = [{\r\n            userId: session.user.id,\r\n            roleId: role.id,\r\n            createdAt: new Date(),\r\n            role: role\r\n          }]\r\n        }\r\n  \r\n        session.user.roles = userRoleRecords.map(ur => ({\r\n          name: ur.role.name,\r\n        }))\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: \"jwt\",\r\n  },\r\n}))\r\n\r\nexport async function register(username: string, password: string) {\r\n  const db = createDb()\r\n  \r\n  const existing = await db.query.users.findFirst({\r\n    where: eq(users.username, username)\r\n  })\r\n\r\n  if (existing) {\r\n    throw new Error(\"用户名已存在\")\r\n  }\r\n\r\n  const hashedPassword = await hashPassword(password)\r\n  \r\n  const [user] = await db.insert(users)\r\n    .values({\r\n      username,\r\n      password: hashedPassword,\r\n    })\r\n    .returning()\r\n\r\n  return user\r\n}\r\n", "interface CursorData {\r\n  timestamp: number\r\n  id: string\r\n}\r\n\r\nexport function encodeCursor(timestamp: number, id: string): string {\r\n  const data: CursorData = { timestamp, id }\r\n  return Buffer.from(JSON.stringify(data)).toString('base64')\r\n}\r\n\r\nexport function decodeCursor(cursor: string): CursorData {\r\n  const data = JSON.parse(Buffer.from(cursor, 'base64').toString())\r\n  return data as CursorData\r\n} ", "import { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { drizzle } from \"drizzle-orm/d1\"\r\nimport * as schema from \"./schema\"\r\n\r\nexport const createDb = () => drizzle(getRequestContext().env.DB, { schema })\r\n\r\nexport type Db = ReturnType<typeof createDb>\r\n", "export const ROLES = {\r\n  EMPEROR: 'emperor',\r\n  <PERSON>U<PERSON>: 'duke',\r\n  <PERSON><PERSON><PERSON><PERSON>: 'knight',\r\n  CIVILIAN: 'civilian',\r\n} as const;\r\n\r\nexport type Role = typeof ROLES[keyof typeof ROLES];\r\n\r\nexport const PERMISSIONS = {\r\n  MANAGE_EMAIL: 'manage_email',\r\n  <PERSON><PERSON>GE_WEBHOOK: 'manage_webhook',\r\n  PROMOTE_USER: 'promote_user',\r\n  MANAGE_CONFIG: 'manage_config',\r\n  MANAGE_API_KEY: 'manage_api_key',\r\n} as const;\r\n\r\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\r\n\r\nexport const ROLE_PERMISSIONS: Record<Role, Permission[]> = {\r\n  [ROLES.EMPEROR]: Object.values(PERMISSIONS),\r\n  [ROLES.DUKE]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_API_KEY,\r\n  ],\r\n  [ROLES.KNIGHT]: [\r\n    PERMISSIONS.<PERSON>NAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n  ],\r\n  [ROLES.CIVILIAN]: [],\r\n} as const;\r\n\r\nexport function hasPermission(userRoles: Role[], permission: Permission): boolean {\r\n  return userRoles.some(role => ROLE_PERMISSIONS[role]?.includes(permission));\r\n} ", "import { integer, sqliteTable, text, primaryKey, uniqueIndex, index } from \"drizzle-orm/sqlite-core\"\r\nimport type { AdapterAccountType } from \"next-auth/adapters\"\r\nimport { relations } from 'drizzle-orm';\r\n\r\n// https://authjs.dev/getting-started/adapters/drizzle\r\nexport const users = sqliteTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: integer(\"emailVerified\", { mode: \"timestamp_ms\" }),\r\n  image: text(\"image\"),\r\n  username: text(\"username\").unique(),\r\n  password: text(\"password\"),\r\n})\r\nexport const accounts = sqliteTable(\r\n  \"account\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => users.id, { onDelete: \"cascade\" }),\r\n    type: text(\"type\").$type<AdapterAccountType>().notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n  },\r\n  (account) => ({\r\n    compoundKey: primaryKey({\r\n      columns: [account.provider, account.providerAccountId],\r\n    }),\r\n  })\r\n)\r\n\r\nexport const emails = sqliteTable(\"email\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  address: text(\"address\").notNull().unique(),\r\n  userId: text(\"userId\").references(() => users.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp_ms\" }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"email_expires_at_idx\").on(table.expiresAt),\r\n}))\r\n\r\nexport const messages = sqliteTable(\"message\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  emailId: text(\"emailId\")\r\n    .notNull()\r\n    .references(() => emails.id, { onDelete: \"cascade\" }),\r\n  fromAddress: text(\"from_address\").notNull(),\r\n  subject: text(\"subject\").notNull(),\r\n  content: text(\"content\").notNull(),\r\n  html: text(\"html\"),\r\n  receivedAt: integer(\"received_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  emailIdIdx: index(\"message_email_id_idx\").on(table.emailId),\r\n}))\r\n\r\nexport const webhooks = sqliteTable('webhook', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id')\r\n    .notNull()\r\n    .references(() => users.id, { onDelete: \"cascade\" }),\r\n  url: text('url').notNull(),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n})\r\n\r\nexport const roles = sqliteTable(\"role\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\").notNull(),\r\n  description: text(\"description\"),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n});\r\n\r\nexport const userRoles = sqliteTable(\"user_role\", {\r\n  userId: text(\"user_id\").notNull().references(() => users.id, { onDelete: \"cascade\" }),\r\n  roleId: text(\"role_id\").notNull().references(() => roles.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  pk: primaryKey({ columns: [table.userId, table.roleId] }),\r\n}));\r\n\r\nexport const apiKeys = sqliteTable('api_keys', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id),\r\n  name: text('name').notNull(),\r\n  key: text('key').notNull().unique(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n}, (table) => ({\r\n  nameUserIdUnique: uniqueIndex('name_user_id_unique').on(table.name, table.userId)\r\n}));\r\n\r\nexport const revoked_credentials = sqliteTable('revoked_credential', {\r\n  jti: text('jti').primaryKey(),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp_ms' }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"revoked_expires_at_idx\").on(table.expiresAt),\r\n}));\r\n\r\nexport const apiKeysRelations = relations(apiKeys, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [apiKeys.userId],\r\n    references: [users.id],\r\n  }),\r\n}));\r\n\r\nexport const userRolesRelations = relations(userRoles, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userRoles.userId],\r\n    references: [users.id],\r\n  }),\r\n  role: one(roles, {\r\n    fields: [userRoles.roleId],\r\n    references: [roles.id],\r\n  }),\r\n}));\r\n\r\nexport const usersRelations = relations(users, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n  apiKeys: many(apiKeys),\r\n}));\r\n\r\nexport const rolesRelations = relations(roles, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n}));", "import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport async function hashPassword(password: string): Promise<string> {\r\n  const encoder = new TextEncoder()\r\n  const salt = process.env.AUTH_SECRET || ''\r\n  const data = encoder.encode(password + salt)\r\n  const hash = await crypto.subtle.digest('SHA-256', data)\r\n  return btoa(String.fromCharCode(...new Uint8Array(hash)))\r\n}\r\n\r\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\r\n  const hash = await hashPassword(password)\r\n  return hash === hashedPassword\r\n}", "import { z } from \"zod\"\r\n\r\nexport const authSchema = z.object({\r\n  username: z.string()\r\n    .min(1, \"用户名不能为空\")\r\n    .max(20, \"用户名不能超过20个字符\")\r\n    .regex(/^[a-zA-Z0-9_-]+$/, \"用户名只能包含字母、数字、下划线和横杠\")\r\n    .refine(val => !val.includes('@'), \"用户名不能是邮箱格式\"),\r\n  password: z.string()\r\n    .min(8, \"密码长度必须大于等于8位\")\r\n})\r\n\r\nexport type AuthSchema = z.infer<typeof authSchema>"], "names": ["runtime", "DELETE", "request", "params", "userId", "getUserId", "db", "createDb", "id", "email", "query", "emails", "<PERSON><PERSON><PERSON><PERSON>", "where", "and", "eq", "NextResponse", "json", "error", "status", "delete", "messages", "emailId", "success", "console", "GET", "searchParams", "URL", "url", "cursorStr", "get", "baseConditions", "totalResult", "select", "count", "sql", "from", "totalCount", "Number", "conditions", "timestamp", "decodeCursor", "push", "or", "lt", "receivedAt", "Date", "results", "find<PERSON>any", "orderBy", "desc", "limit", "PAGE_SIZE", "hasMore", "length", "nextCursor", "encodeCursor", "getTime", "messageList", "slice", "map", "msg", "from_address", "fromAddress", "subject", "received_at", "total", "headersList", "headers", "session", "auth", "user", "COLORS", "ROLE_DESCRIPTIONS", "ROLES", "EMPEROR", "DUKE", "KNIGHT", "CIVILIAN", "getDefaultRole", "defaultRole", "getRequestContext", "env", "SITE_CONFIG", "findOrCreateRole", "<PERSON><PERSON><PERSON>", "role", "roles", "name", "newRole", "insert", "values", "description", "returning", "assignRoleToUser", "roleId", "userRoles", "checkPermission", "permission", "userRoleNames", "userRoleRecords", "with", "ur", "hasPermission", "handlers", "POST", "signIn", "signOut", "NextAuth", "secret", "process", "AUTH_SECRET", "adapter", "DrizzleAdapter", "usersTable", "users", "accountsTable", "accounts", "providers", "GitHub", "clientId", "AUTH_GITHUB_ID", "clientSecret", "AUTH_GITHUB_SECRET", "CredentialsProvider", "credentials", "username", "label", "type", "placeholder", "password", "authorize", "authSchema", "parse", "comparePassword", "Error", "undefined", "events", "existingRole", "callbacks", "jwt", "token", "image", "generateAvatarUrl", "initial", "toUpperCase", "colorIndex", "Array", "reduce", "acc", "char", "charCodeAt", "backgroundColor", "svg", "trim", "bytes", "TextEncoder", "encode", "base64", "<PERSON><PERSON><PERSON>", "toString", "createdAt", "strategy", "register", "existing", "hashedPassword", "hashPassword", "JSON", "stringify", "cursor", "data", "drizzle", "DB", "schema", "MANAGE_EMAIL", "MANAGE_WEBHOOK", "PROMOTE_USER", "MANAGE_CONFIG", "MANAGE_API_KEY", "Object", "PERMISSIONS", "some", "ROLE_PERMISSIONS", "includes", "sqliteTable", "text", "<PERSON><PERSON><PERSON>", "$defaultFn", "crypto", "randomUUID", "unique", "emailVerified", "integer", "mode", "notNull", "references", "onDelete", "$type", "provider", "providerAccountId", "refresh_token", "access_token", "expires_at", "token_type", "scope", "id_token", "session_state", "<PERSON><PERSON><PERSON>", "columns", "account", "address", "expiresAt", "expiresAtIdx", "index", "on", "table", "content", "html", "emailIdIdx", "webhooks", "enabled", "default", "updatedAt", "pk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "nameUserIdUnique", "uniqueIndex", "revoked_credentials", "jti", "apiKeysRelations", "relations", "one", "fields", "userRolesRelations", "usersRelations", "many", "rolesRelations", "cn", "inputs", "twMerge", "clsx", "encoder", "salt", "btoa", "String", "fromCharCode", "Uint8Array", "subtle", "digest", "z", "min", "max", "regex", "refine", "val"], "sourceRoot": "", "ignoreList": []}