{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseRSC.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\n\nimport { getModuleTrace, formatModuleTrace } from './getModuleTrace'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nfunction formatRSCErrorMessage(\n  message: string,\n  isPagesDir: boolean,\n  fileName: string\n): [string, string] {\n  let formattedMessage = message\n  let formattedVerboseMessage = ''\n\n  // Comes from the \"React Server Components\" transform in SWC, always\n  // attach the module trace.\n  const NEXT_RSC_ERR_REACT_API = /.+NEXT_RSC_ERR_REACT_API: (.*?)\\n/s\n  const NEXT_RSC_ERR_SERVER_IMPORT = /.+NEXT_RSC_ERR_SERVER_IMPORT: (.*?)\\n/s\n  const NEXT_RSC_ERR_CLIENT_IMPORT = /.+NEXT_RSC_ERR_CLIENT_IMPORT: (.*?)\\n/s\n  const NEXT_RSC_ERR_CLIENT_METADATA_EXPORT =\n    /.+NEXT_RSC_ERR_CLIENT_METADATA_EXPORT: (.*?)\\n/s\n  const NEXT_RSC_ERR_CONFLICT_METADATA_EXPORT =\n    /NEXT_RSC_ERR_CONFLICT_METADATA_EXPORT/s\n  const NEXT_RSC_ERR_CLIENT_DIRECTIVE = /.+NEXT_RSC_ERR_CLIENT_DIRECTIVE\\n/s\n  const NEXT_RSC_ERR_CLIENT_DIRECTIVE_PAREN =\n    /.+NEXT_RSC_ERR_CLIENT_DIRECTIVE_PAREN\\n/s\n  const NEXT_RSC_ERR_INVALID_API = /.+NEXT_RSC_ERR_INVALID_API: (.*?)\\n/s\n  const NEXT_RSC_ERR_ERROR_FILE_SERVER_COMPONENT =\n    /.+NEXT_RSC_ERR_ERROR_FILE_SERVER_COMPONENT/\n\n  if (NEXT_RSC_ERR_REACT_API.test(message)) {\n    const matches = message.match(NEXT_RSC_ERR_REACT_API)\n    if (matches && matches[1] === 'Component') {\n      formattedMessage = `\\n\\nYou’re importing a class component. It only works in a Client Component but none of its parents are marked with \"use client\", so they're Server Components by default.\\nLearn more: https://nextjs.org/docs/app/building-your-application/rendering/client-components\\n\\n`\n    } else {\n      formattedMessage = message.replace(\n        NEXT_RSC_ERR_REACT_API,\n        `\\n\\nYou're importing a component that needs $1. This React hook only works in a client component. To fix, mark the file (or its parent) with the \\`\"use client\"\\` directive. \\n\\nLearn more: https://nextjs.org/docs/app/api-reference/directives/use-client\\n\\n`\n      )\n    }\n    formattedVerboseMessage =\n      '\\n\\nMaybe one of these should be marked as a client entry with \"use client\":\\n'\n  } else if (NEXT_RSC_ERR_SERVER_IMPORT.test(message)) {\n    let shouldAddUseClient = true\n    const matches = message.match(NEXT_RSC_ERR_SERVER_IMPORT)\n    switch (matches && matches[1]) {\n      case 'react-dom/server':\n        // If importing \"react-dom/server\", we should show a different error.\n        formattedMessage = `\\n\\nYou're importing a component that imports react-dom/server. To fix it, render or return the content directly as a Server Component instead for perf and security.\\nLearn more: https://nextjs.org/docs/app/building-your-application/rendering`\n        break\n      case 'next/router':\n        // If importing \"next/router\", we should tell them to use \"next/navigation\".\n        formattedMessage = `\\n\\nYou have a Server Component that imports next/router. Use next/navigation instead.\\nLearn more: https://nextjs.org/docs/app/api-reference/functions/use-router`\n        shouldAddUseClient = false\n        break\n      default:\n        formattedMessage = message.replace(\n          NEXT_RSC_ERR_SERVER_IMPORT,\n          `\\n\\nYou're importing a component that imports $1. It only works in a Client Component but none of its parents are marked with \"use client\", so they're Server Components by default.\\nLearn more: https://nextjs.org/docs/app/building-your-application/rendering\\n\\n`\n        )\n    }\n    formattedVerboseMessage = shouldAddUseClient\n      ? '\\n\\nMaybe one of these should be marked as a client entry \"use client\":\\n'\n      : '\\n\\nImport trace:\\n'\n  } else if (NEXT_RSC_ERR_CLIENT_IMPORT.test(message)) {\n    if (isPagesDir) {\n      formattedMessage = message.replace(\n        NEXT_RSC_ERR_CLIENT_IMPORT,\n        `\\n\\nYou're importing a component that needs $1. That only works in a Server Component which is not supported in the pages/ directory. Read more: https://nextjs.org/docs/app/building-your-application/rendering/server-components\\n\\n`\n      )\n      formattedVerboseMessage = '\\n\\nImport trace for requested module:\\n'\n    } else {\n      formattedMessage = message.replace(\n        NEXT_RSC_ERR_CLIENT_IMPORT,\n        `\\n\\nYou're importing a component that needs $1. That only works in a Server Component but one of its parents is marked with \"use client\", so it's a Client Component.\\nLearn more: https://nextjs.org/docs/app/building-your-application/rendering\\n\\n`\n      )\n      formattedVerboseMessage =\n        '\\n\\nOne of these is marked as a client entry with \"use client\":\\n'\n    }\n  } else if (NEXT_RSC_ERR_CLIENT_DIRECTIVE.test(message)) {\n    formattedMessage = message.replace(\n      NEXT_RSC_ERR_CLIENT_DIRECTIVE,\n      `\\n\\nThe \"use client\" directive must be placed before other expressions. Move it to the top of the file to resolve this issue.\\n\\n`\n    )\n    formattedVerboseMessage = '\\n\\nImport path:\\n'\n  } else if (NEXT_RSC_ERR_CLIENT_DIRECTIVE_PAREN.test(message)) {\n    formattedMessage = message.replace(\n      NEXT_RSC_ERR_CLIENT_DIRECTIVE_PAREN,\n      `\\n\\n\"use client\" must be a directive, and placed before other expressions. Remove the parentheses and move it to the top of the file to resolve this issue.\\nLearn more: https://nextjs.org/docs/app/api-reference/directives/use-client\\n\\n`\n    )\n    formattedVerboseMessage = '\\n\\nImport path:\\n'\n  } else if (NEXT_RSC_ERR_INVALID_API.test(message)) {\n    formattedMessage = message.replace(\n      NEXT_RSC_ERR_INVALID_API,\n      `\\n\\n\"$1\" is not supported in app/. Read more: https://nextjs.org/docs/app/building-your-application/data-fetching\\n\\n`\n    )\n    formattedVerboseMessage = '\\n\\nFile path:\\n'\n  } else if (NEXT_RSC_ERR_ERROR_FILE_SERVER_COMPONENT.test(message)) {\n    formattedMessage = message.replace(\n      NEXT_RSC_ERR_ERROR_FILE_SERVER_COMPONENT,\n      `\\n\\n${fileName} must be a Client Component. Add the \"use client\" directive the top of the file to resolve this issue.\\nLearn more: https://nextjs.org/docs/app/api-reference/directives/use-client\\n\\n`\n    )\n    formattedVerboseMessage = '\\n\\nImport path:\\n'\n  } else if (NEXT_RSC_ERR_CLIENT_METADATA_EXPORT.test(message)) {\n    formattedMessage = message.replace(\n      NEXT_RSC_ERR_CLIENT_METADATA_EXPORT,\n      `\\n\\nYou are attempting to export \"$1\" from a component marked with \"use client\", which is disallowed. Either remove the export, or the \"use client\" directive. Read more: https://nextjs.org/docs/app/api-reference/directives/use-client\\n\\n`\n    )\n\n    formattedVerboseMessage = '\\n\\nFile path:\\n'\n  } else if (NEXT_RSC_ERR_CONFLICT_METADATA_EXPORT.test(message)) {\n    formattedMessage = message.replace(\n      NEXT_RSC_ERR_CONFLICT_METADATA_EXPORT,\n      `\\n\\n\"metadata\" and \"generateMetadata\" cannot be exported at the same time, please keep one of them. Read more: https://nextjs.org/docs/app/api-reference/file-conventions/metadata\\n\\n`\n    )\n\n    formattedVerboseMessage = '\\n\\nFile path:\\n'\n  }\n\n  return [formattedMessage, formattedVerboseMessage]\n}\n\n// Check if the error is specifically related to React Server Components.\n// If so, we'll format the error message to be more helpful.\nexport function getRscError(\n  fileName: string,\n  err: Error,\n  module: any,\n  compilation: webpack.Compilation,\n  compiler: webpack.Compiler\n): SimpleWebpackError | false {\n  if (!err.message || !/NEXT_RSC_ERR_/.test(err.message)) {\n    return false\n  }\n\n  const { isPagesDir, moduleTrace } = getModuleTrace(\n    module,\n    compilation,\n    compiler\n  )\n\n  const formattedError = formatRSCErrorMessage(\n    err.message,\n    isPagesDir,\n    fileName\n  )\n\n  const { formattedModuleTrace, lastInternalFileName, invalidImportMessage } =\n    formatModuleTrace(compiler, moduleTrace)\n\n  const error = new SimpleWebpackError(\n    lastInternalFileName,\n    'ReactServerComponentsError:\\n' +\n      formattedError[0] +\n      invalidImportMessage +\n      formattedError[1] +\n      formattedModuleTrace\n  )\n\n  // Delete the stack because it's created here.\n  error.stack = ''\n\n  return error\n}\n"], "names": ["getRscError", "formatRSCErrorMessage", "message", "isPagesDir", "fileName", "formattedMessage", "formattedVerboseMessage", "NEXT_RSC_ERR_REACT_API", "NEXT_RSC_ERR_SERVER_IMPORT", "NEXT_RSC_ERR_CLIENT_IMPORT", "NEXT_RSC_ERR_CLIENT_METADATA_EXPORT", "NEXT_RSC_ERR_CONFLICT_METADATA_EXPORT", "NEXT_RSC_ERR_CLIENT_DIRECTIVE", "NEXT_RSC_ERR_CLIENT_DIRECTIVE_PAREN", "NEXT_RSC_ERR_INVALID_API", "NEXT_RSC_ERR_ERROR_FILE_SERVER_COMPONENT", "test", "matches", "match", "replace", "shouldAddUseClient", "err", "module", "compilation", "compiler", "moduleTrace", "getModuleTrace", "formattedError", "formattedModuleTrace", "lastInternalFileName", "invalidImportMessage", "formatModuleTrace", "error", "SimpleWebpackError", "stack"], "mappings": ";;;;+BA2HgBA;;;eAAAA;;;gCAzHkC;oCACf;AAEnC,SAASC,sBACPC,OAAe,EACfC,UAAmB,EACnBC,QAAgB;IAEhB,IAAIC,mBAAmBH;IACvB,IAAII,0BAA0B;IAE9B,oEAAoE;IACpE,2BAA2B;IAC3B,MAAMC,yBAAyB;IAC/B,MAAMC,6BAA6B;IACnC,MAAMC,6BAA6B;IACnC,MAAMC,sCACJ;IACF,MAAMC,wCACJ;IACF,MAAMC,gCAAgC;IACtC,MAAMC,sCACJ;IACF,MAAMC,2BAA2B;IACjC,MAAMC,2CACJ;IAEF,IAAIR,uBAAuBS,IAAI,CAACd,UAAU;QACxC,MAAMe,UAAUf,QAAQgB,KAAK,CAACX;QAC9B,IAAIU,WAAWA,OAAO,CAAC,EAAE,KAAK,aAAa;YACzCZ,mBAAmB,CAAC,6QAA6Q,CAAC;QACpS,OAAO;YACLA,mBAAmBH,QAAQiB,OAAO,CAChCZ,wBACA,CAAC,gQAAgQ,CAAC;QAEtQ;QACAD,0BACE;IACJ,OAAO,IAAIE,2BAA2BQ,IAAI,CAACd,UAAU;QACnD,IAAIkB,qBAAqB;QACzB,MAAMH,UAAUf,QAAQgB,KAAK,CAACV;QAC9B,OAAQS,WAAWA,OAAO,CAAC,EAAE;YAC3B,KAAK;gBACH,qEAAqE;gBACrEZ,mBAAmB,CAAC,kPAAkP,CAAC;gBACvQ;YACF,KAAK;gBACH,4EAA4E;gBAC5EA,mBAAmB,CAAC,kKAAkK,CAAC;gBACvLe,qBAAqB;gBACrB;YACF;gBACEf,mBAAmBH,QAAQiB,OAAO,CAChCX,4BACA,CAAC,qQAAqQ,CAAC;QAE7Q;QACAF,0BAA0Bc,qBACtB,8EACA;IACN,OAAO,IAAIX,2BAA2BO,IAAI,CAACd,UAAU;QACnD,IAAIC,YAAY;YACdE,mBAAmBH,QAAQiB,OAAO,CAChCV,4BACA,CAAC,sOAAsO,CAAC;YAE1OH,0BAA0B;QAC5B,OAAO;YACLD,mBAAmBH,QAAQiB,OAAO,CAChCV,4BACA,CAAC,sPAAsP,CAAC;YAE1PH,0BACE;QACJ;IACF,OAAO,IAAIM,8BAA8BI,IAAI,CAACd,UAAU;QACtDG,mBAAmBH,QAAQiB,OAAO,CAChCP,+BACA,CAAC,iIAAiI,CAAC;QAErIN,0BAA0B;IAC5B,OAAO,IAAIO,oCAAoCG,IAAI,CAACd,UAAU;QAC5DG,mBAAmBH,QAAQiB,OAAO,CAChCN,qCACA,CAAC,4OAA4O,CAAC;QAEhPP,0BAA0B;IAC5B,OAAO,IAAIQ,yBAAyBE,IAAI,CAACd,UAAU;QACjDG,mBAAmBH,QAAQiB,OAAO,CAChCL,0BACA,CAAC,qHAAqH,CAAC;QAEzHR,0BAA0B;IAC5B,OAAO,IAAIS,yCAAyCC,IAAI,CAACd,UAAU;QACjEG,mBAAmBH,QAAQiB,OAAO,CAChCJ,0CACA,CAAC,IAAI,EAAEX,SAAS,uLAAuL,CAAC;QAE1ME,0BAA0B;IAC5B,OAAO,IAAII,oCAAoCM,IAAI,CAACd,UAAU;QAC5DG,mBAAmBH,QAAQiB,OAAO,CAChCT,qCACA,CAAC,6OAA6O,CAAC;QAGjPJ,0BAA0B;IAC5B,OAAO,IAAIK,sCAAsCK,IAAI,CAACd,UAAU;QAC9DG,mBAAmBH,QAAQiB,OAAO,CAChCR,uCACA,CAAC,sLAAsL,CAAC;QAG1LL,0BAA0B;IAC5B;IAEA,OAAO;QAACD;QAAkBC;KAAwB;AACpD;AAIO,SAASN,YACdI,QAAgB,EAChBiB,GAAU,EACVC,MAAW,EACXC,WAAgC,EAChCC,QAA0B;IAE1B,IAAI,CAACH,IAAInB,OAAO,IAAI,CAAC,gBAAgBc,IAAI,CAACK,IAAInB,OAAO,GAAG;QACtD,OAAO;IACT;IAEA,MAAM,EAAEC,UAAU,EAAEsB,WAAW,EAAE,GAAGC,IAAAA,8BAAc,EAChDJ,QACAC,aACAC;IAGF,MAAMG,iBAAiB1B,sBACrBoB,IAAInB,OAAO,EACXC,YACAC;IAGF,MAAM,EAAEwB,oBAAoB,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAE,GACxEC,IAAAA,iCAAiB,EAACP,UAAUC;IAE9B,MAAMO,QAAQ,IAAIC,sCAAkB,CAClCJ,sBACA,kCACEF,cAAc,CAAC,EAAE,GACjBG,uBACAH,cAAc,CAAC,EAAE,GACjBC;IAGJ,8CAA8C;IAC9CI,MAAME,KAAK,GAAG;IAEd,OAAOF;AACT"}