(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[559],{211:(e,t,r)=>{Promise.resolve().then(r.bind(r,4477)),Promise.resolve().then(r.bind(r,1374)),Promise.resolve().then(r.bind(r,9283)),Promise.resolve().then(r.bind(r,3393))},4283:(e,t,r)=>{Promise.resolve().then(r.bind(r,8294)),Promise.resolve().then(r.bind(r,3453)),Promise.resolve().then(r.bind(r,8095)),Promise.resolve().then(r.bind(r,1021))},6024:(e,t,r)=>{Promise.resolve().then(r.bind(r,6506)),Promise.resolve().then(r.bind(r,8336)),Promise.resolve().then(r.bind(r,7734)),Promise.resolve().then(r.bind(r,3094)),Promise.resolve().then(r.bind(r,4990)),Promise.resolve().then(r.bind(r,5989)),Promise.resolve().then(r.bind(r,5111)),Promise.resolve().then(r.bind(r,4134)),Promise.resolve().then(r.bind(r,6076)),Promise.resolve().then(r.bind(r,7751)),Promise.resolve().then(r.bind(r,2546)),Promise.resolve().then(r.bind(r,8908))},9576:(e,t,r)=>{Promise.resolve().then(r.bind(r,6365)),Promise.resolve().then(r.bind(r,4428)),Promise.resolve().then(r.bind(r,4082)),Promise.resolve().then(r.bind(r,6210)),Promise.resolve().then(r.bind(r,4677)),Promise.resolve().then(r.bind(r,7229)),Promise.resolve().then(r.bind(r,9171)),Promise.resolve().then(r.bind(r,7482)),Promise.resolve().then(r.bind(r,5264)),Promise.resolve().then(r.bind(r,1963)),Promise.resolve().then(r.bind(r,454)),Promise.resolve().then(r.bind(r,8120))},8294:(e,t,r)=>{"use strict";r.d(t,{FloatMenu:()=>i});var s=r(9796),a=r(4040),o=r(1511),n=r(4270);function i(){return(0,s.jsx)("div",{className:"fixed bottom-6 right-6",children:(0,s.jsx)(n.Bc,{children:(0,s.jsxs)(n.m_,{children:[(0,s.jsx)(n.k$,{asChild:!0,children:(0,s.jsxs)(o.$,{variant:"outline",size:"icon",className:"bg-white dark:bg-background rounded-full shadow-lg group relative border-primary/20",onClick:()=>window.open("https://github.com/beilunyang/moemail","_blank"),children:[(0,s.jsx)(a.A,{className:"w-4 h-4 transition-all duration-300 text-primary group-hover:scale-110"}),(0,s.jsx)("span",{className:"sr-only",children:"获取网站源代码"})]})}),(0,s.jsx)(n.ZI,{children:(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)("p",{children:"获取网站源代码"})})})]})})})}},3453:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});var s=r(9796),a=r(9845);function o({children:e,...t}){return(0,s.jsx)(a.N,{...t,children:e})}},1511:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>d});var s=r(9796),a=r(2992),o=r(6850),n=r(1943),i=r(2304);let d=(0,n.F)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...n},l)=>{let u=a?o.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(d({variant:t,size:r,className:e})),ref:l,...n})});l.displayName="Button"},8095:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>b});var s=r(9796),a=r(2992),o=r(5071),n=r(1943),i=r(3751),d=r(2304);let l=o.Kq,u=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.LM,{ref:r,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));u.displayName=o.LM.displayName;let m=(0,n.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),c=a.forwardRef(({className:e,variant:t,...r},a)=>(0,s.jsx)(o.bL,{ref:a,className:(0,d.cn)(m({variant:t}),e),...r}));c.displayName=o.bL.displayName;let p=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.bm,{ref:r,className:(0,d.cn)("absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}));p.displayName=o.bm.displayName;let f=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.hE,{ref:r,className:(0,d.cn)("text-sm font-semibold [&+div]:text-xs",e),...t}));f.displayName=o.hE.displayName;let h=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.VY,{ref:r,className:(0,d.cn)("text-sm opacity-90",e),...t}));h.displayName=o.VY.displayName;var v=r(120);function b(){let{toasts:e}=(0,v.dj)();return(0,s.jsxs)(l,{children:[e.map(function({id:e,title:t,description:r,action:a,...o}){return(0,s.jsxs)(c,{...o,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&(0,s.jsx)(f,{children:t}),r&&(0,s.jsx)(h,{children:r})]}),a,(0,s.jsx)(p,{})]},e)}),(0,s.jsx)(u,{})]})}},4270:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>i,ZI:()=>u,k$:()=>l,m_:()=>d});var s=r(9796),a=r(2992),o=r(4831),n=r(2304);let i=o.Kq,d=o.bL,l=o.l9,u=a.forwardRef(({className:e,sideOffset:t=4,...r},a)=>(0,s.jsx)(o.UC,{ref:a,sideOffset:t,className:(0,n.cn)("z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));u.displayName=o.UC.displayName},120:(e,t,r)=>{"use strict";r.d(t,{dj:()=>c});var s=r(2992);let a=0,o=new Map,n=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function u(e){l=i(l,e),d.forEach(e=>{e(l)})}function m({...e}){let t=(a=(a+1)%Number.MAX_VALUE).toString(),r=()=>u({type:"DISMISS_TOAST",toastId:t});return u({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function c(){let[e,t]=s.useState(l);return s.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},2304:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(6073),a=r(6980);function o(...e){return(0,a.QP)((0,s.$)(e))}},1021:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>o});var s=r(9796),a=r(4123);function o({children:e}){return(0,s.jsx)(a.CP,{children:e})}},4477:(e,t,r)=>{"use strict";r.d(t,{FloatMenu:()=>s});let s=(0,r(6853).YR)(function(){throw Error("Attempted to call FloatMenu() from the server but FloatMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\float-menu.tsx","FloatMenu")},1374:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(6853).YR)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\theme\\theme-provider.tsx","ThemeProvider")},9283:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>s});let s=(0,r(6853).YR)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\toaster.tsx","Toaster")},189:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>m,viewport:()=>c});var s=r(861),a=r(1374),o=r(9283),n=r(2596),i=r(2147),d=r.n(i);r(6547);var l=r(3393),u=r(4477);let m={title:"MoeMail - 萌萌哒临时邮箱服务",description:"安全、快速、一次性的临时邮箱地址，保护您的隐私，远离垃圾邮件。支持即时收件，到期自动失效。",keywords:"临时邮箱, 一次性邮箱, 匿名邮箱, 隐私保护, 垃圾邮件过滤, 即时收件, 自动过期, 安全邮箱, 注册验证, 临时账号, 萌系邮箱, 电子邮件, 隐私安全, 邮件服务, MoeMail",authors:[{name:"SoftMoe Studio"}],creator:"SoftMoe Studio",publisher:"SoftMoe Studio",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0}},openGraph:{type:"website",locale:"zh_CN",url:"https://moemail.app",title:"MoeMail - 萌萌哒临时邮箱服务",description:"安全、快速、一次性的临时邮箱地址，保护您的隐私，远离垃圾邮件。支持即时收件，到期自动失效。",siteName:"MoeMail"},twitter:{card:"summary_large_image",title:"MoeMail - 萌萌哒临时邮箱服务",description:"安全、快速、一次性的临时邮箱地址，保护您的隐私，远离垃圾邮件。支持即时收件，到期自动失效。"},manifest:"/manifest.json",icons:[{rel:"apple-touch-icon",url:"/icons/icon-192x192.png"}]},c={themeColor:"#826DD9",width:"device-width",initialScale:1,maximumScale:1,userScalable:!1};function p({children:e}){return(0,s.jsxs)("html",{lang:"zh",suppressHydrationWarning:!0,children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("meta",{name:"application-name",content:"MoeMail"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-title",content:"MoeMail"}),(0,s.jsx)("meta",{name:"format-detection",content:"telephone=no"}),(0,s.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"})]}),(0,s.jsx)("body",{className:(0,n.cn)(d().variable,"font-zpix min-h-screen antialiased","bg-background text-foreground","transition-colors duration-300"),children:(0,s.jsxs)(a.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!1,storageKey:"temp-mail-theme",children:[(0,s.jsx)(l.Providers,{children:e}),(0,s.jsx)(o.Toaster,{}),(0,s.jsx)(u.FloatMenu,{})]})})]})}},1514:(e,t,r)=>{"use strict";r.d(t,{F:()=>o}),r(615),r(9230),r(658);var s=r(1639),a=r(144);let o=async()=>{let e=(await (0,a.b3)()).get("X-User-Id");if(e)return e;let t=await (0,s.j2)();return t?.user.id}},1639:(e,t,r)=>{"use strict";r.d(t,{fG:()=>_,LO:()=>I,iz:()=>y,j2:()=>A,Yj:()=>w,kz:()=>P});var s=r(3757),a=r(7031),o=r(1049),n=r(615),i=r(9230),d=r(9066),l=r(888),u=r(789),m=r(236),c=r(2596),p=r(2342),f=r(5356).Buffer;let h=["#2196F3","#009688","#9C27B0","#F44336","#673AB7","#3F51B5","#4CAF50","#FF5722","#795548","#607D8B"];var v=r(1514);let b={[u.gg.EMPEROR]:"皇帝（网站所有者）",[u.gg.DUKE]:"公爵（超级用户）",[u.gg.KNIGHT]:"骑士（高级用户）",[u.gg.CIVILIAN]:"平民（普通用户）"},x=async()=>{let e=await (0,l.getRequestContext)().env.SITE_CONFIG.get("DEFAULT_ROLE");return e===u.gg.DUKE||e===u.gg.KNIGHT||e===u.gg.CIVILIAN?e:u.gg.CIVILIAN};async function g(e,t){let r=await e.query.roles.findFirst({where:(0,d.eq)(i.roles.name,t)});if(!r){let[s]=await e.insert(i.roles).values({name:t,description:b[t]}).returning();r=s}return r}async function y(e,t,r){await e.delete(i.userRoles).where((0,d.eq)(i.userRoles.userId,t)),await e.insert(i.userRoles).values({userId:t,roleId:r})}async function w(e){let t=await (0,v.F)();if(!t)return!1;let r=(0,n.d)(),s=(await r.query.userRoles.findMany({where:(0,d.eq)(i.userRoles.userId,t),with:{role:!0}})).map(e=>e.role.name);return(0,u._m)(s,e)}let{handlers:{GET:_,POST:I},auth:A,signIn:N,signOut:E}=(0,s.Ay)(()=>({secret:process.env.AUTH_SECRET,adapter:(0,o._)((0,n.d)(),{usersTable:i.users,accountsTable:i.accounts}),providers:[(0,a.A)({clientId:process.env.AUTH_GITHUB_ID,clientSecret:process.env.AUTH_GITHUB_SECRET}),(0,m.A)({name:"Credentials",credentials:{username:{label:"用户名",type:"text",placeholder:"请输入用户名"},password:{label:"密码",type:"password",placeholder:"请输入密码"}},async authorize(e){if(!e)throw Error("请输入用户名和密码");let{username:t,password:r}=e;try{p.Q.parse({username:t,password:r})}catch(e){throw Error("输入格式不正确")}let s=(0,n.d)(),a=await s.query.users.findFirst({where:(0,d.eq)(i.users.username,t)});if(!a||!await (0,c.b)(r,a.password))throw Error("用户名或密码错误");return{...a,password:void 0}}})],events:{async signIn({user:e}){if(e.id)try{let t=(0,n.d)();if(await t.query.userRoles.findFirst({where:(0,d.eq)(i.userRoles.userId,e.id)}))return;let r=await x(),s=await g(t,r);await y(t,e.id,s.id)}catch(e){console.error("Error assigning role:",e)}}},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.name=t.name||t.username,e.username=t.username,e.image=t.image||function(e){let t=e[0].toUpperCase(),r=Array.from(e).reduce((e,t)=>e+t.charCodeAt(0),0)%h.length,s=h[r],a=`
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
      <rect width="100" height="100" fill="${s}"/>
      <text 
        x="50%" 
        y="50%" 
        fill="white" 
        font-family="system-ui, -apple-system, sans-serif" 
        font-size="45" 
        font-weight="500"
        text-anchor="middle"
        alignment-baseline="central"
        dominant-baseline="central"
        style="text-transform: uppercase"
      >
        ${t}
      </text>
    </svg>
  `.trim(),o=new TextEncoder().encode(a),n=f.from(o).toString("base64");return`data:image/svg+xml;base64,${n}`}(e.name)),e),async session({session:e,token:t}){if(t&&e.user){e.user.id=t.id,e.user.name=t.name,e.user.username=t.username,e.user.image=t.image;let r=(0,n.d)(),s=await r.query.userRoles.findMany({where:(0,d.eq)(i.userRoles.userId,e.user.id),with:{role:!0}});if(!s.length){let t=await x(),a=await g(r,t);await y(r,e.user.id,a.id),s=[{userId:e.user.id,roleId:a.id,createdAt:new Date,role:a}]}e.user.roles=s.map(e=>({name:e.role.name}))}return e}},session:{strategy:"jwt"}}));async function P(e,t){let r=(0,n.d)();if(await r.query.users.findFirst({where:(0,d.eq)(i.users.username,e)}))throw Error("用户名已存在");let s=await (0,c.E)(t),[a]=await r.insert(i.users).values({username:e,password:s}).returning();return a}},615:(e,t,r)=>{"use strict";r.d(t,{d:()=>n});var s=r(888),a=r(7384),o=r(9230);let n=()=>(0,a.f)((0,s.getRequestContext)().env.DB,{schema:o})},789:(e,t,r)=>{"use strict";r.d(t,{Jj:()=>a,_m:()=>n,gg:()=>s});let s={EMPEROR:"emperor",DUKE:"duke",KNIGHT:"knight",CIVILIAN:"civilian"},a={MANAGE_EMAIL:"manage_email",MANAGE_WEBHOOK:"manage_webhook",PROMOTE_USER:"promote_user",MANAGE_CONFIG:"manage_config",MANAGE_API_KEY:"manage_api_key"},o={[s.EMPEROR]:Object.values(a),[s.DUKE]:[a.MANAGE_EMAIL,a.MANAGE_WEBHOOK,a.MANAGE_API_KEY],[s.KNIGHT]:[a.MANAGE_EMAIL,a.MANAGE_WEBHOOK],[s.CIVILIAN]:[]};function n(e,t){return e.some(e=>o[e]?.includes(t))}},9230:(e,t,r)=>{"use strict";r.r(t),r.d(t,{accounts:()=>u,apiKeys:()=>v,apiKeysRelations:()=>x,emails:()=>m,messages:()=>c,revoked_credentials:()=>b,roles:()=>f,rolesRelations:()=>w,userRoles:()=>h,userRolesRelations:()=>g,users:()=>l,usersRelations:()=>y,webhooks:()=>p});var s=r(7243),a=r(1939),o=r(3268),n=r(3797),i=r(1870),d=r(652);let l=(0,s.D)("user",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,a.Qq)("name"),email:(0,a.Qq)("email").unique(),emailVerified:(0,o.nd)("emailVerified",{mode:"timestamp_ms"}),image:(0,a.Qq)("image"),username:(0,a.Qq)("username").unique(),password:(0,a.Qq)("password")}),u=(0,s.D)("account",{userId:(0,a.Qq)("userId").notNull().references(()=>l.id,{onDelete:"cascade"}),type:(0,a.Qq)("type").$type().notNull(),provider:(0,a.Qq)("provider").notNull(),providerAccountId:(0,a.Qq)("providerAccountId").notNull(),refresh_token:(0,a.Qq)("refresh_token"),access_token:(0,a.Qq)("access_token"),expires_at:(0,o.nd)("expires_at"),token_type:(0,a.Qq)("token_type"),scope:(0,a.Qq)("scope"),id_token:(0,a.Qq)("id_token"),session_state:(0,a.Qq)("session_state")},e=>({compoundKey:(0,n.ie)({columns:[e.provider,e.providerAccountId]})})),m=(0,s.D)("email",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),address:(0,a.Qq)("address").notNull().unique(),userId:(0,a.Qq)("userId").references(()=>l.id,{onDelete:"cascade"}),createdAt:(0,o.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),expiresAt:(0,o.nd)("expires_at",{mode:"timestamp_ms"}).notNull()},e=>({expiresAtIdx:(0,i.Pe)("email_expires_at_idx").on(e.expiresAt)})),c=(0,s.D)("message",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),emailId:(0,a.Qq)("emailId").notNull().references(()=>m.id,{onDelete:"cascade"}),fromAddress:(0,a.Qq)("from_address").notNull(),subject:(0,a.Qq)("subject").notNull(),content:(0,a.Qq)("content").notNull(),html:(0,a.Qq)("html"),receivedAt:(0,o.nd)("received_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)},e=>({emailIdIdx:(0,i.Pe)("message_email_id_idx").on(e.emailId)})),p=(0,s.D)("webhook",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,a.Qq)("user_id").notNull().references(()=>l.id,{onDelete:"cascade"}),url:(0,a.Qq)("url").notNull(),enabled:(0,o.nd)("enabled",{mode:"boolean"}).notNull().default(!0),createdAt:(0,o.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),updatedAt:(0,o.nd)("updated_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)}),f=(0,s.D)("role",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,a.Qq)("name").notNull(),description:(0,a.Qq)("description"),createdAt:(0,o.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),updatedAt:(0,o.nd)("updated_at",{mode:"timestamp"}).$defaultFn(()=>new Date)}),h=(0,s.D)("user_role",{userId:(0,a.Qq)("user_id").notNull().references(()=>l.id,{onDelete:"cascade"}),roleId:(0,a.Qq)("role_id").notNull().references(()=>f.id,{onDelete:"cascade"}),createdAt:(0,o.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date)},e=>({pk:(0,n.ie)({columns:[e.userId,e.roleId]})})),v=(0,s.D)("api_keys",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,a.Qq)("user_id").notNull().references(()=>l.id),name:(0,a.Qq)("name").notNull(),key:(0,a.Qq)("key").notNull().unique(),createdAt:(0,o.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),expiresAt:(0,o.nd)("expires_at",{mode:"timestamp"}),enabled:(0,o.nd)("enabled",{mode:"boolean"}).notNull().default(!0)},e=>({nameUserIdUnique:(0,i.GL)("name_user_id_unique").on(e.name,e.userId)})),b=(0,s.D)("revoked_credential",{jti:(0,a.Qq)("jti").primaryKey(),expiresAt:(0,o.nd)("expires_at",{mode:"timestamp_ms"}).notNull()},e=>({expiresAtIdx:(0,i.Pe)("revoked_expires_at_idx").on(e.expiresAt)})),x=(0,d.K1)(v,({one:e})=>({user:e(l,{fields:[v.userId],references:[l.id]})})),g=(0,d.K1)(h,({one:e})=>({user:e(l,{fields:[h.userId],references:[l.id]}),role:e(f,{fields:[h.roleId],references:[f.id]})})),y=(0,d.K1)(l,({many:e})=>({userRoles:e(h),apiKeys:e(v)})),w=(0,d.K1)(f,({many:e})=>({userRoles:e(h)}))},2596:(e,t,r)=>{"use strict";r.d(t,{E:()=>n,b:()=>i,cn:()=>o});var s=r(8649),a=r(5588);function o(...e){return(0,a.QP)((0,s.$)(e))}async function n(e){let t=new TextEncoder,r=process.env.AUTH_SECRET||"",s=t.encode(e+r);return btoa(String.fromCharCode(...new Uint8Array(await crypto.subtle.digest("SHA-256",s))))}async function i(e,t){return await n(e)===t}},2342:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a});var s=r(9267);let a=s.z.object({username:s.z.string().min(1,"用户名不能为空").max(20,"用户名不能超过20个字符").regex(/^[a-zA-Z0-9_-]+$/,"用户名只能包含字母、数字、下划线和横杠").refine(e=>!e.includes("@"),"用户名不能是邮箱格式"),password:s.z.string().min(8,"密码长度必须大于等于8位")})},3393:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});let s=(0,r(6853).YR)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\providers.tsx","Providers")},4452:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(9288);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.Ol)(".",await e.params,"favicon.ico")+""}]},6547:()=>{}}]);
//# sourceMappingURL=559.js.map