import { SignJWT } from 'jose';

const JWT_ADDRESS_SECRET = new TextEncoder().encode(process.env.JWT_ADDRESS_SECRET);

/**
 * Generates a JWT (Address Credential) for a given email address.
 *
 * @param emailAddress The email address to be the subject of the JWT.
 * @param expiresIn The lifetime of the token in seconds. If 0, it creates a "permanent" token (expires in 100 years).
 * @returns A signed JWT string.
 */
export async function generateAddressCredential(emailAddress: string, expiresIn: number): Promise<string> {
  if (!process.env.JWT_ADDRESS_SECRET) {
    throw new Error('JWT_ADDRESS_SECRET environment variable is not set.');
  }

  const iat = Math.floor(Date.now() / 1000);
  // If expiresIn is 0, set expiration to 100 years, otherwise use the provided value.
  const exp = expiresIn === 0 ? iat + 3153600000 : iat + expiresIn; // 3153600000 seconds = 100 years

  const token = await new SignJWT({ sub: emailAddress })
    .setProtectedHeader({ alg: 'HS256' })
    .setJti(crypto.randomUUID())
    .setIssuedAt(iat)
    .setExpirationTime(exp)
    .setIssuer('moemail:api')
    .setAudience('moemail:mailbox:access')
    .sign(JWT_ADDRESS_SECRET);

  return token;
} 