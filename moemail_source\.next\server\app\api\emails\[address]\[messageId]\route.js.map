{"version": 3, "file": "app/api/emails/[address]/[messageId]/route.js", "mappings": "qFAAA,6DCAA,mHGAA,2SFKO,IAAMA,EAAU,OAAM,eAEPC,EAClBC,CAAgB,CAChB,QAAEC,CAAM,CAA0D,EAEpE,IAAMC,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE9B,GAAI,CACF,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACb,IAAEC,CAAE,WAAEC,CAAS,CAAE,CAAG,MAAMN,EAQhC,GAAI,CAACO,MAPeJ,CAOR,CAPWK,KAAK,CAACC,MAAM,CAACC,SAAS,CAAC,CAC5CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACNC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACJ,EAAE,CAAEA,GACdQ,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACR,MAAM,CAAEA,GAExB,GAGE,OAAOa,EAAAA,EAAYA,CAACC,IAAI,CACpB,CAAEC,MAAO,0CAA2C,EACpD,CAAEC,OAAQ,GAAI,GAWpB,GAAG,CAPa,MAAMd,EAAGK,CAOZ,IAPiB,CAACU,QAAQ,CAACR,SAAS,CAAC,CAChDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACNC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACK,EAAAA,QAAQA,CAACC,OAAO,CAAEd,GACrBQ,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACK,EAAAA,QAAQA,CAACb,EAAE,CAAEC,GAEtB,GAGE,OAAOQ,EAAAA,EAAYA,CAACC,IAAI,CACpB,CAAEC,MAAO,sCAAuC,EAChD,CAAEC,OAAQ,GAAI,GAOpB,OAHA,MAAMd,EAAGiB,MAAM,CAACF,EAAAA,QAAQA,EACnBP,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACK,EAAAA,QAAQA,CAACb,EAAE,CAAEC,IAEpBQ,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEM,SAAS,CAAK,EAC3C,CAAE,MAAOL,EAAO,CAEd,OADAM,QAAQN,KAAK,CAAC,0BAA2BA,GAClCF,EAAAA,EAAYA,CAACC,IAAI,CACpB,CAAEC,MAAO,0BAA2B,EACpC,CAAEC,OAAQ,GAAI,EAEpB,CACF,CAEO,eAAeM,EAAIC,CAAiB,CAAE,QAAExB,CAAM,CAA0D,EAC7G,GAAI,CACF,GAAM,IAAEK,CAAE,WAAEC,CAAS,CAAE,CAAG,MAAMN,EAC1BG,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACbH,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAS9B,GAAI,CAPU,MAAMC,CAOR,CAPWK,KAAK,CAACC,MAAM,CAACC,SAAS,CAAC,CAC5CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACJ,EAAE,CAAEA,GACdQ,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACR,MAAM,CAAEA,GAEtB,GAGE,OAAOa,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,OAAQ,EACjB,CAAEC,OAAQ,GAAI,GAIlB,IAAMQ,EAAU,MAAMtB,EAAGK,KAAK,CAACU,QAAQ,CAACR,SAAS,CAAC,CAChDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACK,EAAAA,QAAQA,CAACb,EAAE,CAAEC,GAChBO,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACK,EAAAA,QAAQA,CAACC,OAAO,CAAEd,GAEzB,GAEA,GAAI,CAACoB,EACH,OADY,EACLX,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,mBAAoB,EAC7B,CAAEC,OAAQ,GAAI,GAIlB,OAAOH,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBU,QAAS,CACPpB,GAAIoB,EAAQpB,EAAE,CACdqB,aAAcD,EAAQE,WAAW,CACjCC,QAASH,EAAQG,OAAO,CACxBC,QAASJ,EAAQI,OAAO,CACxBC,KAAML,EAAQK,IAAI,CAClBC,YAAaN,EAAQO,UAAU,CAACC,OAAO,EACzC,CACF,EACF,CAAE,MAAOjB,EAAO,CAEd,OADAM,QAAQN,KAAK,CAAC,2BAA4BA,GACnCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,yBAA0B,EACnC,CAAEC,OAAQ,GAAI,EAElB,CACF,CCrGA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,+CACA,6CACA,iBACA,uDACA,CAAK,CACL,+GACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,wFACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,+CACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,uIAiBnxJ,CAAC,CAAC,EAAC,kHCwBI,IAAMf,EAAY,UAEvB,IAAMD,EADc,OAAMiC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,EAAAA,EACNC,GAAG,CAAC,aAE/B,GAAIlC,EAAQ,OAAOA,EAEnB,IAAMmC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAE1B,OAAOD,GAASE,KAAKjC,EACvB,EAAC,oOCxDD,IAAMkC,EAAS,CACb,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACD,eCGD,IAAMC,EAA0C,CAC9C,CAACC,EAAAA,EAAKA,CAACC,OAAO,CAAC,CAAE,YACjB,CAACD,EAAAA,EAAKA,CAACE,IAAI,CAAC,CAAE,WACd,CAACF,EAAAA,EAAKA,CAACG,MAAM,CAAC,CAAE,WAChB,CAACH,EAAAA,EAAKA,CAACI,QAAQ,CAAC,CAAE,UACpB,EAEMC,EAAiB,UACrB,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACC,WAAW,CAACf,GAAG,CAAC,uBAElE,IACkBM,EAAAA,EAAKA,CAACE,IAAI,EAC1BI,IAAgBN,EAAAA,EAAKA,CAACG,MAAM,EAC5BG,IAAgBN,EAAAA,EAAKA,CAACI,QAAQ,CAEvBE,CADP,CAIKN,EAAAA,EAAKA,CAACI,QAAQ,EAGvB,eAAeM,EAAiBhD,CAAM,CAAEiD,CAAc,EACpD,IAAIC,EAAO,MAAMlD,EAAGK,KAAK,CAAC8C,KAAK,CAAC5C,SAAS,CAAC,CACxCC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACyC,EAAAA,KAAKA,CAACC,IAAI,CAAEH,EACxB,GAEA,GAAI,CAACC,EAAM,CACT,GAAM,CAACG,EAAQ,CAAG,MAAMrD,EAAGsD,MAAM,CAACH,EAAAA,KAAKA,EACpCI,MAAM,CAAC,CACNH,KAAMH,EACNO,YAAanB,CAAiB,CAACY,EAAS,GAEzCQ,SAAS,GACZP,EAAOG,CACT,CAEA,OAAOH,CACT,CAEO,eAAeQ,EAAiB1D,CAAM,CAAEF,CAAc,CAAE6D,CAAc,EAC3E,MAAM3D,EAAGiB,MAAM,CAAC2C,EAAAA,SAASA,EACtBpD,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACkD,EAAAA,SAASA,CAAC9D,MAAM,CAAEA,IAE9B,MAAME,EAAGsD,MAAM,CAACM,EAAAA,SAASA,EACtBL,MAAM,CAAC,QACNzD,SACA6D,CACF,EACJ,CAEO,eAAeE,EAAY/D,CAAc,EAC9C,IAAME,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,MAAO6D,CAJiB,MAAM9D,EAAGK,KAAK,CAACuD,SAAS,CAACG,QAAQ,CAAC,CACxDvD,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACkD,EAAAA,SAASA,CAAC9D,MAAM,CAAEA,GAC5BkE,KAAM,CAAEd,MAAM,CAAK,CACrB,GACsB,CAAC,EAAE,CAACA,IAAI,CAACE,IAAI,CAG9B,eAAea,EAAgBC,CAAsB,EAC1D,IAAMpE,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE9B,GAAI,CAACD,EAAQ,MAAO,GAEpB,IAAME,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMbkE,EAAgBL,CALE,MAAM9D,EAAGK,KAAK,CAACuD,SAAS,CAACG,QAAQ,CAAC,CACxDvD,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACkD,EAAAA,SAASA,CAAC9D,MAAM,CAAEA,GAC5BkE,KAAM,CAAEd,MAAM,CAAK,CACrB,IAEsCkB,GAAG,CAACC,GAAMA,EAAGnB,IAAI,CAACE,IAAI,EAC5D,MAAOkB,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAACH,EAAyBD,EAChD,CAEO,GAAM,CACXK,SAAU,CAAEnD,KAAG,MAAEoD,CAAI,CAAE,MACvBtC,CAAI,QACJuC,CAAM,SACNC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,IAAO,EAClBC,OAAQC,QAAQ/B,GAAG,CAACgC,WAAW,CAC/BC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAAC/E,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAAI,CAClCgF,WAAYC,EAAAA,KAAKA,CACjBC,cAAeC,EAAAA,QAAQA,GAEzBC,UAAW,CACTC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC,CACLC,SAAUV,QAAQ/B,GAAG,CAAC0C,cAAc,CACpCC,aAAcZ,QAAQ/B,GAAG,CAAC4C,kBAAkB,GAE9CC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CAClBvC,KAAM,cACNwC,YAAa,CACXC,SAAU,CAAEC,MAAO,MAAOC,KAAM,OAAQC,YAAa,QAAS,EAC9DC,SAAU,CAAEH,MAAO,KAAMC,KAAM,WAAYC,YAAa,OAAQ,CAClE,EACA,MAAME,UAAUN,CAAW,EACzB,GAAI,CAACA,EACH,MAAUO,KADM,CACA,aAGlB,GAAM,UAAEN,CAAQ,UAAEI,CAAQ,CAAE,CAAGL,EAE/B,GAAI,CACFQ,EAAAA,CAAUA,CAACC,KAAK,CAAC,UAAER,WAAUI,CAAS,EAExC,CAAE,MAAOpF,EAAO,CACd,MAAM,MAAU,UAClB,CAEA,IAAMb,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEbkC,EAAO,MAAMnC,EAAGK,KAAK,CAAC6E,KAAK,CAAC3E,SAAS,CAAC,CAC1CC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACwE,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAEA,GAAI,CAAC1D,GAKD,CADY,EAJL,IAIWmE,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAACL,EAAoB9D,EAAK8D,QAAQ,EAHrE,MAAM,MAAU,YAQlB,MAAO,CACL,GAAG9D,CAAI,CACP8D,cAAUM,CACZ,CACF,CACF,GACD,CACDC,OAAQ,CACN,MAAM/B,OAAO,MAAEtC,CAAI,CAAE,EACnB,GAAKA,CAAD,CAAMjC,EAAE,CAEZ,CAFc,EAEV,CACF,IAAMF,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,GAJqB,CAIjBwG,KAJuBzG,EAAGK,KAAK,CAACuD,SAAS,CAACrD,SAAS,CAAC,CACtDC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACkD,EAAAA,SAASA,CAAC9D,MAAM,CAAEqC,EAAKjC,EAAE,CACrC,GAEkB,OAElB,IAAM0C,EAAc,MAAMD,IACpBO,EAAO,MAAMF,EAAiBhD,EAAI4C,EACxC,OAAMc,EAAiB1D,EAAImC,EAAKjC,EAAE,CAAEgD,EAAKhD,EAAE,CAC7C,CAAE,MAAOW,EAAO,CACdM,QAAQN,KAAK,CAAC,wBAAyBA,EACzC,CACF,CACF,EACA6F,UAAW,CACHC,IAAN,MAAU,OAAEC,CAAK,CAAEzE,MAAI,CAAE,IACnBA,IACFyE,EAAM1G,EAAE,CAAGiC,EAAKjC,EAAE,CAClB0G,EAAMxD,IAAI,CAAGjB,EAAKiB,IAAI,EAAIjB,EAAK0D,QAAQ,CACvCe,EAAMf,QAAQ,CAAG1D,EAAK0D,QAAQ,CAC9Be,EAAMC,KAAK,CAAG1E,EAAK0E,KAAK,ED/JzB,SAASC,CAA8B,EAC5C,IAAMC,CC8J6CD,CD9JnC1D,CAAI,CAAC,EAAE,CAAC4D,WAAW,GAE7BC,EAAaC,MAAMC,IAAI,CAAC/D,GAAMgE,MAAM,CACxC,CAACC,EAAKC,IAASD,EAAMC,EAAKC,UAAU,CAAC,GAAI,GACvCnF,EAAOoF,MAAM,CAEXC,EAAkBrF,CAAM,CAAC6E,EAAW,CAEpCS,EAAM,CAAC;;2CAE4B,EAAED,EAAgB;;;;;;;;;;;;;QAarD,EAAEV,QAAQ;;;EAGhB,CAAC,CAACY,IAAI,GAGAC,EADU,IAAIC,cACEC,MAAM,CAACJ,GACvBK,EAASC,EAAOb,IAADa,CAAMJ,GAAOK,QAAQ,CAAC,UAE3C,MAAO,CAAC,0BAA0B,EAAEF,EAAAA,CAAQ,EC8HQnB,EAAMxD,IAAI,GAEnDwD,GAET,MAAM3E,QAAQ,SAAEA,CAAO,OAAE2E,CAAK,CAAE,EAC9B,GAAIA,GAAS3E,EAAQE,IAAI,CAAE,CACzBF,EAAQE,IAAI,CAACjC,EAAE,CAAG0G,EAAM1G,EAAE,CAC1B+B,EAAQE,IAAI,CAACiB,IAAI,CAAGwD,EAAMxD,IAAI,CAC9BnB,EAAQE,IAAI,CAAC0D,QAAQ,CAAGe,EAAMf,QAAQ,CACtC5D,EAAQE,IAAI,CAAC0E,KAAK,CAAGD,EAAMC,KAAK,CAEhC,IAAM7G,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACf6D,EAAkB,MAAM9D,EAAGK,KAAK,CAACuD,SAAS,CAACG,QAAQ,CAAC,CACtDvD,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACkD,EAAAA,SAASA,CAAC9D,MAAM,CAAEmC,EAAQE,IAAI,CAACjC,EAAE,EAC3C8D,KAAM,CAAEd,MAAM,CAAK,CACrB,GAEA,GAAI,CAACY,EAAgB0D,MAAM,CAAE,CAC3B,IAAM5E,EAAc,MAAMD,IACpBO,EAAO,MAAMF,EAAiBhD,EAAI4C,EACxC,OAAMc,EAAiB1D,EAAIiC,EAAQE,IAAI,CAACjC,EAAE,CAAEgD,EAAKhD,EAAE,EACnD4D,EAAkB,CAAC,CACjBhE,OAAQmC,EAAQE,IAAI,CAACjC,EAAE,CACvByD,OAAQT,EAAKhD,EAAE,CACfgI,UAAW,IAAIC,KACfjF,KAAMA,CACR,EAAE,CAGJjB,EAAQE,IAAI,CAACgB,KAAK,CAAGW,EAAgBM,GAAG,CAACC,GAAO,OACxCA,EAAGnB,IAAI,CAACE,IAAI,CACpB,EACF,CAEA,OAAOnB,CACT,CACF,EACAA,QAAS,CACPmG,SAAU,KACZ,CACF,IAAG,eAEmBC,EAASxC,CAAgB,CAAEI,CAAgB,EAC/D,IAAMjG,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,GAJiB,CAIbqI,KAJmBtI,EAAGK,GAIZ,EAJiB,CAAC6E,KAAK,CAAC3E,SAAS,CAAC,CAC9CC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACwE,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAGE,MAAUM,MAAM,UAGlB,IAAMoC,EAAiB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACvC,GAEpC,CAAC9D,EAAK,CAAG,MAAMnC,EAAGsD,MAAM,CAAC4B,EAAAA,KAAKA,EACjC3B,MAAM,CAAC,UACNsC,EACAI,SAAUsC,CACZ,GACC9E,SAAS,GAEZ,OAAOtB,CACT,gFCvOO,IAAMlC,EAAW,IAAMwI,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAAC5F,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAAC4F,EAAE,CAAE,CAAEC,MAAMA,CAAAA,CAAC,GAAE,gCAAHA,gCCJnE,IAAMrG,EAAQ,CACnBC,QAAS,UACTC,KAAM,OACNC,OAAQ,SACRC,SAAU,UACZ,EAAW,EAIgB,CACzBkG,aAAc,eACdC,eAAgB,iBAChBC,aAAc,eACdC,cAAe,gBACfC,eAAgB,gBAClB,EAIaC,EAA+C,CAC1D,CAAC3G,EAAMC,OAAO,CAAC,CAAE2G,OAAO3F,MAAM,CAAC4F,GAC/B,CAAC7G,EAAME,IAAI,CAAC,CAAE,CACZ2G,EAAYP,YAAY,CACxBO,EAAYN,cAAc,CAC1BM,EAAYH,cAAc,CAC3B,CACD,CAAC1G,EAAMG,MAAM,CAAC,CAAE,CACd0G,EAAYP,YAAY,CACxBO,EAAYN,cAAc,CAC3B,CACD,CAACvG,EAAMI,QAAQ,CAAC,CAAE,EAAE,EACX,SAEK4B,EAAcV,CAAiB,CAAEM,CAAsB,EACrE,OAAON,EAAUwF,IAAI,CAAClG,GAAQ+F,CAAgB,CAAC/F,EAAK,EAAEmG,SAASnF,GACjE,kVC9BO,IAAMgB,EAAQoE,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvCpJ,GAAIqJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MACNC,UAAU,GACVC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IACrCvG,KAAMmG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXnJ,MAAOmJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASK,MAAM,GAC3BC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,gBAAiB,CAAEC,KAAM,cAAe,GAC/DlD,MAAO0C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZ1D,SAAU0D,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYK,MAAM,GACjC3D,SAAUsD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACjB,GAAE,EACsBD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CACjC,UACA,CACExJ,OAAQyJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UACVS,OAAO,GACPC,UAAU,CAAC,IAAM/E,EAAMhF,EAAE,CAAE,CAAEgK,SAAU,SAAU,GACpDnE,KAAMwD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQY,KAAK,GAAuBH,OAAO,GACtDI,SAAUb,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYS,OAAO,GAClCK,kBAAmBd,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,qBAAqBS,OAAO,GACpDM,cAAef,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBACpBgB,aAAchB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACnBiB,WAAYV,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cACpBW,WAAYlB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,cACjBmB,MAAOnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZoB,SAAUpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YACfqB,cAAerB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACtB,EACA,GAAc,EACZsB,KADY,OACCrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CACtBsB,QAAS,CAACC,EAAQX,QAAQ,CAAEW,EAAQV,iBAAiB,CAAC,GAE1D,GAGW/J,CAFZ,CAEqBgJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,QAAS,CACzCpJ,GAAIqJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DqB,QAASzB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAAGJ,MAAM,GACzC9J,OAAQyJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UAAUU,UAAU,CAAC,IAAM/E,EAAMhF,EAAE,CAAE,CAAEgK,SAAU,SAAU,GACxEhC,UAAW4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPP,UAAU,CAAC,IAAM,IAAItB,MACxB8C,UAAWnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbkB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAMJ,SAAS,CAChE,IAAG,EAEqB3B,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7CpJ,GAAIqJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D3I,QAASuI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACXS,OAAO,GACPC,UAAU,CAAC,IAAM3J,EAAOJ,EAAE,CAAE,CAAEgK,SAAU,SAAU,GACrD1I,YAAa+H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBAAgBS,OAAO,GACzCvI,QAAS8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAChCtI,QAAS6H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAChCrI,KAAM4H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACX1H,WAAYiI,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cAAe,CAAEC,KAAM,cAAe,GACvDC,OAAO,GACPP,UAAU,CAAC,IAAM,IAAItB,KAC1B,EAAG,GAAY,EACbmD,GADa,QACDH,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAMrK,OAAO,EAC5D,GAEauK,CAFV,CAEqBjC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7CpJ,GAAIqJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D7J,OAAQyJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACVS,OAAO,GACPC,UAAU,CAAC,IAAM/E,EAAMhF,EAAE,CAAE,CAAEgK,SAAU,SAAU,GACpDsB,IAAKjC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOS,OAAO,GACxByB,QAAS3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAG0B,OAAO,EAAC,GACnExD,UAAW4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPP,UAAU,CAAC,IAAM,IAAItB,MACxBwD,UAAW7B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPP,UAAU,CAAC,IAAM,IAAItB,KAC1B,GAAE,EAEmBmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvCpJ,GAAIqJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DvG,KAAMmG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQS,OAAO,GAC1BxG,YAAa+F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,eAClBrB,UAAW4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAItB,MAC7EwD,UAAW7B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAItB,KAC/E,GAAG,EAEsBmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,YAAa,CAChDxJ,OAAQyJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAAGC,UAAU,CAAC,IAAM/E,EAAMhF,EAAE,CAAE,CAAEgK,SAAU,SAAU,GACnFvG,OAAQ4F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAAGC,UAAU,CAAC,IAAM9G,EAAMjD,EAAE,CAAE,CAAEgK,SAAU,SAAU,GACnFhC,UAAW4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAItB,KAC/E,EAAG,GAAY,EACbyD,GADa,CACTpC,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CAAEsB,QAAS,CAACO,EAAMvL,MAAM,CAAEuL,EAAM1H,MAAM,CAAC,GACxD,GAEakI,CAFT,CAEmBvC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,WAAY,CAC7CpJ,GAAIqJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D7J,OAAQyJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAAGC,UAAU,CAAC,IAAM/E,EAAMhF,EAAE,EAC3DkD,KAAMmG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQS,OAAO,GAC1B8B,IAAKvC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOS,OAAO,GAAGJ,MAAM,GACjC1B,UAAW4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAItB,MAC7E8C,UAAWnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrD0B,QAAS3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAG0B,OAAO,CAAC,GACrE,EAAG,GAAY,EACbK,GADa,cACKC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,uBAAuBZ,EAAE,CAACC,EAAMjI,IAAI,CAAEiI,EAAMvL,MAAM,CAClF,IAAI,EAE+BwJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,qBAAsB,CACnE2C,IAAK1C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOC,UAAU,GAC3ByB,UAAWnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbkB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,0BAA0BC,EAAE,CAACC,EAAMJ,SAAS,EAClE,GAEaiB,CAFT,CAE4BC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACN,EAAS,CAAC,KAAEO,CAAG,CAAE,GAAM,EAC/DjK,KAAMiK,EAAIlH,EAAO,CACfmH,OAAQ,CAACR,EAAQ/L,MAAM,CAAC,CACxBmK,WAAY,CAAC/E,EAAMhF,EAAE,CAAC,GAE1B,GAEaoM,CAFT,CAE8BH,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACvI,EAAW,CAAC,KAAEwI,CAAG,CAAE,GAAM,EACnEjK,KAAMiK,EAAIlH,EAAO,CACfmH,OAAQ,CAACzI,EAAU9D,MAAM,CAAC,CAC1BmK,WAAY,CAAC/E,EAAMhF,EAAE,CAAC,GAExBgD,KAAMkJ,EAAIjJ,EAAO,CACfkJ,OAAQ,CAACzI,EAAUD,MAAM,CAAC,CAC1BsG,WAAY,CAAC9G,EAAMjD,EAAE,CAAC,GAE1B,GAEaqM,CAFT,CAE0BJ,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACjH,EAAO,CAAC,MAAEsH,CAAI,CAAE,GAAM,EAC5D5I,UAAW4I,EAAK5I,GAChBiI,QAASW,EAAKX,GAChB,GAEaY,CAFT,CAE0BN,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAChJ,EAAO,CAAC,MAAEqJ,CAAI,CAAE,GAAM,EAC5D5I,UAAW4I,EAAK5I,GAClB,IAAI,sFC5IG,SAAS8I,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,CAEO,eAAenE,EAAavC,CAAgB,EACjD,IAAM6G,EAAU,IAAIjF,YACdkF,EAAOlI,QAAQ/B,GAAG,CAACgC,WAAW,EAAI,GAClCkI,EAAOF,EAAQhF,MAAM,CAAC7B,EAAW8G,GAEvC,OAAOE,KAAKC,OAAOC,YAAY,IAAI,IAAIC,WAD1B,MAAM1D,OAAO2D,MAAM,CAACC,MAAM,CAAC,UAAWN,KAErD,CAEO,eAAe1G,EAAgBL,CAAgB,CAAEsC,CAAsB,EAE5E,OADa,MAAMC,EAAavC,KAChBsC,CAClB,8DChBO,IAAMnC,EAAamH,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACjC1H,SAAU0H,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,WACPC,GAAG,CAAC,GAAI,gBACRC,KAAK,CAAC,mBAAoB,uBAC1BC,MAAM,CAACC,GAAO,CAACA,EAAIvE,QAAQ,CAAC,KAAM,cACrCpD,SAAUsH,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,eACZ,GAAE", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/emails/[address]/[messageId]/route.ts", "webpack://_N_E/./app/api/emails/[address]/[messageId]/route.ts?fb6b", "webpack://_N_E/?cf2f", "webpack://_N_E/./app/lib/apiKey.ts", "webpack://_N_E/./app/lib/avatar.ts", "webpack://_N_E/./app/lib/auth.ts", "webpack://_N_E/./app/lib/db.ts", "webpack://_N_E/./app/lib/permissions.ts", "webpack://_N_E/./app/lib/schema.ts", "webpack://_N_E/./app/lib/utils.ts", "webpack://_N_E/./app/lib/validation.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { NextResponse } from \"next/server\"\r\nimport { createDb } from \"@/lib/db\"\r\nimport { messages, emails } from \"@/lib/schema\"\r\nimport { and, eq } from \"drizzle-orm\"\r\nimport { getUserId } from \"@/lib/apiKey\"\r\nexport const runtime = \"edge\"\r\n\r\nexport async function DELETE(\r\n    request: Request,\r\n    { params }: { params: Promise<{ id: string; messageId: string }> }\r\n) {\r\n  const userId = await getUserId()\r\n\r\n  try {\r\n    const db = createDb()\r\n    const { id, messageId } = await params\r\n    const email = await db.query.emails.findFirst({\r\n      where: and(\r\n          eq(emails.id, id),\r\n          eq(emails.userId, userId!)\r\n      )\r\n    })\r\n\r\n    if (!email) {\r\n      return NextResponse.json(\r\n          { error: \"Email not found or no permission to view\" },\r\n          { status: 403 }\r\n      )\r\n    }\r\n\r\n    const message = await db.query.messages.findFirst({\r\n      where: and(\r\n          eq(messages.emailId, id),\r\n          eq(messages.id, messageId)\r\n      )\r\n    })\r\n\r\n    if(!message) {\r\n      return NextResponse.json(\r\n          { error: \"Message not found or already deleted\" },\r\n          { status: 404 }\r\n      )\r\n    }\r\n\r\n    await db.delete(messages)\r\n        .where(eq(messages.id, messageId))\r\n\r\n    return NextResponse.json({ success: true })\r\n  } catch (error) {\r\n    console.error('Failed to delete email:', error)\r\n    return NextResponse.json(\r\n        { error: \"Failed to delete message\" },\r\n        { status: 500 }\r\n    )\r\n  }\r\n}\r\n\r\nexport async function GET(_request: Request, { params }: { params: Promise<{ id: string; messageId: string }> }) {\r\n  try {\r\n    const { id, messageId } = await params\r\n    const db = createDb()\r\n    const userId = await getUserId()\r\n\r\n    const email = await db.query.emails.findFirst({\r\n      where: and(\r\n        eq(emails.id, id),\r\n        eq(emails.userId, userId!)\r\n      )\r\n    })\r\n\r\n    if (!email) {\r\n      return NextResponse.json(\r\n        { error: \"无权限查看\" },\r\n        { status: 403 }\r\n      )\r\n    }\r\n\r\n    const message = await db.query.messages.findFirst({\r\n      where: and(\r\n        eq(messages.id, messageId),\r\n        eq(messages.emailId, id)\r\n      )\r\n    })\r\n    \r\n    if (!message) {\r\n      return NextResponse.json(\r\n        { error: \"Message not found\" },\r\n        { status: 404 }\r\n      )\r\n    }\r\n    \r\n    return NextResponse.json({ \r\n      message: {\r\n        id: message.id,\r\n        from_address: message.fromAddress,\r\n        subject: message.subject,\r\n        content: message.content,\r\n        html: message.html,\r\n        received_at: message.receivedAt.getTime()\r\n      }\r\n    })\r\n  } catch (error) {\r\n    console.error('Failed to fetch message:', error)\r\n    return NextResponse.json(\r\n      { error: \"Failed to fetch message\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\emails\\\\[address]\\\\[messageId]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/emails/[address]/[messageId]/route\",\n        pathname: \"/api/emails/[address]/[messageId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/emails/[address]/[messageId]/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\emails\\\\[address]\\\\[messageId]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Femails%2F%5Baddress%5D%2F%5BmessageId%5D%2Froute&page=%2Fapi%2Femails%2F%5Baddress%5D%2F%5BmessageId%5D%2Froute&pagePath=private-next-app-dir%2Fapi%2Femails%2F%5Baddress%5D%2F%5BmessageId%5D%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fapi%2Femails%2F%5Baddress%5D%2F%5BmessageId%5D%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/emails/[address]/[messageId]/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/emails/[address]/[messageId]/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/emails/[address]/[messageId]/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { createDb } from \"./db\"\r\nimport { api<PERSON><PERSON>s } from \"./schema\"\r\nimport { eq, and, gt } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport type { User } from \"next-auth\"\r\nimport { auth } from \"./auth\"\r\nimport { headers } from \"next/headers\"\r\n\r\nasync function getUserByApiKey(key: string): Promise<User | null> {\r\n  const db = createDb()\r\n  const apiKey = await db.query.apiKeys.findFirst({\r\n    where: and(\r\n      eq(apiKeys.key, key),\r\n      eq(apiKeys.enabled, true),\r\n      gt(apiKeys.expiresAt, new Date())\r\n    ),\r\n    with: {\r\n      user: true\r\n    }\r\n  })\r\n\r\n  if (!apiKey) return null\r\n\r\n  return apiKey.user\r\n}\r\n\r\nexport async function handleApiKeyAuth(apiKey: string, pathname: string) {\r\n  if (!pathname.startsWith('/api/emails') && !pathname.startsWith('/api/config')) {\r\n    return NextResponse.json(\r\n      { error: \"无权限查看\" },\r\n      { status: 403 }\r\n    )\r\n  }\r\n\r\n  const user = await getUserByApiKey(apiKey)\r\n  if (!user?.id) {\r\n    return NextResponse.json(\r\n      { error: \"无效的 API Key\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  const response = NextResponse.next()\r\n  response.headers.set(\"X-User-Id\", user.id)\r\n  return response\r\n}\r\n\r\nexport const getUserId = async () => {\r\n  const headersList = await headers()\r\n  const userId = headersList.get(\"X-User-Id\")\r\n  \r\n  if (userId) return userId\r\n\r\n  const session = await auth()\r\n\r\n  return session?.user.id\r\n}\r\n", "const COLORS = [\r\n  '#2196F3', // 蓝色\r\n  '#009688', // 青色\r\n  '#9C27B0', // 紫色\r\n  '#F44336', // 红色\r\n  '#673AB7', // 深紫色\r\n  '#3F51B5', // 靛蓝\r\n  '#4CAF50', // 绿色\r\n  '#FF5722', // 深橙\r\n  '#795548', // 棕色\r\n  '#607D8B', // 蓝灰\r\n];\r\n\r\nexport function generateAvatarUrl(name: string): string {\r\n  const initial = name[0].toUpperCase();\r\n  \r\n  const colorIndex = Array.from(name).reduce(\r\n    (acc, char) => acc + char.charCodeAt(0), 0\r\n  ) % COLORS.length;\r\n  \r\n  const backgroundColor = COLORS[colorIndex];\r\n  \r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\" width=\"100\" height=\"100\">\r\n      <rect width=\"100\" height=\"100\" fill=\"${backgroundColor}\"/>\r\n      <text \r\n        x=\"50%\" \r\n        y=\"50%\" \r\n        fill=\"white\" \r\n        font-family=\"system-ui, -apple-system, sans-serif\" \r\n        font-size=\"45\" \r\n        font-weight=\"500\"\r\n        text-anchor=\"middle\"\r\n        alignment-baseline=\"central\"\r\n        dominant-baseline=\"central\"\r\n        style=\"text-transform: uppercase\"\r\n      >\r\n        ${initial}\r\n      </text>\r\n    </svg>\r\n  `.trim();\r\n\r\n  const encoder = new TextEncoder();\r\n  const bytes = encoder.encode(svg);\r\n  const base64 = Buffer.from(bytes).toString('base64');\r\n  \r\n  return `data:image/svg+xml;base64,${base64}`;\r\n} ", "import NextAuth from \"next-auth\"\r\nimport GitHub from \"next-auth/providers/github\"\r\nimport { Dr<PERSON>zleAdapter } from \"@auth/drizzle-adapter\"\r\nimport { createDb, Db } from \"./db\"\r\nimport { accounts, users, roles, userRoles } from \"./schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { Permission, hasPermission, ROLES, Role } from \"./permissions\"\r\nimport CredentialsProvider from \"next-auth/providers/credentials\"\r\nimport { hashPassword, comparePassword } from \"@/lib/utils\"\r\nimport { authSchema } from \"@/lib/validation\"\r\nimport { generateAvatarUrl } from \"./avatar\"\r\nimport { getUserId } from \"./apiKey\"\r\n\r\nconst ROLE_DESCRIPTIONS: Record<Role, string> = {\r\n  [ROLES.EMPEROR]: \"皇帝（网站所有者）\",\r\n  [ROLES.DUKE]: \"公爵（超级用户）\",\r\n  [ROLES.KNIGHT]: \"骑士（高级用户）\",\r\n  [ROLES.CIVILIAN]: \"平民（普通用户）\",\r\n}\r\n\r\nconst getDefaultRole = async (): Promise<Role> => {\r\n  const defaultRole = await getRequestContext().env.SITE_CONFIG.get(\"DEFAULT_ROLE\")\r\n\r\n  if (\r\n    defaultRole === ROLES.DUKE ||\r\n    defaultRole === ROLES.KNIGHT ||\r\n    defaultRole === ROLES.CIVILIAN\r\n  ) {\r\n    return defaultRole as Role\r\n  }\r\n  \r\n  return ROLES.CIVILIAN\r\n}\r\n\r\nasync function findOrCreateRole(db: Db, roleName: Role) {\r\n  let role = await db.query.roles.findFirst({\r\n    where: eq(roles.name, roleName),\r\n  })\r\n\r\n  if (!role) {\r\n    const [newRole] = await db.insert(roles)\r\n      .values({\r\n        name: roleName,\r\n        description: ROLE_DESCRIPTIONS[roleName],\r\n      })\r\n      .returning()\r\n    role = newRole\r\n  }\r\n\r\n  return role\r\n}\r\n\r\nexport async function assignRoleToUser(db: Db, userId: string, roleId: string) {\r\n  await db.delete(userRoles)\r\n    .where(eq(userRoles.userId, userId))\r\n\r\n  await db.insert(userRoles)\r\n    .values({\r\n      userId,\r\n      roleId,\r\n    })\r\n}\r\n\r\nexport async function getUserRole(userId: string) {\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n  return userRoleRecords[0].role.name\r\n}\r\n\r\nexport async function checkPermission(permission: Permission) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) return false\r\n\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n\r\n  const userRoleNames = userRoleRecords.map(ur => ur.role.name)\r\n  return hasPermission(userRoleNames as Role[], permission)\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  auth,\r\n  signIn,\r\n  signOut\r\n} = NextAuth(() => ({\r\n  secret: process.env.AUTH_SECRET,\r\n  adapter: DrizzleAdapter(createDb(), {\r\n    usersTable: users,\r\n    accountsTable: accounts,\r\n  }),\r\n  providers: [\r\n    GitHub({\r\n      clientId: process.env.AUTH_GITHUB_ID,\r\n      clientSecret: process.env.AUTH_GITHUB_SECRET,\r\n    }),\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        username: { label: \"用户名\", type: \"text\", placeholder: \"请输入用户名\" },\r\n        password: { label: \"密码\", type: \"password\", placeholder: \"请输入密码\" },\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials) {\r\n          throw new Error(\"请输入用户名和密码\")\r\n        }\r\n\r\n        const { username, password } = credentials\r\n\r\n        try {\r\n          authSchema.parse({ username, password })\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        } catch (error) {\r\n          throw new Error(\"输入格式不正确\")\r\n        }\r\n\r\n        const db = createDb()\r\n\r\n        const user = await db.query.users.findFirst({\r\n          where: eq(users.username, username as string),\r\n        })\r\n\r\n        if (!user) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        const isValid = await comparePassword(password as string, user.password as string)\r\n        if (!isValid) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        return {\r\n          ...user,\r\n          password: undefined,\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  events: {\r\n    async signIn({ user }) {\r\n      if (!user.id) return\r\n\r\n      try {\r\n        const db = createDb()\r\n        const existingRole = await db.query.userRoles.findFirst({\r\n          where: eq(userRoles.userId, user.id),\r\n        })\r\n\r\n        if (existingRole) return\r\n\r\n        const defaultRole = await getDefaultRole()\r\n        const role = await findOrCreateRole(db, defaultRole)\r\n        await assignRoleToUser(db, user.id, role.id)\r\n      } catch (error) {\r\n        console.error('Error assigning role:', error)\r\n      }\r\n    },\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id\r\n        token.name = user.name || user.username\r\n        token.username = user.username\r\n        token.image = user.image || generateAvatarUrl(token.name as string)\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token && session.user) {\r\n        session.user.id = token.id as string\r\n        session.user.name = token.name as string\r\n        session.user.username = token.username as string\r\n        session.user.image = token.image as string\r\n\r\n        const db = createDb()\r\n        let userRoleRecords = await db.query.userRoles.findMany({\r\n          where: eq(userRoles.userId, session.user.id),\r\n          with: { role: true },\r\n        })\r\n  \r\n        if (!userRoleRecords.length) {\r\n          const defaultRole = await getDefaultRole()\r\n          const role = await findOrCreateRole(db, defaultRole)\r\n          await assignRoleToUser(db, session.user.id, role.id)\r\n          userRoleRecords = [{\r\n            userId: session.user.id,\r\n            roleId: role.id,\r\n            createdAt: new Date(),\r\n            role: role\r\n          }]\r\n        }\r\n  \r\n        session.user.roles = userRoleRecords.map(ur => ({\r\n          name: ur.role.name,\r\n        }))\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: \"jwt\",\r\n  },\r\n}))\r\n\r\nexport async function register(username: string, password: string) {\r\n  const db = createDb()\r\n  \r\n  const existing = await db.query.users.findFirst({\r\n    where: eq(users.username, username)\r\n  })\r\n\r\n  if (existing) {\r\n    throw new Error(\"用户名已存在\")\r\n  }\r\n\r\n  const hashedPassword = await hashPassword(password)\r\n  \r\n  const [user] = await db.insert(users)\r\n    .values({\r\n      username,\r\n      password: hashedPassword,\r\n    })\r\n    .returning()\r\n\r\n  return user\r\n}\r\n", "import { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { drizzle } from \"drizzle-orm/d1\"\r\nimport * as schema from \"./schema\"\r\n\r\nexport const createDb = () => drizzle(getRequestContext().env.DB, { schema })\r\n\r\nexport type Db = ReturnType<typeof createDb>\r\n", "export const ROLES = {\r\n  EMPEROR: 'emperor',\r\n  <PERSON>U<PERSON>: 'duke',\r\n  <PERSON><PERSON><PERSON><PERSON>: 'knight',\r\n  CIVILIAN: 'civilian',\r\n} as const;\r\n\r\nexport type Role = typeof ROLES[keyof typeof ROLES];\r\n\r\nexport const PERMISSIONS = {\r\n  MANAGE_EMAIL: 'manage_email',\r\n  <PERSON><PERSON>GE_WEBHOOK: 'manage_webhook',\r\n  PROMOTE_USER: 'promote_user',\r\n  MANAGE_CONFIG: 'manage_config',\r\n  MANAGE_API_KEY: 'manage_api_key',\r\n} as const;\r\n\r\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\r\n\r\nexport const ROLE_PERMISSIONS: Record<Role, Permission[]> = {\r\n  [ROLES.EMPEROR]: Object.values(PERMISSIONS),\r\n  [ROLES.DUKE]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_API_KEY,\r\n  ],\r\n  [ROLES.KNIGHT]: [\r\n    PERMISSIONS.<PERSON>NAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n  ],\r\n  [ROLES.CIVILIAN]: [],\r\n} as const;\r\n\r\nexport function hasPermission(userRoles: Role[], permission: Permission): boolean {\r\n  return userRoles.some(role => ROLE_PERMISSIONS[role]?.includes(permission));\r\n} ", "import { integer, sqliteTable, text, primaryKey, uniqueIndex, index } from \"drizzle-orm/sqlite-core\"\r\nimport type { AdapterAccountType } from \"next-auth/adapters\"\r\nimport { relations } from 'drizzle-orm';\r\n\r\n// https://authjs.dev/getting-started/adapters/drizzle\r\nexport const users = sqliteTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: integer(\"emailVerified\", { mode: \"timestamp_ms\" }),\r\n  image: text(\"image\"),\r\n  username: text(\"username\").unique(),\r\n  password: text(\"password\"),\r\n})\r\nexport const accounts = sqliteTable(\r\n  \"account\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => users.id, { onDelete: \"cascade\" }),\r\n    type: text(\"type\").$type<AdapterAccountType>().notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n  },\r\n  (account) => ({\r\n    compoundKey: primaryKey({\r\n      columns: [account.provider, account.providerAccountId],\r\n    }),\r\n  })\r\n)\r\n\r\nexport const emails = sqliteTable(\"email\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  address: text(\"address\").notNull().unique(),\r\n  userId: text(\"userId\").references(() => users.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp_ms\" }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"email_expires_at_idx\").on(table.expiresAt),\r\n}))\r\n\r\nexport const messages = sqliteTable(\"message\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  emailId: text(\"emailId\")\r\n    .notNull()\r\n    .references(() => emails.id, { onDelete: \"cascade\" }),\r\n  fromAddress: text(\"from_address\").notNull(),\r\n  subject: text(\"subject\").notNull(),\r\n  content: text(\"content\").notNull(),\r\n  html: text(\"html\"),\r\n  receivedAt: integer(\"received_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  emailIdIdx: index(\"message_email_id_idx\").on(table.emailId),\r\n}))\r\n\r\nexport const webhooks = sqliteTable('webhook', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id')\r\n    .notNull()\r\n    .references(() => users.id, { onDelete: \"cascade\" }),\r\n  url: text('url').notNull(),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n})\r\n\r\nexport const roles = sqliteTable(\"role\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\").notNull(),\r\n  description: text(\"description\"),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n});\r\n\r\nexport const userRoles = sqliteTable(\"user_role\", {\r\n  userId: text(\"user_id\").notNull().references(() => users.id, { onDelete: \"cascade\" }),\r\n  roleId: text(\"role_id\").notNull().references(() => roles.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  pk: primaryKey({ columns: [table.userId, table.roleId] }),\r\n}));\r\n\r\nexport const apiKeys = sqliteTable('api_keys', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id),\r\n  name: text('name').notNull(),\r\n  key: text('key').notNull().unique(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n}, (table) => ({\r\n  nameUserIdUnique: uniqueIndex('name_user_id_unique').on(table.name, table.userId)\r\n}));\r\n\r\nexport const revoked_credentials = sqliteTable('revoked_credential', {\r\n  jti: text('jti').primaryKey(),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp_ms' }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"revoked_expires_at_idx\").on(table.expiresAt),\r\n}));\r\n\r\nexport const apiKeysRelations = relations(apiKeys, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [apiKeys.userId],\r\n    references: [users.id],\r\n  }),\r\n}));\r\n\r\nexport const userRolesRelations = relations(userRoles, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userRoles.userId],\r\n    references: [users.id],\r\n  }),\r\n  role: one(roles, {\r\n    fields: [userRoles.roleId],\r\n    references: [roles.id],\r\n  }),\r\n}));\r\n\r\nexport const usersRelations = relations(users, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n  apiKeys: many(apiKeys),\r\n}));\r\n\r\nexport const rolesRelations = relations(roles, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n}));", "import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport async function hashPassword(password: string): Promise<string> {\r\n  const encoder = new TextEncoder()\r\n  const salt = process.env.AUTH_SECRET || ''\r\n  const data = encoder.encode(password + salt)\r\n  const hash = await crypto.subtle.digest('SHA-256', data)\r\n  return btoa(String.fromCharCode(...new Uint8Array(hash)))\r\n}\r\n\r\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\r\n  const hash = await hashPassword(password)\r\n  return hash === hashedPassword\r\n}", "import { z } from \"zod\"\r\n\r\nexport const authSchema = z.object({\r\n  username: z.string()\r\n    .min(1, \"用户名不能为空\")\r\n    .max(20, \"用户名不能超过20个字符\")\r\n    .regex(/^[a-zA-Z0-9_-]+$/, \"用户名只能包含字母、数字、下划线和横杠\")\r\n    .refine(val => !val.includes('@'), \"用户名不能是邮箱格式\"),\r\n  password: z.string()\r\n    .min(8, \"密码长度必须大于等于8位\")\r\n})\r\n\r\nexport type AuthSchema = z.infer<typeof authSchema>"], "names": ["runtime", "DELETE", "request", "params", "userId", "getUserId", "db", "createDb", "id", "messageId", "email", "query", "emails", "<PERSON><PERSON><PERSON><PERSON>", "where", "and", "eq", "NextResponse", "json", "error", "status", "messages", "emailId", "delete", "success", "console", "GET", "_request", "message", "from_address", "fromAddress", "subject", "content", "html", "received_at", "receivedAt", "getTime", "headers", "get", "session", "auth", "user", "COLORS", "ROLE_DESCRIPTIONS", "ROLES", "EMPEROR", "DUKE", "KNIGHT", "CIVILIAN", "getDefaultRole", "defaultRole", "getRequestContext", "env", "SITE_CONFIG", "findOrCreateRole", "<PERSON><PERSON><PERSON>", "role", "roles", "name", "newRole", "insert", "values", "description", "returning", "assignRoleToUser", "roleId", "userRoles", "getUserRole", "userRoleRecords", "find<PERSON>any", "with", "checkPermission", "permission", "userRoleNames", "map", "ur", "hasPermission", "handlers", "POST", "signIn", "signOut", "NextAuth", "secret", "process", "AUTH_SECRET", "adapter", "DrizzleAdapter", "usersTable", "users", "accountsTable", "accounts", "providers", "GitHub", "clientId", "AUTH_GITHUB_ID", "clientSecret", "AUTH_GITHUB_SECRET", "CredentialsProvider", "credentials", "username", "label", "type", "placeholder", "password", "authorize", "Error", "authSchema", "parse", "comparePassword", "undefined", "events", "existingRole", "callbacks", "jwt", "token", "image", "generateAvatarUrl", "initial", "toUpperCase", "colorIndex", "Array", "from", "reduce", "acc", "char", "charCodeAt", "length", "backgroundColor", "svg", "trim", "bytes", "TextEncoder", "encode", "base64", "<PERSON><PERSON><PERSON>", "toString", "createdAt", "Date", "strategy", "register", "existing", "hashedPassword", "hashPassword", "drizzle", "DB", "schema", "MANAGE_EMAIL", "MANAGE_WEBHOOK", "PROMOTE_USER", "MANAGE_CONFIG", "MANAGE_API_KEY", "ROLE_PERMISSIONS", "Object", "PERMISSIONS", "some", "includes", "sqliteTable", "text", "<PERSON><PERSON><PERSON>", "$defaultFn", "crypto", "randomUUID", "unique", "emailVerified", "integer", "mode", "notNull", "references", "onDelete", "$type", "provider", "providerAccountId", "refresh_token", "access_token", "expires_at", "token_type", "scope", "id_token", "session_state", "<PERSON><PERSON><PERSON>", "columns", "account", "address", "expiresAt", "expiresAtIdx", "index", "on", "table", "emailIdIdx", "webhooks", "url", "enabled", "default", "updatedAt", "pk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "nameUserIdUnique", "uniqueIndex", "jti", "apiKeysRelations", "relations", "one", "fields", "userRolesRelations", "usersRelations", "many", "rolesRelations", "cn", "inputs", "twMerge", "clsx", "encoder", "salt", "data", "btoa", "String", "fromCharCode", "Uint8Array", "subtle", "digest", "z", "min", "max", "regex", "refine", "val"], "sourceRoot": "", "ignoreList": []}