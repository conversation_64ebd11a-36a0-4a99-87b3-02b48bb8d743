[{"F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\api-keys\\route.ts": "1", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\api-keys\\[id]\\route.ts": "2", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\auth\\register\\route.ts": "3", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\auth\\[...auth]\\route.ts": "4", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\config\\route.ts": "5", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\generate\\route.ts": "6", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\route.ts": "7", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\[address]\\credentials\\route.ts": "8", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\[address]\\messages\\route.ts": "9", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\[address]\\route.ts": "10", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\[address]\\[messageId]\\route.ts": "11", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\roles\\init-emperor\\route.ts": "12", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\roles\\promote\\route.ts": "13", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\roles\\users\\route.ts": "14", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\webhook\\route.ts": "15", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\webhook\\test\\route.ts": "16", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\auth\\login-form.tsx": "17", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\auth\\sign-button.tsx": "18", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\emails\\create-dialog.tsx": "19", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\emails\\email-list.tsx": "20", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\emails\\message-list.tsx": "21", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\emails\\message-view.tsx": "22", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\emails\\three-column-layout.tsx": "23", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\float-menu.tsx": "24", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\home\\action-button.tsx": "25", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\home\\feature-card.tsx": "26", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\layout\\header.tsx": "27", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\no-permission-dialog.tsx": "28", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\profile\\api-key-panel.tsx": "29", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\profile\\config-panel.tsx": "30", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\profile\\profile-card.tsx": "31", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\profile\\promote-panel.tsx": "32", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\profile\\webhook-config.tsx": "33", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\theme\\theme-provider.tsx": "34", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\theme\\theme-toggle.tsx": "35", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\alert-dialog.tsx": "36", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\button.tsx": "37", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\card.tsx": "38", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\dialog.tsx": "39", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\dropdown-menu.tsx": "40", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\input.tsx": "41", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\label.tsx": "42", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\logo.tsx": "43", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\radio-group.tsx": "44", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\select.tsx": "45", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\switch.tsx": "46", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\tabs.tsx": "47", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\toast-action.tsx": "48", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\toast.tsx": "49", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\toaster.tsx": "50", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\tooltip.tsx": "51", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\use-toast.ts": "52", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\config\\email.ts": "53", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\config\\index.ts": "54", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\config\\webhook.ts": "55", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\fonts.ts": "56", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\hooks\\use-config.ts": "57", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\hooks\\use-copy.ts": "58", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\hooks\\use-role-permission.ts": "59", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\hooks\\use-throttle.ts": "60", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\hooks\\use-user-role.ts": "61", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\layout.tsx": "62", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\apiKey.ts": "63", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\auth.ts": "64", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\avatar.ts": "65", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\cursor.ts": "66", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\db.ts": "67", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\jwt.ts": "68", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\permissions.ts": "69", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\schema.ts": "70", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\utils.ts": "71", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\validation.ts": "72", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\webhook.ts": "73", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\login\\page.tsx": "74", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\moe\\page.tsx": "75", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\page.tsx": "76", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\profile\\page.tsx": "77", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\providers.tsx": "78", "F:\\CODE\\Project\\Mail\\moemail_source\\app\\types\\email.ts": "79"}, {"size": 2071, "mtime": 1749284707565, "results": "80", "hashOfConfig": "81"}, {"size": 2401, "mtime": 1749284707564, "results": "82", "hashOfConfig": "81"}, {"size": 819, "mtime": 1749284707566, "results": "83", "hashOfConfig": "81"}, {"size": 95, "mtime": 1749284707565, "results": "84", "hashOfConfig": "81"}, {"size": 1776, "mtime": 1749284707566, "results": "85", "hashOfConfig": "81"}, {"size": 3329, "mtime": 1749287529417, "results": "86", "hashOfConfig": "81"}, {"size": 2039, "mtime": 1749284707568, "results": "87", "hashOfConfig": "81"}, {"size": 1681, "mtime": 1749287716252, "results": "88", "hashOfConfig": "81"}, {"size": 371, "mtime": 1749290427177, "results": "89", "hashOfConfig": "81"}, {"size": 3501, "mtime": 1749284707567, "results": "90", "hashOfConfig": "81"}, {"size": 2778, "mtime": 1749284707567, "results": "91", "hashOfConfig": "81"}, {"size": 1703, "mtime": 1749284707569, "results": "92", "hashOfConfig": "81"}, {"size": 1977, "mtime": 1749284707569, "results": "93", "hashOfConfig": "81"}, {"size": 1225, "mtime": 1749284707570, "results": "94", "hashOfConfig": "81"}, {"size": 1654, "mtime": 1749284707570, "results": "95", "hashOfConfig": "81"}, {"size": 1090, "mtime": 1749284707571, "results": "96", "hashOfConfig": "81"}, {"size": 12236, "mtime": 1749284707572, "results": "97", "hashOfConfig": "81"}, {"size": 1623, "mtime": 1749284707572, "results": "98", "hashOfConfig": "81"}, {"size": 6129, "mtime": 1749284707573, "results": "99", "hashOfConfig": "81"}, {"size": 8414, "mtime": 1749284707573, "results": "100", "hashOfConfig": "81"}, {"size": 8749, "mtime": 1749284707573, "results": "101", "hashOfConfig": "81"}, {"size": 6678, "mtime": 1749284707573, "results": "102", "hashOfConfig": "81"}, {"size": 6135, "mtime": 1749284707573, "results": "103", "hashOfConfig": "81"}, {"size": 1179, "mtime": 1749284707574, "results": "104", "hashOfConfig": "81"}, {"size": 685, "mtime": 1749284707574, "results": "105", "hashOfConfig": "81"}, {"size": 695, "mtime": 1749284707574, "results": "106", "hashOfConfig": "81"}, {"size": 639, "mtime": 1749284707575, "results": "107", "hashOfConfig": "81"}, {"size": 1286, "mtime": 1749284707575, "results": "108", "hashOfConfig": "81"}, {"size": 17951, "mtime": 1749284707576, "results": "109", "hashOfConfig": "81"}, {"size": 4628, "mtime": 1749284707576, "results": "110", "hashOfConfig": "81"}, {"size": 4308, "mtime": 1749284707576, "results": "111", "hashOfConfig": "81"}, {"size": 4881, "mtime": 1749284707577, "results": "112", "hashOfConfig": "81"}, {"size": 6693, "mtime": 1749284707577, "results": "113", "hashOfConfig": "81"}, {"size": 303, "mtime": 1749284707577, "results": "114", "hashOfConfig": "81"}, {"size": 682, "mtime": 1749284707578, "results": "115", "hashOfConfig": "81"}, {"size": 4564, "mtime": 1749284707578, "results": "116", "hashOfConfig": "81"}, {"size": 1829, "mtime": 1749284707579, "results": "117", "hashOfConfig": "81"}, {"size": 1953, "mtime": 1749284707579, "results": "118", "hashOfConfig": "81"}, {"size": 3927, "mtime": 1749284707579, "results": "119", "hashOfConfig": "81"}, {"size": 1820, "mtime": 1749284707580, "results": "120", "hashOfConfig": "81"}, {"size": 847, "mtime": 1749284707580, "results": "121", "hashOfConfig": "81"}, {"size": 607, "mtime": 1749284707580, "results": "122", "hashOfConfig": "81"}, {"size": 1846, "mtime": 1749284707580, "results": "123", "hashOfConfig": "81"}, {"size": 1524, "mtime": 1749284707581, "results": "124", "hashOfConfig": "81"}, {"size": 3587, "mtime": 1749284707581, "results": "125", "hashOfConfig": "81"}, {"size": 1188, "mtime": 1749284707581, "results": "126", "hashOfConfig": "81"}, {"size": 1949, "mtime": 1749284707581, "results": "127", "hashOfConfig": "81"}, {"size": 969, "mtime": 1749284707582, "results": "128", "hashOfConfig": "81"}, {"size": 4120, "mtime": 1749284707582, "results": "129", "hashOfConfig": "81"}, {"size": 1188, "mtime": 1749284707582, "results": "130", "hashOfConfig": "81"}, {"size": 1169, "mtime": 1749284707582, "results": "131", "hashOfConfig": "81"}, {"size": 4094, "mtime": 1749284707583, "results": "132", "hashOfConfig": "81"}, {"size": 214, "mtime": 1749284707583, "results": "133", "hashOfConfig": "81"}, {"size": 50, "mtime": 1749284707584, "results": "134", "hashOfConfig": "81"}, {"size": 293, "mtime": 1749284707584, "results": "135", "hashOfConfig": "81"}, {"size": 163, "mtime": 1749284707585, "results": "136", "hashOfConfig": "81"}, {"size": 1578, "mtime": 1749284707586, "results": "137", "hashOfConfig": "81"}, {"size": 872, "mtime": 1749284707586, "results": "138", "hashOfConfig": "81"}, {"size": 605, "mtime": 1749284707586, "results": "139", "hashOfConfig": "81"}, {"size": 406, "mtime": 1749284707587, "results": "140", "hashOfConfig": "81"}, {"size": 478, "mtime": 1749284707587, "results": "141", "hashOfConfig": "81"}, {"size": 3244, "mtime": 1749284707588, "results": "142", "hashOfConfig": "81"}, {"size": 1421, "mtime": 1749284707588, "results": "143", "hashOfConfig": "81"}, {"size": 6607, "mtime": 1749284707588, "results": "144", "hashOfConfig": "81"}, {"size": 1313, "mtime": 1749284707589, "results": "145", "hashOfConfig": "81"}, {"size": 409, "mtime": 1749284707589, "results": "146", "hashOfConfig": "81"}, {"size": 270, "mtime": 1749284707589, "results": "147", "hashOfConfig": "81"}, {"size": 1897, "mtime": 1749288355264, "results": "148", "hashOfConfig": "81"}, {"size": 1006, "mtime": 1749284707590, "results": "149", "hashOfConfig": "81"}, {"size": 5323, "mtime": 1749286539755, "results": "150", "hashOfConfig": "81"}, {"size": 685, "mtime": 1749284707590, "results": "151", "hashOfConfig": "81"}, {"size": 477, "mtime": 1749284707590, "results": "152", "hashOfConfig": "81"}, {"size": 1394, "mtime": 1749284707591, "results": "153", "hashOfConfig": "81"}, {"size": 502, "mtime": 1749284707591, "results": "154", "hashOfConfig": "81"}, {"size": 1008, "mtime": 1749284707592, "results": "155", "hashOfConfig": "81"}, {"size": 2435, "mtime": 1749284707592, "results": "156", "hashOfConfig": "81"}, {"size": 719, "mtime": 1749284707592, "results": "157", "hashOfConfig": "81"}, {"size": 227, "mtime": 1749284707593, "results": "158", "hashOfConfig": "81"}, {"size": 313, "mtime": 1749284707593, "results": "159", "hashOfConfig": "81"}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9v4ww9", {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\api-keys\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\api-keys\\[id]\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\auth\\register\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\auth\\[...auth]\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\config\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\generate\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\[address]\\credentials\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\[address]\\messages\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\[address]\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\[address]\\[messageId]\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\roles\\init-emperor\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\roles\\promote\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\roles\\users\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\webhook\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\webhook\\test\\route.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\auth\\login-form.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\auth\\sign-button.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\emails\\create-dialog.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\emails\\email-list.tsx", ["397"], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\emails\\message-list.tsx", [], ["398"], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\emails\\message-view.tsx", ["399"], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\emails\\three-column-layout.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\float-menu.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\home\\action-button.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\home\\feature-card.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\layout\\header.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\no-permission-dialog.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\profile\\api-key-panel.tsx", ["400"], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\profile\\config-panel.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\profile\\profile-card.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\profile\\promote-panel.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\profile\\webhook-config.tsx", [], ["401", "402"], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\theme\\theme-provider.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\theme\\theme-toggle.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\alert-dialog.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\button.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\card.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\dialog.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\dropdown-menu.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\input.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\label.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\logo.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\radio-group.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\select.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\switch.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\tabs.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\toast-action.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\toast.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\toaster.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\tooltip.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\use-toast.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\config\\email.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\config\\index.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\config\\webhook.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\fonts.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\hooks\\use-config.ts", ["403"], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\hooks\\use-copy.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\hooks\\use-role-permission.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\hooks\\use-throttle.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\hooks\\use-user-role.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\layout.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\apiKey.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\auth.ts", [], ["404"], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\avatar.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\cursor.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\db.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\jwt.ts", ["405"], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\permissions.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\schema.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\utils.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\validation.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\lib\\webhook.ts", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\login\\page.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\moe\\page.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\page.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\profile\\page.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\providers.tsx", [], [], "F:\\CODE\\Project\\Mail\\moemail_source\\app\\types\\email.ts", [], [], {"ruleId": "406", "severity": 1, "message": "407", "line": 117, "column": 6, "nodeType": "408", "endLine": 117, "endColumn": 15, "suggestions": "409"}, {"ruleId": "406", "severity": 1, "message": "410", "line": 185, "column": 6, "nodeType": "408", "endLine": 185, "endColumn": 16, "suggestions": "411", "suppressions": "412"}, {"ruleId": "406", "severity": 1, "message": "413", "line": 148, "column": 6, "nodeType": "408", "endLine": 148, "endColumn": 38, "suggestions": "414"}, {"ruleId": "406", "severity": 1, "message": "415", "line": 69, "column": 6, "nodeType": "408", "endLine": 69, "endColumn": 23, "suggestions": "416"}, {"ruleId": "417", "severity": 2, "message": "418", "line": 69, "column": 14, "nodeType": null, "messageId": "419", "endLine": 69, "endColumn": 20, "suppressions": "420"}, {"ruleId": "417", "severity": 2, "message": "418", "line": 97, "column": 14, "nodeType": null, "messageId": "419", "endLine": 97, "endColumn": 20, "suppressions": "421"}, {"ruleId": "406", "severity": 1, "message": "422", "line": 59, "column": 6, "nodeType": "408", "endLine": 59, "endColumn": 35, "suggestions": "423"}, {"ruleId": "417", "severity": 2, "message": "424", "line": 121, "column": 18, "nodeType": null, "messageId": "419", "endLine": 121, "endColumn": 23, "suppressions": "425"}, {"ruleId": "417", "severity": 2, "message": "424", "line": 59, "column": 12, "nodeType": null, "messageId": "419", "endLine": 59, "endColumn": 17}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchEmails'. Either include it or remove the dependency array.", "ArrayExpression", ["426"], "React Hook useEffect has missing dependencies: 'fetchMessages' and 'startPolling'. Either include them or remove the dependency array.", ["427"], ["428"], "React Hook useEffect has a missing dependency: 'updateIframeContent'. Either include it or remove the dependency array.", ["429"], "React Hook useEffect has a missing dependency: 'fetchApiKeys'. Either include it or remove the dependency array.", ["430"], "@typescript-eslint/no-unused-vars", "'_error' is defined but never used.", "unusedVar", ["431"], ["432"], "React Hook useEffect has a missing dependency: 'store'. Either include it or remove the dependency array.", ["433"], "'error' is defined but never used.", ["434"], {"desc": "435", "fix": "436"}, {"desc": "437", "fix": "438"}, {"kind": "439", "justification": "440"}, {"desc": "441", "fix": "442"}, {"desc": "443", "fix": "444"}, {"kind": "439", "justification": "440"}, {"kind": "439", "justification": "440"}, {"desc": "445", "fix": "446"}, {"kind": "439", "justification": "440"}, "Update the dependencies array to be: [fetchEmails, session]", {"range": "447", "text": "448"}, "Update the dependencies array to be: [email.id, fetchMessages, startPolling]", {"range": "449", "text": "450"}, "directive", "", "Update the dependencies array to be: [message.html, viewMode, theme, updateIframeContent]", {"range": "451", "text": "452"}, "Update the dependencies array to be: [canManageApiKey, fetchApiKeys]", {"range": "453", "text": "454"}, "Update the dependencies array to be: [store, store.config, store.loading]", {"range": "455", "text": "456"}, [3531, 3540], "[fetchEmails, session]", [5058, 5068], "[email.id, fetchMessages, startPolling]", [4489, 4521], "[message.html, viewMode, theme, updateIframeContent]", [2104, 2121], "[canManage<PERSON><PERSON><PERSON><PERSON>, fetchApiKeys]", [1502, 1531], "[store, store.config, store.loading]"]