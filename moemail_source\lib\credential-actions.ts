import { SignJWT } from "jose";
import { createDb } from "@/lib/db";
import { revoked_credentials } from "@/lib/schema";
import { eq } from "drizzle-orm";
import { verifyAddressCredential as edgeVerify } from "@/lib/edge-auth";

const secretKey = process.env.AUTH_SECRET;
if (!secretKey) {
  throw new Error("AUTH_SECRET must be set");
}
const secret = new TextEncoder().encode(secretKey);

const issuer = "moemail";
const audience = "moemail:credential";

export async function generateAddressCredential(
  address: string,
  expiresInSeconds: number = 3600 // 1 hour
) {
  const now = Math.floor(Date.now() / 1000);
  const jwt = new SignJWT({})
    .setProtectedHeader({ alg: "HS256" })
    .setSubject(address)
    .setIssuer(issuer)
    .setAudience(audience)
    .setIssuedAt(now);

  if (expiresInSeconds > 0) {
    jwt.setExpirationTime(now + expiresInSeconds);
  }

  const token = await jwt.sign(secret);
  return token;
}

export async function revokeAddressCredential(token: string) {
    const db = createDb();
    const payload = await edgeVerify(token);
    if (!payload || !payload.exp) {
        return;
    }

    const isAlreadyRevoked = await db.query.revoked_credentials.findFirst({
        where: eq(revoked_credentials.jti, token),
    });

    if (isAlreadyRevoked) {
        return;
    }

    await db.insert(revoked_credentials).values({
        jti: token,
        expiresAt: new Date(payload.exp * 1000)
    });
} 