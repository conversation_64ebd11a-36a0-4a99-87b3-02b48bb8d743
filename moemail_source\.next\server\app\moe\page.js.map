{"version": 3, "file": "app/moe/page.js", "mappings": "qFAAA,6DCAA,mHEAA,0nCDWA,MACA,CACA,GACA,CACA,UACA,MACA,CACA,uBAAiC,EACjC,MAfA,IAAoB,sCAAgG,CAepH,+DAES,EACF,CACP,CAEA,UACA,sBAAoC,uCAAqP,aACzR,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QApCA,IAAsB,qCAA6F,CAoCnH,4DACA,aApCA,IAAsB,sCAAgF,CAoCtG,+CACA,WApCA,IAAsB,sCAAgF,CAoCtG,+CACA,cApCA,IAAsB,sCAAmF,CAoCzG,kDACA,UACA,sBAAoC,uCAAqP,aACzR,SACA,aACA,WACA,eACA,CACA,EACA,CAEA,kEAKO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAGO,MAAwB,oBAAkB,EACjD,YACA,KAAc,GAAS,UACvB,iBACA,gBAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,mCC5DD,iCAEA,gCALA,CAEA,CAGA,EAWA,gCACA,wBACA,oCACA,sDACA,gCAEA,+BACA,oDACA,MACI,QAA8B,EAClC,iBACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEL,IAAM,EAAS,OAAS,EACxB,UAAe,GAAU,KACzB,IAvBA,GAwBA,iBACA,OA9BA,KA+BA,OAAW,GACX,SA/BA,KAgCA,YA/BA,KAgCA,SAnCA,KAoCA,gBACA,YAAgB,KAChB,wBACA,0BACA,wBACA,cAlCA,OAmCA,6BA5BA,OA6BA,OAnCA,CAAoB,MAAQ,SAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,wIAoCnxJ,oCACA,mBACA,wBAtDA,KAuDA,2BACA,CAAC,EACM,EAAqB,EACb,cACf,MAAW,OAAO,EAClB,KACA,IAJmC,YAIX,KACxB,QAAiB,CACjB,CAAK,CACL,kBC1EA,sCAAoJ,CAEpJ,sCAAqK,CAErK,sCAA+J,CAE/J,oCAAuJ,CAEvJ,sCAAqI,iBCRrI,sCAAoJ,CAEpJ,sCAAqK,CAErK,sCAA+J,CAE/J,sCAAuJ,CAEvJ,sCAAqI,mJCK/H,MAAY,OAAgB,CAAC,WAAa,EAC9C,CAAC,MAAQ,EAAE,EAAG,CAAsD,wDAAK,SAAU,EACnF,CAAC,MAAQ,EAAE,EAAG,CAAc,gBAAK,SAAU,EAC3C,CAAC,MAAQ,EAAE,EAAG,CAAuD,yDAAK,SAAU,EACpF,CAAC,MAAQ,EAAE,EAAG,CAAa,eAAK,SAAU,EAC3C,wBEGM,eACP,SACA,+CACA,UACA,GDxBA,kECwB2B,UAE3B,QACA,yGEbM,EAAa,QAGb,CAAC,EAAoB,EAAgB,CAAI,OAAkB,CAAC,GAG5D,CAAC,CAHoC,CAGrB,EAAe,CAAI,CAHmC,CAGG,GAUzE,EAAc,KAViB,OAUjB,CAClB,CAAC,EAAgC,KAC/B,GAAM,cACJ,OACA,UACA,GAAU,WACV,WACA,EACA,QAAQ,aACR,OACA,EACA,GAAG,EACL,CAAI,EACE,CAAC,EAAQ,EAAS,CAAU,MAAV,IAAU,CAAmC,IAAI,EACnE,EAAe,OAAe,CAAC,EAAc,GAAU,EAAU,IAAI,CAAC,CAC7B,SAAO,IAEhD,CAFqD,EAErC,GAAS,GAAQ,CAAC,CAAC,EAAO,QAAQ,MAAM,EAE9D,EAFkE,IAGhE,WAAC,GAAc,MAAO,EAAc,mBAAkB,EACpD,oBAAC,IAAS,CAAC,OAAV,CACC,KAAK,SACL,KAAK,QACL,eAAc,EACd,aAAY,EAAS,GACrB,IAD4B,YACb,EAAW,GAAK,gBAC/B,QACA,EACC,GAAG,EACJ,IAAK,EACL,QAAS,OAAoB,CAAC,EAAM,QAAS,IAEtC,GAAS,MACV,IACF,EAAiC,QAAU,CAD1B,CACgC,qBAAqB,EAIjE,EAAiC,QAAS,GAAM,gBAAgB,EAEzE,CAAC,IAEF,GACC,UAAC,GACC,CADF,OACW,EACT,QAAS,CAAC,EAAiC,aAC3C,QACA,UACA,WACA,WACA,OACA,EAIA,MAAO,CAAE,UAAW,mBAAoB,IAC1C,CAEJ,CAEJ,GAGF,EAAM,YAAc,EAMpB,IAAM,EAAiB,iBAYjB,EAAuB,aAC3B,CAAC,EAAyC,KACxC,GAAM,cAAE,aAAc,EAAY,GAAG,EAAe,CAAI,EAClD,EAAU,EAAgB,EAAgB,GAChD,CAFoD,KAGlD,GAF0D,EAE1D,KAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAAQ,QACvC,mBAAC,IAAS,CAAC,KAAV,CACC,aAAY,EAAS,EAAQ,OAAO,EACpC,gBAAe,EAAQ,SAAW,GAAK,OACtC,GAAG,EACJ,IAAK,GACP,CACF,CAEJ,GAGF,EAAe,YAAc,EAe7B,IAAM,EAAyB,aAC7B,CACE,cACE,UACA,UACA,UACA,GAAU,EACV,GAAG,EACL,CACA,KAEA,IAAM,EAAY,SAAyB,IAAI,EACzC,EAAe,OAAe,CAAC,EAAK,GPhHlC,EOiHY,OAAW,CAAC,GAC1B,EAAc,EADmB,CACnB,IAAO,CAAC,GAoB5B,IApBmC,GAqBjC,EAlBI,UAAU,KACd,IAAM,EAAQ,EAAI,QAClB,GAAI,CAAC,EAAO,OAOZ,IAAM,EAAa,OAJO,yBACxB,OAFwB,iBAAiB,UAGzC,WAE4B,IAC9B,GAAI,IAAgB,GAAW,EAAY,CACzC,IAAM,EAAQ,IAAI,MAAM,QAAS,SAAE,CAAQ,CAAC,EAC5C,EAAW,KAAK,EAAO,GACvB,EAAM,EADwB,WACxB,CAAc,EACtB,CACF,EAF6B,CAEzB,EAAa,EAAS,EAAQ,EAGhC,UAAC,IAAS,CAAC,MAAV,CACC,KAAK,QACL,cAAW,GACX,eAAgB,EACf,GAAG,EACJ,SAAU,GACV,IAAK,EACL,MAAO,CACL,GAAG,EAAM,MACT,GAAG,EACH,SAAU,WACV,cAAe,OACf,QAAS,EACT,OAAQ,CACV,GAGN,GAOF,SAAS,EAAS,GAAkB,OAC3B,EAAU,UAAY,WAC/B,CANA,EAAiB,YAhES,EAgEK,iBD3L/B,IAAM,EAAa,CAAC,UAAW,YAAa,YAAa,YAAY,EAK/D,EAAmB,aAGnB,CAAC,EAAyB,EAAqB,CAAIA,CAAAA,EAAAA,EAAAA,CAAAA,CAAAC,CAAmB,EAAkB,CAC5F,IAA2B,CAC3B,EACD,EACK,EAA2B,QAA2B,CAAC,EACvD,EAAgB,IAUhB,CAAC,EAAoB,EAAoB,CAC7C,EAAgD,GAiB5C,EAAmB,UAlBsB,CACmB,CAiBzC,CACvB,CAAC,EAAqC,KACpC,GAAM,mBACJ,EACA,oBACA,EACA,MAAO,WACP,GAAW,WACX,GAAW,cACX,MACA,EACA,QAAO,EACP,gBACA,GAAG,EACL,CAAI,EACE,EAAwB,EAAyB,GACjD,EAAY,QAAY,CAAC,GADyC,CAEjE,EAAO,EAAQ,CAAI,KAAJ,CAAI,CAAoB,CAAC,CAC7C,KAAM,EACN,YAAa,GAAgB,KAC7B,SAAU,EACV,OAAQ,CACV,CAAC,EAED,MACEC,CAAA,EAAAA,EAAAA,GAAAA,CAAAA,CAAC,GACC,MAAO,OACP,WACA,WACA,QACA,EACA,cAAe,EAEf,SAAAA,CAAAA,EAAAA,EAAAA,GAAAA,CAAAA,CAAkB,KAAjB,CACC,SAAO,EACN,GAAG,cACJ,EACA,IAAK,OACL,EAEA,SAAAA,CAAAA,EAAAA,EAAAA,GAAAA,CAAAA,CAACC,EAAAA,EAAAC,CAAU,IAAV,CACC,KAAK,aACL,gBAAe,EACf,mBAAkB,EAClB,gBAAe,EAAW,GAAK,OAC/B,IAAK,EACJ,GAAG,EACJ,IAAK,GACP,EACF,EAGN,GAGF,EAAW,YAAc,EAMzB,IAAM,EAAY,iBAQZ,EAAuB,aAC3B,CAAC,EAAyC,KACxC,GAAM,mBAAE,WAAmB,EAAU,GAAG,EAAU,CAAI,EAChD,EAAU,EAAqB,CADa,CACF,GAC1C,EAAa,EAAQ,UADsC,EAE3D,EAAwB,EAAyB,GACjD,EAAa,EAAc,GAC3B,EAAY,KAFsD,GAEtD,CAAyC,GADT,CACa,EACzD,EAAeC,CAAAA,EAAAA,EAAAA,CAAAA,CAAAC,CAAgB,EAAc,GAAG,EACtC,EAAQ,QAAU,EAAU,MACtC,EAA6B,UAAO,GAiB1C,EAjB+C,KAEzC,YAAU,KACd,IAAM,EAAgB,IAChB,EAAW,SAAS,EAAM,GAAG,GAAG,CAClC,EAAqB,SAAU,EAEnC,EACM,EAAc,IAAO,EAAqB,SAAU,EAG1D,OAFA,SAAS,iBAAiB,UAAW,GACrC,SAAS,CADyC,eACzC,CAAiB,QAAS,GAC5B,KACL,GAF4C,MAEnC,oBAAoB,UAAW,GACxC,SAAS,CAD4C,kBAC5C,CAAoB,QAAS,EACxC,CACF,EAAG,CAAC,CAAC,EAGHJ,CAAAA,CALmD,CAKnDA,EAAAA,GAAAA,CAAAA,CAAkB,KAAjB,CACC,SAAO,EACN,GAAG,EACJ,UAAW,CAAC,EACZ,OAAQ,EAER,SAAAA,CAAAA,EAAAA,EAAAA,GAAAA,CAAAA,CAAC,GACC,SAAU,EACV,SAAU,EAAQ,iBAClB,EACC,GAAG,EACH,GAAG,EACJ,KAAM,EAAQ,KACd,IAAK,EACL,QAAS,IAAM,EAAQ,cAAc,EAAU,KAAK,EACpD,UAAW,OAAAK,CAAqB,IAEZ,QAAS,EAAvB,EAAM,KAAiB,EAAM,eAAe,CAClD,CAAC,EACD,QAAS,OAAAA,CAAqB,EAAU,QAAS,KAM3C,EAAqB,QAAS,GAAI,SAAS,MAAM,CACvD,CAAC,GACH,EAGN,GAGF,EAAe,YAAc,EAY7B,IAAM,EAA4B,aAChC,CAAC,EAA8C,KAC7C,GAAM,mBAAE,EAAmB,GAAG,EAAe,CAAI,EAC3C,EAAa,EAAc,GACjC,GAF6C,GAEtCL,CAAA,EAAAA,EAAAA,GAAAA,CAAAA,CAAC,GAAgB,GAAG,EAAa,GAAG,EAAgB,IAAK,EAAc,CAChF,GAGF,EAAoB,YAdG,EAcWF,oBE/L5B,MAAS,OAAgB,CAAC,QAAU,EACxC,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,MAAM,GAAK,UAAU,EAC1D,gBCPD,IAAMQ,EAAaC,EAAAA,UAAgB,CAGjC,CAAC,MAHaD,KAGXE,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAExB,UAACC,EAAwB,CACvBH,EADuB,QACZI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,aAAcJ,GAC3B,GAAGC,CAAK,CACTC,IAAKA,KAIXJ,EAAWO,WAAW,CAAGF,EAAyBE,GAAD,GAAvCP,KAAmD,CAE7D,IAAMQ,EAAiBP,EAAAA,UAAgB,CAGrC,CAAC,CAAEC,SAHeM,EAGN,CAAE,GAAGL,EAAO,CAAEC,IAExB,UAACC,EAAwB,CACvBD,EADuB,EAClBA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,2OACAJ,GAED,GAAGC,CAAK,UAET,UAACE,EAA6B,CAACH,MAAD,IAAW,4CACvC,UAACO,EAAMA,CAACP,GAADO,OAAW,+CAK1BD,EAAeD,WAAW,CAAGF,EAAyBE,GAAD,OAAvCC,CAAmD,yBCpC1D,IAAME,EAAiC,CAC5C,CAAEC,MAAO,MAAOC,MAAO,IAAe,EACtC,CAD8B,KAAK,CAC1B,OAAQA,MAAO,KAAoB,EAAb,CAC7BD,IADkC,EAC3B,GADgC,EAC1BC,MAAO,MAAwB,CAAjB,CAC7B,CAAED,GADgC,GACzB,EAD8B,GACxBC,EAD6B,IACtB,CAAE,EACzB,2BCUM,SAASC,GAAa,gBAAEC,CAAc,CAAqB,EAChE,GAAM,CAAEC,QAAM,CAAE,CAAGC,CAAAA,EAAAA,GAAAA,CAAAA,CAASA,GACtB,CAACC,EAAMC,EAAQ,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3B,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACG,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACrC,CAACK,EAAeC,EAAiB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC7C,CAACO,EAAYC,EAAc,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACT,CAAc,CAAC,EAAE,CAACE,KAAK,CAACgB,QAAQ,IACvE,OAAEC,CAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GACpB,iBAAEC,CAAe,CAAE,CAAGC,CAAAA,EAAAA,GAAAA,CAAAA,CAAOA,GAQ7BC,EAAc,UAClB,GAAI,CAACX,EAAUY,IAAI,GAAI,CACrBL,EAAM,CACJM,MAAO,KACPC,YAAa,SACbC,QAAS,aACX,GACA,MACF,CAEAhB,GAAW,GACX,GAAI,CACF,IAAMiB,EAAW,MAAMC,MAAM,uBAAwB,CACnDC,OAAQ,OACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAAC,CACnBC,KAAMvB,EACNwB,OAAQtB,EACRE,WAAYqB,SAASrB,EACvB,EACF,GAEA,GAAI,CAACY,EAASU,EAAE,CAAE,CAChB,IAAMC,EAAO,MAAMX,EAASY,IAAI,GAChCrB,EAAM,CACJM,MAAO,KACPC,YAAa,EAA4Be,KAAK,CAC9Cd,QAAS,aACX,GACA,MACF,CAEAR,EAAM,CACJM,MAAO,KACPC,YAAa,WACf,GACAtB,IACAI,GAAQ,GACRK,EAAa,GACf,CAAE,KAAM,CACNM,EAAM,CACJM,MAAO,KACPC,YAAa,SACbC,QAAS,aACX,EACF,QAAU,CACRhB,GAAW,EACb,CACF,EAQA,MACE,WAAC+B,EAAAA,EAAMA,CAAAA,CAACnC,KAAMA,EAAMoC,aAAcnC,YAChC,UAACoC,EAAAA,EAAaA,CAAAA,CAACC,OAAO,aACpB,WAACC,EAAAA,CAAMA,CAAAA,CAACtD,UAAU,kBAChB,UAACuD,EAAAA,CAAIA,CAAAA,CAACvD,UAAU,YAAY,aAIhC,WAACwD,EAAAA,EAAaA,CAAAA,WACZ,UAACC,EAAAA,EAAYA,CAAAA,UACX,UAACC,EAAAA,EAAWA,CAAAA,UAAC,eAEf,WAACC,MAAAA,CAAI3D,UAAU,2BACb,WAAC2D,MAAAA,CAAI3D,UAAU,uBACb,UAAC4D,EAAAA,CAAKA,CAAAA,CACJlD,MAAOU,EACPyC,SAAWC,GAAMzC,EAAayC,EAAEC,MAAM,CAACrD,KAAK,EAC5CsD,YAAY,QACZhE,UAAU,WAEVa,IAAQoD,mBAAmBC,SAAU,EAAK,GAC1C,WAACC,EAAAA,EAAMA,CAAAA,CAACzD,MAAOY,EAAe8C,cAAe7C,YAC3C,UAAC8C,EAAAA,EAAaA,CAAAA,CAACrE,UAAU,qBACvB,UAACsE,EAAAA,EAAWA,CAAAA,CAAAA,KAEd,UAACC,EAAAA,EAAaA,CAAAA,UACX1D,GAAQoD,mBAAmBO,IAAIC,GAC9B,OAACC,EAAAA,EAAUA,CAAAA,CAAShE,MAAO+D,YAAG,IAAEA,IAAfA,SAKzB,UAACnB,EAAAA,CAAMA,CAAAA,CACLnB,QAAQ,UACRwC,KAAK,OACLC,QAjGe,CAiGNC,GAjGYxD,EAAayD,EAAO,IAADA,KAkGnC,kBAEL,UAACC,EAASA,CAAC/E,MAAD+E,IAAW,iBAIzB,WAACpB,MAAAA,CAAI3D,UAAU,oCACb,UAACgF,EAAAA,CAAKA,CAAAA,CAAChF,UAAU,0CAAiC,SAClD,UAACF,EAAUA,CACTY,MAAOc,EACP4C,WAFStE,GAEM2B,EACfzB,UAAU,sBAETQ,EAAegE,GAAG,CAAC,GAClB,KADahE,IACb,EAACmD,MAAAA,CAAuB3D,UAAU,oCAChC,UAACM,EAAcA,CAACI,MAAOuE,EAAOvE,KAAK,CAACgB,QAAQ,CAA7BpB,EAAiC4E,GAAID,EAAOvE,KAAK,CAACgB,QAAQ,KACzE,UAACsD,EAAAA,CAAKA,CAAAA,CAACG,QAASF,EAAOvE,KAAK,CAACgB,QAAQ,GAAI1B,UAAU,MXxE9C,4BWyEFiF,EAAOxE,KAAK,KAHPwE,EAAOvE,KAAK,QAU5B,WAACiD,MAAAA,CAAI3D,UAAU,kEACb,UAACoF,OAAAA,CAAKpF,UAAU,oBAAW,cAC1BoB,EACC,WAACuC,MAAAA,CAAI3D,UAAU,4CACb,UAACoF,OAAAA,CAAKpF,UAAU,oBAAY,GAAGoB,EAAU,CAAC,EAAEE,EAAAA,CAAe,GAC3D,UAACqC,MAAAA,CACC3D,UAAU,+DACV4E,QA/HS,CA+HAS,IA9HvBxD,EAAgB,GAAGT,EAAU,CAAC,EAAEE,EAAAA,CAAe,CACjD,WA+HgB,UAACgE,EAAAA,CAAIA,CAAAA,CAACtF,UAAU,gBAIpB,UAACoF,OAAAA,CAAKpF,UAAU,yBAAgB,cAItC,WAAC2D,MAAAA,CAAI3D,UAAU,mCACb,UAACsD,EAAAA,CAAMA,CAAAA,CAACnB,QAAQ,UAAUyC,QAAS,IAAM5D,GAAQ,GAAQuE,SAAUrE,WAAS,OAG5E,UAACoC,EAAAA,CAAMA,CAAAA,CAACsB,QAAS7C,EAAawD,SAAUrE,WACrCA,EAAU,SAAW,eAMlC,2BClLO,SAASsE,GACdC,CAAK,CACLC,CAAa,EAEb,IAAMC,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAACC,KAAKC,GAAG,IAE/B,MAAOC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CACf,CAAC,GAAGC,KACH,IAAMF,EAAMD,KAAKC,GAAG,GAChBA,EAAMH,EAAQM,OAAO,EAAIP,IAC3BD,GADkC,EAC5BO,GACNL,EAAQM,OAAO,CAAGH,EAEtB,EACA,CAACL,EAAIC,EAAM,CAEf,sCCJM,GAAY,cAGZ,CAAC,GAA0B,GAAsB,CAAI,OAAkB,CAAC,GAAW,CACvF,KAAiB,CAClB,EACK,GAAiB,SAAiB,CAAC,EAKnC,GAA2C,IAC/C,GAAM,CAAE,qBAAoB,GAAG,EAAiB,CAAI,EAC9C,EAAc,GAAe,GACnC,IAFgD,EAEzC,SAD8C,CAC7B,MAAhB,CAAsB,GAAG,EAAc,GAAG,EAAkB,OAAO,EAAM,CACnF,EAEA,GAAY,YAAc,GAWO,aAC/B,CAAC,EAA6C,KAC5C,GAAM,oBAAE,EAAoB,GAAG,EAAa,CAAI,EAC1C,EAAc,GAAe,GADS,MAErC,SAD8C,CAC7B,MAAhB,CAAyB,GAAG,EAAc,GAAG,EAAc,IAAK,EAAc,CACxF,GAGiB,YAdE,EAcY,mBAWjC,IAAM,GAAsD,IAG1D,GAAM,oBAAE,EAAoB,GAAG,EAAY,CAAI,EACzC,EAAc,GAAe,EADQ,CAE3C,MAAO,SAD8C,CAC7B,MAAhB,CAAwB,GAAG,EAAc,GAAG,EAAa,CACnE,EAEA,GAAkB,YAbE,EAaY,kBAYhC,IAAM,GAA2B,aAC/B,CAAC,EAA6C,KAC5C,GAAM,oBAAE,EAAoB,GAAG,EAAa,CAAI,EAC1C,EAAc,GAAe,GADS,MAErC,SAD8C,CAC7B,MAAhB,CAAyB,GAAG,EAAc,GAAG,EAAc,IAAK,EAAc,CACxF,GAGF,GAAmB,YAdE,EAcY,mBAMjC,IAAM,GAAe,qBAMf,CAAC,GAA4B,GAA4B,CAC7D,GAAyD,IAOrD,GAAY,KAPqD,CAOrD,GAAe,CAAC,IAR6B,gBAQT,EAEhD,GAA2B,aAC/B,CAAC,EAA6C,KAC5C,GAAM,oBAAE,WAAoB,EAAU,GAAG,EAAa,CAAI,EACpD,EAAc,GAAe,GADmB,EAE7B,SAAkC,IAAI,EACzD,EAAe,OAAe,CAAC,EAAc,GAC7C,EAAkB,KADqC,GACrC,CAAwC,IAAI,EAEpE,MACE,UAAiB,MAAhB,CACC,YAAa,GACb,UAAW,GACX,SAAS,eAET,mBAAC,IAA2B,MAAO,YAAoB,EACrD,oBAAiB,MAAhB,CACC,KAAK,cACJ,GAAG,EACH,GAAG,EACJ,IAAK,EACL,gBAAiB,OAAoB,CAAC,EAAa,gBAAiB,IAClE,EAAM,eAAe,EACrB,EAAU,SAAS,MAAM,CAAE,eAAe,CAAK,CAAC,CAClD,CAAC,EACD,qBAAsB,GAAW,EAAM,eAAe,EACtD,kBAAmB,GAAW,EAAM,eAAe,EAQnD,oBAAC,aAAW,EAAS,EAEnB,UAAC,eAAmB,EAAwB,IAEhD,CACF,GAGN,GAGF,GAAmB,YAAc,GAMjC,IAAM,GAAa,mBAMb,GAAyB,aAC7B,CAAC,EAA2C,KAC1C,GAAM,CAAE,qBAAoB,GAAG,EAAW,CAAI,EACxC,EAAc,GAAe,CADO,EAE1C,MAAO,SAD8C,CAC7B,MAAhB,CAAuB,GAAG,EAAc,GAAG,EAAY,IAAK,EAAc,CACpF,GAGF,GAAiB,YAAc,GAM/B,IAAM,GAAmB,yBAMnB,GAA+B,aAGnC,CAAC,EAAiD,KAClD,GAAM,oBAAE,EAAoB,GAAG,EAAiB,CAAI,EAC9C,EAAc,GAAe,GACnC,IAFgD,EAEzC,SAD8C,CAC7B,MAAhB,CAA6B,GAAG,EAAc,GAAG,EAAkB,IAAK,EAAc,CAChG,CAAC,EAED,GAAuB,YAAc,GAYrC,IAAM,GAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,oBAAE,EAAoB,GAAG,EAAY,CAAI,EACzC,EAAc,GAAe,EADQ,CAE3C,MAAO,SAD8C,CAC7B,MAAhB,CAAuB,GAAG,EAAc,GAAG,EAAa,IAAK,EAAc,CACrF,GAGF,GAAkB,YAdE,EAcY,kBAMhC,IAAM,GAAc,oBAKd,GAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,oBAAE,EAAoB,GAAG,EAAY,CAAI,EACzC,OADqC,IACnC,EAAU,CAAI,GAA6B,GAAa,GAC1D,EAAc,GAAe,GAC7B,EAAM,KAFsE,CAEtE,CAAe,CAAC,EAAc,GAC1C,MADmD,CAC5C,SAAiB,MAAhB,CAAuB,GAAG,EAAc,GAAG,MAAa,EAAU,CAC5E,GAGF,GAAkB,YAAc,GAQhC,IAAM,GAAwD,CAAC,YAAE,EAAW,IAC1E,IAAM,EAAU,KAAK,GAAY;;mCAAA,EAEE,GAAY,oBAAqB,GAAgB;;0JAAA,EAEsE,GAAY;;sFAAA,EAWtK,OAPM,YAAU,KACS,SAAS,eAC9B,EAAW,SAAS,aAAa,kBAAkB,IAEhC,QAAQ,KAAK,EACpC,EAAG,CAAC,EADuC,EACnB,EAEjB,IACT,EC3PA,IAAMQ,GAAqBnG,EAAAA,UAAgB,CAGzC,CAAC,WAAEC,CAAS,CAAE,CAHQkG,EAGLjG,EAAO,CAAEC,IAC1B,UAACiG,GAA4B,CAC3BnG,IAD2B,MAChBI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,yJACAJ,GAED,GAAGC,CAAK,CACTC,IAAKA,KAGTgG,GAAmB7F,WAAW,CAAG8F,GAA6B9F,KAAD,MAAY,CAEzE,CAFkB6F,GAEZE,GAAqBrG,EAAAA,UAAgB,CAGzC,CAAC,WAAEC,CAAS,CAAE,CAHQoG,EAGLnG,EAAO,CAAEC,IAC1B,WAACmG,GAAiBA,WAChB,UAACH,GAAkBA,CAAAA,EADHG,CAEhB,UAACF,GAA4B,CAC3BjG,IAD2B,EAE3BF,IAHiBkG,MAGN9F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,8fACAJ,GAED,GAAGC,CAAK,MAIfmG,IAAmB/F,WAAW,CAAG8F,GAA6B9F,KAAD,MAAY,CAAvD+F,IAEZE,GAAoB,CAAC,CACzBtG,WAAS,CACT,GAAGC,EACkC,GACrC,UAAC0D,MAAAA,CACC3D,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,mDACAJ,GAED,GAAGC,CAAK,GAGbqG,GAAkBjG,WAAW,CAAG,oBAEhC,IAAMkG,GAAoB,CAAC,WACzBvG,CAAS,CACT,GAAGC,EACkC,GACrC,UAAC0D,MAAAA,CACC3D,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gEACAJ,GAED,GAAGC,CAAK,GAGbsG,GAAkBlG,WAAW,CAAG,oBAEhC,IAAMmG,GAAmBzG,EAAAA,UAAgB,CAGvC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACiG,GAA0B,CACzBjG,EADyB,EACpBA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,wBAAyBJ,GACtC,GAAGC,CAAK,IAGbuG,GAAiBnG,WAAW,CAAG8F,GAA2B9F,GAAD,QAAY,CAErE,IAAMoG,GAAyB1G,EAAAA,UAAgB,CAG7C,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAHSwG,CAGAvG,IAC1B,UAACiG,GAAgC,CAC/BjG,IAAKA,EACLF,EAF+B,QAEpBI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAC9C,GAAGC,CAAK,GAGbwG,IAAuBpG,WAAW,CAAG8F,GAAiC9F,SAAD,EAAY,CAEjF,IAFsBoG,GAEI1G,EAAAA,UAAgB,CAGxC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACiG,GAA2B,CAC1BjG,EAD0B,EACrBA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACsG,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,GAAI1G,GAC/B,GAAGC,CAAK,GAGb0G,IAAkBtG,WAAW,CAAG8F,GAA4B9F,GAAD,QAAY,CAEvE,IAAMuG,GAAoB7G,EAAAA,UAAgB,CAGxC,CAAC,WAAEC,CAAS,CAHS4G,GAGJ3G,EAAO,CAAEC,IAC1B,UAACiG,GAA2B,CAC1BjG,EAD0B,EACrBA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACXsG,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAAC,CAAEvE,QAAS,SAAU,GACpC,eACAnC,GAED,GAAGC,CAAK,IAGb2G,GAAkBvG,WAAW,CAAG8F,GAA4B9F,GAAD,QAAY,CAAtDuG,eE/EV,SAASC,GAAU,eAAEC,CAAa,iBAAEC,CAAe,CAAkB,EAC1E,GAAM,CAAEhE,KAAMiE,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,GAC9B,QAAEpG,CAAM,CAAE,CAAGC,CAAAA,EAAAA,GAAAA,CAAAA,CAASA,GACtB,MAAEoG,CAAI,CAAE,CDxCT,SAASC,EACd,GAAM,CAAEpE,KAAMiE,CAAO,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,GAC9B,CAACC,EAAME,EAAQ,CAAGnG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,MAQ9C,MAAO,MACLiG,EACAhG,QAAS,CAAC8F,CACZ,CACF,IC2BQ,CAACK,EAAQC,EAAU,CAAGrG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAU,EAAE,EAC1C,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACsG,EAAYC,EAAc,CAAGvG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,CAACwG,EAAYC,EAAc,CAAGzG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MACtD,CAAC0G,EAAaC,EAAe,CAAG3G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACzC,CAAC4G,EAAOC,EAAS,CAAG7G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAC7B,CAAC8G,EAAeC,EAAiB,CAAG/G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAe,MAC3D,OAAEU,CAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GAEpBqG,EAAc,MAAOC,IACzB,GAAI,CACF,IAAMC,EAAM,IAAIC,IAAI,cAAeC,OAAOC,QAAQ,CAACC,MAAM,EACrDL,GACFC,EAAIK,GADM,SACM,CAACC,GAAG,CAAC,SAAUP,GAEjC,IAAM9F,EAAW,MAAMC,MAAM8F,GACvBpF,EAAO,MAAMX,EAASY,IAAI,GAEhC,GAAI,CAACkF,EAAQ,CACX,IAAMQ,EAAY3F,EAAKsE,MAAM,CAGvBsB,EAAqBD,EAAUE,SAAS,CAC5CC,GAAYC,EAAUC,IAAI,CAACC,GAAYA,EAAS9D,EAAE,GAAK2D,EAAS3D,EAAE,GAGpE,GAA2B,CAAC,IAAxByD,EAA2B,CAC7BrB,EAAUoB,GACVhB,EAAc3E,EAAK0E,UAAU,EAC7BK,EAAS/E,EAAK8E,KAAK,EACnB,MACF,CACA,IAAMoB,EAAkBP,EAAUQ,KAAK,CAAC,EAAGP,GAC3CrB,EAAU,IAAI2B,KAbI5B,EAa0B,EAC5CS,EAAS/E,EAAK8E,KAAK,EACnB,MACF,CACAP,EAAU6B,GAAQ,IAAIA,KAASpG,EAAKsE,MAAM,CAAC,EAC3CK,EAAc3E,EAAK0E,UAAU,EAC7BK,EAAS/E,EAAK8E,KAAK,CACrB,CAAE,MAAO5E,EAAO,CACdmG,QAAQnG,KAAK,CAAC,0BAA2BA,EAC3C,QAAU,CACR9B,GAAW,GACXqG,EAAc,IACdI,GAAe,EACjB,CACF,EAEMyB,EAAgB,UACpB7B,GAAc,GACd,MAAMS,GACR,EAEMqB,EAAe9D,GAAa1B,IAChC,GAAI6D,CAD0BnC,CACb,OAEjB,GAAM,cAAE+D,CAAY,WAAEC,CAAS,cAAEC,CAAY,CAAE,CAAG3F,EAAE4F,aAAa,CAEzCH,EAAeC,GADN,IAAfC,GAGkBhC,IAClCG,GAAe,GACfK,EAF8C,GAIlD,EAAG,KAMG0B,EAAe,MAAOC,IAC1B,GAAI,CACF,IAAMxH,EAAW,MAAMC,MAAM,CAAC,YAAY,EAAEuH,EAAM1E,EAAE,EAAE,CAAE,CACtD5C,OAAQ,QACV,GAEA,GAAI,CAACF,EAASU,EAAE,CAAE,CAChB,IAAMC,EAAO,MAAMX,EAASY,IAAI,GAChCrB,EAAM,CACJM,MAAO,KACPC,YAAa,EAA4Be,KAAK,CAC9Cd,QAAS,aACX,GACA,MACF,CAEAmF,EAAU6B,GAAQA,EAAKU,MAAM,CAAC/F,GAAKA,EAAEoB,EAAE,GAAK0E,EAAM1E,EAAE,GACpD4C,EAASqB,GAAQA,EAAO,GAExBxH,EAAM,CACJM,MAAO,KACPC,YAAa,OACf,GAEI6E,IAAoB6C,EAAM1E,EAAE,EAAE,EAClB,KAElB,CAAE,KAAM,CACNvD,EAAM,CACJM,MAAO,KACPC,YAAa,SACbC,QAAS,aACX,EACF,QAAU,CACR6F,EAAiB,KACnB,CACF,SAEA,EAGE,EAHE,CAGF,IAHY,EAGZ,wBACE,WAACrE,MAAAA,CAAI3D,UAAU,iCACb,WAAC2D,MAAAA,CAAI3D,UAAU,6EACb,WAAC2D,MAAAA,CAAI3D,UAAU,oCACb,UAACsD,EAAAA,CAAMA,CAAAA,CACLnB,QAAQ,QACRwC,KAAK,OACLC,QAASyE,EACT9D,SAAUgC,EACVvH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,UAAWmH,GAAc,yBAEvC,UAACxC,EAASA,CAAC/E,MAAD+E,IAAW,cAEvB,UAACK,OAAAA,CAAKpF,UAAU,iCACbkH,IAAS4C,GAAAA,EAAKA,CAACC,OAAO,CACrB,GAAGlC,EAAM,MAAM,CAAC,CAEhB,GAAGA,EAAM,CAAC,EAAEhH,GAAQmJ,WAAaC,GAAAA,CAAYA,CAACC,iBAAiB,CAAC,IAAI,CAAC,MAI3E,UAACvJ,GAAYA,CAACC,QAADD,OAAiB0I,OAGhC,UAAC1F,MAAAA,CAAI3D,UAAU,2BAA2BmK,SAAUb,WACjDpI,EACC,UAACyC,MAAAA,CAAI3D,UAAU,6CAAoC,WACjDqH,EAAOnD,MAAM,CAAG,EAClB,WAACP,MAAAA,CAAI3D,UAAU,sBACZqH,EAAO7C,GAAG,CAACoF,GACV,WAACjG,MAAAA,CAEC3D,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,mEACZ,qBACA2G,IAAoB6C,EAAM1E,EAAE,EAAI,iBAElCN,QAAS,IAAMkC,EAAc8C,aAE7B,UAACQ,GAAAA,CAAIA,CAAAA,CAACpK,UAAU,4BAChB,WAAC2D,MAAAA,CAAI3D,UAAU,4BACb,UAAC2D,MAAAA,CAAI3D,UAAU,gCAAwB4J,EAAMS,OAAO,GACpD,UAAC1G,MAAAA,CAAI3D,UAAU,iCACgC,OAA5C,IAAI6F,KAAK+D,EAAMU,SAAS,EAAEC,WAAW,GACpC,OAEA,CAAC,MAAM,EAAE,IAAI1E,KAAK+D,EAAMU,SAAS,EAAEE,cAAc,IAAI,MAI3D,UAAClH,EAAAA,CAAMA,CAAAA,CACLnB,QAAQ,QACRwC,KAAK,OACL3E,UAAU,4CACV4E,QAAS,IACPd,EAAE2G,eAAe,GACjBzC,EAAiB4B,EACnB,WAEA,UAACc,GAAAA,CAAMA,CAAAA,CAAC1K,UAAU,iCA3Bf4J,EAAM1E,EAAE,GA+BhByC,GACC,UAAChE,MAAAA,CAAI3D,UAAU,kDAAyC,eAM5D,UAAC2D,MAAAA,CAAI3D,UAAU,6CAAoC,sBAOzD,UAAC2K,GAAWA,CAAC5J,KAAM,CAAC,CAACgH,EAAe5E,WAAxBwH,EAAsC,IAAM3C,EAAiB,eACvE,WAAC5B,GAAkBA,WACjB,WAACE,GAAiBA,GADDF,QAEf,GADgBE,EAChB,KAACE,GAAgBA,UAAC,SAClB,OADiBA,EACjB,EAACC,GAAsBA,WAAC,WACbsB,GAAesC,OADH5D,CACW,oCAGpC,WAACF,GAAiBA,WAChB,GADgBA,EAChB,KAACK,GAAiBA,UAAC,OACnB,UADkBA,GACAD,CAChB3G,UAAU,gBADM2G,yBAEhB/B,QAAS,IAAMmD,GAAiB4B,EAAa5B,YAC9C,kBA5FU,IAoGvB,CCnPM,OAAW,OAAgB,CAAC,UAAY,EAC5C,CAAC,MAAQ,EAAE,EAAG,CAAU,YAAK,SAAU,EACvC,CAAC,MAAQ,EAAE,EAAG,CAAW,aAAK,SAAU,EACxC,CAAC,OAAQ,CAAE,MAAO,KAAM,CAAQ,WAAM,CAAG,KAAK,EAAG,CAAK,MAAI,CAAK,OAAK,SAAU,EAC9E,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EAC1C,ECwBM,SAAS6C,GAAY,OAAEhB,CAAK,iBAAEiB,CAAe,mBAAEC,CAAiB,CAAoB,EACzF,GAAM,CAACC,EAAUC,EAAY,CAAG/J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAY,EAAE,EAChD,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACsG,EAAYC,EAAc,CAAGvG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,CAACwG,EAAYC,EAAc,CAAGzG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MACtD,CAAC0G,EAAaC,EAAe,CAAG3G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAEzCgK,GADiBrF,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,GACTA,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAY,EAAE,EAAE,CACpC,CAACiC,EAAOC,EAAS,CAAG7G,CAAAA,EAAAA,EAAAA,QADoC,CAC5BA,CAAC,GAC7B,CAACiK,EAAiBC,EAAmB,CAAGlK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAiB,MACjE,OAAEU,CAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GAOpBwJ,EAAgB,MAAOlD,IAC3B,GAAI,CACF,IAAMC,EAAM,IAAIC,IAAI,CAAC,YAAY,EAAEwB,EAAM1E,EAAE,EAAE,CAAEmD,OAAOC,QAAQ,CAACC,MAAM,EACjEL,GACFC,EAAIK,GADM,SACM,CAACC,GAAG,CAAC,SAAUP,GAEjC,IAAM9F,EAAW,MAAMC,MAAM8F,GACvBpF,EAAO,MAAMX,EAASY,IAAI,GAEhC,GAAI,CAACkF,EAAQ,CACX,IAAMmD,EAActI,EAAKgI,QAAQ,CAC3BO,EAAcL,EAAYhF,OAAO,CAEjC0C,EAAqB0C,EAAYzC,SAAS,CAC9C2C,GAAUD,EAAYvC,IAAI,CAACyC,GAAUA,EAAOtG,EAAE,GAAKqG,EAAOrG,EAAE,GAG9D,GAA2B,CAAC,IAAxByD,EAA2B,CAC7BqC,EAAYK,GACZ3D,EAAc3E,EAAK0E,UAAU,EAC7BK,EAAS/E,EAAK8E,KAAK,EACnB,MACF,CACA,IAAM4D,EAAoBJ,EAAYnC,KAAK,CAAC,EAAGP,GAC/CqC,EAAY,IAAIS,KAAsBH,EAAY,EAClDxD,EAAS/E,EAAK8E,KAAK,EACnB,MACF,CACAmD,EAAY7B,GAAQ,IAAIA,KAASpG,EAAKgI,QAAQ,CAAC,EAC/CrD,EAAc3E,EAAK0E,UAAU,EAC7BK,EAAS/E,EAAK8E,KAAK,CACrB,CAAE,MAAO5E,EAAO,CACdmG,QAAQnG,KAAK,CAAC,4BAA6BA,EAC7C,QAAU,CACR9B,GAAW,GACXqG,GAAc,GACdI,EAAe,GACjB,CACF,EAkBMyB,EAAgB,UACpB7B,GAAc,GACd,MAAM4D,GACR,EAEM9B,EAAe9D,GAAY,IAC/B,GAAImC,CAD0BnC,CACb,OAEjB,GAAM,cAAE+D,CAAY,WAAEC,CAAS,cAAEC,CAAY,CAAE,CAAG3F,EAAE4F,aAAa,CAEzCH,EAAeC,GADN,IAAfC,GAGkBhC,IAClCG,GAAe,GACfwD,EAAc3D,GAElB,EAAG,KAEGkC,EAAe,MAAO+B,IAC1B,GAAI,CACF,IAAMtJ,EAAW,MAAMC,MAAM,CAAC,YAAY,EAAEuH,EAAM1E,EAAE,CAAC,CAAC,EAAEwG,EAAQxG,EAAE,EAAE,CAAE,CACpE5C,OAAQ,QACV,GAEA,GAAI,CAACF,EAASU,EAAE,CAAE,CAChB,IAAMC,EAAO,MAAMX,EAASY,IAAI,GAChCrB,EAAM,CACJM,MAAO,KACPC,YAAa,EAA4Be,KAAK,CAC9Cd,QAAS,aACX,GACA,MACF,CAEA6I,EAAY7B,GAAQA,EAAKU,MAAM,CAAC/F,GAAKA,EAAEoB,EAAE,GAAKwG,EAAQxG,EAAE,GACxD4C,EAASqB,GAAQA,EAAO,GAExBxH,EAAM,CACJM,MAAO,KACPC,YAAa,OACf,GAEI4I,IAAsBY,EAAQxG,EAAE,EAAE,EACpB,KAEpB,CAAE,KAAM,CACNvD,EAAM,CACJM,MAAO,KACPC,YAAa,SACbC,QAAS,aACX,EACF,QAAU,CACRgJ,EAAmB,KACrB,CACF,EAiBA,MACA,iCACE,WAACxH,MAAAA,CAAI3D,UAAU,iCACb,WAAC2D,MAAAA,CAAI3D,UAAU,6EACb,UAACsD,EAAAA,CAAMA,CAAAA,CACLnB,QAAQ,QACRwC,KAAK,OACLC,QAASyE,EACT9D,SAAUgC,EACVvH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,UAAWmH,GAAc,yBAEvC,UAACxC,EAASA,CAAC/E,MAAD+E,IAAW,cAEvB,UAACK,OAAAA,CAAKpF,UAAU,iCACb6H,EAAQ,EAAI,GAAGA,EAAM,IAAI,CAAC,CAAG,YAIlC,UAAClE,MAAAA,CAAI3D,UAAU,uBAAuBmK,SAAUb,WAC7CpI,EACC,UAACyC,MAAAA,CAAI3D,UAAU,iDAAwC,WACrD+K,EAAS7G,MAAM,CAAG,EACpB,WAACP,MAAAA,CAAI3D,UAAU,uCACZ+K,EAASvG,GAAG,CAACkH,GACZ,UAAC/H,MAAAA,CAECiB,QAAS,IAAMiG,EAAgBa,EAAQxG,EAAE,EACzClF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,8CACA0K,IAAsBY,EAAQxG,EAAE,EAAI,0BAGtC,WAACvB,MAAAA,CAAI3D,UAAU,mCACb,UAACoK,GAAAA,CAAIA,CAAAA,CAACpK,UAAU,iCAChB,WAAC2D,MAAAA,CAAI3D,UAAU,2BACb,UAAC2L,IAAAA,CAAE3L,UAAU,wCAAgC0L,EAAQE,OAAO,GAC5D,WAACjI,MAAAA,CAAI3D,UAAU,+DACb,UAACoF,OAAAA,CAAKpF,UAAU,oBAAY0L,EAAQG,YAAY,GAChD,WAACzG,OAAAA,CAAKpF,UAAU,oCACd,UAAC8L,GAAQA,CAAC9L,IAAD8L,MAAW,YACnB,IAAIjG,KAAK6F,EAAQK,WAAW,EAAEvB,cAAc,YAInD,UAAClH,EAAAA,CAAMA,CAAAA,CACHnB,QAAQ,QACRwC,KAAK,OACL3E,UAAU,4CACV4E,QAAS,IACPd,EAAE2G,eAAe,GACjBU,EAAmBO,EACrB,WAEF,UAAChB,GAAAA,CAAMA,CAAAA,CAAC1K,UAAU,mCA5BjB0L,EAAQxG,EAAE,GAiClByC,GACC,UAAChE,MAAAA,CAAI3D,UAAU,kDAAyC,eAM5D,UAAC2D,MAAAA,CAAI3D,UAAU,iDAAwC,cAM7D,UAAC2K,GAAWA,CAAC5J,KAAM,CAAC,CAACmK,EAAiB/H,WAA1BwH,EAAwC,IAAMQ,EAAmB,eAC3E,WAAC/E,GAAkBA,WACjB,WAACE,GAAiBA,GADDF,QAEf,UAACI,GAAgBA,UAAC,SAClB,OADiBA,EACjB,EAACC,GAAsBA,WAAC,WACbyE,GAAiBU,OADLnF,CACa,YAGtC,WAACF,GAAiBA,WAChB,GADgBA,EAChB,KAACK,GAAiBA,UAAC,OACnB,UAACD,GAAiBA,CACd3G,UAAU,gBADI2G,yBAEd/B,QAAS,IAAMsG,GAAmBvB,EAAauB,YAClD,iBAQX,2BC7PO,SAASc,GAAY,SAAEC,CAAO,WAAEC,CAAS,CAAoB,EAClE,GAAM,CAACR,EAASS,EAAW,CAAGlL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAiB,MACjD,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACmL,EAAUC,EAAY,CAAGpL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,QAC7CqL,EAAY1G,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAoB,MACtC,OAAE2G,CAAK,CAAE,CAAGC,CAAAA,EAAAA,GAAAA,CAAAA,CAAQA,UAuH1B,EAEI,OAFS,CAET,EAAC7I,MAAAA,CAAI3D,UAAU,iDACb,UAACyM,GAAAA,CAAOA,CAAAA,CAACzM,UAAU,2CAKpB0L,EAGH,OAHY,EAGZ,EAAC/H,MAAAA,CAAI3D,UAAU,iCACb,WAAC2D,MAAAA,CAAI3D,UAAU,qDACb,UAAC0M,KAAAA,CAAG1M,UAAU,+BAAuB0L,EAAQE,OAAO,GACpD,WAACjI,MAAAA,CAAI3D,UAAU,4CACb,WAAC2L,IAAAA,WAAE,OAAKD,EAAQG,YAAY,IAC5B,WAACF,IAAAA,WAAE,MAAI,IAAI9F,KAAK6F,EAAQK,WAAW,EAAEvB,cAAc,YAItDkB,EAAQiB,IAAI,EACX,UAAChJ,MAAAA,CAAI3D,UAAU,0CACb,WAACF,EAAUA,CACTY,MAAO0L,EACPhI,WAFStE,GAEOY,GAAU2L,EAAY3L,GACtCV,UAAU,oCAEV,WAAC2D,MAAAA,CAAI3D,UAAU,wCACb,UAACM,EAAcA,CAACI,MAAM,OAAOwE,GAAG,OAAjB5E,EACf,UAAC0E,EAAAA,CAAKA,CAAAA,CACJG,QAAQ,OACRnF,UAAU,kCACX,eAIH,WAAC2D,MAAAA,CAAI3D,UAAU,wCACb,UAACM,EAAcA,CAACI,MAAM,OAAOwE,GAAG,OAAjB5E,EACf,UAAC0E,EAAAA,CAAKA,CAAAA,CACJG,QAAQ,OACRnF,UAAU,kCACX,kBAQT,UAAC2D,MAAAA,CAAI3D,UAAU,yCACC,SAAboM,GAAuBV,EAAQiB,IAAI,CAClC,UAACC,SAAAA,CACC1M,IAAKoM,EACLtM,UAAU,yDACV6M,QAAQ,mCAGV,UAAClJ,MAAAA,CAAI3D,UAAU,2CACZ0L,EAAQoB,OAAO,QAlDL,IAwDvB,CCtMO,SAASC,KACd,GAAM,CAACC,EAAeC,EAAiB,CAAGhM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAe,MAC3D,CAAC6J,EAAmBoC,EAAqB,CAAGjM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MACpE,iBAAEY,CAAe,CAAE,CAAGC,CAAAA,EAAAA,GAAAA,CAAAA,CAAOA,GAE7BqL,EAAc,oFACdC,EAAc,8EACdC,EAAa,gDASbC,EALJ,EAA8B,SAKbC,CAJbP,EAAsB,KADH,IAEhB,IADY,GAMf3H,EAAmB,KACvBxD,EAAgBmL,GAAe3C,SAAW,GAC5C,EAEA,MACE,WAAC1G,MAAAA,CAAI3D,UAAU,4CAEb,WAAC2D,MAAAA,CAAI3D,UAAU,6DACb,WAAC2D,MAAAA,CAAI3D,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,aAAc+M,aAC/B,UAACxJ,MAAAA,CAAI3D,UAAWoN,WACd,UAACI,KAAAA,CAAGxN,UAAWqN,WAAY,WAE7B,UAAC1J,MAAAA,CAAI3D,UAAU,gCACb,UAAC6G,GAASA,CACRC,KADQD,SACO,IACboG,EAAiBrD,GACjBsD,EAAqB,KACvB,EACAnG,gBAAiBiG,GAAe9H,UAKtC,WAACvB,MAAAA,CAAI3D,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,aAAc+M,aAC/B,UAACxJ,MAAAA,CAAI3D,UAAWoN,WACd,UAACI,KAAAA,CAAGxN,UAAWqN,WACZL,EACC,WAACrJ,MAAAA,CAAI3D,UAAU,2CACb,UAACoF,OAAAA,CAAKpF,UAAU,4BAAoBgN,EAAc3C,OAAO,GACzD,UAAC1G,MAAAA,CAAI3D,UAAU,uCAAuC4E,QAASS,WAC7D,UAACC,EAAAA,CAAIA,CAAAA,CAACtF,UAAU,gBAIpB,eAILgN,GACC,UAACrJ,MAAAA,CAAI3D,UAAU,gCACb,UAAC4K,GAAWA,CACVhB,MAAOoD,CADGpC,CAEVC,gBAAiBqC,EACjBpC,kBAAmBA,SAM3B,WAACnH,MAAAA,CAAI3D,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,aAAc+M,aAC/B,UAACxJ,MAAAA,CAAI3D,UAAWoN,WACd,UAACI,KAAAA,CAAGxN,UAAWqN,WACZvC,EAAoB,OAAS,eAGjCkC,GAAiBlC,GAChB,UAACnH,MAAAA,CAAI3D,UAAU,gCACb,UAACgM,GAAWA,CACVC,OADUD,CACDgB,EAAc9H,EAAE,CACzBgH,UAAWpB,EACX2C,QAAS,IAAMP,EAAqB,gBAQ9C,UAACvJ,MAAAA,CAAI3D,UAAU,oCACb,WAAC2D,MAAAA,CAAI3D,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,SAAU+M,aACX,SAAfG,GACC,iCACE,UAAC3J,MAAAA,CAAI3D,UAAWoN,WACd,UAACI,KAAAA,CAAGxN,UAAWqN,WAAY,WAE7B,UAAC1J,MAAAA,CAAI3D,UAAU,gCACb,UAAC6G,GAASA,CACRC,KADQD,SACO,IACboG,EAAiBrD,EACnB,EACA7C,gBAAiBiG,GAAe9H,UAMxB,WAAfoI,GAA2BN,GAC1B,WAACrJ,MAAAA,CAAI3D,UAAU,iCACb,WAAC2D,MAAAA,CAAI3D,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgN,EAAa,mBAC9B,UAACM,SAAAA,CACC9I,QAAS,KACPqI,EAAiB,KACnB,EACAjN,UAAU,yCACX,aAGD,WAAC2D,MAAAA,CAAI3D,UAAU,mDACb,UAACoF,OAAAA,CAAKpF,UAAU,8CAAsCgN,EAAc3C,OAAO,GAC3E,UAAC1G,MAAAA,CAAI3D,UAAU,uCAAuC4E,QAASS,WAC7D,UAACC,EAAAA,CAAIA,CAAAA,CAACtF,UAAU,mBAItB,UAAC2D,MAAAA,CAAI3D,UAAU,gCACb,UAAC4K,GAAWA,CACVhB,MAAOoD,CADGpC,CAEVC,gBAAiBqC,EACjBpC,kBAAmBA,SAMX,YAAfwC,GAA4BN,GAAiBlC,GAC5C,WAACnH,MAAAA,CAAI3D,UAAU,iCACb,WAAC2D,MAAAA,CAAI3D,UAAWoN,YACd,UAACM,SAAAA,CACC9I,QAAS,IAAMsI,EAAqB,MACpClN,UAAU,gCACX,aAGD,UAACoF,OAAAA,CAAKpF,UAAU,+BAAsB,YAExC,UAAC2D,MAAAA,CAAI3D,UAAU,gCACb,UAACgM,GAAWA,CACVC,OADUD,CACDgB,EAAc9H,EAAE,CACzBgH,UAAWpB,EACX2C,QAAS,IAAMP,EAAqB,oBAStD,6GCtKO,SAASS,IACd,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,GAClB,QAAEhN,CAAM,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE5B,MACE,UAAC6C,MAAAA,CAAI3D,UAAU,gEACb,UAAC2D,MAAAA,CAAI3D,UAAU,6FACb,UAAC2D,MAAAA,CAAI3D,UAAU,qFACb,WAAC2D,MAAAA,CAAI3D,UAAU,kCACb,UAAC8N,KAAAA,CAAG9N,UAAU,yCAAgC,SAC9C,UAAC2L,IAAAA,CAAE3L,UAAU,sDAA6C,wBAExDa,GAAQkN,cACN,WAACpC,IAAAA,CAAE3L,UAAU,uDAA6C,WAASa,EAAOkN,YAAY,IAG1F,UAACzK,EAAAA,CAAMA,CAAAA,CACLsB,QAAS,IAAMgJ,EAAOI,IAAI,CAAC,KAC3BhO,UAAU,iCACX,iBAQb,0iCCxBO,IAAMiO,EAAU,OAAM,eAECC,IAC5B,IAAMlH,EAAU,MAAMmH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAErBnH,GAASoH,MACZC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,KAGX,IAAMC,EAAgB,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAACC,EAAAA,EAAWA,CAACC,YAAY,EAEpE,MACE,UAAC9K,MAAAA,CAAI3D,UAAU,kGACb,WAAC2D,MAAAA,CAAI3D,UAAU,iEACb,UAAC0O,EAAAA,CAAMA,CAAAA,CAAAA,GACP,WAACC,OAAAA,CAAK3O,UAAU,mBACd,UAAC+M,EAAAA,iBAAiBA,CAAAA,CAAAA,GACjB,CAACuB,GAAiB,UAACX,EAAAA,kBAAkBA,CAAAA,CAAAA,UAKhD,mLCjBM,EAAc,gCACd,EAAgB,CAAE,SAAS,EAAO,WAAY,EAAK,EAMnD,EAAa,mBAGb,CAAC,EAAY,EAAe,EAAqB,CAAI,OAAgB,CAGzE,GAGI,CAAC,EAA+B,EAA2B,CAAI,OAAkB,CACrF,EACA,CAAC,EAAqB,EA+BlB,CAAC,EAAqB,EAAqB,CAC/C,EAAkD,CAlCa,EAuC3D,EAAyB,IArCP,CAgCsC,MADb,CAMlB,CAC7B,CAAC,EAA2C,IAExC,UAAC,EAAW,SAAX,CAAoB,MAAO,EAAM,wBAChC,mBAAC,EAAW,KAAX,CAAgB,MAAO,EAAM,wBAC5B,mBAAC,GAAsB,GAAG,EAAO,IAAK,EAAc,EACtD,EACF,GAKN,EAAiB,YAAc,EAgB/B,IAAM,EAA6B,aAGjC,CAAC,EAA+C,KAChD,GAAM,yBACJ,cACA,OACA,GAAO,MACP,EACA,iBAAkB,0BAClB,EACA,wCACA,4BACA,GAA4B,EAC5B,GAAG,EACL,CAAI,EACE,EAAY,SAAoC,IAAI,EACpD,EAAe,OAAe,CAAC,EAAc,GAAG,EACpC,QAAY,CAAC,GAAG,CAC3B,EAAkB,EAAmB,CAAI,OAAoB,CAAC,CACnE,KAAM,EADoC,YAE7B,GAA2B,KACxC,SAAU,EACV,OAAQ,CACV,CAAC,EACK,CAAC,EAAkB,EAAmB,CAAU,YAAS,GACzD,CADsC,CAAwB,CAC3C,MAAc,CAAC,GAClC,EAAW,EAAc,GACzB,EAF8C,EAEtB,QAAO,GAC/B,CAAC,CADmC,CACd,EAF0B,CAEM,WAAS,CAAC,EAUtE,KAVkD,EAE5C,YAAU,KACd,IAAM,EAAO,EAAI,QACjB,GAAI,EAEF,IAFQ,GACR,EAAK,iBAAiB,EAAa,GAC5B,IAAM,EAAK,OADiC,YACjC,CAAoB,EAAa,EAEvD,EAAG,CAAC,EAAiB,EAGnB,OALqE,CAKrE,EAAC,GACC,MAAO,EACP,cACA,IAAK,OACL,mBACA,EACA,YAAmB,cACjB,GAAe,EAAoB,GACnC,CAAC,EAAmB,EAEtB,CAH8C,cACxB,EAEA,YAAY,IAAM,EAAoB,IAAI,CAAI,CAAC,EACrE,mBAA0B,cACxB,IAAM,EAAuB,GAAe,EAAY,CAAC,EACzD,CAAC,GAEH,sBAA6B,cAC3B,IAAM,EAAuB,GAAe,EAAY,CAAC,EACzD,CAAC,GAGH,mBAAC,IAAS,CAAC,IAAV,CACC,SAAU,GAA4C,IAAxB,EAA4B,GAAK,EAC/D,mBAAkB,EACjB,GAAG,EACJ,IAAK,EACL,MAAO,CAAE,QAAS,OAAQ,GAAG,EAAM,OACnC,YAAa,OAAoB,CAAC,EAAM,YAAa,KACnD,EAAgB,SAAU,CAC5B,CAAC,EACD,QAAS,OAAoB,CAAC,EAAM,QAAS,IAK3C,IAAM,EAAkB,CAAC,EAAgB,QAEzC,GAAI,EAAM,SAAW,EAAM,eAAiB,GAAmB,CAAC,EAAkB,CAChF,IAAM,EAAkB,IAAI,YAAY,EAAa,GAGrD,GAFA,EAAM,KAD4D,QAC5D,CAAc,cAAc,GAE9B,CAAC,EAAgB,SAF4B,OAE5B,CAAkB,CACrC,IAAM,EAAQ,IAAW,KAAF,CAAE,CAAO,GAAU,EAAK,SAAS,EAOxD,EAJuB,CAFJ,EAAM,KAAK,CAMnB,EAN6B,EAAK,MAAM,EAC/B,EAAM,KAAK,GAAU,EAAK,KAAO,MACD,EAAK,CAAE,EAAF,IAAE,CACzD,SAEoC,IAAI,GAAU,EAAK,IAAI,OAAQ,EAC1C,EAC7B,CACF,CAEA,EAAgB,SAAU,CAC5B,CAAC,EACD,MAN0D,CAMlD,OAAoB,CAAC,EAAM,OAAQ,IAAM,EAAoB,IAAM,CAAD,CAAC,EAInF,CAAC,EAMK,EAAY,uBAaZ,EAA6B,aACjC,CAAC,EAA0C,KACzC,GAAM,yBACJ,YACA,GAAY,SACZ,EAAS,GACT,qBACA,EACA,GAAG,EACL,CAAI,EACE,EAAS,OAAK,CAAC,EACf,EAAK,GAAa,EAClB,EAAU,EAAsB,EAAW,GAC3C,EAAmB,EAAQ,gBADuC,GAClB,EAChD,EAAW,EAAc,GAEzB,oBAFgD,CAE9C,uBAAoB,mBAAuB,EAAiB,CAAI,EASxE,OAPM,YAAU,KACd,GAAI,EAEF,OADA,EADa,EAEN,IAAM,GAEjB,EAAG,CAAC,EAAW,EAAoB,CAHZ,CAGkC,EAGvD,QALqC,EAKpC,EAAW,KAH0C,GAG1C,CAAX,CACC,MAAO,KACP,EACA,mBACA,EAEA,mBAAC,IAAS,CAAC,KAAV,CACC,SAAU,EAAmB,EAAI,GACjC,mBAAkB,EAAQ,YACzB,GAAG,EACJ,IAAK,EACL,YAAa,OAAoB,CAAC,EAAM,YAAa,IAG9C,EAEA,EAAQ,MAFG,KAEH,CAAY,EAAE,CAFX,EAAM,eAAe,CAGvC,CAAC,EACD,QAAS,OAAoB,CAAC,EAAM,QAAS,IAAM,EAAQ,YAAY,EAAE,CAAC,CAC1E,UAAW,OAAoB,CAAC,EAAM,UAAW,IAC/C,GAAI,UAAM,KAAiB,EAAM,SAAU,CACzC,EAAQ,eAAe,EACvB,MACF,CAEA,GAAI,EAAM,SAAW,EAAM,cAAe,OAE1C,IAAM,EAAc,SAqDvB,CAAe,CAA4B,EAA2B,GAAiB,MAC9F,IAAM,GARsB,EAQK,CAArB,CAA2B,CARE,EAQF,CAPnC,EADsD,IACvC,EAOyB,EAPlB,CAOqB,CANhC,cAAR,EAAsB,aAAe,iBAAuB,YAAc,GAOjF,KAAoB,aAAhB,GAA8B,CAAC,YAAa,YAAY,EAAE,SAAS,EAAG,EAAG,GACzD,KADgE,UAChF,GAAgC,CAAC,UAAW,WAAW,EAAE,SAAS,EAAG,EAAG,OACrE,CAD4E,CACpD,EAAG,EAzDW,EAAO,EAAQ,YAAa,EAAQ,GAAG,EAE1E,GAAoB,SAAhB,EAA2B,CAC7B,GAAI,EAAM,SAAW,EAAM,SAAW,EAAM,QAAU,EAAM,SAAU,OACtE,EAAM,eAAe,EAErB,IAAI,EAAiB,IADI,KAAF,CAAE,CAAO,GAAU,EAAK,SAAS,EAC7B,IAAI,GAAU,EAAK,IAAI,OAAQ,EAE1D,GAAI,OAAwB,MAAe,QAAQ,UAC1B,SAAhB,GAA0C,SAAhB,EAAwB,CACrC,OAAQ,EAAxB,GAAwB,EAAe,QAAQ,EACnD,IAAM,EAAe,EAAe,QAAQ,EAAM,aAAa,EAC/D,EAAiB,EAAQ,KACrB,SA6DX,CAAa,CAAY,GAAoB,OAC7C,EAAM,IAAO,CAAC,EAAG,IAAU,GAAO,EAAa,GAAS,EAAM,MAAM,CAAE,CAC/E,EA/D8B,EAAgB,EAAe,CAAC,EAC1C,EAAe,MAAM,EAAe,CAAC,CAC3C,CAMA,WAAW,IAAM,EAAW,GAC9B,CACF,CAAC,EAEA,OAJ6C,CAAC,CAI9C,mBAAO,EACJ,EAAS,kBAAE,EAAkB,WAAgC,MAApB,CAAyB,CAAC,EACnE,GACN,EAGN,GAGF,EAAqB,YAAc,EAKnC,IAAM,EAAuD,CAC3D,UAAW,OAAQ,QAAS,OAC5B,WAAY,OAAQ,UAAW,OAC/B,OAAQ,QAAS,KAAM,QACvB,SAAU,OAAQ,IAAK,MACzB,EAgBA,SAAS,EAAW,EAA2B,GAAgB,GAAO,IAC9D,EAA6B,SAAS,cAC5C,QAAW,KAAa,EAEtB,GAAI,IAAc,EAFgB,EAGlC,EAAU,MAAM,CAAE,cAD4B,CACd,CAAC,EAC7B,SAAS,gBAAkB,GAFe,MAIlD,CAUA,IAAM,EAAO,EACP,EAAO,OAbkD", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/moe/page.tsx?ef4b", "webpack://_N_E/|ssr?d544", "webpack://_N_E/?be25", "webpack://_N_E/?765e", "webpack://_N_E/../../../src/icons/refresh-cw.ts", "webpack://_N_E/./node_modules/.pnpm/nanoid@5.1.5/node_modules/nanoid/url-alphabet/index.js?7be2", "webpack://_N_E/./node_modules/.pnpm/nanoid@5.1.5/node_modules/nanoid/index.browser.js?b07b", "webpack://_N_E/../src/radio-group.tsx", "webpack://_N_E/../src/radio.tsx", "webpack://_N_E/../../../src/icons/circle.ts", "webpack://_N_E/./app/components/ui/radio-group.tsx", "webpack://_N_E/./app/types/email.ts", "webpack://_N_E/./app/components/emails/create-dialog.tsx", "webpack://_N_E/./app/hooks/use-throttle.ts", "webpack://_N_E/../src/alert-dialog.tsx", "webpack://_N_E/./app/components/ui/alert-dialog.tsx", "webpack://_N_E/./app/hooks/use-user-role.ts", "webpack://_N_E/./app/components/emails/email-list.tsx", "webpack://_N_E/../../../src/icons/calendar.ts", "webpack://_N_E/./app/components/emails/message-list.tsx", "webpack://_N_E/./app/components/emails/message-view.tsx", "webpack://_N_E/./app/components/emails/three-column-layout.tsx", "webpack://_N_E/./app/components/no-permission-dialog.tsx", "webpack://_N_E/./app/moe/page.tsx", "webpack://_N_E/../src/roving-focus-group.tsx"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/not-found-error\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page4 = () => import(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\moe\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\";\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'moe',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\moe\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\moe\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\";\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\";\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/moe/page\",\n        pathname: \"/moe\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "var _self___RSC_MANIFEST;\nimport \"next/dist/server/web/globals\";\nimport { adapter } from \"next/dist/server/web/adapter\";\nimport { getRender } from \"next/dist/build/webpack/loaders/next-edge-ssr-loader/render\";\nimport { IncrementalCache } from \"next/dist/server/lib/incremental-cache\";\nimport { renderToHTMLOrFlight as renderToHTML } from \"next/dist/server/app-render/app-render\";\nimport * as pageMod from \"next-app-loader?name=app%2Fmoe%2Fpage&page=%2Fmoe%2Fpage&pagePath=private-next-app-dir%2Fmoe%2Fpage.tsx&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fmoe%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/moe/page.tsx?__next_edge_ssr_entry__\";\nimport { PAGE_TYPES } from \"next/dist/lib/page-types\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nconst incrementalCacheHandler = null\nconst cacheHandlers = {\n\n};\nif (!globalThis.__nextCacheHandlers) {\n    ;\n    globalThis.__nextCacheHandlers = cacheHandlers;\n}\nconst Document = null;\nconst appMod = null;\nconst errorMod = null;\nconst error500Mod = null;\nconst sriEnabled = false\nconst isServerComponent = true\nconst dev = false\nconst serverActions = undefined\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst buildManifest = self.__BUILD_MANIFEST;\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST);\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/moe/page\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nconst subresourceIntegrityManifest = sriEnabled ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST) : undefined;\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST);\nconst interceptionRouteRewrites = maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? [];\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/moe/page\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nconst render = getRender({\n    pagesType: PAGE_TYPES.APP,\n    dev,\n    page: \"/moe/page\",\n    appMod,\n    pageMod,\n    errorMod,\n    error500Mod,\n    Document,\n    buildManifest,\n    renderToHTML,\n    reactLoadableManifest,\n    clientReferenceManifest: isServerComponent ? rscManifest : null,\n    serverActionsManifest: isServerComponent ? rscServerManifest : null,\n    serverActions: isServerComponent ? serverActions : undefined,\n    subresourceIntegrityManifest,\n    config: nextConfig,\n    buildId: process.env.__NEXT_BUILD_ID,\n    nextFontManifest,\n    incrementalCacheHandler,\n    interceptionRouteRewrites\n});\nexport const ComponentMod = pageMod;\nexport default function nHandler(opts) {\n    return adapter({\n        ...opts,\n        IncrementalCache,\n        handler: render\n    });\n}\n\n//# sourceMappingURL=edge-ssr-app.js.map", "import(/* webpackMode: \"eager\", webpackExports: [\"SignButton\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\auth\\\\sign-button.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThreeColumnLayout\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\emails\\\\three-column-layout.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"NoPermissionDialog\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\no-permission-dialog.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThemeToggle\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\theme\\\\theme-toggle.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Logo\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\ui\\\\logo.tsx\");\n", "import(/* webpackMode: \"eager\", webpackExports: [\"SignButton\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\auth\\\\sign-button.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThreeColumnLayout\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\emails\\\\three-column-layout.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"NoPermissionDialog\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\no-permission-dialog.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThemeToggle\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\theme\\\\theme-toggle.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Logo\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\ui\\\\logo.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('RefreshCw', [\n  ['path', { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' }],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n]);\n\nexport default RefreshCw;\n", "export const urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n", "/* @ts-self-types=\"./index.d.ts\" */\nimport { url<PERSON>lphabet as scopedUrlAlphabet } from './url-alphabet/index.js'\nexport { urlAlphabet } from './url-alphabet/index.js'\nexport let random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nexport let customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << Math.log2(alphabet.length - 1)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length >= size) return id\n      }\n    }\n  }\n}\nexport let customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size | 0, random)\nexport let nanoid = (size = 21) => {\n  let id = ''\n  let bytes = crypto.getRandomValues(new Uint8Array((size |= 0)))\n  while (size--) {\n    id += scopedUrlAlphabet[bytes[size] & 63]\n  }\n  return id\n}\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { Radio, RadioIndicator, createRadioScope } from './radio';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ARROW_KEYS = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];\n\n/* -------------------------------------------------------------------------------------------------\n * RadioGroup\n * -----------------------------------------------------------------------------------------------*/\nconst RADIO_GROUP_NAME = 'RadioGroup';\n\ntype ScopedProps<P> = P & { __scopeRadioGroup?: Scope };\nconst [createRadioGroupContext, createRadioGroupScope] = createContextScope(RADIO_GROUP_NAME, [\n  createRovingFocusGroupScope,\n  createRadioScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\nconst useRadioScope = createRadioScope();\n\ntype RadioGroupContextValue = {\n  name?: string;\n  required: boolean;\n  disabled: boolean;\n  value: string | null;\n  onValueChange(value: string): void;\n};\n\nconst [RadioGroupProvider, useRadioGroupContext] =\n  createRadioGroupContext<RadioGroupContextValue>(RADIO_GROUP_NAME);\n\ntype RadioGroupElement = React.ComponentRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RadioGroupProps extends PrimitiveDivProps {\n  name?: RadioGroupContextValue['name'];\n  required?: React.ComponentPropsWithoutRef<typeof Radio>['required'];\n  disabled?: React.ComponentPropsWithoutRef<typeof Radio>['disabled'];\n  dir?: RovingFocusGroupProps['dir'];\n  orientation?: RovingFocusGroupProps['orientation'];\n  loop?: RovingFocusGroupProps['loop'];\n  defaultValue?: string;\n  value?: string | null;\n  onValueChange?: RadioGroupContextValue['onValueChange'];\n}\n\nconst RadioGroup = React.forwardRef<RadioGroupElement, RadioGroupProps>(\n  (props: ScopedProps<RadioGroupProps>, forwardedRef) => {\n    const {\n      __scopeRadioGroup,\n      name,\n      defaultValue,\n      value: valueProp,\n      required = false,\n      disabled = false,\n      orientation,\n      dir,\n      loop = true,\n      onValueChange,\n      ...groupProps\n    } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? null,\n      onChange: onValueChange as (value: string | null) => void,\n      caller: RADIO_GROUP_NAME,\n    });\n\n    return (\n      <RadioGroupProvider\n        scope={__scopeRadioGroup}\n        name={name}\n        required={required}\n        disabled={disabled}\n        value={value}\n        onValueChange={setValue}\n      >\n        <RovingFocusGroup.Root\n          asChild\n          {...rovingFocusGroupScope}\n          orientation={orientation}\n          dir={direction}\n          loop={loop}\n        >\n          <Primitive.div\n            role=\"radiogroup\"\n            aria-required={required}\n            aria-orientation={orientation}\n            data-disabled={disabled ? '' : undefined}\n            dir={direction}\n            {...groupProps}\n            ref={forwardedRef}\n          />\n        </RovingFocusGroup.Root>\n      </RadioGroupProvider>\n    );\n  }\n);\n\nRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RadioGroupItem';\n\ntype RadioGroupItemElement = React.ComponentRef<typeof Radio>;\ntype RadioProps = React.ComponentPropsWithoutRef<typeof Radio>;\ninterface RadioGroupItemProps extends Omit<RadioProps, 'onCheck' | 'name'> {\n  value: string;\n}\n\nconst RadioGroupItem = React.forwardRef<RadioGroupItemElement, RadioGroupItemProps>(\n  (props: ScopedProps<RadioGroupItemProps>, forwardedRef) => {\n    const { __scopeRadioGroup, disabled, ...itemProps } = props;\n    const context = useRadioGroupContext(ITEM_NAME, __scopeRadioGroup);\n    const isDisabled = context.disabled || disabled;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    const ref = React.useRef<React.ComponentRef<typeof Radio>>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const checked = context.value === itemProps.value;\n    const isArrowKeyPressedRef = React.useRef(false);\n\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (ARROW_KEYS.includes(event.key)) {\n          isArrowKeyPressedRef.current = true;\n        }\n      };\n      const handleKeyUp = () => (isArrowKeyPressedRef.current = false);\n      document.addEventListener('keydown', handleKeyDown);\n      document.addEventListener('keyup', handleKeyUp);\n      return () => {\n        document.removeEventListener('keydown', handleKeyDown);\n        document.removeEventListener('keyup', handleKeyUp);\n      };\n    }, []);\n\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!isDisabled}\n        active={checked}\n      >\n        <Radio\n          disabled={isDisabled}\n          required={context.required}\n          checked={checked}\n          {...radioScope}\n          {...itemProps}\n          name={context.name}\n          ref={composedRefs}\n          onCheck={() => context.onValueChange(itemProps.value)}\n          onKeyDown={composeEventHandlers((event) => {\n            // According to WAI ARIA, radio groups don't activate items on enter keypress\n            if (event.key === 'Enter') event.preventDefault();\n          })}\n          onFocus={composeEventHandlers(itemProps.onFocus, () => {\n            /**\n             * Our `RovingFocusGroup` will focus the radio when navigating with arrow keys\n             * and we need to \"check\" it in that case. We click it to \"check\" it (instead\n             * of updating `context.value`) so that the radio change event fires.\n             */\n            if (isArrowKeyPressedRef.current) ref.current?.click();\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nRadioGroupItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioGroupIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'RadioGroupIndicator';\n\ntype RadioGroupIndicatorElement = React.ComponentRef<typeof RadioIndicator>;\ntype RadioIndicatorProps = React.ComponentPropsWithoutRef<typeof RadioIndicator>;\ninterface RadioGroupIndicatorProps extends RadioIndicatorProps {}\n\nconst RadioGroupIndicator = React.forwardRef<RadioGroupIndicatorElement, RadioGroupIndicatorProps>(\n  (props: ScopedProps<RadioGroupIndicatorProps>, forwardedRef) => {\n    const { __scopeRadioGroup, ...indicatorProps } = props;\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    return <RadioIndicator {...radioScope} {...indicatorProps} ref={forwardedRef} />;\n  }\n);\n\nRadioGroupIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nconst Root = RadioGroup;\nconst Item = RadioGroupItem;\nconst Indicator = RadioGroupIndicator;\n\nexport {\n  createRadioGroupScope,\n  //\n  RadioGroup,\n  RadioGroupItem,\n  RadioGroupIndicator,\n  //\n  Root,\n  Item,\n  Indicator,\n};\nexport type { RadioGroupProps, RadioGroupItemProps, RadioGroupIndicatorProps };\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Radio\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_NAME = 'Radio';\n\ntype ScopedProps<P> = P & { __scopeRadio?: Scope };\nconst [createRadioContext, createRadioScope] = createContextScope(RADIO_NAME);\n\ntype RadioContextValue = { checked: boolean; disabled?: boolean };\nconst [RadioProvider, useRadioContext] = createRadioContext<RadioContextValue>(RADIO_NAME);\n\ntype RadioElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface RadioProps extends PrimitiveButtonProps {\n  checked?: boolean;\n  required?: boolean;\n  onCheck?(): void;\n}\n\nconst Radio = React.forwardRef<RadioElement, RadioProps>(\n  (props: ScopedProps<RadioProps>, forwardedRef) => {\n    const {\n      __scopeRadio,\n      name,\n      checked = false,\n      required,\n      disabled,\n      value = 'on',\n      onCheck,\n      form,\n      ...radioProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n\n    return (\n      <RadioProvider scope={__scopeRadio} checked={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"radio\"\n          aria-checked={checked}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...radioProps}\n          ref={composedRefs}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            // radios cannot be unchecked so we only communicate a checked state\n            if (!checked) onCheck?.();\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if radio is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect radio updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <RadioBubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n          />\n        )}\n      </RadioProvider>\n    );\n  }\n);\n\nRadio.displayName = RADIO_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'RadioIndicator';\n\ntype RadioIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\nexport interface RadioIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst RadioIndicator = React.forwardRef<RadioIndicatorElement, RadioIndicatorProps>(\n  (props: ScopedProps<RadioIndicatorProps>, forwardedRef) => {\n    const { __scopeRadio, forceMount, ...indicatorProps } = props;\n    const context = useRadioContext(INDICATOR_NAME, __scopeRadio);\n    return (\n      <Presence present={forceMount || context.checked}>\n        <Primitive.span\n          data-state={getState(context.checked)}\n          data-disabled={context.disabled ? '' : undefined}\n          {...indicatorProps}\n          ref={forwardedRef}\n        />\n      </Presence>\n    );\n  }\n);\n\nRadioIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'RadioBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface RadioBubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: boolean;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst RadioBubbleInput = React.forwardRef<HTMLInputElement, RadioBubbleInputProps>(\n  (\n    {\n      __scopeRadio,\n      control,\n      checked,\n      bubbles = true,\n      ...props\n    }: ScopedProps<RadioBubbleInputProps>,\n    forwardedRef\n  ) => {\n    const ref = React.useRef<HTMLInputElement>(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n\n    // Bubble checked change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        'checked'\n      ) as PropertyDescriptor;\n      const setChecked = descriptor.set;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event('click', { bubbles });\n        setChecked.call(input, checked);\n        input.dispatchEvent(event);\n      }\n    }, [prevChecked, checked, bubbles]);\n\n    return (\n      <Primitive.input\n        type=\"radio\"\n        aria-hidden\n        defaultChecked={checked}\n        {...props}\n        tabIndex={-1}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          ...controlSize,\n          position: 'absolute',\n          pointerEvents: 'none',\n          opacity: 0,\n          margin: 0,\n        }}\n      />\n    );\n  }\n);\n\nRadioBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction getState(checked: boolean) {\n  return checked ? 'checked' : 'unchecked';\n}\n\nexport {\n  createRadioScope,\n  //\n  Radio,\n  RadioIndicator,\n};\nexport type { RadioProps };\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Circle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Circle = createLucideIcon('Circle', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n]);\n\nexport default Circle;\n", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\r\nimport { Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn(\"grid gap-2\", className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  )\r\n})\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  )\r\n})\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\r\n\r\nexport { RadioGroup, RadioGroupItem } ", "export interface ExpiryOption {\r\n  label: string\r\n  value: number\r\n}\r\n\r\nexport const EXPIRY_OPTIONS: ExpiryOption[] = [\r\n  { label: '1小时', value: 1000 * 60 * 60 },\r\n  { label: '24小时', value: 1000 * 60 * 60 * 24 },\r\n  { label: '3天', value: 1000 * 60 * 60 * 24 * 3 },\r\n  { label: '永久', value: 0 }\r\n]\r\n", "\"use client\"\r\n\r\nimport { useEffect, useState } from \"react\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\"\r\nimport { Copy, Plus, RefreshCw } from \"lucide-react\"\r\nimport { useToast } from \"@/components/ui/use-toast\"\r\nimport { nanoid } from \"nanoid\"\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\r\nimport { EXPIRY_OPTIONS } from \"@/types/email\"\r\nimport { useCopy } from \"@/hooks/use-copy\"\r\nimport { useConfig } from \"@/hooks/use-config\"\r\n\r\ninterface CreateDialogProps {\r\n  onEmailCreated: () => void\r\n}\r\n\r\nexport function CreateDialog({ onEmailCreated }: CreateDialogProps) {\r\n  const { config } = useConfig()  \r\n  const [open, setOpen] = useState(false)\r\n  const [loading, setLoading] = useState(false)\r\n  const [emailName, setEmailName] = useState(\"\")\r\n  const [currentDomain, setCurrentDomain] = useState(\"\")\r\n  const [expiryTime, setExpiryTime] = useState(EXPIRY_OPTIONS[1].value.toString())\r\n  const { toast } = useToast()\r\n  const { copyToClipboard } = useCopy()\r\n\r\n  const generateRandomName = () => setEmailName(nanoid(8))\r\n\r\n  const copyEmailAddress = () => {\r\n    copyToClipboard(`${emailName}@${currentDomain}`)\r\n  }\r\n\r\n  const createEmail = async () => {\r\n    if (!emailName.trim()) {\r\n      toast({\r\n        title: \"错误\",\r\n        description: \"请输入邮箱名\",\r\n        variant: \"destructive\"\r\n      })\r\n      return\r\n    }\r\n\r\n    setLoading(true)\r\n    try {\r\n      const response = await fetch(\"/api/emails/generate\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({\r\n          name: emailName,\r\n          domain: currentDomain,\r\n          expiryTime: parseInt(expiryTime)\r\n        })\r\n      })\r\n\r\n      if (!response.ok) {\r\n        const data = await response.json()\r\n        toast({\r\n          title: \"错误\",\r\n          description: (data as { error: string }).error,\r\n          variant: \"destructive\"\r\n        })\r\n        return\r\n      }\r\n\r\n      toast({\r\n        title: \"成功\",\r\n        description: \"已创建新的临时邮箱\"\r\n      })\r\n      onEmailCreated()\r\n      setOpen(false)\r\n      setEmailName(\"\")\r\n    } catch {\r\n      toast({\r\n        title: \"错误\",\r\n        description: \"创建邮箱失败\",\r\n        variant: \"destructive\"\r\n      })\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    if ((config?.emailDomainsArray?.length ?? 0) > 0) {\r\n      setCurrentDomain(config?.emailDomainsArray[0] ?? \"\")\r\n    }\r\n  }, [config])\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={setOpen}>\r\n      <DialogTrigger asChild>\r\n        <Button className=\"gap-2\">\r\n          <Plus className=\"w-4 h-4\" />\r\n          创建新邮箱\r\n        </Button>\r\n      </DialogTrigger>\r\n      <DialogContent>\r\n        <DialogHeader>\r\n          <DialogTitle>创建新的临时邮箱</DialogTitle>\r\n        </DialogHeader>\r\n        <div className=\"space-y-4 py-4\">\r\n          <div className=\"flex gap-2\">\r\n            <Input\r\n              value={emailName}\r\n              onChange={(e) => setEmailName(e.target.value)}\r\n              placeholder=\"输入邮箱名\"\r\n              className=\"flex-1\"\r\n            />\r\n            {(config?.emailDomainsArray?.length ?? 0) > 1 && (\r\n              <Select value={currentDomain} onValueChange={setCurrentDomain}>\r\n                <SelectTrigger className=\"w-[180px]\">\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {config?.emailDomainsArray?.map(d => (\r\n                    <SelectItem key={d} value={d}>@{d}</SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            )}\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"icon\"\r\n              onClick={generateRandomName}\r\n              type=\"button\"\r\n            >\r\n              <RefreshCw className=\"w-4 h-4\" />\r\n            </Button>\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-4\">\r\n            <Label className=\"shrink-0 text-muted-foreground\">过期时间</Label>\r\n            <RadioGroup\r\n              value={expiryTime}\r\n              onValueChange={setExpiryTime}\r\n              className=\"flex gap-6\"\r\n            >\r\n              {EXPIRY_OPTIONS.map((option) => (\r\n                <div key={option.value} className=\"flex items-center gap-2\">\r\n                  <RadioGroupItem value={option.value.toString()} id={option.value.toString()} />\r\n                  <Label htmlFor={option.value.toString()} className=\"cursor-pointer text-sm\">\r\n                    {option.label}\r\n                  </Label>\r\n                </div>\r\n              ))}\r\n            </RadioGroup>\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\r\n            <span className=\"shrink-0\">完整邮箱地址将为:</span>\r\n            {emailName ? (\r\n              <div className=\"flex items-center gap-2 min-w-0\">\r\n                <span className=\"truncate\">{`${emailName}@${currentDomain}`}</span>\r\n                <div\r\n                  className=\"shrink-0 cursor-pointer hover:text-primary transition-colors\"\r\n                  onClick={copyEmailAddress}\r\n                >\r\n                  <Copy className=\"size-4\" />\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <span className=\"text-gray-400\">...</span>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"flex justify-end gap-2\">\r\n          <Button variant=\"outline\" onClick={() => setOpen(false)} disabled={loading}>\r\n            取消\r\n          </Button>\r\n          <Button onClick={createEmail} disabled={loading}>\r\n            {loading ? \"创建中...\" : \"创建\"}\r\n          </Button>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n} ", "import { useCallback, useRef } from 'react'\r\n\r\nexport function useThrottle<T extends (...args: any[]) => void>(\r\n  fn: T,\r\n  delay: number\r\n): T {\r\n  const lastRun = useRef(Date.now())\r\n\r\n  return useCallback(\r\n    ((...args) => {\r\n      const now = Date.now()\r\n      if (now - lastRun.current >= delay) {\r\n        fn(...args)\r\n        lastRun.current = now\r\n      }\r\n    }) as T,\r\n    [fn, delay]\r\n  )\r\n} ", "import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { createDialogScope } from '@radix-ui/react-dialog';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createSlottable } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst ROOT_NAME = 'AlertDialog';\n\ntype ScopedProps<P> = P & { __scopeAlertDialog?: Scope };\nconst [createAlertDialogContext, createAlertDialogScope] = createContextScope(ROOT_NAME, [\n  createDialogScope,\n]);\nconst useDialogScope = createDialogScope();\n\ntype DialogProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Root>;\ninterface AlertDialogProps extends Omit<DialogProps, 'modal'> {}\n\nconst AlertDialog: React.FC<AlertDialogProps> = (props: ScopedProps<AlertDialogProps>) => {\n  const { __scopeAlertDialog, ...alertDialogProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Root {...dialogScope} {...alertDialogProps} modal={true} />;\n};\n\nAlertDialog.displayName = ROOT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogTrigger\n * -----------------------------------------------------------------------------------------------*/\nconst TRIGGER_NAME = 'AlertDialogTrigger';\n\ntype AlertDialogTriggerElement = React.ComponentRef<typeof DialogPrimitive.Trigger>;\ntype DialogTriggerProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Trigger>;\ninterface AlertDialogTriggerProps extends DialogTriggerProps {}\n\nconst AlertDialogTrigger = React.forwardRef<AlertDialogTriggerElement, AlertDialogTriggerProps>(\n  (props: ScopedProps<AlertDialogTriggerProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...triggerProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Trigger {...dialogScope} {...triggerProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'AlertDialogPortal';\n\ntype DialogPortalProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Portal>;\ninterface AlertDialogPortalProps extends DialogPortalProps {}\n\nconst AlertDialogPortal: React.FC<AlertDialogPortalProps> = (\n  props: ScopedProps<AlertDialogPortalProps>\n) => {\n  const { __scopeAlertDialog, ...portalProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Portal {...dialogScope} {...portalProps} />;\n};\n\nAlertDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'AlertDialogOverlay';\n\ntype AlertDialogOverlayElement = React.ComponentRef<typeof DialogPrimitive.Overlay>;\ntype DialogOverlayProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>;\ninterface AlertDialogOverlayProps extends DialogOverlayProps {}\n\nconst AlertDialogOverlay = React.forwardRef<AlertDialogOverlayElement, AlertDialogOverlayProps>(\n  (props: ScopedProps<AlertDialogOverlayProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...overlayProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Overlay {...dialogScope} {...overlayProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogOverlay.displayName = OVERLAY_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'AlertDialogContent';\n\ntype AlertDialogContentContextValue = {\n  cancelRef: React.MutableRefObject<AlertDialogCancelElement | null>;\n};\n\nconst [AlertDialogContentProvider, useAlertDialogContentContext] =\n  createAlertDialogContext<AlertDialogContentContextValue>(CONTENT_NAME);\n\ntype AlertDialogContentElement = React.ComponentRef<typeof DialogPrimitive.Content>;\ntype DialogContentProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>;\ninterface AlertDialogContentProps\n  extends Omit<DialogContentProps, 'onPointerDownOutside' | 'onInteractOutside'> {}\n\nconst Slottable = createSlottable('AlertDialogContent');\n\nconst AlertDialogContent = React.forwardRef<AlertDialogContentElement, AlertDialogContentProps>(\n  (props: ScopedProps<AlertDialogContentProps>, forwardedRef) => {\n    const { __scopeAlertDialog, children, ...contentProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const contentRef = React.useRef<AlertDialogContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    const cancelRef = React.useRef<AlertDialogCancelElement | null>(null);\n\n    return (\n      <DialogPrimitive.WarningProvider\n        contentName={CONTENT_NAME}\n        titleName={TITLE_NAME}\n        docsSlug=\"alert-dialog\"\n      >\n        <AlertDialogContentProvider scope={__scopeAlertDialog} cancelRef={cancelRef}>\n          <DialogPrimitive.Content\n            role=\"alertdialog\"\n            {...dialogScope}\n            {...contentProps}\n            ref={composedRefs}\n            onOpenAutoFocus={composeEventHandlers(contentProps.onOpenAutoFocus, (event) => {\n              event.preventDefault();\n              cancelRef.current?.focus({ preventScroll: true });\n            })}\n            onPointerDownOutside={(event) => event.preventDefault()}\n            onInteractOutside={(event) => event.preventDefault()}\n          >\n            {/**\n             * We have to use `Slottable` here as we cannot wrap the `AlertDialogContentProvider`\n             * around everything, otherwise the `DescriptionWarning` would be rendered straight away.\n             * This is because we want the accessibility checks to run only once the content is actually\n             * open and that behaviour is already encapsulated in `DialogContent`.\n             */}\n            <Slottable>{children}</Slottable>\n            {process.env.NODE_ENV === 'development' && (\n              <DescriptionWarning contentRef={contentRef} />\n            )}\n          </DialogPrimitive.Content>\n        </AlertDialogContentProvider>\n      </DialogPrimitive.WarningProvider>\n    );\n  }\n);\n\nAlertDialogContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'AlertDialogTitle';\n\ntype AlertDialogTitleElement = React.ComponentRef<typeof DialogPrimitive.Title>;\ntype DialogTitleProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>;\ninterface AlertDialogTitleProps extends DialogTitleProps {}\n\nconst AlertDialogTitle = React.forwardRef<AlertDialogTitleElement, AlertDialogTitleProps>(\n  (props: ScopedProps<AlertDialogTitleProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...titleProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Title {...dialogScope} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'AlertDialogDescription';\n\ntype AlertDialogDescriptionElement = React.ComponentRef<typeof DialogPrimitive.Description>;\ntype DialogDescriptionProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>;\ninterface AlertDialogDescriptionProps extends DialogDescriptionProps {}\n\nconst AlertDialogDescription = React.forwardRef<\n  AlertDialogDescriptionElement,\n  AlertDialogDescriptionProps\n>((props: ScopedProps<AlertDialogDescriptionProps>, forwardedRef) => {\n  const { __scopeAlertDialog, ...descriptionProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Description {...dialogScope} {...descriptionProps} ref={forwardedRef} />;\n});\n\nAlertDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogAction\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACTION_NAME = 'AlertDialogAction';\n\ntype AlertDialogActionElement = React.ComponentRef<typeof DialogPrimitive.Close>;\ntype DialogCloseProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Close>;\ninterface AlertDialogActionProps extends DialogCloseProps {}\n\nconst AlertDialogAction = React.forwardRef<AlertDialogActionElement, AlertDialogActionProps>(\n  (props: ScopedProps<AlertDialogActionProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...actionProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Close {...dialogScope} {...actionProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogAction.displayName = ACTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogCancel\n * -----------------------------------------------------------------------------------------------*/\n\nconst CANCEL_NAME = 'AlertDialogCancel';\n\ntype AlertDialogCancelElement = React.ComponentRef<typeof DialogPrimitive.Close>;\ninterface AlertDialogCancelProps extends DialogCloseProps {}\n\nconst AlertDialogCancel = React.forwardRef<AlertDialogCancelElement, AlertDialogCancelProps>(\n  (props: ScopedProps<AlertDialogCancelProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...cancelProps } = props;\n    const { cancelRef } = useAlertDialogContentContext(CANCEL_NAME, __scopeAlertDialog);\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const ref = useComposedRefs(forwardedRef, cancelRef);\n    return <DialogPrimitive.Close {...dialogScope} {...cancelProps} ref={ref} />;\n  }\n);\n\nAlertDialogCancel.displayName = CANCEL_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<AlertDialogContentElement | null>;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef }) => {\n  const MESSAGE = `\\`${CONTENT_NAME}\\` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the \\`${CONTENT_NAME}\\` by passing a \\`${DESCRIPTION_NAME}\\` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an \\`id\\` and passing the same value to the \\`aria-describedby\\` prop in \\`${CONTENT_NAME}\\`. If the description is confusing or duplicative for sighted users, you can use the \\`@radix-ui/react-visually-hidden\\` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;\n\n  React.useEffect(() => {\n    const hasDescription = document.getElementById(\n      contentRef.current?.getAttribute('aria-describedby')!\n    );\n    if (!hasDescription) console.warn(MESSAGE);\n  }, [MESSAGE, contentRef]);\n\n  return null;\n};\n\nconst Root = AlertDialog;\nconst Trigger = AlertDialogTrigger;\nconst Portal = AlertDialogPortal;\nconst Overlay = AlertDialogOverlay;\nconst Content = AlertDialogContent;\nconst Action = AlertDialogAction;\nconst Cancel = AlertDialogCancel;\nconst Title = AlertDialogTitle;\nconst Description = AlertDialogDescription;\n\nexport {\n  createAlertDialogScope,\n  //\n  AlertDialog,\n  AlertDialogTrigger,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogContent,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Action,\n  Cancel,\n  Title,\n  Description,\n};\nexport type {\n  AlertDialogProps,\n  AlertDialogTriggerProps,\n  AlertDialogPortalProps,\n  AlertDialogOverlayProps,\n  AlertDialogContentProps,\n  AlertDialogActionProps,\n  AlertDialogCancelProps,\n  AlertDialogTitleProps,\n  AlertDialogDescriptionProps,\n};\n", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nconst AlertDialog = AlertDialogPrimitive.Root\r\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\r\n\r\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\r\nconst AlertDialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\r\n\r\nconst AlertDialogContent = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPortal>\r\n    <AlertDialogOverlay />\r\n    <AlertDialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </AlertDialogPortal>\r\n))\r\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\r\n\r\nconst AlertDialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\r\n\r\nconst AlertDialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\r\n\r\nconst AlertDialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\r\n\r\nconst AlertDialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogDescription.displayName = AlertDialogPrimitive.Description.displayName\r\n\r\nconst AlertDialogAction = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Action\r\n    ref={ref}\r\n    className={cn(buttonVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\r\n\r\nconst AlertDialogCancel = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Cancel\r\n    ref={ref}\r\n    className={cn(\r\n      buttonVariants({ variant: \"outline\" }),\r\n      \"mt-2 sm:mt-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n} ", "\"use client\"\r\n\r\nimport { useSession } from \"next-auth/react\"\r\nimport { Role } from \"@/lib/permissions\"\r\nimport { useEffect, useState } from \"react\"\r\n\r\nexport function useUserRole() {\r\n  const { data: session } = useSession()\r\n  const [role, setRole] = useState<Role | null>(null)\r\n\r\n  useEffect(() => {\r\n    if (session?.user?.roles?.[0]?.name) {\r\n      setRole(session.user.roles[0].name as Role)\r\n    }\r\n  }, [session])\r\n\r\n  return {\r\n    role,\r\n    loading: !session\r\n  }\r\n} ", "\"use client\"\r\n\r\nimport { useEffect, useState } from \"react\"\r\nimport { useSession } from \"next-auth/react\"\r\nimport { CreateDialog } from \"./create-dialog\"\r\nimport { Mail, RefreshCw, Trash2 } from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { useThrottle } from \"@/hooks/use-throttle\"\r\nimport { EMAIL_CONFIG } from \"@/config\"\r\nimport { useToast } from \"@/components/ui/use-toast\"\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\"\r\nimport { ROLES } from \"@/lib/permissions\"\r\nimport { useUserRole } from \"@/hooks/use-user-role\"\r\nimport { useConfig } from \"@/hooks/use-config\"\r\n\r\ninterface Email {\r\n  id: string\r\n  address: string\r\n  createdAt: number\r\n  expiresAt: number\r\n}\r\n\r\ninterface EmailListProps {\r\n  onEmailSelect: (email: Email | null) => void\r\n  selectedEmailId?: string\r\n}\r\n\r\ninterface EmailResponse {\r\n  emails: Email[]\r\n  nextCursor: string | null\r\n  total: number\r\n}\r\n\r\nexport function EmailList({ onEmailSelect, selectedEmailId }: EmailListProps) {\r\n  const { data: session } = useSession()\r\n  const { config } = useConfig()\r\n  const { role } = useUserRole()\r\n  const [emails, setEmails] = useState<Email[]>([])\r\n  const [loading, setLoading] = useState(true)\r\n  const [refreshing, setRefreshing] = useState(false)\r\n  const [nextCursor, setNextCursor] = useState<string | null>(null)\r\n  const [loadingMore, setLoadingMore] = useState(false)\r\n  const [total, setTotal] = useState(0)\r\n  const [emailToDelete, setEmailToDelete] = useState<Email | null>(null)\r\n  const { toast } = useToast()\r\n\r\n  const fetchEmails = async (cursor?: string) => {\r\n    try {\r\n      const url = new URL(\"/api/emails\", window.location.origin)\r\n      if (cursor) {\r\n        url.searchParams.set('cursor', cursor)\r\n      }\r\n      const response = await fetch(url)\r\n      const data = await response.json() as EmailResponse\r\n      \r\n      if (!cursor) {\r\n        const newEmails = data.emails\r\n        const oldEmails = emails\r\n\r\n        const lastDuplicateIndex = newEmails.findIndex(\r\n          newEmail => oldEmails.some(oldEmail => oldEmail.id === newEmail.id)\r\n        )\r\n\r\n        if (lastDuplicateIndex === -1) {\r\n          setEmails(newEmails)\r\n          setNextCursor(data.nextCursor)\r\n          setTotal(data.total)\r\n          return\r\n        }\r\n        const uniqueNewEmails = newEmails.slice(0, lastDuplicateIndex)\r\n        setEmails([...uniqueNewEmails, ...oldEmails])\r\n        setTotal(data.total)\r\n        return\r\n      }\r\n      setEmails(prev => [...prev, ...data.emails])\r\n      setNextCursor(data.nextCursor)\r\n      setTotal(data.total)\r\n    } catch (error) {\r\n      console.error(\"Failed to fetch emails:\", error)\r\n    } finally {\r\n      setLoading(false)\r\n      setRefreshing(false)\r\n      setLoadingMore(false)\r\n    }\r\n  }\r\n\r\n  const handleRefresh = async () => {\r\n    setRefreshing(true)\r\n    await fetchEmails()\r\n  }\r\n\r\n  const handleScroll = useThrottle((e: React.UIEvent<HTMLDivElement>) => {\r\n    if (loadingMore) return\r\n\r\n    const { scrollHeight, scrollTop, clientHeight } = e.currentTarget\r\n    const threshold = clientHeight * 1.5\r\n    const remainingScroll = scrollHeight - scrollTop\r\n\r\n    if (remainingScroll <= threshold && nextCursor) {\r\n      setLoadingMore(true)\r\n      fetchEmails(nextCursor)\r\n    }\r\n  }, 200)\r\n\r\n  useEffect(() => {\r\n    if (session) fetchEmails()\r\n  }, [session])\r\n\r\n  const handleDelete = async (email: Email) => {\r\n    try {\r\n      const response = await fetch(`/api/emails/${email.id}`, {\r\n        method: \"DELETE\"\r\n      })\r\n\r\n      if (!response.ok) {\r\n        const data = await response.json()\r\n        toast({\r\n          title: \"错误\",\r\n          description: (data as { error: string }).error,\r\n          variant: \"destructive\"\r\n        })\r\n        return\r\n      }\r\n\r\n      setEmails(prev => prev.filter(e => e.id !== email.id))\r\n      setTotal(prev => prev - 1)\r\n\r\n      toast({\r\n        title: \"成功\",\r\n        description: \"邮箱已删除\"\r\n      })\r\n      \r\n      if (selectedEmailId === email.id) {\r\n        onEmailSelect(null)\r\n      }\r\n    } catch {\r\n      toast({\r\n        title: \"错误\",\r\n        description: \"删除邮箱失败\",\r\n        variant: \"destructive\"\r\n      })\r\n    } finally {\r\n      setEmailToDelete(null)\r\n    }\r\n  }\r\n\r\n  if (!session) return null\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex flex-col h-full\">\r\n        <div className=\"p-2 flex justify-between items-center border-b border-primary/20\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              onClick={handleRefresh}\r\n              disabled={refreshing}\r\n              className={cn(\"h-8 w-8\", refreshing && \"animate-spin\")}\r\n            >\r\n              <RefreshCw className=\"h-4 w-4\" />\r\n            </Button>\r\n            <span className=\"text-xs text-gray-500\">\r\n              {role === ROLES.EMPEROR ? (\r\n                `${total}/∞ 个邮箱`\r\n              ) : (\r\n                `${total}/${config?.maxEmails || EMAIL_CONFIG.MAX_ACTIVE_EMAILS} 个邮箱`\r\n              )}\r\n            </span>\r\n          </div>\r\n          <CreateDialog onEmailCreated={handleRefresh} />\r\n        </div>\r\n        \r\n        <div className=\"flex-1 overflow-auto p-2\" onScroll={handleScroll}>\r\n          {loading ? (\r\n            <div className=\"text-center text-sm text-gray-500\">加载中...</div>\r\n          ) : emails.length > 0 ? (\r\n            <div className=\"space-y-1\">\r\n              {emails.map(email => (\r\n                <div\r\n                  key={email.id}\r\n                  className={cn(\"flex items-center gap-2 p-2 rounded cursor-pointer text-sm group\",\r\n                    \"hover:bg-primary/5\",\r\n                    selectedEmailId === email.id && \"bg-primary/10\"\r\n                  )}\r\n                  onClick={() => onEmailSelect(email)}\r\n                >\r\n                  <Mail className=\"h-4 w-4 text-primary/60\" />\r\n                  <div className=\"truncate flex-1\">\r\n                    <div className=\"font-medium truncate\">{email.address}</div>\r\n                    <div className=\"text-xs text-gray-500\">\r\n                      {new Date(email.expiresAt).getFullYear() === 9999 ? (\r\n                        \"永久有效\"\r\n                      ) : (\r\n                        `过期时间: ${new Date(email.expiresAt).toLocaleString()}`\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    className=\"opacity-0 group-hover:opacity-100 h-8 w-8\"\r\n                    onClick={(e) => {\r\n                      e.stopPropagation()\r\n                      setEmailToDelete(email)\r\n                    }}\r\n                  >\r\n                    <Trash2 className=\"h-4 w-4 text-destructive\" />\r\n                  </Button>\r\n                </div>\r\n              ))}\r\n              {loadingMore && (\r\n                <div className=\"text-center text-sm text-gray-500 py-2\">\r\n                  加载更多...\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center text-sm text-gray-500\">\r\n              还没有邮箱，创建一个吧！\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <AlertDialog open={!!emailToDelete} onOpenChange={() => setEmailToDelete(null)}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>确认删除</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              确定要删除邮箱 {emailToDelete?.address} 吗？此操作将同时删除该邮箱中的所有邮件，且不可恢复。\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>取消</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              className=\"bg-destructive hover:bg-destructive/90\"\r\n              onClick={() => emailToDelete && handleDelete(emailToDelete)}\r\n            >\r\n              删除\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  )\r\n} ", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDEwaDE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('Calendar', [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n]);\n\nexport default Calendar;\n", "\"use client\"\r\n\r\nimport { useState, useEffect, useRef } from \"react\"\r\nimport {Mail, Calendar, RefreshCw, Trash2} from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { useThrottle } from \"@/hooks/use-throttle\"\r\nimport { EMAIL_CONFIG } from \"@/config\"\r\nimport { useToast } from \"@/components/ui/use-toast\"\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle\r\n} from \"@/components/ui/alert-dialog\";\r\n\r\ninterface Message {\r\n  id: string\r\n  from_address: string\r\n  subject: string\r\n  received_at: number\r\n}\r\n\r\ninterface MessageListProps {\r\n  email: {\r\n    id: string\r\n    address: string\r\n  }\r\n  onMessageSelect: (messageId: string | null) => void\r\n  selectedMessageId?: string | null\r\n}\r\n\r\ninterface MessageResponse {\r\n  messages: Message[]\r\n  nextCursor: string | null\r\n  total: number\r\n}\r\n\r\nexport function MessageList({ email, onMessageSelect, selectedMessageId }: MessageListProps) {\r\n  const [messages, setMessages] = useState<Message[]>([])\r\n  const [loading, setLoading] = useState(true)\r\n  const [refreshing, setRefreshing] = useState(false)\r\n  const [nextCursor, setNextCursor] = useState<string | null>(null)\r\n  const [loadingMore, setLoadingMore] = useState(false)\r\n  const pollTimeoutRef = useRef<Timer>()\r\n  const messagesRef = useRef<Message[]>([]) // 添加 ref 来追踪最新的消息列表\r\n  const [total, setTotal] = useState(0)\r\n  const [messageToDelete, setMessageToDelete] = useState<Message | null>(null)\r\n  const { toast } = useToast()\r\n\r\n  // 当 messages 改变时更新 ref\r\n  useEffect(() => {\r\n    messagesRef.current = messages\r\n  }, [messages])\r\n\r\n  const fetchMessages = async (cursor?: string) => {\r\n    try {\r\n      const url = new URL(`/api/emails/${email.id}`, window.location.origin)\r\n      if (cursor) {\r\n        url.searchParams.set('cursor', cursor)\r\n      }\r\n      const response = await fetch(url)\r\n      const data = await response.json() as MessageResponse\r\n      \r\n      if (!cursor) {\r\n        const newMessages = data.messages\r\n        const oldMessages = messagesRef.current\r\n\r\n        const lastDuplicateIndex = newMessages.findIndex(\r\n          newMsg => oldMessages.some(oldMsg => oldMsg.id === newMsg.id)\r\n        )\r\n\r\n        if (lastDuplicateIndex === -1) {\r\n          setMessages(newMessages)\r\n          setNextCursor(data.nextCursor)\r\n          setTotal(data.total)\r\n          return\r\n        }\r\n        const uniqueNewMessages = newMessages.slice(0, lastDuplicateIndex)\r\n        setMessages([...uniqueNewMessages, ...oldMessages])\r\n        setTotal(data.total)\r\n        return\r\n      }\r\n      setMessages(prev => [...prev, ...data.messages])\r\n      setNextCursor(data.nextCursor)\r\n      setTotal(data.total)\r\n    } catch (error) {\r\n      console.error(\"Failed to fetch messages:\", error)\r\n    } finally {\r\n      setLoading(false)\r\n      setRefreshing(false)\r\n      setLoadingMore(false)\r\n    }\r\n  }\r\n\r\n  const startPolling = () => {\r\n    stopPolling()\r\n    pollTimeoutRef.current = setInterval(() => {\r\n      if (!refreshing && !loadingMore) {\r\n        fetchMessages()\r\n      }\r\n    }, EMAIL_CONFIG.POLL_INTERVAL)\r\n  }\r\n\r\n  const stopPolling = () => {\r\n    if (pollTimeoutRef.current) {\r\n      clearInterval(pollTimeoutRef.current)\r\n      pollTimeoutRef.current = undefined\r\n    }\r\n  }\r\n\r\n  const handleRefresh = async () => {\r\n    setRefreshing(true)\r\n    await fetchMessages()\r\n  }\r\n\r\n  const handleScroll = useThrottle((e: React.UIEvent<HTMLDivElement>) => {\r\n    if (loadingMore) return\r\n\r\n    const { scrollHeight, scrollTop, clientHeight } = e.currentTarget\r\n    const threshold = clientHeight * 1.5\r\n    const remainingScroll = scrollHeight - scrollTop\r\n\r\n    if (remainingScroll <= threshold && nextCursor) {\r\n      setLoadingMore(true)\r\n      fetchMessages(nextCursor)\r\n    }\r\n  }, 200)\r\n\r\n  const handleDelete = async (message: Message) => {\r\n    try {\r\n      const response = await fetch(`/api/emails/${email.id}/${message.id}`, {\r\n        method: \"DELETE\"\r\n      })\r\n\r\n      if (!response.ok) {\r\n        const data = await response.json()\r\n        toast({\r\n          title: \"错误\",\r\n          description: (data as { error: string }).error,\r\n          variant: \"destructive\"\r\n        })\r\n        return\r\n      }\r\n\r\n      setMessages(prev => prev.filter(e => e.id !== message.id))\r\n      setTotal(prev => prev - 1)\r\n\r\n      toast({\r\n        title: \"成功\",\r\n        description: \"邮件已删除\"\r\n      })\r\n\r\n      if (selectedMessageId === message.id) {\r\n        onMessageSelect(null)\r\n      }\r\n    } catch {\r\n      toast({\r\n        title: \"错误\",\r\n        description: \"删除邮件失败\",\r\n        variant: \"destructive\"\r\n      })\r\n    } finally {\r\n      setMessageToDelete(null)\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (!email.id) {\r\n      return\r\n    }\r\n    setLoading(true)\r\n    setNextCursor(null)\r\n    fetchMessages()\r\n    startPolling() \r\n\r\n    return () => {\r\n      stopPolling() \r\n    }\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [email.id])\r\n\r\n  return (\r\n  <>\r\n    <div className=\"h-full flex flex-col\">\r\n      <div className=\"p-2 flex justify-between items-center border-b border-primary/20\">\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          onClick={handleRefresh}\r\n          disabled={refreshing}\r\n          className={cn(\"h-8 w-8\", refreshing && \"animate-spin\")}\r\n        >\r\n          <RefreshCw className=\"h-4 w-4\" />\r\n        </Button>\r\n        <span className=\"text-xs text-gray-500\">\r\n          {total > 0 ? `${total} 封邮件` : \"暂无邮件\"}\r\n        </span>\r\n      </div>\r\n\r\n      <div className=\"flex-1 overflow-auto\" onScroll={handleScroll}>\r\n        {loading ? (\r\n          <div className=\"p-4 text-center text-sm text-gray-500\">加载中...</div>\r\n        ) : messages.length > 0 ? (\r\n          <div className=\"divide-y divide-primary/10\">\r\n            {messages.map(message => (\r\n              <div\r\n                key={message.id}\r\n                onClick={() => onMessageSelect(message.id)}\r\n                className={cn(\r\n                  \"p-3 hover:bg-primary/5 cursor-pointer group\",\r\n                  selectedMessageId === message.id && \"bg-primary/10\"\r\n                )}\r\n              >\r\n                <div className=\"flex items-start gap-3\">\r\n                  <Mail className=\"w-4 h-4 text-primary/60 mt-1\" />\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"font-medium text-sm truncate\">{message.subject}</p>\r\n                    <div className=\"mt-1 flex items-center gap-2 text-xs text-gray-500\">\r\n                      <span className=\"truncate\">{message.from_address}</span>\r\n                      <span className=\"flex items-center gap-1\">\r\n                        <Calendar className=\"w-3 h-3\" />\r\n                        {new Date(message.received_at).toLocaleString()}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                  <Button\r\n                      variant=\"ghost\"\r\n                      size=\"icon\"\r\n                      className=\"opacity-0 group-hover:opacity-100 h-8 w-8\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation()\r\n                        setMessageToDelete(message)\r\n                      }}\r\n                  >\r\n                    <Trash2 className=\"h-4 w-4 text-destructive\" />\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            ))}\r\n            {loadingMore && (\r\n              <div className=\"text-center text-sm text-gray-500 py-2\">\r\n                加载更多...\r\n              </div>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <div className=\"p-4 text-center text-sm text-gray-500\">\r\n            暂无邮件\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n    <AlertDialog open={!!messageToDelete} onOpenChange={() => setMessageToDelete(null)}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>确认删除</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            确定要删除邮件 {messageToDelete?.subject} 吗？\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel>取消</AlertDialogCancel>\r\n          <AlertDialogAction\r\n              className=\"bg-destructive hover:bg-destructive/90\"\r\n              onClick={() => messageToDelete && handleDelete(messageToDelete)}\r\n          >\r\n            删除\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  </>\r\n  )\r\n} ", "\"use client\"\r\n\r\nimport { useState, useEffect, useRef } from \"react\"\r\nimport { Loader2 } from \"lucide-react\"\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport { useTheme } from \"next-themes\"\r\n\r\ninterface Message {\r\n  id: string\r\n  from_address: string\r\n  subject: string\r\n  content: string\r\n  html: string | null\r\n  received_at: number\r\n}\r\n\r\ninterface MessageViewProps {\r\n  emailId: string\r\n  messageId: string\r\n  onClose: () => void\r\n}\r\n\r\ntype ViewMode = \"html\" | \"text\"\r\n\r\nexport function MessageView({ emailId, messageId }: MessageViewProps) {\r\n  const [message, setMessage] = useState<Message | null>(null)\r\n  const [loading, setLoading] = useState(true)\r\n  const [viewMode, setViewMode] = useState<ViewMode>(\"html\")\r\n  const iframeRef = useRef<HTMLIFrameElement>(null)\r\n  const { theme } = useTheme()\r\n\r\n  useEffect(() => {\r\n    const fetchMessage = async () => {\r\n      try {\r\n        const response = await fetch(`/api/emails/${emailId}/${messageId}`)\r\n        const data = await response.json() as { message: Message }\r\n        setMessage(data.message)\r\n        if (!data.message.html) {\r\n          setViewMode(\"text\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Failed to fetch message:\", error)\r\n      } finally {\r\n        setLoading(false)\r\n      }\r\n    }\r\n\r\n    fetchMessage()\r\n  }, [emailId, messageId])\r\n\r\n  const updateIframeContent = () => {\r\n    if (viewMode === \"html\" && message?.html && iframeRef.current) {\r\n      const iframe = iframeRef.current\r\n      const doc = iframe.contentDocument || iframe.contentWindow?.document\r\n\r\n      if (doc) {\r\n        doc.open()\r\n        doc.write(`\r\n          <!DOCTYPE html>\r\n          <html>\r\n            <head>\r\n              <base target=\"_blank\">\r\n              <style>\r\n                html, body {\r\n                  margin: 0;\r\n                  padding: 0;\r\n                  min-height: 100%;\r\n                  font-family: system-ui, -apple-system, sans-serif;\r\n                  color: ${theme === 'dark' ? '#fff' : '#000'};\r\n                  background: ${theme === 'dark' ? '#1a1a1a' : '#fff'};\r\n                }\r\n                body {\r\n                  padding: 20px;\r\n                }\r\n                img {\r\n                  max-width: 100%;\r\n                  height: auto;\r\n                }\r\n                a {\r\n                  color: #2563eb;\r\n                }\r\n                /* 滚动条样式 */\r\n                ::-webkit-scrollbar {\r\n                  width: 6px;\r\n                  height: 6px;\r\n                }\r\n                ::-webkit-scrollbar-track {\r\n                  background: transparent;\r\n                }\r\n                ::-webkit-scrollbar-thumb {\r\n                  background: ${theme === 'dark'\r\n                    ? 'rgba(130, 109, 217, 0.3)'\r\n                    : 'rgba(130, 109, 217, 0.2)'};\r\n                  border-radius: 9999px;\r\n                  transition: background-color 0.2s;\r\n                }\r\n                ::-webkit-scrollbar-thumb:hover {\r\n                  background: ${theme === 'dark'\r\n                    ? 'rgba(130, 109, 217, 0.5)'\r\n                    : 'rgba(130, 109, 217, 0.4)'};\r\n                }\r\n                /* Firefox 滚动条 */\r\n                * {\r\n                  scrollbar-width: thin;\r\n                  scrollbar-color: ${theme === 'dark'\r\n                    ? 'rgba(130, 109, 217, 0.3) transparent'\r\n                    : 'rgba(130, 109, 217, 0.2) transparent'};\r\n                }\r\n              </style>\r\n            </head>\r\n            <body>${message.html}</body>\r\n          </html>\r\n        `)\r\n        doc.close()\r\n\r\n        // 更新高度以填充容器\r\n        const updateHeight = () => {\r\n          const container = iframe.parentElement\r\n          if (container) {\r\n            iframe.style.height = `${container.clientHeight}px`\r\n          }\r\n        }\r\n\r\n        updateHeight()\r\n        window.addEventListener('resize', updateHeight)\r\n\r\n        // 监听内容变化\r\n        const resizeObserver = new ResizeObserver(updateHeight)\r\n        resizeObserver.observe(doc.body)\r\n\r\n        // 监听图片加载\r\n        doc.querySelectorAll('img').forEach((img: HTMLImageElement) => {\r\n          img.onload = updateHeight\r\n        })\r\n\r\n        return () => {\r\n          window.removeEventListener('resize', updateHeight)\r\n          resizeObserver.disconnect()\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 监听主题变化和内容变化\r\n  useEffect(() => {\r\n    updateIframeContent()\r\n  }, [message?.html, viewMode, theme])\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-32\">\r\n        <Loader2 className=\"w-5 h-5 animate-spin text-primary/60\" />\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (!message) return null\r\n\r\n  return (\r\n    <div className=\"h-full flex flex-col\">\r\n      <div className=\"p-4 space-y-3 border-b border-primary/20\">\r\n        <h3 className=\"text-base font-bold\">{message.subject}</h3>\r\n        <div className=\"text-xs text-gray-500 space-y-1\">\r\n          <p>发件人：{message.from_address}</p>\r\n          <p>时间：{new Date(message.received_at).toLocaleString()}</p>\r\n        </div>\r\n      </div>\r\n      \r\n      {message.html && (\r\n        <div className=\"border-b border-primary/20 p-2\">\r\n          <RadioGroup\r\n            value={viewMode}\r\n            onValueChange={(value) => setViewMode(value as ViewMode)}\r\n            className=\"flex items-center gap-4\"\r\n          >\r\n            <div className=\"flex items-center space-x-2\">\r\n              <RadioGroupItem value=\"html\" id=\"html\" />\r\n              <Label \r\n                htmlFor=\"html\" \r\n                className=\"text-xs cursor-pointer\"\r\n              >\r\n                HTML 格式\r\n              </Label>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <RadioGroupItem value=\"text\" id=\"text\" />\r\n              <Label \r\n                htmlFor=\"text\" \r\n                className=\"text-xs cursor-pointer\"\r\n              >\r\n                纯文本格式\r\n              </Label>\r\n            </div>\r\n          </RadioGroup>\r\n        </div>\r\n      )}\r\n      \r\n      <div className=\"flex-1 overflow-auto relative\">\r\n        {viewMode === \"html\" && message.html ? (\r\n          <iframe\r\n            ref={iframeRef}\r\n            className=\"absolute inset-0 w-full h-full border-0 bg-transparent\"\r\n            sandbox=\"allow-same-origin allow-popups\"\r\n          />\r\n        ) : (\r\n          <div className=\"p-4 text-sm whitespace-pre-wrap\">\r\n            {message.content}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  )\r\n} ", "\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { EmailList } from \"./email-list\"\r\nimport { MessageList } from \"./message-list\"\r\nimport { MessageView } from \"./message-view\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { useCopy } from \"@/hooks/use-copy\"\r\nimport { Copy } from \"lucide-react\"\r\n\r\ninterface Email {\r\n  id: string\r\n  address: string\r\n}\r\n\r\nexport function ThreeColumnLayout() {\r\n  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null)\r\n  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null)\r\n  const { copyToClipboard } = useCopy()\r\n\r\n  const columnClass = \"border-2 border-primary/20 bg-background rounded-lg overflow-hidden flex flex-col\"\r\n  const headerClass = \"p-2 border-b-2 border-primary/20 flex items-center justify-between shrink-0\"\r\n  const titleClass = \"text-sm font-bold px-2 w-full overflow-hidden\"\r\n\r\n  // 移动端视图逻辑\r\n  const getMobileView = () => {\r\n    if (selectedMessageId) return \"message\"\r\n    if (selectedEmail) return \"emails\"\r\n    return \"list\"\r\n  }\r\n\r\n  const mobileView = getMobileView()\r\n\r\n  const copyEmailAddress = () => {\r\n    copyToClipboard(selectedEmail?.address || \"\")\r\n  }\r\n\r\n  return (\r\n    <div className=\"pb-5 pt-20 h-full flex flex-col\">\r\n      {/* 桌面端三栏布局 */}\r\n      <div className=\"hidden lg:grid grid-cols-12 gap-4 h-full min-h-0\">\r\n        <div className={cn(\"col-span-3\", columnClass)}>\r\n          <div className={headerClass}>\r\n            <h2 className={titleClass}>我的邮箱</h2>\r\n          </div>\r\n          <div className=\"flex-1 overflow-auto\">\r\n            <EmailList\r\n              onEmailSelect={(email) => {\r\n                setSelectedEmail(email)\r\n                setSelectedMessageId(null)\r\n              }}\r\n              selectedEmailId={selectedEmail?.id}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className={cn(\"col-span-4\", columnClass)}>\r\n          <div className={headerClass}>\r\n            <h2 className={titleClass}>\r\n              {selectedEmail ? (\r\n                <div className=\"w-full flex items-center gap-2\">\r\n                  <span className=\"truncate min-w-0\">{selectedEmail.address}</span>\r\n                  <div className=\"shrink-0 cursor-pointer text-primary\" onClick={copyEmailAddress}>\r\n                    <Copy className=\"size-4\" />\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                \"选择邮箱查看消息\"\r\n              )}\r\n            </h2>\r\n          </div>\r\n          {selectedEmail && (\r\n            <div className=\"flex-1 overflow-auto\">\r\n              <MessageList\r\n                email={selectedEmail}\r\n                onMessageSelect={setSelectedMessageId}\r\n                selectedMessageId={selectedMessageId}\r\n              />\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div className={cn(\"col-span-5\", columnClass)}>\r\n          <div className={headerClass}>\r\n            <h2 className={titleClass}>\r\n              {selectedMessageId ? \"邮件内容\" : \"选择邮件查看详情\"}\r\n            </h2>\r\n          </div>\r\n          {selectedEmail && selectedMessageId && (\r\n            <div className=\"flex-1 overflow-auto\">\r\n              <MessageView\r\n                emailId={selectedEmail.id}\r\n                messageId={selectedMessageId}\r\n                onClose={() => setSelectedMessageId(null)}\r\n              />\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 移动端单栏布局 */}\r\n      <div className=\"lg:hidden h-full min-h-0\">\r\n        <div className={cn(\"h-full\", columnClass)}>\r\n          {mobileView === \"list\" && (\r\n            <>\r\n              <div className={headerClass}>\r\n                <h2 className={titleClass}>我的邮箱</h2>\r\n              </div>\r\n              <div className=\"flex-1 overflow-auto\">\r\n                <EmailList\r\n                  onEmailSelect={(email) => {\r\n                    setSelectedEmail(email)\r\n                  }}\r\n                  selectedEmailId={selectedEmail?.id}\r\n                />\r\n              </div>\r\n            </>\r\n          )}\r\n\r\n          {mobileView === \"emails\" && selectedEmail && (\r\n            <div className=\"h-full flex flex-col\">\r\n              <div className={cn(headerClass, \"gap-2\")}>\r\n                <button\r\n                  onClick={() => {\r\n                    setSelectedEmail(null)\r\n                  }}\r\n                  className=\"text-sm text-primary shrink-0\"\r\n                >\r\n                  ← 返回邮箱列表\r\n                </button>\r\n                <div className=\"flex-1 flex items-center gap-2 min-w-0\">\r\n                  <span className=\"truncate min-w-0 flex-1 text-right\">{selectedEmail.address}</span>\r\n                  <div className=\"shrink-0 cursor-pointer text-primary\" onClick={copyEmailAddress}>\r\n                    <Copy className=\"size-4\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex-1 overflow-auto\">\r\n                <MessageList\r\n                  email={selectedEmail}\r\n                  onMessageSelect={setSelectedMessageId}\r\n                  selectedMessageId={selectedMessageId}\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {mobileView === \"message\" && selectedEmail && selectedMessageId && (\r\n            <div className=\"h-full flex flex-col\">\r\n              <div className={headerClass}>\r\n                <button\r\n                  onClick={() => setSelectedMessageId(null)}\r\n                  className=\"text-sm text-primary\"\r\n                >\r\n                  ← 返回消息列表\r\n                </button>\r\n                <span className=\"text-sm font-medium\">邮件内容</span>\r\n              </div>\r\n              <div className=\"flex-1 overflow-auto\">\r\n                <MessageView\r\n                  emailId={selectedEmail.id}\r\n                  messageId={selectedMessageId}\r\n                  onClose={() => setSelectedMessageId(null)}\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n} ", "\"use client\"\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { useRouter } from \"next/navigation\"\r\nimport { useConfig } from \"@/hooks/use-config\"\r\nexport function NoPermissionDialog() {\r\n  const router = useRouter()\r\n  const { config } = useConfig()\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-background/50 backdrop-blur-sm z-50\">\r\n      <div className=\"fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] w-[90%] max-w-md\">\r\n        <div className=\"bg-background border-2 border-primary/20 rounded-lg p-6 md:p-12 shadow-lg\">\r\n          <div className=\"text-center space-y-4\">\r\n            <h1 className=\"text-xl md:text-2xl font-bold\">权限不足</h1>\r\n            <p className=\"text-sm md:text-base text-muted-foreground\">你没有权限访问此页面，请联系网站管理员</p>\r\n            {\r\n              config?.adminContact && (\r\n                <p className=\"text-sm md:text-base text-muted-foreground\">管理员联系方式：{config.adminContact}</p>\r\n              )\r\n            }\r\n            <Button \r\n              onClick={() => router.push(\"/\")}\r\n              className=\"mt-4 w-full md:w-auto\"\r\n            >\r\n              返回首页\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n} ", "import { Header } from \"@/components/layout/header\"\r\nimport { ThreeColumnLayout } from \"@/components/emails/three-column-layout\"\r\nimport { NoPermissionDialog } from \"@/components/no-permission-dialog\"\r\nimport { auth } from \"@/lib/auth\"\r\nimport { redirect } from \"next/navigation\"\r\nimport { checkPermission } from \"@/lib/auth\"\r\nimport { PERMISSIONS } from \"@/lib/permissions\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nexport default async function MoePage() {\r\n  const session = await auth()\r\n  \r\n  if (!session?.user) {\r\n    redirect(\"/\")\r\n  }\r\n\r\n  const hasPermission = await checkPermission(PERMISSIONS.MANAGE_EMAIL)\r\n\r\n  return (\r\n    <div className=\"bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 h-screen\">\r\n      <div className=\"container mx-auto h-full px-4 lg:px-8 max-w-[1600px]\">\r\n        <Header />\r\n        <main className=\"h-full\">\r\n          <ThreeColumnLayout />\r\n          {!hasPermission && <NoPermissionDialog />}\r\n        </main>\r\n      </div>\r\n    </div>\r\n  )\r\n} ", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n"], "names": ["INDICATOR_NAME", "createContextScope", "jsx", "Root", "Primitive", "<PERSON><PERSON>", "useComposedRefs", "composeEventHandlers", "RadioGroup", "React", "className", "props", "ref", "RadioGroupPrimitive", "cn", "displayName", "RadioGroupItem", "Circle", "EXPIRY_OPTIONS", "label", "value", "CreateDialog", "onEmailCreated", "config", "useConfig", "open", "<PERSON><PERSON><PERSON>", "useState", "loading", "setLoading", "emailName", "setEmailName", "currentDomain", "setCurrentDomain", "expiryTime", "setExpiryTime", "toString", "toast", "useToast", "copyToClipboard", "useCopy", "createEmail", "trim", "title", "description", "variant", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "name", "domain", "parseInt", "ok", "data", "json", "error", "Dialog", "onOpenChange", "DialogTrigger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Plus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "div", "Input", "onChange", "e", "target", "placeholder", "emailDomainsArray", "length", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "map", "d", "SelectItem", "size", "onClick", "generateRandomName", "nanoid", "RefreshCw", "Label", "option", "id", "htmlFor", "span", "copyEmail<PERSON>ddress", "Copy", "disabled", "useThrottle", "fn", "delay", "lastRun", "useRef", "Date", "now", "useCallback", "args", "current", "AlertDialogOverlay", "AlertDialogPrimitive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogPortal", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogTitle", "AlertDialogDescription", "buttonVariants", "AlertDialogAction", "AlertDialogCancel", "EmailList", "onEmailSelect", "selectedEmailId", "session", "useSession", "role", "useUserRole", "setRole", "emails", "setEmails", "refreshing", "setRefreshing", "nextCursor", "setNextCursor", "loadingMore", "setLoadingMore", "total", "setTotal", "emailToDelete", "setEmailToDelete", "fetchEmails", "cursor", "url", "URL", "window", "location", "origin", "searchParams", "set", "newEmails", "lastDuplicateIndex", "findIndex", "newEmail", "oldEmails", "some", "oldEmail", "uniqueNewEmails", "slice", "prev", "console", "handleRefresh", "handleScroll", "scrollHeight", "scrollTop", "clientHeight", "currentTarget", "handleDelete", "email", "filter", "ROLES", "EMPEROR", "maxEmails", "EMAIL_CONFIG", "MAX_ACTIVE_EMAILS", "onScroll", "Mail", "address", "expiresAt", "getFullYear", "toLocaleString", "stopPropagation", "Trash2", "AlertDialog", "MessageList", "onMessageSelect", "selectedMessageId", "messages", "setMessages", "messagesRef", "messageToDelete", "setMessageToDelete", "fetchMessages", "newMessages", "oldMessages", "newMsg", "oldMsg", "uniqueNewMessages", "message", "p", "subject", "from_address", "Calendar", "received_at", "MessageView", "emailId", "messageId", "setMessage", "viewMode", "setViewMode", "iframeRef", "theme", "useTheme", "Loader2", "h3", "html", "iframe", "sandbox", "content", "ThreeColumnLayout", "selectedEmail", "setSelectedEmail", "setSelectedMessageId", "columnClass", "headerClass", "titleClass", "mobileView", "getMobileView", "h2", "onClose", "button", "NoPermissionDialog", "router", "useRouter", "h1", "adminContact", "push", "runtime", "MoePage", "auth", "user", "redirect", "hasPermission", "checkPermission", "PERMISSIONS", "MANAGE_EMAIL", "Header", "main"], "sourceRoot": "", "ignoreList": [7, 8]}