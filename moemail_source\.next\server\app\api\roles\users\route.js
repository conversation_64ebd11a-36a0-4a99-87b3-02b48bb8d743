(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[272],{5521:e=>{"use strict";e.exports=require("node:async_hooks")},5356:e=>{"use strict";e.exports=require("node:buffer")},9212:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>b,default:()=>k});var s,a={};r.r(a),r.d(a,{POST:()=>g,runtime:()=>_});var i={};r.r(i),r.d(i,{patchFetch:()=>v,routeModule:()=>x,serverHooks:()=>h,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>q});var o=r(793),n=r(6590),l=r(3439),c=r(4651),d=r(6292),u=r(8498),m=r(615),p=r(9230),f=r(9066);let _="edge";async function g(e){try{let{searchText:t}=await e.json();if(!t)return Response.json({error:"请提供用户名或邮箱地址"},{status:400});let r=(0,m.d)(),s=await r.query.users.findFirst({where:t.includes("@")?(0,f.eq)(p.users.email,t):(0,f.eq)(p.users.username,t),with:{userRoles:{with:{role:!0}}}});if(!s)return Response.json({error:"未找到用户"},{status:404});return Response.json({user:{id:s.id,name:s.name,username:s.username,email:s.email,role:s.userRoles[0]?.role.name}})}catch(e){return console.error("Failed to find user:",e),Response.json({error:"查询用户失败"},{status:500})}}let x=new c.AppRouteRouteModule({definition:{kind:d.A.APP_ROUTE,page:"/api/roles/users/route",pathname:"/api/roles/users",filename:"route",bundlePath:"app/api/roles/users/route"},resolvedPagePath:"F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\roles\\users\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:y,workUnitAsyncStorage:q,serverHooks:h}=x;function v(){return(0,u.V5)({workAsyncStorage:y,workUnitAsyncStorage:q})}let I=null==(s=self.__RSC_MANIFEST)?void 0:s["/api/roles/users/route"],D=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);I&&D&&(0,n.fQ)({page:"/api/roles/users/route",clientReferenceManifest:I,serverActionsManifest:D,serverModuleMap:(0,o.e)({serverActionsManifest:D})});let b=i,k=l.s.wrap(x,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\Mail\\moemail_source",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\Mail\\moemail_source"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\Mail\\moemail_source\\next.config.ts",configFileName:"next.config.ts"}})},7032:()=>{},6760:()=>{},615:(e,t,r)=>{"use strict";r.d(t,{d:()=>o});var s=r(888),a=r(7384),i=r(9230);let o=()=>(0,a.f)((0,s.getRequestContext)().env.DB,{schema:i})},9230:(e,t,r)=>{"use strict";r.r(t),r.d(t,{accounts:()=>d,apiKeys:()=>g,apiKeysRelations:()=>y,emails:()=>u,messages:()=>m,revoked_credentials:()=>x,roles:()=>f,rolesRelations:()=>v,userRoles:()=>_,userRolesRelations:()=>q,users:()=>c,usersRelations:()=>h,webhooks:()=>p});var s=r(7243),a=r(1939),i=r(3268),o=r(3797),n=r(1870),l=r(652);let c=(0,s.D)("user",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,a.Qq)("name"),email:(0,a.Qq)("email").unique(),emailVerified:(0,i.nd)("emailVerified",{mode:"timestamp_ms"}),image:(0,a.Qq)("image"),username:(0,a.Qq)("username").unique(),password:(0,a.Qq)("password")}),d=(0,s.D)("account",{userId:(0,a.Qq)("userId").notNull().references(()=>c.id,{onDelete:"cascade"}),type:(0,a.Qq)("type").$type().notNull(),provider:(0,a.Qq)("provider").notNull(),providerAccountId:(0,a.Qq)("providerAccountId").notNull(),refresh_token:(0,a.Qq)("refresh_token"),access_token:(0,a.Qq)("access_token"),expires_at:(0,i.nd)("expires_at"),token_type:(0,a.Qq)("token_type"),scope:(0,a.Qq)("scope"),id_token:(0,a.Qq)("id_token"),session_state:(0,a.Qq)("session_state")},e=>({compoundKey:(0,o.ie)({columns:[e.provider,e.providerAccountId]})})),u=(0,s.D)("email",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),address:(0,a.Qq)("address").notNull().unique(),userId:(0,a.Qq)("userId").references(()=>c.id,{onDelete:"cascade"}),createdAt:(0,i.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),expiresAt:(0,i.nd)("expires_at",{mode:"timestamp_ms"}).notNull()},e=>({expiresAtIdx:(0,n.Pe)("email_expires_at_idx").on(e.expiresAt)})),m=(0,s.D)("message",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),emailId:(0,a.Qq)("emailId").notNull().references(()=>u.id,{onDelete:"cascade"}),fromAddress:(0,a.Qq)("from_address").notNull(),subject:(0,a.Qq)("subject").notNull(),content:(0,a.Qq)("content").notNull(),html:(0,a.Qq)("html"),receivedAt:(0,i.nd)("received_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)},e=>({emailIdIdx:(0,n.Pe)("message_email_id_idx").on(e.emailId)})),p=(0,s.D)("webhook",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,a.Qq)("user_id").notNull().references(()=>c.id,{onDelete:"cascade"}),url:(0,a.Qq)("url").notNull(),enabled:(0,i.nd)("enabled",{mode:"boolean"}).notNull().default(!0),createdAt:(0,i.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),updatedAt:(0,i.nd)("updated_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)}),f=(0,s.D)("role",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,a.Qq)("name").notNull(),description:(0,a.Qq)("description"),createdAt:(0,i.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),updatedAt:(0,i.nd)("updated_at",{mode:"timestamp"}).$defaultFn(()=>new Date)}),_=(0,s.D)("user_role",{userId:(0,a.Qq)("user_id").notNull().references(()=>c.id,{onDelete:"cascade"}),roleId:(0,a.Qq)("role_id").notNull().references(()=>f.id,{onDelete:"cascade"}),createdAt:(0,i.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date)},e=>({pk:(0,o.ie)({columns:[e.userId,e.roleId]})})),g=(0,s.D)("api_keys",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,a.Qq)("user_id").notNull().references(()=>c.id),name:(0,a.Qq)("name").notNull(),key:(0,a.Qq)("key").notNull().unique(),createdAt:(0,i.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),expiresAt:(0,i.nd)("expires_at",{mode:"timestamp"}),enabled:(0,i.nd)("enabled",{mode:"boolean"}).notNull().default(!0)},e=>({nameUserIdUnique:(0,n.GL)("name_user_id_unique").on(e.name,e.userId)})),x=(0,s.D)("revoked_credential",{jti:(0,a.Qq)("jti").primaryKey(),expiresAt:(0,i.nd)("expires_at",{mode:"timestamp_ms"}).notNull()},e=>({expiresAtIdx:(0,n.Pe)("revoked_expires_at_idx").on(e.expiresAt)})),y=(0,l.K1)(g,({one:e})=>({user:e(c,{fields:[g.userId],references:[c.id]})})),q=(0,l.K1)(_,({one:e})=>({user:e(c,{fields:[_.userId],references:[c.id]}),role:e(f,{fields:[_.roleId],references:[f.id]})})),h=(0,l.K1)(c,({many:e})=>({userRoles:e(_),apiKeys:e(g)})),v=(0,l.K1)(f,({many:e})=>({userRoles:e(_)}))}},e=>{var t=t=>e(e.s=t);e.O(0,[810,702,92],()=>t(9212));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/roles/users/route"]=r}]);
//# sourceMappingURL=route.js.map