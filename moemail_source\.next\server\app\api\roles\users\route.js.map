{"version": 3, "file": "app/api/roles/users/route.js", "mappings": "qFAAA,6DCAA,mHGAA,4QFIO,IAAMA,EAAU,OAAM,eAEPC,EAAKC,CAAgB,EACzC,GAAI,CAEF,GAAM,YAAEC,CAAU,CAAE,CADP,EACUC,IADJF,EAAQE,IAAI,GAG/B,GAAI,CAACD,EACH,OAAOE,GADQ,MACCD,IAAI,CAAC,CAAEE,MAAO,aAAc,EAAG,CAAEC,OAAQ,GAAI,GAG/D,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEbC,EAAO,MAAMF,EAAGG,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC,CAC1CC,MAAOX,EAAWY,QAAQ,CAAC,KAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,KAAKA,CAACK,KAAK,CAAEd,GAAca,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,KAAKA,CAACM,QAAQ,CAAEf,GACnFgB,KAAM,CACJC,UAAW,CACTD,KAAM,CACJE,MAAM,CACR,CACF,CACF,CACF,GAEA,GAAI,CAACX,EACH,IADS,GACFL,SAASD,IAAI,CAAC,CAAEE,MAAO,OAAQ,EAAG,CAAEC,OAAQ,GAAI,GAGzD,OAAOF,SAASD,IAAI,CAAC,CACnBM,KAAM,CACJY,GAAIZ,EAAKY,EAAE,CACXC,KAAMb,EAAKa,IAAI,CACfL,SAAUR,EAAKQ,QAAQ,CACvBD,MAAOP,EAAKO,KAAK,CACjBI,KAAMX,EAAKU,SAAS,CAAC,EAAE,EAAEC,KAAKE,IAChC,CACF,EACF,CAAE,MAAOjB,EAAO,CAEd,OADAkB,QAAQlB,KAAK,CAAC,uBAAwBA,GAC/BD,SAASD,IAAI,CAClB,CAAEE,MAAO,QAAS,EAClB,CAAEC,OAAQ,GAAI,EAElB,CACF,CCzCA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,8BACA,4BACA,iBACA,sCACA,CAAK,CACL,6FACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,CAAQ,yDAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,uEACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,8BACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,uIAiBnxJ,CAAC,CAAC,EAAC,qGCnBI,IAAME,EAAW,IAAMgB,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACC,EAAE,CAAE,CAAEC,MAAMA,CAAAA,CAAC,GAAE,gCAAHA,gTCCnE,IAAMjB,EAAQkB,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvCR,GAAIS,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MACNC,UAAU,GACVC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IACrCZ,KAAMQ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXd,MAAOc,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASK,MAAM,GAC3BC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,gBAAiB,CAAEC,KAAM,cAAe,GAC/DC,MAAOT,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZb,SAAUa,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYK,MAAM,GACjCK,SAAUV,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACjB,GAAE,EACsBD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CACjC,UACA,CACEY,OAAQX,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UACVY,OAAO,GACPC,UAAU,CAAC,IAAMhC,EAAMU,EAAE,CAAE,CAAEuB,SAAU,SAAU,GACpDC,KAAMf,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQgB,KAAK,GAAuBJ,OAAO,GACtDK,SAAUjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYY,OAAO,GAClCM,kBAAmBlB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,qBAAqBY,OAAO,GACpDO,cAAenB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBACpBoB,aAAcpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACnBqB,WAAYd,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cACpBe,WAAYtB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,cACjBuB,MAAOvB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZwB,SAAUxB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YACfyB,cAAezB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACtB,EACA,GAAc,EACZ0B,KADY,OACCzB,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CACtB0B,QAAS,CAACC,EAAQX,QAAQ,CAAEW,EAAQV,iBAAiB,CAAC,GAE1D,GAGWW,CAFZ,CAEqB9B,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,QAAS,CACzCR,GAAIS,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D0B,QAAS9B,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWY,OAAO,GAAGP,MAAM,GACzCM,OAAQX,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UAAUa,UAAU,CAAC,IAAMhC,EAAMU,EAAE,CAAE,CAAEuB,SAAU,SAAU,GACxEiB,UAAWxB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDI,OAAO,GACPV,UAAU,CAAC,IAAM,IAAI8B,MACxBC,UAAW1B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGI,OAAO,EACpE,EAAG,GAAY,EACbsB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAMJ,SAAS,EAChE,GAEaK,CAFV,CAEqBvC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7CR,GAAIS,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DmC,QAASvC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACXY,OAAO,GACPC,UAAU,CAAC,IAAMgB,EAAOtC,EAAE,CAAE,CAAEuB,SAAU,SAAU,GACrD0B,YAAaxC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBAAgBY,OAAO,GACzC6B,QAASzC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWY,OAAO,GAChC8B,QAAS1C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWY,OAAO,GAChC+B,KAAM3C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACX4C,WAAYrC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cAAe,CAAEC,KAAM,cAAe,GACvDI,OAAO,GACPV,UAAU,CAAC,IAAM,IAAI8B,KAC1B,EAAG,GAAY,EACba,GADa,QACDV,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAME,OAAO,EAC5D,GAEaO,CAFV,CAEqB/C,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7CR,GAAIS,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DO,OAAQX,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACVY,OAAO,GACPC,UAAU,CAAC,IAAMhC,EAAMU,EAAE,CAAE,CAAEuB,SAAU,SAAU,GACpDiC,IAAK/C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOY,OAAO,GACxBoC,QAASzC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGI,OAAO,GAAGqC,OAAO,EAAC,GACnElB,UAAWxB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDI,OAAO,GACPV,UAAU,CAAC,IAAM,IAAI8B,MACxBkB,UAAW3C,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDI,OAAO,GACPV,UAAU,CAAC,IAAM,IAAI8B,KAC1B,GAAE,EAEmBjC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvCR,GAAIS,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DZ,KAAMQ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQY,OAAO,GAC1BuC,YAAanD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,eAClB+B,UAAWxB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAI8B,MAC7EkB,UAAW3C,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAI8B,KAC/E,GAAG,EAEsBjC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,YAAa,CAChDY,OAAQX,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWY,OAAO,GAAGC,UAAU,CAAC,IAAMhC,EAAMU,EAAE,CAAE,CAAEuB,SAAU,SAAU,GACnFsC,OAAQpD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWY,OAAO,GAAGC,UAAU,CAAC,IAAMwC,EAAM9D,EAAE,CAAE,CAAEuB,SAAU,SAAU,GACnFiB,UAAWxB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAI8B,KAC/E,EAAG,GAAY,EACbsB,GADa,CACTrD,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CAAE0B,QAAS,CAACU,EAAM1B,MAAM,CAAE0B,EAAMe,MAAM,CAAC,GACxD,GAEaG,CAFT,CAEmBxD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,WAAY,CAC7CR,GAAIS,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DO,OAAQX,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWY,OAAO,GAAGC,UAAU,CAAC,IAAMhC,EAAMU,EAAE,EAC3DC,KAAMQ,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQY,OAAO,GAC1B4C,IAAKxD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOY,OAAO,GAAGP,MAAM,GACjC0B,UAAWxB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAI8B,MAC7EC,UAAW1B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrDwC,QAASzC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGI,OAAO,GAAGqC,OAAO,EAAC,EACrE,EAAG,GAAY,EACbQ,GADa,cACKC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,uBAAuBtB,EAAE,CAACC,EAAM7C,IAAI,CAAE6C,EAAM1B,MAAM,EAClF,GAEagD,CAFT,CAE+B5D,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,qBAAsB,CACnE6D,IAAK5D,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOC,UAAU,GAC3BgC,UAAW1B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGI,OAAO,EACpE,EAAG,GAAY,EACbsB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,0BAA0BC,EAAE,CAACC,EAAMJ,SAAS,EAClE,GAEa4B,CAFT,CAE4BC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACP,EAAS,CAAC,KAAEQ,CAAG,CAAE,GAAM,EAC/DpF,KAAMoF,EAAIlF,EAAO,CACfmF,OAAQ,CAACT,EAAQ5C,MAAM,CAAC,CACxBE,WAAY,CAAChC,EAAMU,EAAE,CAAC,GAE1B,GAEa0E,CAFT,CAE8BH,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACzE,EAAW,CAAC,CAAE0E,KAAG,CAAE,GAAM,EACnEpF,KAAMoF,EAAIlF,EAAO,CACfmF,OAAQ,CAAC3E,EAAUsB,MAAM,CAAC,CAC1BE,WAAY,CAAChC,EAAMU,EAAE,CAAC,GAExBD,KAAMyE,EAAIV,EAAO,CACfW,OAAQ,CAAC3E,EAAU+D,MAAM,CAAC,CAC1BvC,WAAY,CAACwC,EAAM9D,EAAE,CAAC,GAE1B,GAEa2E,CAFT,CAE0BJ,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACjF,EAAO,CAAC,MAAEsF,CAAI,CAAE,GAAM,EAC5D9E,UAAW8E,EAAK9E,GAChBkE,QAASY,EAAKZ,GAChB,GAEaa,CAFT,CAE0BN,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACT,EAAO,CAAC,MAAEc,CAAI,CAAE,GAAM,EAC5D9E,UAAW8E,EAAK9E,GAClB,IAAI", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/roles/users/route.ts", "webpack://_N_E/./app/api/roles/users/route.ts?7c51", "webpack://_N_E/?1c31", "webpack://_N_E/./app/lib/db.ts", "webpack://_N_E/./app/lib/schema.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { createDb } from \"@/lib/db\"\r\nimport { users } from \"@/lib/schema\"\r\nimport { eq } from \"drizzle-orm\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nexport async function POST(request: Request) {\r\n  try {\r\n    const json = await request.json()\r\n    const { searchText } = json as { searchText: string }\r\n\r\n    if (!searchText) {\r\n      return Response.json({ error: \"请提供用户名或邮箱地址\" }, { status: 400 })\r\n    }\r\n\r\n    const db = createDb()\r\n\r\n    const user = await db.query.users.findFirst({\r\n      where: searchText.includes('@') ? eq(users.email, searchText) : eq(users.username, searchText),\r\n      with: {\r\n        userRoles: {\r\n          with: {\r\n            role: true\r\n          }\r\n        }\r\n      }\r\n    });\r\n\r\n    if (!user) {\r\n      return Response.json({ error: \"未找到用户\" }, { status: 404 })\r\n    }\r\n\r\n    return Response.json({\r\n      user: {\r\n        id: user.id,\r\n        name: user.name,\r\n        username: user.username,\r\n        email: user.email,\r\n        role: user.userRoles[0]?.role.name\r\n      }\r\n    })\r\n  } catch (error) {\r\n    console.error(\"Failed to find user:\", error)\r\n    return Response.json(\r\n      { error: \"查询用户失败\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\roles\\\\users\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/roles/users/route\",\n        pathname: \"/api/roles/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/roles/users/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\roles\\\\users\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Froles%2Fusers%2Froute&page=%2Fapi%2Froles%2Fusers%2Froute&pagePath=private-next-app-dir%2Fapi%2Froles%2Fusers%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fapi%2Froles%2Fusers%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/roles/users/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/roles/users/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/roles/users/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { drizzle } from \"drizzle-orm/d1\"\r\nimport * as schema from \"./schema\"\r\n\r\nexport const createDb = () => drizzle(getRequestContext().env.DB, { schema })\r\n\r\nexport type Db = ReturnType<typeof createDb>\r\n", "import { integer, sqliteTable, text, primaryKey, uniqueIndex, index } from \"drizzle-orm/sqlite-core\"\r\nimport type { AdapterAccountType } from \"next-auth/adapters\"\r\nimport { relations } from 'drizzle-orm';\r\n\r\n// https://authjs.dev/getting-started/adapters/drizzle\r\nexport const users = sqliteTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: integer(\"emailVerified\", { mode: \"timestamp_ms\" }),\r\n  image: text(\"image\"),\r\n  username: text(\"username\").unique(),\r\n  password: text(\"password\"),\r\n})\r\nexport const accounts = sqliteTable(\r\n  \"account\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => users.id, { onDelete: \"cascade\" }),\r\n    type: text(\"type\").$type<AdapterAccountType>().notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n  },\r\n  (account) => ({\r\n    compoundKey: primaryKey({\r\n      columns: [account.provider, account.providerAccountId],\r\n    }),\r\n  })\r\n)\r\n\r\nexport const emails = sqliteTable(\"email\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  address: text(\"address\").notNull().unique(),\r\n  userId: text(\"userId\").references(() => users.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp_ms\" }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"email_expires_at_idx\").on(table.expiresAt),\r\n}))\r\n\r\nexport const messages = sqliteTable(\"message\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  emailId: text(\"emailId\")\r\n    .notNull()\r\n    .references(() => emails.id, { onDelete: \"cascade\" }),\r\n  fromAddress: text(\"from_address\").notNull(),\r\n  subject: text(\"subject\").notNull(),\r\n  content: text(\"content\").notNull(),\r\n  html: text(\"html\"),\r\n  receivedAt: integer(\"received_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  emailIdIdx: index(\"message_email_id_idx\").on(table.emailId),\r\n}))\r\n\r\nexport const webhooks = sqliteTable('webhook', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id')\r\n    .notNull()\r\n    .references(() => users.id, { onDelete: \"cascade\" }),\r\n  url: text('url').notNull(),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n})\r\n\r\nexport const roles = sqliteTable(\"role\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\").notNull(),\r\n  description: text(\"description\"),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n});\r\n\r\nexport const userRoles = sqliteTable(\"user_role\", {\r\n  userId: text(\"user_id\").notNull().references(() => users.id, { onDelete: \"cascade\" }),\r\n  roleId: text(\"role_id\").notNull().references(() => roles.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  pk: primaryKey({ columns: [table.userId, table.roleId] }),\r\n}));\r\n\r\nexport const apiKeys = sqliteTable('api_keys', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id),\r\n  name: text('name').notNull(),\r\n  key: text('key').notNull().unique(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n}, (table) => ({\r\n  nameUserIdUnique: uniqueIndex('name_user_id_unique').on(table.name, table.userId)\r\n}));\r\n\r\nexport const revoked_credentials = sqliteTable('revoked_credential', {\r\n  jti: text('jti').primaryKey(),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp_ms' }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"revoked_expires_at_idx\").on(table.expiresAt),\r\n}));\r\n\r\nexport const apiKeysRelations = relations(apiKeys, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [apiKeys.userId],\r\n    references: [users.id],\r\n  }),\r\n}));\r\n\r\nexport const userRolesRelations = relations(userRoles, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userRoles.userId],\r\n    references: [users.id],\r\n  }),\r\n  role: one(roles, {\r\n    fields: [userRoles.roleId],\r\n    references: [roles.id],\r\n  }),\r\n}));\r\n\r\nexport const usersRelations = relations(users, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n  apiKeys: many(apiKeys),\r\n}));\r\n\r\nexport const rolesRelations = relations(roles, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n}));"], "names": ["runtime", "POST", "request", "searchText", "json", "Response", "error", "status", "db", "createDb", "user", "query", "users", "<PERSON><PERSON><PERSON><PERSON>", "where", "includes", "eq", "email", "username", "with", "userRoles", "role", "id", "name", "console", "drizzle", "getRequestContext", "env", "DB", "schema", "sqliteTable", "text", "<PERSON><PERSON><PERSON>", "$defaultFn", "crypto", "randomUUID", "unique", "emailVerified", "integer", "mode", "image", "password", "userId", "notNull", "references", "onDelete", "type", "$type", "provider", "providerAccountId", "refresh_token", "access_token", "expires_at", "token_type", "scope", "id_token", "session_state", "<PERSON><PERSON><PERSON>", "columns", "account", "emails", "address", "createdAt", "Date", "expiresAt", "expiresAtIdx", "index", "on", "table", "messages", "emailId", "fromAddress", "subject", "content", "html", "receivedAt", "emailIdIdx", "webhooks", "url", "enabled", "default", "updatedAt", "description", "roleId", "roles", "pk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "nameUserIdUnique", "uniqueIndex", "revoked_credentials", "jti", "apiKeysRelations", "relations", "one", "fields", "userRolesRelations", "usersRelations", "many", "rolesRelations"], "sourceRoot": "", "ignoreList": []}