{"version": 3, "file": "app/api/api-keys/route.js", "mappings": "qFAAA,6DCAA,mHGAA,sUFSO,IAAMA,EAAU,OAAM,eAEPC,IAEpB,GAAI,CAACC,MADuBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CACvB,EADwBC,EAAWA,CAACC,cAAc,EAEpE,OAAOC,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEC,MAAO,MAAO,EAAG,CAAEC,OAAQ,GAAI,GAG5D,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAC1B,GAAI,CACF,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACbC,EAAO,MAAMF,EAAGG,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAC3CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,OAAOA,CAACI,MAAM,CAAEV,EAASW,IAAI,CAACC,EAAE,EAC1CC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACR,EAAAA,OAAOA,CAACS,SAAS,CACjC,GAEA,OAAOnB,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBS,QAASF,EAAKY,GAAG,CAACC,GAAQ,EACxB,CADwB,EACrBA,CAAG,CACNA,SAAKC,EACP,EACF,EACF,CAAE,MAAOpB,EAAO,CAEd,OADAqB,QAAQrB,KAAK,CAAC,4BAA6BA,GACpCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,gBAAiB,EAC1B,CAAEC,OAAQ,GAAI,EAElB,CACF,CAEO,eAAeqB,EAAKC,CAAgB,EAEzC,GAAI,CAAC7B,MADuBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAACC,EAAAA,EAAWA,CAACC,cAAc,EAEpE,OAAOC,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEC,MAAO,MAAO,EAAG,CAAEC,OAAQ,GAAI,GAG5D,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAC1B,GAAI,CACF,GAAM,MAAEqB,CAAI,CAAE,CAAG,MAAMD,EAAQxB,IAAI,GACnC,GAAI,CAACyB,GAAMC,OACT,CADiB,MACV3B,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,QAAS,EAClB,CAAEC,OAAQ,GAAI,GAIlB,IAAMkB,EAAM,CAAC,GAAG,EAAEO,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,CAAC,KAAK,CACxBtB,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GASnB,OAPA,MAAMD,EAAGuB,MAAM,CAACnB,EAAAA,OAAOA,EAAEoB,MAAM,CAAC,MAC9BJ,EACAL,MACAP,OAAQV,EAASW,IAAI,CAACC,EAAE,CACxBe,UAAW,IAAIC,KAAKA,KAAKC,GAAG,GAAK,MAAM,EACzC,GAD8C,EAGvCjC,EAAYA,CAHgC,IAG3B,CAHgC,KAG7BqB,CAAI,EACjC,CAAE,MAAOnB,EAAO,CAEd,OADAqB,QAAQrB,KAAK,CAAC,4BAA6BA,GACpCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,eAAgB,EACzB,CAAEC,OAAQ,GAAI,EAElB,CACF,CCnEA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,2BACA,yBACA,iBACA,mCACA,CAAK,CACL,yFACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,oEACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,2BACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,uIAiBnxJ,CAAC,CAAC,EAAC,kHCwBI,IAAM+B,EAAY,UAEvB,IAAMpB,EAASqB,CADK,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,EAAAA,EACNC,GAAG,CAAC,aAE/B,GAAIvB,EAAQ,OAAOA,EAEnB,IAAMV,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAE1B,OAAOD,GAASW,KAAKC,EACvB,EAAC,2NCxDD,IAAMsB,EAAS,CACb,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACD,eCGD,IAAMC,EAA0C,CAC9C,CAACC,EAAAA,EAAKA,CAACC,OAAO,CAAC,CAAE,YACjB,CAACD,EAAAA,EAAKA,CAACE,IAAI,CAAC,CAAE,WACd,CAACF,EAAAA,EAAKA,CAACG,MAAM,CAAC,CAAE,WAChB,CAACH,EAAAA,EAAKA,CAACI,QAAQ,CAAC,CAAE,UACpB,EAEMC,EAAiB,UACrB,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACC,WAAW,CAACZ,GAAG,CAAC,uBAGhES,IAAgBN,EAAAA,EAAKA,CAACE,IAAI,EAC1BI,IAAgBN,EAAAA,EAAKA,CAACG,MAAM,EAC5BG,IAAgBN,EAAAA,EAAKA,CAACI,QAAQ,CAEvBE,CADP,CAIKN,EAAAA,EAAKA,CAACI,QAAQ,EAGvB,eAAeM,EAAiB5C,CAAM,CAAE6C,CAAc,EACpD,IAAIC,EAAO,MAAM9C,EAAGG,KAAK,CAAC4C,KAAK,CAACC,SAAS,CAAC,CACxC1C,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACwC,EAAAA,KAAKA,CAAC3B,IAAI,CAAEyB,EACxB,GAEA,GAAI,CAACC,EAAM,CACT,GAAM,CAACG,EAAQ,CAAG,MAAMjD,EAAGuB,MAAM,CAACwB,EAAAA,KAAKA,EACpCvB,MAAM,CAAC,CACNJ,KAAMyB,EACNK,YAAajB,CAAiB,CAACY,EAAS,GAEzCM,SAAS,GACZL,EAAOG,CACT,CAEA,OAAOH,CACT,CAEO,eAAeM,EAAiBpD,CAAM,CAAEQ,CAAc,CAAE6C,CAAc,EAC3E,MAAMrD,EAAGsD,MAAM,CAACC,EAAAA,SAASA,EACtBjD,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgD,EAAAA,SAASA,CAAC/C,MAAM,CAAEA,IAE9B,MAAMR,EAAGuB,MAAM,CAACgC,EAAAA,SAASA,EACtB/B,MAAM,CAAC,QACNhB,SACA6C,CACF,EACJ,CAWO,eAAe9D,EAAgBiE,CAAsB,EAC1D,IAAMhD,EAAS,MAAMoB,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE9B,GAAI,CAACpB,EAAQ,OAAO,EAEpB,IAAMR,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMbwD,EAAgBC,CALE,MAAM1D,EAAGG,KAAK,CAACoD,SAAS,CAAClD,QAAQ,CAAC,CACxDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgD,EAAAA,SAASA,CAAC/C,MAAM,CAAEA,GAC5BmD,KAAM,CAAEb,MAAM,CAAK,CACrB,IAEsChC,GAAG,CAAC8C,GAAMA,EAAGd,IAAI,CAAC1B,IAAI,EAC5D,MAAO9B,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAACmE,EAAyBD,EAChD,CAEO,GAAM,CACXK,SAAU,KAAExE,CAAG,MAAE6B,CAAI,CAAE,MACvBnB,CAAI,QACJ+D,CAAM,SACNC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,IAAO,EAClBC,OAAQC,QAAQxB,GAAG,CAACyB,WAAW,CAC/BC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACpE,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAAI,CAClCqE,WAAYC,EAAAA,KAAKA,CACjBC,cAAeC,EAAAA,QAAQA,GAEzBC,UAAW,CACTC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC,CACLC,SAAUV,QAAQxB,GAAG,CAACmC,cAAc,CACpCC,aAAcZ,QAAQxB,GAAG,CAACqC,kBAAkB,GAE9CC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CAClB5D,KAAM,cACN6D,YAAa,CACXC,SAAU,CAAEC,MAAO,MAAOC,KAAM,OAAQC,YAAa,QAAS,EAC9DC,SAAU,CAAEH,MAAO,KAAMC,KAAM,WAAYC,YAAa,OAAQ,CAClE,EACA,MAAME,UAAUN,CAAW,EACzB,GAAI,CAACA,EACH,MAAUO,KADM,CACA,aAGlB,GAAM,UAAEN,CAAQ,UAAEI,CAAQ,CAAE,CAAGL,EAE/B,GAAI,CACFQ,EAAAA,CAAUA,CAACC,KAAK,CAAC,UAAER,WAAUI,CAAS,EAExC,CAAE,MAAO1F,EAAO,CACd,MAAM,MAAU,UAClB,CAEA,IAAMI,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEbQ,EAAO,MAAMT,EAAGG,KAAK,CAACoE,KAAK,CAACvB,SAAS,CAAC,CAC1C1C,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgE,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAEA,GAAI,CAACzE,GAKD,CADY,EAJL,IAIWkF,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAACL,EAAoB7E,EAAK6E,QAAQ,EAHrE,MAAM,MAAU,YAQlB,MAAO,CACL,GAAG7E,CAAI,CACP6E,SAAUtE,MACZ,CACF,CACF,GACD,CACD4E,OAAQ,CACN,MAAM9B,OAAO,MAAErD,CAAI,CAAE,EACnB,GAAKA,CAAD,CAAMC,EAAE,CAEZ,CAFc,EAEV,CACF,IAAMV,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,GAJqB,CAIjB4F,KAJuB7F,EAAGG,KAAK,CAACoD,SAAS,CAACP,SAAS,CAAC,CACtD1C,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgD,EAAAA,SAASA,CAAC/C,MAAM,CAAEC,EAAKC,EAAE,CACrC,GAEkB,OAElB,IAAM8B,EAAc,MAAMD,IACpBO,EAAO,MAAMF,EAAiB5C,EAAIwC,EACxC,OAAMY,EAAiBpD,EAAIS,EAAKC,EAAE,CAAEoC,EAAKpC,EAAE,CAC7C,CAAE,MAAOd,EAAO,CACdqB,QAAQrB,KAAK,CAAC,wBAAyBA,EACzC,CACF,CACF,EACAkG,UAAW,CACHC,IAAN,MAAU,OAAEC,CAAK,MAAEvF,CAAI,CAAE,IACnBA,IACFuF,EADQ,EACA,CAAGvF,EAAKC,EAAE,CAClBsF,EAAM5E,IAAI,CAAGX,EAAKW,IAAI,EAAIX,EAAKyE,QAAQ,CACvCc,EAAMd,QAAQ,CAAGzE,EAAKyE,QAAQ,CAC9Bc,EAAMC,KAAK,CAAGxF,EAAKwF,KAAK,ED/JzB,SAASC,CAA8B,EAC5C,IAAMC,CC8J6CD,CD9JnC9E,CAAI,CAAC,EAAE,CAACgF,WAAW,GAE7BC,EAAaC,MAAMC,IAAI,CAACnF,GAAMoF,MAAM,CACxC,CAACC,EAAKC,IAASD,EAAMC,EAAKC,UAAU,CAAC,GAAI,GACvC3E,EAAO4E,MAAM,CAEXC,EAAkB7E,CAAM,CAACqE,EAAW,CAEpCS,EAAM,CAAC;;2CAE4B,EAAED,EAAgB;;;;;;;;;;;;;QAarD,EAAEV,QAAQ;;;EAGhB,CAAC,CAAC9E,IAAI,GAGA0F,EADU,IAAIC,cACEC,MAAM,CAACH,GACvBI,EAASC,EAAOZ,IAADY,CAAMJ,GAAOK,QAAQ,CAAC,UAE3C,MAAO,CAAC,0BAA0B,EAAEF,EAAAA,CAAQ,EC8HQlB,EAAM5E,IAAI,GAEnD4E,GAET,MAAMlG,QAAQ,SAAEA,CAAO,OAAEkG,CAAK,CAAE,EAC9B,GAAIA,GAASlG,EAAQW,IAAI,CAAE,CACzBX,EAAQW,IAAI,CAACC,EAAE,CAAGsF,EAAMtF,EAAE,CAC1BZ,EAAQW,IAAI,CAACW,IAAI,CAAG4E,EAAM5E,IAAI,CAC9BtB,EAAQW,IAAI,CAACyE,QAAQ,CAAGc,EAAMd,QAAQ,CACtCpF,EAAQW,IAAI,CAACwF,KAAK,CAAGD,EAAMC,KAAK,CAEhC,IAAMjG,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACfyD,EAAkB,MAAM1D,EAAGG,KAAK,CAACoD,SAAS,CAAClD,QAAQ,CAAC,CACtDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgD,EAAAA,SAASA,CAAC/C,MAAM,CAAEV,EAAQW,IAAI,CAACC,EAAE,EAC3CiD,KAAM,CAAEb,MAAM,CAAK,CACrB,GAEA,GAAI,CAACY,EAAgBkD,MAAM,CAAE,CAC3B,IAAMpE,EAAc,MAAMD,IACpBO,EAAO,MAAMF,EAAiB5C,EAAIwC,EACxC,OAAMY,EAAiBpD,EAAIF,EAAQW,IAAI,CAACC,EAAE,CAAEoC,EAAKpC,EAAE,EACnDgD,EAAkB,CAAC,CACjBlD,OAAQV,EAAQW,IAAI,CAACC,EAAE,CACvB2C,OAAQP,EAAKpC,EAAE,CACfG,UAAW,IAAIa,KACfoB,KAAMA,CACR,EAAE,CAGJhD,EAAQW,IAAI,CAACsC,KAAK,CAAGW,EAAgB5C,GAAG,CAAC8C,GAAO,OACxCA,EAAGd,IAAI,CAAC1B,IAAI,CACpB,EACF,CAEA,OAAOtB,CACT,CACF,EACAA,QAAS,CACPuH,SAAU,KACZ,EACF,GAEO,CAFJ,cAEmBC,EAASpC,CAAgB,CAAEI,CAAgB,EAC/D,IAAMtF,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,GAJiB,CAIbsH,KAJmBvH,EAAGG,GAIZ,EAJiB,CAACoE,KAAK,CAACvB,SAAS,CAAC,CAC9C1C,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgE,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAGE,MAAM,MAAU,UAGlB,IAAMsC,EAAiB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACnC,GAEpC,CAAC7E,EAAK,CAAG,MAAMT,EAAGuB,MAAM,CAACgD,EAAAA,KAAKA,EACjC/C,MAAM,CAAC,UACN0D,EACAI,SAAUkC,CACZ,GACCrE,SAAS,GAEZ,OAAO1C,CACT,gFCvOO,IAAMR,EAAW,IAAMyH,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAACjF,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACiF,EAAE,CAAE,CAAEC,MAAMA,CAAAA,CAAC,GAAE,gCAAHA,gCCJnE,IAAM1F,EAAQ,CACnBC,QAAS,UACTC,KAAM,OACNC,OAAQ,SACRC,SAAU,UACZ,EAAW,EAIgB,CACzBuF,aAAc,eACdC,eAAgB,iBAChBC,aAAc,eACdC,cAAe,gBACfvI,eAAgB,gBAClB,EAIawI,EAA+C,CAC1D,CAAC/F,EAAMC,OAAO,CAAC,CAAE+F,OAAO1G,MAAM,CAAChC,GAC/B,CAAC0C,EAAME,IAAI,CAAC,CAAE,CACZ5C,EAAYqI,YAAY,CACxBrI,EAAYsI,cAAc,CAC1BtI,EAAYC,cAAc,CAC3B,CACD,CAACyC,EAAMG,MAAM,CAAC,CAAE,CACd7C,EAAYqI,YAAY,CACxBrI,EAAYsI,cAAc,CAC3B,CACD,CAAC5F,EAAMI,QAAQ,CAAC,CAAE,EAAE,EACX,SAEKhD,EAAciE,CAAiB,CAAEC,CAAsB,EACrE,OAAOD,EAAU4E,IAAI,CAACrF,GAAQmF,CAAgB,CAACnF,EAAK,EAAEsF,SAAS5E,GACjE,kVC9BO,IAAMe,EAAQ8D,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvC3H,GAAI4H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MACNC,UAAU,GACVC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IACrCtH,KAAMkH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXK,MAAOL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASM,MAAM,GAC3BC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,gBAAiB,CAAEC,KAAM,cAAe,GAC/D9C,MAAOqC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZpD,SAAUoD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYM,MAAM,GACjCtD,SAAUgD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACjB,GAAE,EACsBD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CACjC,UACA,CACE7H,OAAQ8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UACVU,OAAO,GACPC,UAAU,CAAC,IAAM1E,EAAM7D,EAAE,CAAE,CAAEwI,SAAU,SAAU,GACpD9D,KAAMkD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQa,KAAK,GAAuBH,OAAO,GACtDI,SAAUd,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYU,OAAO,GAClCK,kBAAmBf,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,qBAAqBU,OAAO,GACpDM,cAAehB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBACpBiB,aAAcjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACnBkB,WAAYV,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cACpBW,WAAYnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,cACjBoB,MAAOpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZqB,SAAUrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YACfsB,cAAetB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACtB,EACA,GAAc,EACZuB,KADY,OACCtB,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CACtBuB,QAAS,CAACC,EAAQX,QAAQ,CAAEW,EAAQV,iBAAiB,CAAC,GAE1D,GAGWW,CAFZ,CAEqB3B,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,QAAS,CACzC3H,GAAI4H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DuB,QAAS3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGJ,MAAM,GACzCpI,OAAQ8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UAAUW,UAAU,CAAC,IAAM1E,EAAM7D,EAAE,CAAE,CAAEwI,SAAU,SAAU,GACxErI,UAAWiI,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAI9G,MACxBD,UAAWqH,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbkB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAM5I,SAAS,EAChE,GAEa6I,CAFV,CAEqBjC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7C3H,GAAI4H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D6B,QAASjC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACXU,OAAO,GACPC,UAAU,CAAC,IAAMe,EAAOtJ,EAAE,CAAE,CAAEwI,SAAU,SAAU,GACrDsB,YAAalC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBAAgBU,OAAO,GACzCyB,QAASnC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC0B,QAASpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC2B,KAAMrC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXsC,WAAY9B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cAAe,CAAEC,KAAM,cAAe,GACvDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAI9G,KAC1B,EAAI2I,GAAW,EACbQ,GADa,QACDV,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAME,OAAO,CAC5D,IAEaO,EAAWzC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7C3H,GAAI4H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DlI,OAAQ8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACVU,OAAO,GACPC,UAAU,CAAC,IAAM1E,EAAM7D,EAAE,CAAE,CAAEwI,SAAU,SAAU,GACpD6B,IAAKzC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GACxBgC,QAASlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGiC,OAAO,EAAC,GACnEpK,UAAWiI,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAI9G,MACxBwJ,UAAWpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAI9G,KAC1B,GAAE,EAEmB2G,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvC3H,GAAI4H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DtH,KAAMkH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1B9F,YAAaoF,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,eAClBzH,UAAWiI,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAI9G,MAC7EwJ,UAAWpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAI9G,KAC/E,GAAG,EAEsB2G,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,YAAa,CAChD7H,OAAQ8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM1E,EAAM7D,EAAE,CAAE,CAAEwI,SAAU,SAAU,GACnF7F,OAAQiF,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMlG,EAAMrC,EAAE,CAAE,CAAEwI,SAAU,SAAU,GACnFrI,UAAWiI,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAI9G,KAC/E,EAAG,GAAY,EACbyJ,GADa,CACT5C,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CAAEuB,QAAS,CAACO,EAAM7J,MAAM,CAAE6J,EAAMhH,MAAM,CAAC,GACxD,GAEajD,CAFT,CAEmBiI,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,WAAY,CAC7C3H,GAAI4H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DlI,OAAQ8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAM1E,EAAM7D,EAAE,EAC3DU,KAAMkH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BjI,IAAKuH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GAAGJ,MAAM,GACjC/H,UAAWiI,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAI9G,MAC7ED,UAAWqH,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrDiC,QAASlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGiC,OAAO,EAAC,EACrE,EAAG,GAAY,EACbG,GADa,cACKC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,uBAAuBjB,EAAE,CAACC,EAAMjJ,IAAI,CAAEiJ,EAAM7J,MAAM,CAClF,IAAI,EAE+B6H,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,qBAAsB,CACnEiD,IAAKhD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOC,UAAU,GAC3B9G,UAAWqH,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbkB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,0BAA0BC,EAAE,CAACC,EAAM5I,SAAS,EAClE,GAEa8J,CAFT,CAE4BC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACpL,EAAS,CAAC,CAAEqL,KAAG,CAAE,GAAM,EAC/DhL,KAAMgL,EAAIlH,EAAO,CACfmH,OAAQ,CAACtL,EAAQI,MAAM,CAAC,CACxByI,WAAY,CAAC1E,EAAM7D,EAAE,CACvB,GACF,GAEaiL,CAFT,CAE8BH,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACjI,EAAW,CAAC,KAAEkI,CAAG,CAAE,GAAM,EACnEhL,KAAMgL,EAAIlH,EAAO,CACfmH,OAAQ,CAACnI,EAAU/C,MAAM,CAAC,CAC1ByI,WAAY,CAAC1E,EAAM7D,EAAE,CAAC,GAExBoC,KAAM2I,EAAI1I,EAAO,CACf2I,OAAQ,CAACnI,EAAUF,MAAM,CAAC,CAC1B4F,WAAY,CAAClG,EAAMrC,EAAE,CAAC,GAE1B,GAEakL,CAFT,CAE0BJ,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACjH,EAAO,CAAC,MAAEsH,CAAI,CAAE,GAAM,EAC5DtI,UAAWsI,EAAKtI,GAChBnD,QAASyL,EAAKzL,GAChB,GAEa0L,CAFT,CAE0BN,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACzI,EAAO,CAAC,MAAE8I,CAAI,CAAE,GAAM,EAC5DtI,UAAWsI,EAAKtI,GAClB,IAAI,sFC5IG,SAASwI,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,CAEO,eAAevE,EAAanC,CAAgB,EACjD,IAAM6G,EAAU,IAAInF,YACdoF,EAAOlI,QAAQxB,GAAG,CAACyB,WAAW,EAAI,GAClCkI,EAAOF,EAAQlF,MAAM,CAAC3B,EAAW8G,GAEvC,OAAOE,KAAKC,OAAOC,YAAY,IAAI,IAAIC,WAD1B,MAAMhE,OAAOiE,MAAM,CAACC,MAAM,CAAC,UAAWN,KAErD,CAEO,eAAe1G,EAAgBL,CAAgB,CAAEkC,CAAsB,EAE5E,OAAOoF,MADYnF,EAAanC,KAChBkC,CAClB,8DChBO,IAAM/B,EAAaoH,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACjC3H,SAAU2H,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,WACPC,GAAG,CAAC,GAAI,gBACRC,KAAK,CAAC,mBAAoB,uBAC1BC,MAAM,CAACC,GAAO,CAACA,EAAI9E,QAAQ,CAAC,KAAM,cACrC9C,SAAUuH,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,eACZ,GAAE,+CEWK,eACP,SACA,+CACA,UACA,GDxBA,kECwB2B,UAE3B,QACA", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/api-keys/route.ts", "webpack://_N_E/./app/api/api-keys/route.ts?c5f2", "webpack://_N_E/?04fe", "webpack://_N_E/./app/lib/apiKey.ts", "webpack://_N_E/./app/lib/avatar.ts", "webpack://_N_E/./app/lib/auth.ts", "webpack://_N_E/./app/lib/db.ts", "webpack://_N_E/./app/lib/permissions.ts", "webpack://_N_E/./app/lib/schema.ts", "webpack://_N_E/./app/lib/utils.ts", "webpack://_N_E/./app/lib/validation.ts", "webpack://_N_E/./node_modules/.pnpm/nanoid@5.1.5/node_modules/nanoid/url-alphabet/index.js", "webpack://_N_E/./node_modules/.pnpm/nanoid@5.1.5/node_modules/nanoid/index.browser.js"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { auth } from \"@/lib/auth\"\r\nimport { createDb } from \"@/lib/db\"\r\nimport { api<PERSON>eys } from \"@/lib/schema\"\r\nimport { nanoid } from \"nanoid\"\r\nimport { NextResponse } from \"next/server\"\r\nimport { checkPermission } from \"@/lib/auth\"\r\nimport { PERMISSIONS } from \"@/lib/permissions\"\r\nimport { desc, eq } from \"drizzle-orm\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nexport async function GET() {\r\n  const hasPermission = await checkPermission(PERMISSIONS.MANAGE_API_KEY)\r\n  if (!hasPermission) {\r\n    return NextResponse.json({ error: \"权限不足\" }, { status: 403 })\r\n  }\r\n\r\n  const session = await auth()\r\n  try {\r\n    const db = createDb()\r\n    const keys = await db.query.apiKeys.findMany({\r\n      where: eq(apiKeys.userId, session!.user.id!),\r\n      orderBy: desc(apiKeys.createdAt),\r\n    })\r\n\r\n    return NextResponse.json({\r\n      apiKeys: keys.map(key => ({\r\n        ...key,\r\n        key: undefined\r\n      }))\r\n    })\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch API keys:\", error)\r\n    return NextResponse.json(\r\n      { error: \"获取 API Keys 失败\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n}\r\n\r\nexport async function POST(request: Request) {\r\n  const hasPermission = await checkPermission(PERMISSIONS.MANAGE_API_KEY)\r\n  if (!hasPermission) {\r\n    return NextResponse.json({ error: \"权限不足\" }, { status: 403 })\r\n  }\r\n\r\n  const session = await auth()\r\n  try {\r\n    const { name } = await request.json() as { name: string }\r\n    if (!name?.trim()) {\r\n      return NextResponse.json(\r\n        { error: \"名称不能为空\" },\r\n        { status: 400 }\r\n      )\r\n    }\r\n\r\n    const key = `mk_${nanoid(32)}`\r\n    const db = createDb()\r\n    \r\n    await db.insert(apiKeys).values({\r\n      name,\r\n      key,\r\n      userId: session!.user.id!,\r\n      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year\r\n    })\r\n\r\n    return NextResponse.json({ key })\r\n  } catch (error) {\r\n    console.error(\"Failed to create API key:\", error)\r\n    return NextResponse.json(\r\n      { error: \"创建 API Key 失败\" },\r\n      { status: 500 }\r\n    )\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\api-keys\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/api-keys/route\",\n        pathname: \"/api/api-keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/api-keys/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\api-keys\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fapi-keys%2Froute&page=%2Fapi%2Fapi-keys%2Froute&pagePath=private-next-app-dir%2Fapi%2Fapi-keys%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fapi%2Fapi-keys%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/api-keys/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/api-keys/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/api-keys/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { createDb } from \"./db\"\r\nimport { api<PERSON><PERSON>s } from \"./schema\"\r\nimport { eq, and, gt } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport type { User } from \"next-auth\"\r\nimport { auth } from \"./auth\"\r\nimport { headers } from \"next/headers\"\r\n\r\nasync function getUserByApiKey(key: string): Promise<User | null> {\r\n  const db = createDb()\r\n  const apiKey = await db.query.apiKeys.findFirst({\r\n    where: and(\r\n      eq(apiKeys.key, key),\r\n      eq(apiKeys.enabled, true),\r\n      gt(apiKeys.expiresAt, new Date())\r\n    ),\r\n    with: {\r\n      user: true\r\n    }\r\n  })\r\n\r\n  if (!apiKey) return null\r\n\r\n  return apiKey.user\r\n}\r\n\r\nexport async function handleApiKeyAuth(apiKey: string, pathname: string) {\r\n  if (!pathname.startsWith('/api/emails') && !pathname.startsWith('/api/config')) {\r\n    return NextResponse.json(\r\n      { error: \"无权限查看\" },\r\n      { status: 403 }\r\n    )\r\n  }\r\n\r\n  const user = await getUserByApiKey(apiKey)\r\n  if (!user?.id) {\r\n    return NextResponse.json(\r\n      { error: \"无效的 API Key\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  const response = NextResponse.next()\r\n  response.headers.set(\"X-User-Id\", user.id)\r\n  return response\r\n}\r\n\r\nexport const getUserId = async () => {\r\n  const headersList = await headers()\r\n  const userId = headersList.get(\"X-User-Id\")\r\n  \r\n  if (userId) return userId\r\n\r\n  const session = await auth()\r\n\r\n  return session?.user.id\r\n}\r\n", "const COLORS = [\r\n  '#2196F3', // 蓝色\r\n  '#009688', // 青色\r\n  '#9C27B0', // 紫色\r\n  '#F44336', // 红色\r\n  '#673AB7', // 深紫色\r\n  '#3F51B5', // 靛蓝\r\n  '#4CAF50', // 绿色\r\n  '#FF5722', // 深橙\r\n  '#795548', // 棕色\r\n  '#607D8B', // 蓝灰\r\n];\r\n\r\nexport function generateAvatarUrl(name: string): string {\r\n  const initial = name[0].toUpperCase();\r\n  \r\n  const colorIndex = Array.from(name).reduce(\r\n    (acc, char) => acc + char.charCodeAt(0), 0\r\n  ) % COLORS.length;\r\n  \r\n  const backgroundColor = COLORS[colorIndex];\r\n  \r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\" width=\"100\" height=\"100\">\r\n      <rect width=\"100\" height=\"100\" fill=\"${backgroundColor}\"/>\r\n      <text \r\n        x=\"50%\" \r\n        y=\"50%\" \r\n        fill=\"white\" \r\n        font-family=\"system-ui, -apple-system, sans-serif\" \r\n        font-size=\"45\" \r\n        font-weight=\"500\"\r\n        text-anchor=\"middle\"\r\n        alignment-baseline=\"central\"\r\n        dominant-baseline=\"central\"\r\n        style=\"text-transform: uppercase\"\r\n      >\r\n        ${initial}\r\n      </text>\r\n    </svg>\r\n  `.trim();\r\n\r\n  const encoder = new TextEncoder();\r\n  const bytes = encoder.encode(svg);\r\n  const base64 = Buffer.from(bytes).toString('base64');\r\n  \r\n  return `data:image/svg+xml;base64,${base64}`;\r\n} ", "import NextAuth from \"next-auth\"\r\nimport GitHub from \"next-auth/providers/github\"\r\nimport { Dr<PERSON>zleAdapter } from \"@auth/drizzle-adapter\"\r\nimport { createDb, Db } from \"./db\"\r\nimport { accounts, users, roles, userRoles } from \"./schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { Permission, hasPermission, ROLES, Role } from \"./permissions\"\r\nimport CredentialsProvider from \"next-auth/providers/credentials\"\r\nimport { hashPassword, comparePassword } from \"@/lib/utils\"\r\nimport { authSchema } from \"@/lib/validation\"\r\nimport { generateAvatarUrl } from \"./avatar\"\r\nimport { getUserId } from \"./apiKey\"\r\n\r\nconst ROLE_DESCRIPTIONS: Record<Role, string> = {\r\n  [ROLES.EMPEROR]: \"皇帝（网站所有者）\",\r\n  [ROLES.DUKE]: \"公爵（超级用户）\",\r\n  [ROLES.KNIGHT]: \"骑士（高级用户）\",\r\n  [ROLES.CIVILIAN]: \"平民（普通用户）\",\r\n}\r\n\r\nconst getDefaultRole = async (): Promise<Role> => {\r\n  const defaultRole = await getRequestContext().env.SITE_CONFIG.get(\"DEFAULT_ROLE\")\r\n\r\n  if (\r\n    defaultRole === ROLES.DUKE ||\r\n    defaultRole === ROLES.KNIGHT ||\r\n    defaultRole === ROLES.CIVILIAN\r\n  ) {\r\n    return defaultRole as Role\r\n  }\r\n  \r\n  return ROLES.CIVILIAN\r\n}\r\n\r\nasync function findOrCreateRole(db: Db, roleName: Role) {\r\n  let role = await db.query.roles.findFirst({\r\n    where: eq(roles.name, roleName),\r\n  })\r\n\r\n  if (!role) {\r\n    const [newRole] = await db.insert(roles)\r\n      .values({\r\n        name: roleName,\r\n        description: ROLE_DESCRIPTIONS[roleName],\r\n      })\r\n      .returning()\r\n    role = newRole\r\n  }\r\n\r\n  return role\r\n}\r\n\r\nexport async function assignRoleToUser(db: Db, userId: string, roleId: string) {\r\n  await db.delete(userRoles)\r\n    .where(eq(userRoles.userId, userId))\r\n\r\n  await db.insert(userRoles)\r\n    .values({\r\n      userId,\r\n      roleId,\r\n    })\r\n}\r\n\r\nexport async function getUserRole(userId: string) {\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n  return userRoleRecords[0].role.name\r\n}\r\n\r\nexport async function checkPermission(permission: Permission) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) return false\r\n\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n\r\n  const userRoleNames = userRoleRecords.map(ur => ur.role.name)\r\n  return hasPermission(userRoleNames as Role[], permission)\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  auth,\r\n  signIn,\r\n  signOut\r\n} = NextAuth(() => ({\r\n  secret: process.env.AUTH_SECRET,\r\n  adapter: DrizzleAdapter(createDb(), {\r\n    usersTable: users,\r\n    accountsTable: accounts,\r\n  }),\r\n  providers: [\r\n    GitHub({\r\n      clientId: process.env.AUTH_GITHUB_ID,\r\n      clientSecret: process.env.AUTH_GITHUB_SECRET,\r\n    }),\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        username: { label: \"用户名\", type: \"text\", placeholder: \"请输入用户名\" },\r\n        password: { label: \"密码\", type: \"password\", placeholder: \"请输入密码\" },\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials) {\r\n          throw new Error(\"请输入用户名和密码\")\r\n        }\r\n\r\n        const { username, password } = credentials\r\n\r\n        try {\r\n          authSchema.parse({ username, password })\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        } catch (error) {\r\n          throw new Error(\"输入格式不正确\")\r\n        }\r\n\r\n        const db = createDb()\r\n\r\n        const user = await db.query.users.findFirst({\r\n          where: eq(users.username, username as string),\r\n        })\r\n\r\n        if (!user) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        const isValid = await comparePassword(password as string, user.password as string)\r\n        if (!isValid) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        return {\r\n          ...user,\r\n          password: undefined,\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  events: {\r\n    async signIn({ user }) {\r\n      if (!user.id) return\r\n\r\n      try {\r\n        const db = createDb()\r\n        const existingRole = await db.query.userRoles.findFirst({\r\n          where: eq(userRoles.userId, user.id),\r\n        })\r\n\r\n        if (existingRole) return\r\n\r\n        const defaultRole = await getDefaultRole()\r\n        const role = await findOrCreateRole(db, defaultRole)\r\n        await assignRoleToUser(db, user.id, role.id)\r\n      } catch (error) {\r\n        console.error('Error assigning role:', error)\r\n      }\r\n    },\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id\r\n        token.name = user.name || user.username\r\n        token.username = user.username\r\n        token.image = user.image || generateAvatarUrl(token.name as string)\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token && session.user) {\r\n        session.user.id = token.id as string\r\n        session.user.name = token.name as string\r\n        session.user.username = token.username as string\r\n        session.user.image = token.image as string\r\n\r\n        const db = createDb()\r\n        let userRoleRecords = await db.query.userRoles.findMany({\r\n          where: eq(userRoles.userId, session.user.id),\r\n          with: { role: true },\r\n        })\r\n  \r\n        if (!userRoleRecords.length) {\r\n          const defaultRole = await getDefaultRole()\r\n          const role = await findOrCreateRole(db, defaultRole)\r\n          await assignRoleToUser(db, session.user.id, role.id)\r\n          userRoleRecords = [{\r\n            userId: session.user.id,\r\n            roleId: role.id,\r\n            createdAt: new Date(),\r\n            role: role\r\n          }]\r\n        }\r\n  \r\n        session.user.roles = userRoleRecords.map(ur => ({\r\n          name: ur.role.name,\r\n        }))\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: \"jwt\",\r\n  },\r\n}))\r\n\r\nexport async function register(username: string, password: string) {\r\n  const db = createDb()\r\n  \r\n  const existing = await db.query.users.findFirst({\r\n    where: eq(users.username, username)\r\n  })\r\n\r\n  if (existing) {\r\n    throw new Error(\"用户名已存在\")\r\n  }\r\n\r\n  const hashedPassword = await hashPassword(password)\r\n  \r\n  const [user] = await db.insert(users)\r\n    .values({\r\n      username,\r\n      password: hashedPassword,\r\n    })\r\n    .returning()\r\n\r\n  return user\r\n}\r\n", "import { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { drizzle } from \"drizzle-orm/d1\"\r\nimport * as schema from \"./schema\"\r\n\r\nexport const createDb = () => drizzle(getRequestContext().env.DB, { schema })\r\n\r\nexport type Db = ReturnType<typeof createDb>\r\n", "export const ROLES = {\r\n  EMPEROR: 'emperor',\r\n  <PERSON>U<PERSON>: 'duke',\r\n  <PERSON><PERSON><PERSON><PERSON>: 'knight',\r\n  CIVILIAN: 'civilian',\r\n} as const;\r\n\r\nexport type Role = typeof ROLES[keyof typeof ROLES];\r\n\r\nexport const PERMISSIONS = {\r\n  MANAGE_EMAIL: 'manage_email',\r\n  <PERSON><PERSON>GE_WEBHOOK: 'manage_webhook',\r\n  PROMOTE_USER: 'promote_user',\r\n  MANAGE_CONFIG: 'manage_config',\r\n  MANAGE_API_KEY: 'manage_api_key',\r\n} as const;\r\n\r\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\r\n\r\nexport const ROLE_PERMISSIONS: Record<Role, Permission[]> = {\r\n  [ROLES.EMPEROR]: Object.values(PERMISSIONS),\r\n  [ROLES.DUKE]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_API_KEY,\r\n  ],\r\n  [ROLES.KNIGHT]: [\r\n    PERMISSIONS.<PERSON>NAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n  ],\r\n  [ROLES.CIVILIAN]: [],\r\n} as const;\r\n\r\nexport function hasPermission(userRoles: Role[], permission: Permission): boolean {\r\n  return userRoles.some(role => ROLE_PERMISSIONS[role]?.includes(permission));\r\n} ", "import { integer, sqliteTable, text, primaryKey, uniqueIndex, index } from \"drizzle-orm/sqlite-core\"\r\nimport type { AdapterAccountType } from \"next-auth/adapters\"\r\nimport { relations } from 'drizzle-orm';\r\n\r\n// https://authjs.dev/getting-started/adapters/drizzle\r\nexport const users = sqliteTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: integer(\"emailVerified\", { mode: \"timestamp_ms\" }),\r\n  image: text(\"image\"),\r\n  username: text(\"username\").unique(),\r\n  password: text(\"password\"),\r\n})\r\nexport const accounts = sqliteTable(\r\n  \"account\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => users.id, { onDelete: \"cascade\" }),\r\n    type: text(\"type\").$type<AdapterAccountType>().notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n  },\r\n  (account) => ({\r\n    compoundKey: primaryKey({\r\n      columns: [account.provider, account.providerAccountId],\r\n    }),\r\n  })\r\n)\r\n\r\nexport const emails = sqliteTable(\"email\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  address: text(\"address\").notNull().unique(),\r\n  userId: text(\"userId\").references(() => users.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp_ms\" }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"email_expires_at_idx\").on(table.expiresAt),\r\n}))\r\n\r\nexport const messages = sqliteTable(\"message\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  emailId: text(\"emailId\")\r\n    .notNull()\r\n    .references(() => emails.id, { onDelete: \"cascade\" }),\r\n  fromAddress: text(\"from_address\").notNull(),\r\n  subject: text(\"subject\").notNull(),\r\n  content: text(\"content\").notNull(),\r\n  html: text(\"html\"),\r\n  receivedAt: integer(\"received_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  emailIdIdx: index(\"message_email_id_idx\").on(table.emailId),\r\n}))\r\n\r\nexport const webhooks = sqliteTable('webhook', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id')\r\n    .notNull()\r\n    .references(() => users.id, { onDelete: \"cascade\" }),\r\n  url: text('url').notNull(),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n})\r\n\r\nexport const roles = sqliteTable(\"role\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\").notNull(),\r\n  description: text(\"description\"),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n});\r\n\r\nexport const userRoles = sqliteTable(\"user_role\", {\r\n  userId: text(\"user_id\").notNull().references(() => users.id, { onDelete: \"cascade\" }),\r\n  roleId: text(\"role_id\").notNull().references(() => roles.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  pk: primaryKey({ columns: [table.userId, table.roleId] }),\r\n}));\r\n\r\nexport const apiKeys = sqliteTable('api_keys', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id),\r\n  name: text('name').notNull(),\r\n  key: text('key').notNull().unique(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n}, (table) => ({\r\n  nameUserIdUnique: uniqueIndex('name_user_id_unique').on(table.name, table.userId)\r\n}));\r\n\r\nexport const revoked_credentials = sqliteTable('revoked_credential', {\r\n  jti: text('jti').primaryKey(),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp_ms' }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"revoked_expires_at_idx\").on(table.expiresAt),\r\n}));\r\n\r\nexport const apiKeysRelations = relations(apiKeys, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [apiKeys.userId],\r\n    references: [users.id],\r\n  }),\r\n}));\r\n\r\nexport const userRolesRelations = relations(userRoles, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userRoles.userId],\r\n    references: [users.id],\r\n  }),\r\n  role: one(roles, {\r\n    fields: [userRoles.roleId],\r\n    references: [roles.id],\r\n  }),\r\n}));\r\n\r\nexport const usersRelations = relations(users, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n  apiKeys: many(apiKeys),\r\n}));\r\n\r\nexport const rolesRelations = relations(roles, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n}));", "import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport async function hashPassword(password: string): Promise<string> {\r\n  const encoder = new TextEncoder()\r\n  const salt = process.env.AUTH_SECRET || ''\r\n  const data = encoder.encode(password + salt)\r\n  const hash = await crypto.subtle.digest('SHA-256', data)\r\n  return btoa(String.fromCharCode(...new Uint8Array(hash)))\r\n}\r\n\r\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\r\n  const hash = await hashPassword(password)\r\n  return hash === hashedPassword\r\n}", "import { z } from \"zod\"\r\n\r\nexport const authSchema = z.object({\r\n  username: z.string()\r\n    .min(1, \"用户名不能为空\")\r\n    .max(20, \"用户名不能超过20个字符\")\r\n    .regex(/^[a-zA-Z0-9_-]+$/, \"用户名只能包含字母、数字、下划线和横杠\")\r\n    .refine(val => !val.includes('@'), \"用户名不能是邮箱格式\"),\r\n  password: z.string()\r\n    .min(8, \"密码长度必须大于等于8位\")\r\n})\r\n\r\nexport type AuthSchema = z.infer<typeof authSchema>", "export const urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n", "/* @ts-self-types=\"./index.d.ts\" */\nimport { url<PERSON>lphabet as scopedUrlAlphabet } from './url-alphabet/index.js'\nexport { urlAlphabet } from './url-alphabet/index.js'\nexport let random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nexport let customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << Math.log2(alphabet.length - 1)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length >= size) return id\n      }\n    }\n  }\n}\nexport let customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size | 0, random)\nexport let nanoid = (size = 21) => {\n  let id = ''\n  let bytes = crypto.getRandomValues(new Uint8Array((size |= 0)))\n  while (size--) {\n    id += scopedUrlAlphabet[bytes[size] & 63]\n  }\n  return id\n}\n"], "names": ["runtime", "GET", "hasPermission", "checkPermission", "PERMISSIONS", "MANAGE_API_KEY", "NextResponse", "json", "error", "status", "session", "auth", "db", "createDb", "keys", "query", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "find<PERSON>any", "where", "eq", "userId", "user", "id", "orderBy", "desc", "createdAt", "map", "key", "undefined", "console", "POST", "request", "name", "trim", "nanoid", "insert", "values", "expiresAt", "Date", "now", "getUserId", "headersList", "headers", "get", "COLORS", "ROLE_DESCRIPTIONS", "ROLES", "EMPEROR", "DUKE", "KNIGHT", "CIVILIAN", "getDefaultRole", "defaultRole", "getRequestContext", "env", "SITE_CONFIG", "findOrCreateRole", "<PERSON><PERSON><PERSON>", "role", "roles", "<PERSON><PERSON><PERSON><PERSON>", "newRole", "description", "returning", "assignRoleToUser", "roleId", "delete", "userRoles", "permission", "userRoleNames", "userRoleRecords", "with", "ur", "handlers", "signIn", "signOut", "NextAuth", "secret", "process", "AUTH_SECRET", "adapter", "DrizzleAdapter", "usersTable", "users", "accountsTable", "accounts", "providers", "GitHub", "clientId", "AUTH_GITHUB_ID", "clientSecret", "AUTH_GITHUB_SECRET", "CredentialsProvider", "credentials", "username", "label", "type", "placeholder", "password", "authorize", "Error", "authSchema", "parse", "comparePassword", "events", "existingRole", "callbacks", "jwt", "token", "image", "generateAvatarUrl", "initial", "toUpperCase", "colorIndex", "Array", "from", "reduce", "acc", "char", "charCodeAt", "length", "backgroundColor", "svg", "bytes", "TextEncoder", "encode", "base64", "<PERSON><PERSON><PERSON>", "toString", "strategy", "register", "existing", "hashedPassword", "hashPassword", "drizzle", "DB", "schema", "MANAGE_EMAIL", "MANAGE_WEBHOOK", "PROMOTE_USER", "MANAGE_CONFIG", "ROLE_PERMISSIONS", "Object", "some", "includes", "sqliteTable", "text", "<PERSON><PERSON><PERSON>", "$defaultFn", "crypto", "randomUUID", "email", "unique", "emailVerified", "integer", "mode", "notNull", "references", "onDelete", "$type", "provider", "providerAccountId", "refresh_token", "access_token", "expires_at", "token_type", "scope", "id_token", "session_state", "<PERSON><PERSON><PERSON>", "columns", "account", "emails", "address", "expiresAtIdx", "index", "on", "table", "messages", "emailId", "fromAddress", "subject", "content", "html", "receivedAt", "emailIdIdx", "webhooks", "url", "enabled", "default", "updatedAt", "pk", "nameUserIdUnique", "uniqueIndex", "jti", "apiKeysRelations", "relations", "one", "fields", "userRolesRelations", "usersRelations", "many", "rolesRelations", "cn", "inputs", "twMerge", "clsx", "encoder", "salt", "data", "btoa", "String", "fromCharCode", "Uint8Array", "subtle", "digest", "hash", "z", "min", "max", "regex", "refine", "val"], "sourceRoot": "", "ignoreList": [13, 14]}