if(!self.define){let e,s={};const t=(t,c)=>(t=new URL(t+".js",c).href,s[t]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=t,e.onload=s,document.head.appendChild(e)}else e=t,importScripts(t),s()})).then((()=>{let e=s[t];if(!e)throw new Error(`Module ${t} didn’t register its module`);return e})));self.define=(c,n)=>{const i=e||("document"in self?document.currentScript.src:"")||location.href;if(s[i])return;let a={};const o=e=>t(e,i),r={module:{uri:i},exports:a,require:o};s[i]=Promise.all(c.map((e=>r[e]||o(e)))).then((e=>(n(...e),a)))}}define(["./workbox-e9849328"],(function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"804a4d4f4d46d72ec7c42f49918071dd"},{url:"/_next/static/7NqA27To1KPfWbWhtzc2h/_buildManifest.js",revision:"b73d4dd1d4986e947046fc286e26decc"},{url:"/_next/static/7NqA27To1KPfWbWhtzc2h/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/140-7319cd6b287f83ce.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/179-5fce4ae0873ea689.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/415.ca3729d0c622ec5d.js",revision:"ca3729d0c622ec5d"},{url:"/_next/static/chunks/421-714656322aee6619.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/46-c06fe490ac94d88b.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/478-63a83f1993fe772a.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/499-481cbf3b75750439.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/4cf66d08-0d88ad2d16e9c229.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/558.511060672a7c532d.js",revision:"511060672a7c532d"},{url:"/_next/static/chunks/599-805fabe1f1660699.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/607-5e09e2ee0af94186.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/687-c928b58803c384a8.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/_not-found/page-8c0838095eafb9ac.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/api-keys/%5Bid%5D/route-6c9108c4aca6f930.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/api-keys/route-8f9c3a2d24a84236.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/auth/%5B...auth%5D/route-42262bf28f983cb6.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/auth/register/route-7bedbf9731932f99.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/config/route-08f9ff6ff1d0536d.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/emails/%5Baddress%5D/%5BmessageId%5D/route-9084d0bc29d02302.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/emails/%5Baddress%5D/credentials/route-89cc5441d79ed5ec.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/emails/%5Baddress%5D/messages/route-cabcfc8ea98a867c.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/emails/%5Baddress%5D/route-9c22e969857d418b.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/emails/generate/route-18ddea818cdb95e3.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/emails/route-726a6d7e5ef2dba8.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/roles/init-emperor/route-09c24a100598c571.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/roles/promote/route-12280be2d805ec30.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/roles/users/route-8c0e39bca122d097.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/webhook/route-de090e544715a3dd.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/api/webhook/test/route-b13c3ac4b513fb7b.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/layout-4ae61b6e10347213.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/login/page-fd7b89d9689d609c.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/moe/page-23626487272a490c.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/page-9ac2884c1140e101.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/app/profile/page-4ec5d08bbbbefd9d.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/framework-b5a654ae7593d661.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/main-7f41e52be9241c5b.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/main-app-f71817536ec3a067.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/pages/_app-47f717e6fb0c2b12.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/pages/_error-55693689453ec1d5.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-18ae9cc0a8947578.js",revision:"7NqA27To1KPfWbWhtzc2h"},{url:"/_next/static/css/f140e6b974a5e906.css",revision:"f140e6b974a5e906"},{url:"/fonts/zpix.ttf",revision:"04cd0a606f67cf86e1075ff1b4d0b108"},{url:"/icons/icon-192x192.png",revision:"a40c836f3f904b63a1722fd0ae7db9bf"},{url:"/icons/icon-512x512.png",revision:"0e067d29946ced1568867073562bc875"},{url:"/manifest.json",revision:"5198a8da820275e7d6f99ca5ca656c5c"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:t,state:c})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")}),new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")}),new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>!(self.origin===e.origin)),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")}));
