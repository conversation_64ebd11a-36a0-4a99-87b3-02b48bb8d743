(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[641],{5521:e=>{"use strict";e.exports=require("node:async_hooks")},5356:e=>{"use strict";e.exports=require("node:buffer")},1993:(e,t,a)=>{"use strict";a.r(t),a.d(t,{ComponentMod:()=>q,default:()=>D});var i,r={};a.r(r),a.d(r,{POST:()=>_,runtime:()=>C});var s={};a.r(s),a.d(s,{patchFetch:()=>P,routeModule:()=>y,serverHooks:()=>I,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>T});var o=a(793),n=a(6590),c=a(3439),l=a(4651),u=a(6292),m=a(8498),f=a(658),p=a(3922),d=a(615),g=a(9230),h=a(7536),v=a(9066);let x=[{label:"1小时",value:36e5},{label:"24小时",value:864e5},{label:"3天",value:2592e5},{label:"永久",value:0}];var E=a(485),S=a(888),M=a(1514),R=a(1639),w=a(789),b=a(132);let C="edge";async function _(e){let t=(0,d.d)(),a=(0,S.getRequestContext)().env,i=await (0,M.F)(),r=await (0,R.uG)(i);try{if(r!==w.gg.EMPEROR){let e=await a.SITE_CONFIG.get("MAX_EMAILS")||E.q.MAX_ACTIVE_EMAILS.toString(),r=await t.select({count:(0,h.ll)`count(*)`}).from(g.emails).where((0,v.Uo)((0,v.eq)(g.emails.userId,i),(0,v.gt)(g.emails.expiresAt,new Date)));if(Number(r[0].count)>=Number(e))return f.Rp.json({error:`已达到最大邮箱数量限制 (${e})`},{status:403})}let{name:s,expiryTime:o,domain:n}=await e.json();if(!x.some(e=>e.value===o))return f.Rp.json({error:"无效的过期时间"},{status:400});let c=await a.SITE_CONFIG.get("EMAIL_DOMAINS"),l=c?c.split(","):["moemail.app"];if(!l||!l.includes(n))return f.Rp.json({error:"无效的域名"},{status:400});let u=`${s||(0,p.Ak)(8)}@${n}`;if(await t.query.emails.findFirst({where:(0,v.eq)((0,h.ll)`LOWER(${g.emails.address})`,u.toLowerCase())}))return f.Rp.json({error:"该邮箱地址已被使用"},{status:409});let m=new Date,d=new Date(0===o?"9999-01-01T00:00:00.000Z":m.getTime()+o),S=await t.insert(g.emails).values({address:u,createdAt:m,expiresAt:d,userId:i}).returning({id:g.emails.id,address:g.emails.address}),M=await (0,b.Z)(S[0].address,0===o?0:o/1e3);return f.Rp.json({id:S[0].id,email:S[0].address,credential:M})}catch(e){return console.error("Failed to generate email:",e),f.Rp.json({error:"创建邮箱失败"},{status:500})}}let y=new l.AppRouteRouteModule({definition:{kind:u.A.APP_ROUTE,page:"/api/emails/generate/route",pathname:"/api/emails/generate",filename:"route",bundlePath:"app/api/emails/generate/route"},resolvedPagePath:"F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\generate\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:A,workUnitAsyncStorage:T,serverHooks:I}=y;function P(){return(0,m.V5)({workAsyncStorage:A,workUnitAsyncStorage:T})}let O=null==(i=self.__RSC_MANIFEST)?void 0:i["/api/emails/generate/route"],k=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);O&&k&&(0,n.fQ)({page:"/api/emails/generate/route",clientReferenceManifest:O,serverActionsManifest:k,serverModuleMap:(0,o.e)({serverActionsManifest:k})});let q=s,D=c.s.wrap(y,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\Mail\\moemail_source",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\Mail\\moemail_source"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\Mail\\moemail_source\\next.config.ts",configFileName:"next.config.ts"}})},485:(e,t,a)=>{"use strict";a.d(t,{q:()=>i,V:()=>r});let i={MAX_ACTIVE_EMAILS:30,POLL_INTERVAL:1e4},r={MAX_RETRIES:3,TIMEOUT:1e4,RETRY_DELAY:1e3,EVENTS:{NEW_MESSAGE:"new_message"}}}},e=>{var t=t=>e(e.s=t);e.O(0,[810,702,267,658,51,92,137,815],()=>t(1993));var a=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/emails/generate/route"]=a}]);
//# sourceMappingURL=route.js.map