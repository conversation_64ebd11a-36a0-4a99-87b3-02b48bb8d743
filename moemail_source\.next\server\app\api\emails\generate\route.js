(()=>{var e={};e.id=641,e.ids=[641],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},2046:(e,t,r)=>{"use strict";let n,s,i,a,o;r.r(t),r.d(t,{patchFetch:()=>e3,routeModule:()=>e5,serverHooks:()=>e8,workAsyncStorage:()=>e6,workUnitAsyncStorage:()=>e4});var u={};r.r(u),r.d(u,{POST:()=>e2});var l=r(231),c=r(4912),d=r(2271),h=r(1638);let p=require("node:crypto"),f=Symbol.for("drizzle:entityKind");function m(e,t){if(!e||"object"!=typeof e)return!1;if(e instanceof t)return!0;if(!Object.prototype.hasOwnProperty.call(t,f))throw Error(`Class "${t.name??"<unknown>"}" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Drizzle, please report this as a bug.`);let r=Object.getPrototypeOf(e).constructor;if(r)for(;r;){if(f in r&&r[f]===t[f])return!0;r=Object.getPrototypeOf(r)}return!1}Symbol.for("drizzle:hasOwnEntityKind");class y{static [f]="ColumnBuilder";config;constructor(e,t,r){this.config={name:e,keyAsName:""===e,notNull:!1,default:void 0,hasDefault:!1,primaryKey:!1,isUnique:!1,uniqueName:void 0,uniqueType:void 0,dataType:t,columnType:r,generated:void 0}}$type(){return this}notNull(){return this.config.notNull=!0,this}default(e){return this.config.default=e,this.config.hasDefault=!0,this}$defaultFn(e){return this.config.defaultFn=e,this.config.hasDefault=!0,this}$default=this.$defaultFn;$onUpdateFn(e){return this.config.onUpdateFn=e,this.config.hasDefault=!0,this}$onUpdate=this.$onUpdateFn;primaryKey(){return this.config.primaryKey=!0,this.config.notNull=!0,this}setName(e){""===this.config.name&&(this.config.name=e)}}class g{constructor(e,t){this.table=e,this.config=t,this.name=t.name,this.keyAsName=t.keyAsName,this.notNull=t.notNull,this.default=t.default,this.defaultFn=t.defaultFn,this.onUpdateFn=t.onUpdateFn,this.hasDefault=t.hasDefault,this.primary=t.primaryKey,this.isUnique=t.isUnique,this.uniqueName=t.uniqueName,this.uniqueType=t.uniqueType,this.dataType=t.dataType,this.columnType=t.columnType,this.generated=t.generated,this.generatedIdentity=t.generatedIdentity}static [f]="Column";name;keyAsName;primary;notNull;default;defaultFn;onUpdateFn;hasDefault;isUnique;uniqueName;uniqueType;dataType;columnType;enumValues=void 0;generated=void 0;generatedIdentity=void 0;config;mapFromDriverValue(e){return e}mapToDriverValue(e){return e}shouldDisableInsert(){return void 0!==this.config.generated&&"byDefault"!==this.config.generated.type}}let b=Symbol.for("drizzle:Name");class S{static [f]="PgForeignKeyBuilder";reference;_onUpdate="no action";_onDelete="no action";constructor(e,t){this.reference=()=>{let{name:t,columns:r,foreignColumns:n}=e();return{name:t,columns:r,foreignTable:n[0].table,foreignColumns:n}},t&&(this._onUpdate=t.onUpdate,this._onDelete=t.onDelete)}onUpdate(e){return this._onUpdate=void 0===e?"no action":e,this}onDelete(e){return this._onDelete=void 0===e?"no action":e,this}build(e){return new w(e,this)}}class w{constructor(e,t){this.table=e,this.reference=t.reference,this.onUpdate=t._onUpdate,this.onDelete=t._onDelete}static [f]="PgForeignKey";reference;onUpdate;onDelete;getName(){let{name:e,columns:t,foreignColumns:r}=this.reference(),n=t.map(e=>e.name),s=r.map(e=>e.name),i=[this.table[b],...n,r[0].table[b],...s];return e??`${i.join("_")}_fk`}}function E(e,...t){return e(...t)}function v(e,t){return`${e[b]}_${t.join("_")}_unique`}class A{constructor(e,t){this.name=t,this.columns=e}static [f]=null;columns;nullsNotDistinctConfig=!1;nullsNotDistinct(){return this.nullsNotDistinctConfig=!0,this}build(e){return new O(e,this.columns,this.nullsNotDistinctConfig,this.name)}}class C{static [f]=null;name;constructor(e){this.name=e}on(...e){return new A(e,this.name)}}class O{constructor(e,t,r,n){this.table=e,this.columns=t,this.name=n??v(this.table,this.columns.map(e=>e.name)),this.nullsNotDistinct=r}static [f]=null;columns;name;nullsNotDistinct=!1;getName(){return this.name}}function T(e,t,r){for(let n=t;n<e.length;n++){let s=e[n];if("\\"===s){n++;continue}if('"'===s)return[e.slice(t,n).replace(/\\/g,""),n+1];if(!r&&(","===s||"}"===s))return[e.slice(t,n).replace(/\\/g,""),n]}return[e.slice(t).replace(/\\/g,""),e.length]}class D extends y{foreignKeyConfigs=[];static [f]="PgColumnBuilder";array(e){return new U(this.config.name,this,e)}references(e,t={}){return this.foreignKeyConfigs.push({ref:e,actions:t}),this}unique(e,t){return this.config.isUnique=!0,this.config.uniqueName=e,this.config.uniqueType=t?.nulls,this}generatedAlwaysAs(e){return this.config.generated={as:e,type:"always",mode:"stored"},this}buildForeignKeys(e,t){return this.foreignKeyConfigs.map(({ref:r,actions:n})=>E((r,n)=>{let s=new S(()=>({columns:[e],foreignColumns:[r()]}));return n.onUpdate&&s.onUpdate(n.onUpdate),n.onDelete&&s.onDelete(n.onDelete),s.build(t)},r,n))}buildExtraConfigColumn(e){return new N(e,this.config)}}class P extends g{constructor(e,t){t.uniqueName||(t.uniqueName=v(e,[t.name])),super(e,t),this.table=e}static [f]="PgColumn"}class N extends P{static [f]="ExtraConfigColumn";getSQLType(){return this.getSQLType()}indexConfig={order:this.config.order??"asc",nulls:this.config.nulls??"last",opClass:this.config.opClass};defaultConfig={order:"asc",nulls:"last",opClass:void 0};asc(){return this.indexConfig.order="asc",this}desc(){return this.indexConfig.order="desc",this}nullsFirst(){return this.indexConfig.nulls="first",this}nullsLast(){return this.indexConfig.nulls="last",this}op(e){return this.indexConfig.opClass=e,this}}class _{static [f]=null;constructor(e,t,r,n){this.name=e,this.keyAsName=t,this.type=r,this.indexConfig=n}name;keyAsName;type;indexConfig}class U extends D{static [f]="PgArrayBuilder";constructor(e,t,r){super(e,"array","PgArray"),this.config.baseBuilder=t,this.config.size=r}build(e){let t=this.config.baseBuilder.build(e);return new j(e,this.config,t)}}class j extends P{constructor(e,t,r,n){super(e,t),this.baseColumn=r,this.range=n,this.size=t.size}size;static [f]="PgArray";getSQLType(){return`${this.baseColumn.getSQLType()}[${"number"==typeof this.size?this.size:""}]`}mapFromDriverValue(e){return"string"==typeof e&&(e=function(e){let[t]=function e(t,r=0){let n=[],s=r,i=!1;for(;s<t.length;){let a=t[s];if(","===a){(i||s===r)&&n.push(""),i=!0,s++;continue}if(i=!1,"\\"===a){s+=2;continue}if('"'===a){let[e,r]=T(t,s+1,!0);n.push(e),s=r;continue}if("}"===a)return[n,s+1];if("{"===a){let[r,i]=e(t,s+1);n.push(r),s=i;continue}let[o,u]=T(t,s,!1);n.push(o),s=u}return[n,s]}(e,1);return t}(e)),e.map(e=>this.baseColumn.mapFromDriverValue(e))}mapToDriverValue(e,t=!1){let r=e.map(e=>null===e?null:m(this.baseColumn,j)?this.baseColumn.mapToDriverValue(e,!0):this.baseColumn.mapToDriverValue(e));return t?r:function e(t){return`{${t.map(t=>Array.isArray(t)?e(t):"string"==typeof t?`"${t.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`:`${t}`).join(",")}}`}(r)}}let K=Symbol.for("drizzle:isPgEnum");class x extends D{static [f]="PgEnumColumnBuilder";constructor(e,t){super(e,"string","PgEnumColumn"),this.config.enum=t}build(e){return new H(e,this.config)}}class H extends P{static [f]="PgEnumColumn";enum=this.config.enum;enumValues=this.config.enum.enumValues;constructor(e,t){super(e,t),this.enum=t.enum}getSQLType(){return this.enum.enumName}}class R{static [f]="Subquery";constructor(e,t,r,n=!1){this._={brand:"Subquery",sql:e,selectedFields:t,alias:r,isWith:n}}}class k extends null{static [f]=null}let q={startActiveSpan:(e,t)=>i?(a||(a=i.trace.getTracer("drizzle-orm","0.36.4")),E((r,n)=>n.startActiveSpan(e,e=>{try{return t(e)}catch(t){throw e.setStatus({code:r.SpanStatusCode.ERROR,message:t instanceof Error?t.message:"Unknown error"}),t}finally{e.end()}}),i,a)):t()},$=Symbol.for("drizzle:ViewBaseConfig"),I=Symbol.for("drizzle:Schema"),M=Symbol.for("drizzle:Columns"),F=Symbol.for("drizzle:ExtraConfigColumns"),L=Symbol.for("drizzle:OriginalName"),W=Symbol.for("drizzle:BaseName"),z=Symbol.for("drizzle:IsAlias"),J=Symbol.for("drizzle:ExtraConfigBuilder"),B=Symbol.for("drizzle:IsDrizzleTable");class Q{static [f]="Table";static Symbol={Name:b,Schema:I,OriginalName:L,Columns:M,ExtraConfigColumns:F,BaseName:W,IsAlias:z,ExtraConfigBuilder:J};[b];[L];[I];[M];[F];[W];[z]=!1;[B]=!0;[J]=void 0;constructor(e,t,r){this[b]=this[L]=e,this[I]=t,this[W]=r}}class V{static [f]=null}function G(e){return null!=e&&"function"==typeof e.getSQL}class X{static [f]="StringChunk";value;constructor(e){this.value=Array.isArray(e)?e:[e]}getSQL(){return new Y([this])}}class Y{constructor(e){this.queryChunks=e}static [f]="SQL";decoder=ee;shouldInlineParams=!1;append(e){return this.queryChunks.push(...e.queryChunks),this}toQuery(e){return q.startActiveSpan("drizzle.buildSQL",t=>{let r=this.buildQueryFromSourceParams(this.queryChunks,e);return t?.setAttributes({"drizzle.query.text":r.sql,"drizzle.query.params":JSON.stringify(r.params)}),r})}buildQueryFromSourceParams(e,t){let r=Object.assign({},t,{inlineParams:t.inlineParams||this.shouldInlineParams,paramStartIndex:t.paramStartIndex||{value:0}}),{casing:n,escapeName:s,escapeParam:i,prepareTyping:a,inlineParams:o,paramStartIndex:u}=r;return function(e){let t={sql:"",params:[]};for(let r of e)t.sql+=r.sql,t.params.push(...r.params),r.typings?.length&&(t.typings||(t.typings=[]),t.typings.push(...r.typings));return t}(e.map(e=>{if(m(e,X))return{sql:e.value.join(""),params:[]};if(m(e,Z))return{sql:s(e.value),params:[]};if(void 0===e)return{sql:"",params:[]};if(Array.isArray(e)){let t=[new X("(")];for(let[r,n]of e.entries())t.push(n),r<e.length-1&&t.push(new X(", "));return t.push(new X(")")),this.buildQueryFromSourceParams(t,r)}if(m(e,Y))return this.buildQueryFromSourceParams(e.queryChunks,{...r,inlineParams:o||e.shouldInlineParams});if(m(e,Q)){let t=e[Q.Symbol.Schema],r=e[Q.Symbol.Name];return{sql:void 0===t?s(r):s(t)+"."+s(r),params:[]}}if(m(e,g)){let r=n.getColumnCasing(e);if("indexes"===t.invokeSource)return{sql:s(r),params:[]};let i=e.table[Q.Symbol.Schema];return{sql:e.table[z]||void 0===i?s(e.table[Q.Symbol.Name])+"."+s(r):s(i)+"."+s(e.table[Q.Symbol.Name])+"."+s(r),params:[]}}if(m(e,ei)){let t=e[$].schema,r=e[$].name;return{sql:void 0===t?s(r):s(t)+"."+s(r),params:[]}}if(m(e,er)){if(m(e.value,es))return{sql:i(u.value++,e),params:[e],typings:["none"]};let t=null===e.value?null:e.encoder.mapToDriverValue(e.value);if(m(t,Y))return this.buildQueryFromSourceParams([t],r);if(o)return{sql:this.mapInlineParam(t,r),params:[]};let n=["none"];return a&&(n=[a(e.encoder)]),{sql:i(u.value++,t),params:[t],typings:n}}return m(e,es)?{sql:i(u.value++,e),params:[e],typings:["none"]}:m(e,Y.Aliased)&&void 0!==e.fieldAlias?{sql:s(e.fieldAlias),params:[]}:m(e,R)?e._.isWith?{sql:s(e._.alias),params:[]}:this.buildQueryFromSourceParams([new X("("),e._.sql,new X(") "),new Z(e._.alias)],r):e&&"function"==typeof e&&K in e&&!0===e[K]?e.schema?{sql:s(e.schema)+"."+s(e.enumName),params:[]}:{sql:s(e.enumName),params:[]}:G(e)?e.shouldOmitSQLParens?.()?this.buildQueryFromSourceParams([e.getSQL()],r):this.buildQueryFromSourceParams([new X("("),e.getSQL(),new X(")")],r):o?{sql:this.mapInlineParam(e,r),params:[]}:{sql:i(u.value++,e),params:[e],typings:["none"]}}))}mapInlineParam(e,{escapeString:t}){if(null===e)return"null";if("number"==typeof e||"boolean"==typeof e)return e.toString();if("string"==typeof e)return t(e);if("object"==typeof e){let r=e.toString();return"[object Object]"===r?t(JSON.stringify(e)):t(r)}throw Error("Unexpected param value: "+e)}getSQL(){return this}as(e){return void 0===e?this:new Y.Aliased(this,e)}mapWith(e){return this.decoder="function"==typeof e?{mapFromDriverValue:e}:e,this}inlineParams(){return this.shouldInlineParams=!0,this}if(e){return e?this:void 0}}class Z{constructor(e){this.value=e}static [f]="Name";brand;getSQL(){return new Y([this])}}let ee={mapFromDriverValue:e=>e},et={mapToDriverValue:e=>e};({...ee,...et});class er{constructor(e,t=et){this.value=e,this.encoder=t}static [f]="Param";brand;getSQL(){return new Y([this])}}function en(e,...t){let r=[];for(let[n,s]of((t.length>0||e.length>0&&""!==e[0])&&r.push(new X(e[0])),t.entries()))r.push(s,new X(e[n+1]));return new Y(r)}(e=>{e.empty=function(){return new Y([])},e.fromList=function(e){return new Y(e)},e.raw=function(e){return new Y([new X(e)])},e.join=function(e,t){let r=[];for(let[n,s]of e.entries())n>0&&void 0!==t&&r.push(t),r.push(s);return new Y(r)},e.identifier=function(e){return new Z(e)},e.placeholder=function(e){return new es(e)},e.param=function(e,t){return new er(e,t)}})(en||(en={})),(e=>{class t{constructor(e,t){this.sql=e,this.fieldAlias=t}static [f]="SQL.Aliased";isSelectionField=!1;getSQL(){return this.sql}clone(){return new t(this.sql,this.fieldAlias)}}e.Aliased=t})(Y||(Y={}));class es{constructor(e){this.name=e}static [f]="Placeholder";getSQL(){return new Y([this])}}class ei{static [f]="View";[$];constructor({name:e,schema:t,selectedFields:r,query:n}){this[$]={name:e,originalName:e,schema:t,selectedFields:r,query:n,isExisting:!n,isAlias:!1}}getSQL(){return new Y([this])}}function ea(e,t){return"object"!=typeof t||null===t||!("mapToDriverValue"in t)||"function"!=typeof t.mapToDriverValue||G(e)||m(e,er)||m(e,es)||m(e,g)||m(e,Q)||m(e,ei)?e:new er(e,t)}g.prototype.getSQL=function(){return new Y([this])},Q.prototype.getSQL=function(){return new Y([this])},R.prototype.getSQL=function(){return new Y([this])};let eo=(e,t)=>en`${e} = ${ea(t,e)}`,eu=(e,t)=>en`${e} > ${ea(t,e)}`;var el=r(9488);let ec=new TextEncoder,ed=new TextDecoder;function eh(e){let t=e;return("string"==typeof t&&(t=ec.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class ep extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class ef extends ep{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class em extends ep{static code="ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class ey extends ep{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class eg extends ep{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}let eb=(e,t)=>{let r=`SHA-${e.slice(-3)}`;switch(e){case"HS256":case"HS384":case"HS512":return{hash:r,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:r,name:"RSA-PSS",saltLength:parseInt(e.slice(-3),10)>>3};case"RS256":case"RS384":case"RS512":return{hash:r,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:r,name:"ECDSA",namedCurve:t.namedCurve};case"Ed25519":case"EdDSA":return{name:"Ed25519"};default:throw new ef(`alg ${e} is not supported either by JOSE or your javascript runtime`)}},eS=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}};function ew(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function eE(e,t){return e.name===t}function ev(e){return parseInt(e.name.slice(4),10)}function eA(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let eC=(e,...t)=>eA("Key must be ",e,...t);function eO(e,t,...r){return eA(`Key for the ${e} algorithm must be `,t,...r)}let eT=async(e,t,r)=>{if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError(eC(t,"CryptoKey","KeyObject","JSON Web Key"));return crypto.subtle.importKey("raw",t,{hash:`SHA-${e.slice(-3)}`,name:"HMAC"},!1,[r])}return function(e,t,r){switch(t){case"HS256":case"HS384":case"HS512":{if(!eE(e.algorithm,"HMAC"))throw ew("HMAC");let r=parseInt(t.slice(2),10);if(ev(e.algorithm.hash)!==r)throw ew(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!eE(e.algorithm,"RSASSA-PKCS1-v1_5"))throw ew("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if(ev(e.algorithm.hash)!==r)throw ew(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!eE(e.algorithm,"RSA-PSS"))throw ew("RSA-PSS");let r=parseInt(t.slice(2),10);if(ev(e.algorithm.hash)!==r)throw ew(`SHA-${r}`,"algorithm.hash");break}case"Ed25519":case"EdDSA":if(!eE(e.algorithm,"Ed25519"))throw ew("Ed25519");break;case"ES256":case"ES384":case"ES512":{if(!eE(e.algorithm,"ECDSA"))throw ew("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw ew(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}(function(e,t){if(t&&!e.usages.includes(t))throw TypeError(`CryptoKey does not support this operation, its usages must include ${t}.`)})(e,r)}(t,e,r),t},eD=async(e,t,r)=>{let n=await eT(e,t,"sign");return eS(e,n),new Uint8Array(await crypto.subtle.sign(eb(e,n.algorithm),n,r))},eP=(...e)=>{let t;let r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0};function eN(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function e_(e){return e?.[Symbol.toStringTag]==="KeyObject"}let eU=e=>eN(e)||e_(e),ej=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function eK(e){return ej(e)&&"string"==typeof e.kty}let ex=e=>e?.[Symbol.toStringTag],eH=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let n;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):n=r;break;case e.startsWith("PBES2"):n="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):n=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):n="wrapKey";break;case"decrypt"===r:n=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(n&&t.key_ops?.includes?.(n)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${n}" when present`)}return!0},eR=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(eK(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&eH(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!eU(t))throw TypeError(eO(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${ex(t)} instances for symmetric algorithms must be of type "secret"`)}},ek=(e,t,r)=>{if(eK(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&eH(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&eH(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!eU(t))throw TypeError(eO(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${ex(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${ex(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${ex(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${ex(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${ex(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},eq=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?eR(e,t,r):ek(e,t,r)},e$=(e,t,r,n,s)=>{let i;if(void 0!==s.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let a of(i=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!i.has(a))throw new ef(`Extension Header Parameter "${a}" is not recognized`);if(void 0===s[a])throw new e(`Extension Header Parameter "${a}" is missing`);if(i.get(a)&&void 0===n[a])throw new e(`Extension Header Parameter "${a}" MUST be integrity protected`)}return new Set(n.crit)},eI=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new ef('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new ef('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new ef('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new ef('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n={...e};return delete n.alg,delete n.use,crypto.subtle.importKey("jwk",n,t,e.ext??!e.d,e.key_ops??r)},eM=async(e,t,r,n=!1)=>{let s=(o||=new WeakMap).get(e);if(s?.[r])return s[r];let i=await eI({...t,alg:r});return n&&Object.freeze(e),s?s[r]=i:o.set(e,{[r]:i}),i},eF=(e,t)=>{let r;let n=(o||=new WeakMap).get(e);if(n?.[t])return n[t];let s="public"===e.type,i=!!s;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,i,s?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,i,[s?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let n;switch(t){case"RSA-OAEP":n="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":n="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":n="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":n="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:n},i,s?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:n},i,[s?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let n=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!n)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},i,[s?"verify":"sign"])),"ES384"===t&&"P-384"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},i,[s?"verify":"sign"])),"ES512"===t&&"P-521"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},i,[s?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:n},i,s?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return n?n[t]=r:o.set(e,{[t]:r}),r},eL=async(e,t)=>{if(e instanceof Uint8Array||eN(e))return e;if(e_(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return eF(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return eM(e,r,t)}if(eK(e))return e.k?function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:ed.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=ed.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64(e);let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}(e.k):eM(e,e,t,!0);throw Error("unreachable")};class eW{#e;#t;#r;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this.#e=e}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setUnprotectedHeader(e){if(this.#r)throw TypeError("setUnprotectedHeader can only be called once");return this.#r=e,this}async sign(e,t){let r;if(!this.#t&&!this.#r)throw new em("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!eP(this.#t,this.#r))throw new em("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let n={...this.#t,...this.#r},s=e$(em,new Map([["b64",!0]]),t?.crit,this.#t,n),i=!0;if(s.has("b64")&&"boolean"!=typeof(i=this.#t.b64))throw new em('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:a}=n;if("string"!=typeof a||!a)throw new em('JWS "alg" (Algorithm) Header Parameter missing or invalid');eq(a,e,"sign");let o=this.#e;i&&(o=ec.encode(eh(o)));let u=function(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}(r=this.#t?ec.encode(eh(JSON.stringify(this.#t))):ec.encode(""),ec.encode("."),o),l=await eL(e,a),c={signature:eh(await eD(a,l,u)),payload:""};return i&&(c.payload=ed.decode(o)),this.#r&&(c.header=this.#r),this.#t&&(c.protected=ed.decode(r)),c}}class ez{#n;constructor(e){this.#n=new eW(e)}setProtectedHeader(e){return this.#n.setProtectedHeader(e),this}async sign(e,t){let r=await this.#n.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}let eJ=e=>Math.floor(e.getTime()/1e3),eB=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,eQ=e=>{let t;let r=eB.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function eV(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}class eG{#e;constructor(e){if(!ej(e))throw TypeError("JWT Claims Set MUST be an object");this.#e=structuredClone(e)}data(){return ec.encode(JSON.stringify(this.#e))}get iss(){return this.#e.iss}set iss(e){this.#e.iss=e}get sub(){return this.#e.sub}set sub(e){this.#e.sub=e}get aud(){return this.#e.aud}set aud(e){this.#e.aud=e}set jti(e){this.#e.jti=e}set nbf(e){"number"==typeof e?this.#e.nbf=eV("setNotBefore",e):e instanceof Date?this.#e.nbf=eV("setNotBefore",eJ(e)):this.#e.nbf=eJ(new Date)+eQ(e)}set exp(e){"number"==typeof e?this.#e.exp=eV("setExpirationTime",e):e instanceof Date?this.#e.exp=eV("setExpirationTime",eJ(e)):this.#e.exp=eJ(new Date)+eQ(e)}set iat(e){void 0===e?this.#e.iat=eJ(new Date):e instanceof Date?this.#e.iat=eV("setIssuedAt",eJ(e)):"string"==typeof e?this.#e.iat=eV("setIssuedAt",eJ(new Date)+eQ(e)):this.#e.iat=eV("setIssuedAt",e)}}class eX{#t;#s;constructor(e={}){this.#s=new eG(e)}setIssuer(e){return this.#s.iss=e,this}setSubject(e){return this.#s.sub=e,this}setAudience(e){return this.#s.aud=e,this}setJti(e){return this.#s.jti=e,this}setNotBefore(e){return this.#s.nbf=e,this}setExpirationTime(e){return this.#s.exp=e,this}setIssuedAt(e){return this.#s.iat=e,this}setProtectedHeader(e){return this.#t=e,this}async sign(e,t){let r=new ez(this.#s.data());if(r.setProtectedHeader(this.#t),Array.isArray(this.#t?.crit)&&this.#t.crit.includes("b64")&&!1===this.#t.b64)throw new ey("JWTs MUST NOT use unencoded payload");return r.sign(e,t)}}let eY=process.env.AUTH_SECRET;if(!eY)throw Error("AUTH_SECRET must be set in environment variables");new TextEncoder().encode(eY),function(){var e=Error("Cannot find module '@/lib/db'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/schema'");throw e.code="MODULE_NOT_FOUND",e}();let eZ=process.env.AUTH_SECRET;if(!eZ)throw Error("AUTH_SECRET must be set");let e0=new TextEncoder().encode(eZ);async function e1(e,t=3600){let r=Math.floor(Date.now()/1e3),n=new eX({}).setProtectedHeader({alg:"HS256"}).setSubject(e).setIssuer("moemail").setAudience("moemail:credential").setIssuedAt(r);return t>0&&n.setExpirationTime(r+t),await n.sign(e0)}async function e2(e){let t=Object(function(){var e=Error("Cannot find module '@/lib/db'");throw e.code="MODULE_NOT_FOUND",e}())(),r=(0,el.getRequestContext)().env,i=await Object(function(){var e=Error("Cannot find module '@/lib/apiKey'");throw e.code="MODULE_NOT_FOUND",e}())(),a=await Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}())(i);try{if(a!==Object(function(){var e=Error("Cannot find module '@/lib/permissions'");throw e.code="MODULE_NOT_FOUND",e}()).EMPEROR){let e=await r.SITE_CONFIG.get("MAX_EMAILS")||Object(function(){var e=Error("Cannot find module '@/config'");throw e.code="MODULE_NOT_FOUND",e}()).MAX_ACTIVE_EMAILS.toString(),n=await t.select({count:en`count(*)`}).from(Object(function(){var e=Error("Cannot find module '@/lib/schema'");throw e.code="MODULE_NOT_FOUND",e}())).where(function(...e){let t=e.filter(e=>void 0!==e);return 0===t.length?void 0:new Y(1===t.length?t:[new X("("),en.join(t,new X(" and ")),new X(")")])}(eo(Object(function(){var e=Error("Cannot find module '@/lib/schema'");throw e.code="MODULE_NOT_FOUND",e}()).userId,i),eu(Object(function(){var e=Error("Cannot find module '@/lib/schema'");throw e.code="MODULE_NOT_FOUND",e}()).expiresAt,new Date)));if(Number(n[0].count)>=Number(e))return h.NextResponse.json({error:`已达到最大邮箱数量限制 (${e})`},{status:403})}let{name:o,expiryTime:u,domain:l}=await e.json();if(!Object(function(){var e=Error("Cannot find module '@/types/email'");throw e.code="MODULE_NOT_FOUND",e}()).some(e=>e.value===u))return h.NextResponse.json({error:"无效的过期时间"},{status:400});let c=await r.SITE_CONFIG.get("EMAIL_DOMAINS"),d=c?c.split(","):["moemail.app"];if(!d||!d.includes(l))return h.NextResponse.json({error:"无效的域名"},{status:400});let f=`${o||function(e=21){var t;t=e|=0,!n||n.length<t?(n=Buffer.allocUnsafe(128*t),p.webcrypto.getRandomValues(n),s=0):s+t>n.length&&(p.webcrypto.getRandomValues(n),s=0),s+=t;let r="";for(let t=s-e;t<s;t++)r+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&n[t]];return r}(8)}@${l}`;if(await t.query.emails.findFirst({where:eo(en`LOWER(${Object(function(){var e=Error("Cannot find module '@/lib/schema'");throw e.code="MODULE_NOT_FOUND",e}()).address})`,f.toLowerCase())}))return h.NextResponse.json({error:"该邮箱地址已被使用"},{status:409});let m=new Date,y=new Date(0===u?"9999-01-01T00:00:00.000Z":m.getTime()+u),g=await t.insert(Object(function(){var e=Error("Cannot find module '@/lib/schema'");throw e.code="MODULE_NOT_FOUND",e}())).values({address:f,createdAt:m,expiresAt:y,userId:i}).returning({id:Object(function(){var e=Error("Cannot find module '@/lib/schema'");throw e.code="MODULE_NOT_FOUND",e}()).id,address:Object(function(){var e=Error("Cannot find module '@/lib/schema'");throw e.code="MODULE_NOT_FOUND",e}()).address}),b=await e1(g[0].address,0===u?0:u/1e3);return h.NextResponse.json({id:g[0].id,email:g[0].address,credential:b})}catch(e){return console.error("Failed to generate email:",e),h.NextResponse.json({error:"创建邮箱失败"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/db'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/schema'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/types/email'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/config'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/apiKey'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/permissions'");throw e.code="MODULE_NOT_FOUND",e}();let e5=new l.AppRouteRouteModule({definition:{kind:c.RouteKind.APP_ROUTE,page:"/api/emails/generate/route",pathname:"/api/emails/generate",filename:"route",bundlePath:"app/api/emails/generate/route"},resolvedPagePath:"F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\generate\\route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:e6,workUnitAsyncStorage:e4,serverHooks:e8}=e5;function e3(){return(0,d.patchFetch)({workAsyncStorage:e6,workUnitAsyncStorage:e4})}},7032:()=>{},6760:()=>{},9488:(e,t,r)=>{"use strict";var n=Object.create,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,l=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of a(t))u.call(e,o)||o===r||s(e,o,{get:()=>t[o],enumerable:!(n=i(t,o))||n.enumerable});return e},c=((e,t)=>function(){return t||(0,e[a(e)[0]])((t={exports:{}}).exports,t),t.exports})({"../../node_modules/dedent-tabs/dist/dedent-tabs.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(e){for(var t="string"==typeof e?[e]:e.raw,r="",n=0;n<t.length;n++)if(r+=t[n].replace(/\\\n[ \t]*/g,"").replace(/\\`/g,"`").replace(/\\\$/g,"$").replace(/\\\{/g,"{"),n<(1>=arguments.length?0:arguments.length-1)){var s=r.substring(r.lastIndexOf("\n")+1).match(/^(\s*)\S?/);r+=((1>n+1||arguments.length<=n+1?void 0:arguments[n+1])+"").replace(/\n/g,"\n"+s[1])}var i=r.split("\n"),a=null;if(i.forEach(function(e){var t=Math.min,r=e.match(/^(\s+)\S+/);if(r){var n=r[1].length;a=a?t(a,n):n}}),null!==a){var o=a;r=i.map(function(e){return" "===e[0]||"	"===e[0]?e.slice(o):e}).join("\n")}return r.trim().replace(/\\n/g,"\n")}}}),d={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(d,{getOptionalRequestContext:()=>f,getRequestContext:()=>m}),e.exports=l(s({},"__esModule",{value:!0}),d),r(8526);var h=((e,t,r)=>(r=null!=e?n(o(e)):{},l(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(c()),p=Symbol.for("__cloudflare-request-context__");function f(){let e=globalThis[p];if("nodejs"==(process?.release?.name==="node"?"nodejs":"edge"))throw Error(h.default`
			\`getRequestContext\` and \`getOptionalRequestContext\` can only be run
			inside the edge runtime, so please make sure to have included
			\`export const runtime = 'edge'\` in all the routes using such functions
			(regardless of whether they are used directly or indirectly through imports).
		`);return e}function m(){let e=f();if(!e){if(process?.env?.NEXT_PHASE==="phase-production-build")throw Error(h.default`
				\n\`getRequestContext\` is being called at the top level of a route file, this is not supported
				for more details see https://developers.cloudflare.com/pages/framework-guides/nextjs/ssr/troubleshooting/#top-level-getrequestcontext \n
			`);throw Error("Failed to retrieve the Cloudflare request context.")}return e}},8526:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[607,648],()=>r(2046));module.exports=n})();