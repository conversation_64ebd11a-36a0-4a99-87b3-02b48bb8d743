{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/VersionStalenessInfo/VersionStalenessInfo.tsx"], "sourcesContent": ["import type { VersionInfo } from '../../../../../../server/dev/parse-version-info'\n\nexport function VersionStalenessInfo({\n  versionInfo,\n}: {\n  versionInfo: VersionInfo | undefined\n}) {\n  if (!versionInfo) return null\n  const { staleness } = versionInfo\n  let { text, indicatorClass, title } = getStaleness(versionInfo)\n\n  if (!text) return null\n\n  return (\n    <span className=\"nextjs-container-build-error-version-status\">\n      <span className={indicatorClass} />\n      <small data-nextjs-version-checker title={title}>\n        {text}\n      </small>{' '}\n      {staleness === 'fresh' ||\n      staleness === 'newer-than-npm' ||\n      staleness === 'unknown' ? null : (\n        <a\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          href=\"https://nextjs.org/docs/messages/version-staleness\"\n        >\n          (learn more)\n        </a>\n      )}\n      {process.env.TURBOPACK ? ' (Turbopack)' : ''}\n    </span>\n  )\n}\n\nexport function getStaleness({ installed, staleness, expected }: VersionInfo) {\n  let text = ''\n  let title = ''\n  let indicatorClass = ''\n  const versionLabel = `Next.js (${installed})`\n  switch (staleness) {\n    case 'newer-than-npm':\n    case 'fresh':\n      text = versionLabel\n      title = `Latest available version is detected (${installed}).`\n      indicatorClass = 'fresh'\n      break\n    case 'stale-patch':\n    case 'stale-minor':\n      text = `${versionLabel} out of date`\n      title = `There is a newer version (${expected}) available, upgrade recommended! `\n      indicatorClass = 'stale'\n      break\n    case 'stale-major': {\n      text = `${versionLabel} is outdated`\n      title = `An outdated version detected (latest is ${expected}), upgrade is highly recommended!`\n      indicatorClass = 'outdated'\n      break\n    }\n    case 'stale-prerelease': {\n      text = `${versionLabel} is outdated`\n      title = `There is a newer canary version (${expected}) available, please upgrade! `\n      indicatorClass = 'stale'\n      break\n    }\n    case 'unknown':\n      break\n    default:\n      break\n  }\n  return { text, indicatorClass, title }\n}\n"], "names": ["VersionStalenessInfo", "getStaleness", "versionInfo", "staleness", "text", "indicatorClass", "title", "span", "className", "small", "data-nextjs-version-checker", "a", "target", "rel", "href", "process", "env", "TURBOPACK", "installed", "expected", "versionLabel"], "mappings": ";;;;;;;;;;;;;;;IAEgBA,oBAAoB;eAApBA;;IAiCAC,YAAY;eAAZA;;;;AAjCT,SAASD,qBAAqB,KAIpC;IAJoC,IAAA,EACnCE,WAAW,EAGZ,GAJoC;IAKnC,IAAI,CAACA,aAAa,OAAO;IACzB,MAAM,EAAEC,SAAS,EAAE,GAAGD;IACtB,IAAI,EAAEE,IAAI,EAAEC,cAAc,EAAEC,KAAK,EAAE,GAAGL,aAAaC;IAEnD,IAAI,CAACE,MAAM,OAAO;IAElB,qBACE,sBAACG;QAAKC,WAAU;;0BACd,qBAACD;gBAAKC,WAAWH;;0BACjB,qBAACI;gBAAMC,6BAA2B;gBAACJ,OAAOA;0BACvCF;;YACM;YACRD,cAAc,WACfA,cAAc,oBACdA,cAAc,YAAY,qBACxB,qBAACQ;gBACCC,QAAO;gBACPC,KAAI;gBACJC,MAAK;0BACN;;YAIFC,QAAQC,GAAG,CAACC,SAAS,GAAG,iBAAiB;;;AAGhD;AAEO,SAAShB,aAAa,KAA+C;IAA/C,IAAA,EAAEiB,SAAS,EAAEf,SAAS,EAAEgB,QAAQ,EAAe,GAA/C;IAC3B,IAAIf,OAAO;IACX,IAAIE,QAAQ;IACZ,IAAID,iBAAiB;IACrB,MAAMe,eAAe,AAAC,cAAWF,YAAU;IAC3C,OAAQf;QACN,KAAK;QACL,KAAK;YACHC,OAAOgB;YACPd,QAAQ,AAAC,2CAAwCY,YAAU;YAC3Db,iBAAiB;YACjB;QACF,KAAK;QACL,KAAK;YACHD,OAAO,AAAC,KAAEgB,eAAa;YACvBd,QAAQ,AAAC,+BAA4Ba,WAAS;YAC9Cd,iBAAiB;YACjB;QACF,KAAK;YAAe;gBAClBD,OAAO,AAAC,KAAEgB,eAAa;gBACvBd,QAAQ,AAAC,6CAA0Ca,WAAS;gBAC5Dd,iBAAiB;gBACjB;YACF;QACA,KAAK;YAAoB;gBACvBD,OAAO,AAAC,KAAEgB,eAAa;gBACvBd,QAAQ,AAAC,sCAAmCa,WAAS;gBACrDd,iBAAiB;gBACjB;YACF;QACA,KAAK;YACH;QACF;YACE;IACJ;IACA,OAAO;QAAED;QAAMC;QAAgBC;IAAM;AACvC"}