(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[658],{2333:(i,e,o)=>{var a;(()=>{var r={226:function(r,t){!function(n,s){"use strict";var b="function",w="undefined",l="object",d="string",c="major",u="model",p="name",m="type",h="vendor",f="version",v="architecture",g="console",x="mobile",k="tablet",y="smarttv",_="wearable",T="embedded",S="Amazon",q="Apple",N="ASUS",z="BlackBerry",A="Browser",C="Chrome",E="Firefox",U="Google",O="Huawei",R="Microsoft",P="Motorola",j="Opera",M="Samsung",B="Sharp",V="Sony",D="Xiaomi",L="Zebra",G="Facebook",I="Chromium OS",W="Mac OS",F=function(i,e){var o={};for(var a in i)e[a]&&e[a].length%2==0?o[a]=e[a].concat(i[a]):o[a]=i[a];return o},H=function(i){for(var e={},o=0;o<i.length;o++)e[i[o].toUpperCase()]=i[o];return e},Z=function(i,e){return typeof i===d&&-1!==$(e).indexOf($(i))},$=function(i){return i.toLowerCase()},X=function(i,e){if(typeof i===d)return i=i.replace(/^\s\s*/,""),typeof e===w?i:i.substring(0,350)},J=function(i,e){for(var o,a,r,t,n,w,d=0;d<e.length&&!n;){var c=e[d],u=e[d+1];for(o=a=0;o<c.length&&!n&&c[o];)if(n=c[o++].exec(i))for(r=0;r<u.length;r++)w=n[++a],typeof(t=u[r])===l&&t.length>0?2===t.length?typeof t[1]==b?this[t[0]]=t[1].call(this,w):this[t[0]]=t[1]:3===t.length?typeof t[1]!==b||t[1].exec&&t[1].test?this[t[0]]=w?w.replace(t[1],t[2]):void 0:this[t[0]]=w?t[1].call(this,w,t[2]):void 0:4===t.length&&(this[t[0]]=w?t[3].call(this,w.replace(t[1],t[2])):void 0):this[t]=w||s;d+=2}},K=function(i,e){for(var o in e)if(typeof e[o]===l&&e[o].length>0){for(var a=0;a<e[o].length;a++)if(Z(e[o][a],i))return"?"===o?s:o}else if(Z(e[o],i))return"?"===o?s:o;return i},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Y={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[f,[p,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[f,[p,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[p,f],[/opios[\/ ]+([\w\.]+)/i],[f,[p,j+" Mini"]],[/\bopr\/([\w\.]+)/i],[f,[p,j]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[p,f],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[f,[p,"UC"+A]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[f,[p,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[f,[p,"WeChat"]],[/konqueror\/([\w\.]+)/i],[f,[p,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[f,[p,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[f,[p,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[p,/(.+)/,"$1 Secure "+A],f],[/\bfocus\/([\w\.]+)/i],[f,[p,E+" Focus"]],[/\bopt\/([\w\.]+)/i],[f,[p,j+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[f,[p,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[f,[p,"Dolphin"]],[/coast\/([\w\.]+)/i],[f,[p,j+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[f,[p,"MIUI "+A]],[/fxios\/([-\w\.]+)/i],[f,[p,E]],[/\bqihu|(qi?ho?o?|360)browser/i],[[p,"360 "+A]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[p,/(.+)/,"$1 "+A],f],[/(comodo_dragon)\/([\w\.]+)/i],[[p,/_/g," "],f],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[p,f],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[p],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[p,G],f],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[p,f],[/\bgsa\/([\w\.]+) .*safari\//i],[f,[p,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[f,[p,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[f,[p,C+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[p,C+" WebView"],f],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[f,[p,"Android "+A]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[p,f],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[f,[p,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[f,p],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[p,[f,K,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[p,f],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[p,"Netscape"],f],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[f,[p,E+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[p,f],[/(cobalt)\/([\w\.]+)/i],[p,[f,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[v,"amd64"]],[/(ia32(?=;))/i],[[v,$]],[/((?:i[346]|x)86)[;\)]/i],[[v,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[v,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[v,"armhf"]],[/windows (ce|mobile); ppc;/i],[[v,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[v,/ower/,"",$]],[/(sun4\w)[;\)]/i],[[v,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[v,$]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[u,[h,M],[m,k]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[u,[h,M],[m,x]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[u,[h,q],[m,x]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[u,[h,q],[m,k]],[/(macintosh);/i],[u,[h,q]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[u,[h,B],[m,x]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[u,[h,O],[m,k]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[u,[h,O],[m,x]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[u,/_/g," "],[h,D],[m,x]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[u,/_/g," "],[h,D],[m,k]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[u,[h,"OPPO"],[m,x]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[u,[h,"Vivo"],[m,x]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[u,[h,"Realme"],[m,x]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[u,[h,P],[m,x]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[u,[h,P],[m,k]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[u,[h,"LG"],[m,k]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[u,[h,"LG"],[m,x]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[u,[h,"Lenovo"],[m,k]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[u,/_/g," "],[h,"Nokia"],[m,x]],[/(pixel c)\b/i],[u,[h,U],[m,k]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[u,[h,U],[m,x]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[u,[h,V],[m,x]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[u,"Xperia Tablet"],[h,V],[m,k]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[u,[h,"OnePlus"],[m,x]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[u,[h,S],[m,k]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[u,/(.+)/g,"Fire Phone $1"],[h,S],[m,x]],[/(playbook);[-\w\),; ]+(rim)/i],[u,h,[m,k]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[u,[h,z],[m,x]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[u,[h,N],[m,k]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[u,[h,N],[m,x]],[/(nexus 9)/i],[u,[h,"HTC"],[m,k]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[h,[u,/_/g," "],[m,x]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[u,[h,"Acer"],[m,k]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[u,[h,"Meizu"],[m,x]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[h,u,[m,x]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[h,u,[m,k]],[/(surface duo)/i],[u,[h,R],[m,k]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[u,[h,"Fairphone"],[m,x]],[/(u304aa)/i],[u,[h,"AT&T"],[m,x]],[/\bsie-(\w*)/i],[u,[h,"Siemens"],[m,x]],[/\b(rct\w+) b/i],[u,[h,"RCA"],[m,k]],[/\b(venue[\d ]{2,7}) b/i],[u,[h,"Dell"],[m,k]],[/\b(q(?:mv|ta)\w+) b/i],[u,[h,"Verizon"],[m,k]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[u,[h,"Barnes & Noble"],[m,k]],[/\b(tm\d{3}\w+) b/i],[u,[h,"NuVision"],[m,k]],[/\b(k88) b/i],[u,[h,"ZTE"],[m,k]],[/\b(nx\d{3}j) b/i],[u,[h,"ZTE"],[m,x]],[/\b(gen\d{3}) b.+49h/i],[u,[h,"Swiss"],[m,x]],[/\b(zur\d{3}) b/i],[u,[h,"Swiss"],[m,k]],[/\b((zeki)?tb.*\b) b/i],[u,[h,"Zeki"],[m,k]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[h,"Dragon Touch"],u,[m,k]],[/\b(ns-?\w{0,9}) b/i],[u,[h,"Insignia"],[m,k]],[/\b((nxa|next)-?\w{0,9}) b/i],[u,[h,"NextBook"],[m,k]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[h,"Voice"],u,[m,x]],[/\b(lvtel\-)?(v1[12]) b/i],[[h,"LvTel"],u,[m,x]],[/\b(ph-1) /i],[u,[h,"Essential"],[m,x]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[u,[h,"Envizen"],[m,k]],[/\b(trio[-\w\. ]+) b/i],[u,[h,"MachSpeed"],[m,k]],[/\btu_(1491) b/i],[u,[h,"Rotor"],[m,k]],[/(shield[\w ]+) b/i],[u,[h,"Nvidia"],[m,k]],[/(sprint) (\w+)/i],[h,u,[m,x]],[/(kin\.[onetw]{3})/i],[[u,/\./g," "],[h,R],[m,x]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[u,[h,L],[m,k]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[u,[h,L],[m,x]],[/smart-tv.+(samsung)/i],[h,[m,y]],[/hbbtv.+maple;(\d+)/i],[[u,/^/,"SmartTV"],[h,M],[m,y]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[h,"LG"],[m,y]],[/(apple) ?tv/i],[h,[u,q+" TV"],[m,y]],[/crkey/i],[[u,C+"cast"],[h,U],[m,y]],[/droid.+aft(\w)( bui|\))/i],[u,[h,S],[m,y]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[u,[h,B],[m,y]],[/(bravia[\w ]+)( bui|\))/i],[u,[h,V],[m,y]],[/(mitv-\w{5}) bui/i],[u,[h,D],[m,y]],[/Hbbtv.*(technisat) (.*);/i],[h,u,[m,y]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[h,X],[u,X],[m,y]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[m,y]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[h,u,[m,g]],[/droid.+; (shield) bui/i],[u,[h,"Nvidia"],[m,g]],[/(playstation [345portablevi]+)/i],[u,[h,V],[m,g]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[u,[h,R],[m,g]],[/((pebble))app/i],[h,u,[m,_]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[u,[h,q],[m,_]],[/droid.+; (glass) \d/i],[u,[h,U],[m,_]],[/droid.+; (wt63?0{2,3})\)/i],[u,[h,L],[m,_]],[/(quest( 2| pro)?)/i],[u,[h,G],[m,_]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[h,[m,T]],[/(aeobc)\b/i],[u,[h,S],[m,T]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[u,[m,x]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[u,[m,k]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[m,k]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[m,x]],[/(android[-\w\. ]{0,9});.+buil/i],[u,[h,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[f,[p,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[f,[p,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[p,f],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[f,p]],os:[[/microsoft (windows) (vista|xp)/i],[p,f],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[p,[f,K,Q]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[p,"Windows"],[f,K,Q]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[f,/_/g,"."],[p,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[p,W],[f,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[f,p],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[p,f],[/\(bb(10);/i],[f,[p,z]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[f,[p,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[f,[p,E+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[f,[p,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[f,[p,"watchOS"]],[/crkey\/([\d\.]+)/i],[f,[p,C+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[p,I],f],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[p,f],[/(sunos) ?([\w\.\d]*)/i],[[p,"Solaris"],f],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[p,f]]},ii=function(i,e){if(typeof i===l&&(e=i,i=s),!(this instanceof ii))return new ii(i,e).getResult();var o=typeof n!==w&&n.navigator?n.navigator:s,a=i||(o&&o.userAgent?o.userAgent:""),r=o&&o.userAgentData?o.userAgentData:s,t=e?F(Y,e):Y,g=o&&o.userAgent==a;return this.getBrowser=function(){var i,e={};return e[p]=s,e[f]=s,J.call(e,a,t.browser),e[c]=typeof(i=e[f])===d?i.replace(/[^\d\.]/g,"").split(".")[0]:s,g&&o&&o.brave&&typeof o.brave.isBrave==b&&(e[p]="Brave"),e},this.getCPU=function(){var i={};return i[v]=s,J.call(i,a,t.cpu),i},this.getDevice=function(){var i={};return i[h]=s,i[u]=s,i[m]=s,J.call(i,a,t.device),g&&!i[m]&&r&&r.mobile&&(i[m]=x),g&&"Macintosh"==i[u]&&o&&typeof o.standalone!==w&&o.maxTouchPoints&&o.maxTouchPoints>2&&(i[u]="iPad",i[m]=k),i},this.getEngine=function(){var i={};return i[p]=s,i[f]=s,J.call(i,a,t.engine),i},this.getOS=function(){var i={};return i[p]=s,i[f]=s,J.call(i,a,t.os),g&&!i[p]&&r&&"Unknown"!=r.platform&&(i[p]=r.platform.replace(/chrome os/i,I).replace(/macos/i,W)),i},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return a},this.setUA=function(i){return a=typeof i===d&&i.length>350?X(i,350):i,this},this.setUA(a),this};ii.VERSION="1.0.35",ii.BROWSER=H([p,f,c]),ii.CPU=H([v]),ii.DEVICE=H([u,h,m,g,x,y,k,_,T]),ii.ENGINE=ii.OS=H([p,f]),typeof t!==w?(r.exports&&(t=r.exports=ii),t.UAParser=ii):o.amdO?void 0!==(a=(function(){return ii}).call(e,o,e,i))&&(i.exports=a):typeof n!==w&&(n.UAParser=ii);var ie=typeof n!==w&&(n.jQuery||n.Zepto);if(ie&&!ie.ua){var io=new ii;ie.ua=io.getResult(),ie.ua.get=function(){return io.getUA()},ie.ua.set=function(i){io.setUA(i);var e=io.getResult();for(var o in e)ie.ua[o]=e[o]}}}("object"==typeof window?window:this)}},t={};function n(i){var e=t[i];if(void 0!==e)return e.exports;var o=t[i]={exports:{}},a=!0;try{r[i].call(o.exports,o,o.exports,n),a=!1}finally{a&&delete t[i]}return o.exports}n.ab="//";var s=n(226);i.exports=s})()},658:(i,e,o)=>{"use strict";o.d(e,{J8:()=>a.J,Rp:()=>r.R});var a=o(176),r=o(1582);o(2333),"undefined"==typeof URLPattern||URLPattern,o(2296),o(8629),o(5871),o(7627),o(331)}}]);
//# sourceMappingURL=658.js.map