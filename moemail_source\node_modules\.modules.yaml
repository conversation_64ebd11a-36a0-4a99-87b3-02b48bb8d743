hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@apideck/better-ajv-errors@0.3.6(ajv@8.17.1)':
    '@apideck/better-ajv-errors': private
  '@auth/core@0.39.1':
    '@auth/core': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.5':
    '@babel/compat-data': private
  '@babel/core@7.27.4':
    '@babel/core': private
  '@babel/generator@7.27.5':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.4(@babel/core@7.27.4)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.4)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.27.1':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.27.5':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.27.4)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.27.4)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.27.5(@babel/core@7.27.4)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.27.3(@babel/core@7.27.4)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.27.3(@babel/core@7.27.4)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-regenerator@7.27.5(@babel/core@7.27.4)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.27.4)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.27.2(@babel/core@7.27.4)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.27.4)':
    '@babel/preset-modules': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.4':
    '@babel/traverse': private
  '@babel/types@7.27.6':
    '@babel/types': private
  '@cloudflare/kv-asset-handler@0.3.4':
    '@cloudflare/kv-asset-handler': private
  '@cloudflare/unenv-preset@2.0.2(unenv@2.0.0-rc.14)(workerd@1.********.0)':
    '@cloudflare/unenv-preset': private
  '@cloudflare/workerd-darwin-64@1.********.0':
    '@cloudflare/workerd-darwin-64': private
  '@cloudflare/workerd-darwin-arm64@1.********.0':
    '@cloudflare/workerd-darwin-arm64': private
  '@cloudflare/workerd-linux-64@1.********.0':
    '@cloudflare/workerd-linux-64': private
  '@cloudflare/workerd-linux-arm64@1.********.0':
    '@cloudflare/workerd-linux-arm64': private
  '@cloudflare/workerd-windows-64@1.********.0':
    '@cloudflare/workerd-windows-64': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@drizzle-team/brocli@0.10.2':
    '@drizzle-team/brocli': private
  '@edge-runtime/format@2.2.1':
    '@edge-runtime/format': private
  '@edge-runtime/node-utils@2.3.0':
    '@edge-runtime/node-utils': private
  '@edge-runtime/ponyfill@2.4.2':
    '@edge-runtime/ponyfill': private
  '@edge-runtime/primitives@4.1.0':
    '@edge-runtime/primitives': private
  '@edge-runtime/vm@3.2.0':
    '@edge-runtime/vm': private
  '@esbuild-kit/core-utils@3.3.2':
    '@esbuild-kit/core-utils': private
  '@esbuild-kit/esm-loader@2.6.5':
    '@esbuild-kit/esm-loader': private
  '@esbuild-plugins/node-globals-polyfill@0.2.3(esbuild@0.17.19)':
    '@esbuild-plugins/node-globals-polyfill': private
  '@esbuild-plugins/node-modules-polyfill@0.2.2(esbuild@0.17.19)':
    '@esbuild-plugins/node-modules-polyfill': private
  '@esbuild/aix-ppc64@0.19.12':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.17.19':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.15.18':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.17.19':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.17.19':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.17.19':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.17.19':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.17.19':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.17.19':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.17.19':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.17.19':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.15.18':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.17.19':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.17.19':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.17.19':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.17.19':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.17.19':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.17.19':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.17.19':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.17.19':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.17.19':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.17.19':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.17.19':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@fastify/busboy@2.1.1':
    '@fastify/busboy': private
  '@floating-ui/core@1.7.1':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.1':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@hapi/hoek@9.3.0':
    '@hapi/hoek': private
  '@hapi/topo@5.1.0':
    '@hapi/topo': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@img/sharp-darwin-arm64@0.33.5':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.33.5':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.0.4':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.0.4':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.0.4':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.0.5':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-s390x@1.0.4':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.0.4':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.33.5':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.33.5':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.33.5':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.33.5':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.33.5':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.33.5':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.33.5':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.33.5':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.33.5':
    '@img/sharp-win32-x64': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@mapbox/node-pre-gyp@1.0.11':
    '@mapbox/node-pre-gyp': private
  '@next/env@15.1.1':
    '@next/env': private
  '@next/eslint-plugin-next@15.0.3':
    '@next/eslint-plugin-next': private
  '@next/swc-darwin-arm64@15.1.1':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.1.1':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.1.1':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.1.1':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.1.1':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.1.1':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.1.1':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-ia32-msvc@13.5.9':
    '@next/swc-win32-ia32-msvc': private
  '@next/swc-win32-x64-msvc@15.1.1':
    '@next/swc-win32-x64-msvc': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@oven/bun-darwin-aarch64@1.2.15':
    '@oven/bun-darwin-aarch64': private
  '@oven/bun-darwin-x64-baseline@1.2.15':
    '@oven/bun-darwin-x64-baseline': private
  '@oven/bun-darwin-x64@1.2.15':
    '@oven/bun-darwin-x64': private
  '@oven/bun-linux-aarch64-musl@1.2.15':
    '@oven/bun-linux-aarch64-musl': private
  '@oven/bun-linux-aarch64@1.2.15':
    '@oven/bun-linux-aarch64': private
  '@oven/bun-linux-x64-baseline@1.2.15':
    '@oven/bun-linux-x64-baseline': private
  '@oven/bun-linux-x64-musl-baseline@1.2.15':
    '@oven/bun-linux-x64-musl-baseline': private
  '@oven/bun-linux-x64-musl@1.2.15':
    '@oven/bun-linux-x64-musl': private
  '@oven/bun-linux-x64@1.2.15':
    '@oven/bun-linux-x64': private
  '@oven/bun-windows-x64-baseline@1.2.15':
    '@oven/bun-windows-x64-baseline': private
  '@oven/bun-windows-x64@1.2.15':
    '@oven/bun-windows-x64': private
  '@panva/hkdf@1.2.1':
    '@panva/hkdf': private
  '@paralleldrive/cuid2@2.2.2':
    '@paralleldrive/cuid2': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.1(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.1(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.15(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.9(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@18.3.23)(react@19.0.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@rollup/plugin-babel@5.3.1(@babel/core@7.27.4)(rollup@2.79.2)':
    '@rollup/plugin-babel': private
  '@rollup/plugin-node-resolve@11.2.1(rollup@2.79.2)':
    '@rollup/plugin-node-resolve': private
  '@rollup/plugin-replace@2.4.2(rollup@2.79.2)':
    '@rollup/plugin-replace': private
  '@rollup/pluginutils@3.1.0(rollup@2.79.2)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.42.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.42.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.42.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.42.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.42.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.42.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.42.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.42.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.42.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.42.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.42.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.42.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.42.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.42.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.42.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.42.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.42.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.42.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.42.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.42.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.11.0':
    '@rushstack/eslint-patch': private
  '@sideway/address@4.1.5':
    '@sideway/address': private
  '@sideway/formula@3.0.1':
    '@sideway/formula': private
  '@sideway/pinpoint@2.0.0':
    '@sideway/pinpoint': private
  '@sinclair/typebox@0.25.24':
    '@sinclair/typebox': private
  '@surma/rollup-plugin-off-main-thread@2.2.3':
    '@surma/rollup-plugin-off-main-thread': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': private
  '@ts-morph/common@0.11.1':
    '@ts-morph/common': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/chai@5.2.2':
    '@types/chai': private
  '@types/cookie@0.6.0':
    '@types/cookie': private
  '@types/cookiejar@2.1.5':
    '@types/cookiejar': private
  '@types/deep-eql@4.0.2':
    '@types/deep-eql': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/glob@7.2.0':
    '@types/glob': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/methods@1.1.4':
    '@types/methods': private
  '@types/minimatch@5.1.2':
    '@types/minimatch': private
  '@types/node-fetch@2.6.12':
    '@types/node-fetch': private
  '@types/prop-types@15.7.14':
    '@types/prop-types': private
  '@types/resolve@1.17.1':
    '@types/resolve': private
  '@types/superagent@8.1.9':
    '@types/superagent': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@typescript-eslint/eslint-plugin@8.33.1(@typescript-eslint/parser@8.33.1(eslint@8.57.1)(typescript@5.8.3))(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.33.1(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.33.1(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.33.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.33.1(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.33.1(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.33.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.33.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.33.1(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.33.1':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unrs/resolver-binding-darwin-arm64@1.7.11':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.7.11':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.7.11':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.7.11':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.7.11':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.7.11':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.7.11':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.7.11':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.7.11':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.7.11':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.7.11':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.7.11':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.7.11':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.7.11':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.7.11':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.7.11':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.7.11':
    '@unrs/resolver-binding-win32-x64-msvc': private
  '@vercel/build-utils@8.5.0':
    '@vercel/build-utils': private
  '@vercel/error-utils@2.0.3':
    '@vercel/error-utils': private
  '@vercel/fun@1.1.0':
    '@vercel/fun': private
  '@vercel/gatsby-plugin-vercel-analytics@1.0.11':
    '@vercel/gatsby-plugin-vercel-analytics': private
  '@vercel/gatsby-plugin-vercel-builder@2.0.57':
    '@vercel/gatsby-plugin-vercel-builder': private
  '@vercel/go@3.2.1':
    '@vercel/go': private
  '@vercel/hydrogen@1.0.9':
    '@vercel/hydrogen': private
  '@vercel/next@4.3.21':
    '@vercel/next': private
  '@vercel/nft@0.27.3':
    '@vercel/nft': private
  '@vercel/node@3.2.26':
    '@vercel/node': private
  '@vercel/python@4.5.1':
    '@vercel/python': private
  '@vercel/redwood@2.1.8':
    '@vercel/redwood': private
  '@vercel/remix-builder@2.2.14':
    '@vercel/remix-builder': private
  '@vercel/routing-utils@3.1.0':
    '@vercel/routing-utils': private
  '@vercel/ruby@2.1.0':
    '@vercel/ruby': private
  '@vercel/static-build@2.5.35':
    '@vercel/static-build': private
  '@vercel/static-config@3.0.0':
    '@vercel/static-config': private
  '@vitest/expect@3.2.2':
    '@vitest/expect': private
  '@vitest/mocker@3.2.2(vite@6.3.5(@types/node@20.19.0)(jiti@1.21.7)(terser@5.41.0)(tsx@4.19.4)(yaml@2.8.0))':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.2.2':
    '@vitest/pretty-format': private
  '@vitest/runner@3.2.2':
    '@vitest/runner': private
  '@vitest/snapshot@3.2.2':
    '@vitest/snapshot': private
  '@vitest/spy@3.2.2':
    '@vitest/spy': private
  '@vitest/utils@3.2.2':
    '@vitest/utils': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  abbrev@1.1.1:
    abbrev: private
  abort-controller@3.0.0:
    abort-controller: private
  acorn-import-attributes@1.9.5(acorn@8.14.1):
    acorn-import-attributes: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn-walk@8.3.2:
    acorn-walk: private
  acorn@8.14.1:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  aproba@2.0.0:
    aproba: private
  are-we-there-yet@2.0.0:
    are-we-there-yet: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.9:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array-uniq@1.0.3:
    array-uniq: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  as-table@1.0.55:
    as-table: private
  asap@2.0.6:
    asap: private
  assertion-error@2.0.1:
    assertion-error: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  ast-types@0.14.2:
    ast-types: private
  async-function@1.0.0:
    async-function: private
  async-listen@1.2.0:
    async-listen: private
  async-sema@3.1.1:
    async-sema: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axios@1.9.0(debug@4.4.1):
    axios: private
  axobject-query@4.1.0:
    axobject-query: private
  babel-loader@8.4.1(@babel/core@7.27.4)(webpack@5.99.9(esbuild@0.17.19)):
    babel-loader: private
  babel-plugin-polyfill-corejs2@0.4.13(@babel/core@7.27.4):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.11.1(@babel/core@7.27.4):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.4(@babel/core@7.27.4):
    babel-plugin-polyfill-regenerator: private
  balanced-match@1.0.2:
    balanced-match: private
  big.js@5.2.2:
    big.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bindings@1.5.0:
    bindings: private
  blake3-wasm@2.1.5:
    blake3-wasm: private
  bluebird@3.7.2:
    bluebird: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.0:
    browserslist: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-from@1.1.2:
    buffer-from: private
  builtin-modules@3.3.0:
    builtin-modules: private
  bun-types@1.2.15:
    bun-types: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.0:
    bytes: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001721:
    caniuse-lite: private
  chai@5.2.0:
    chai: private
  chalk@5.4.1:
    chalk: private
  check-error@2.1.1:
    check-error: private
  check-more-types@2.24.0:
    check-more-types: private
  chokidar@3.6.0:
    chokidar: private
  chownr@1.1.4:
    chownr: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  cjs-module-lexer@1.2.3:
    cjs-module-lexer: private
  clean-webpack-plugin@4.0.0(webpack@5.99.9(esbuild@0.17.19)):
    clean-webpack-plugin: private
  client-only@0.0.1:
    client-only: private
  code-block-writer@10.1.1:
    code-block-writer: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color-support@1.1.3:
    color-support: private
  color@4.2.3:
    color: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@11.1.0:
    commander: private
  common-tags@1.8.2:
    common-tags: private
  commondir@1.0.1:
    commondir: private
  component-emitter@1.3.1:
    component-emitter: private
  concat-map@0.0.1:
    concat-map: private
  console-control-strings@1.1.0:
    console-control-strings: private
  content-type@1.0.4:
    content-type: private
  convert-hrtime@3.0.0:
    convert-hrtime: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie@0.5.0:
    cookie: private
  cookiejar@2.1.4:
    cookiejar: private
  core-js-compat@3.42.0:
    core-js-compat: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypto-random-string@2.0.0:
    crypto-random-string: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-uri-to-buffer@2.0.2:
    data-uri-to-buffer: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  deep-eql@5.0.2:
    deep-eql: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  defu@6.1.4:
    defu: private
  del@4.1.1:
    del: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  depd@1.1.2:
    depd: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  dezalgo@1.0.4:
    dezalgo: private
  didyoumean@1.2.2:
    didyoumean: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  dlv@1.1.3:
    dlv: private
  doctrine@3.0.0:
    doctrine: private
  dotenv-expand@10.0.0:
    dotenv-expand: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  edge-runtime@2.5.9:
    edge-runtime: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.165:
    electron-to-chromium: private
  emoji-regex@9.2.2:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  end-of-stream@1.1.0:
    end-of-stream: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.4.1:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild-android-64@0.15.18:
    esbuild-android-64: private
  esbuild-android-arm64@0.15.18:
    esbuild-android-arm64: private
  esbuild-darwin-64@0.15.18:
    esbuild-darwin-64: private
  esbuild-darwin-arm64@0.15.18:
    esbuild-darwin-arm64: private
  esbuild-freebsd-64@0.15.18:
    esbuild-freebsd-64: private
  esbuild-freebsd-arm64@0.15.18:
    esbuild-freebsd-arm64: private
  esbuild-linux-32@0.15.18:
    esbuild-linux-32: private
  esbuild-linux-64@0.15.18:
    esbuild-linux-64: private
  esbuild-linux-arm64@0.15.18:
    esbuild-linux-arm64: private
  esbuild-linux-arm@0.15.18:
    esbuild-linux-arm: private
  esbuild-linux-mips64le@0.15.18:
    esbuild-linux-mips64le: private
  esbuild-linux-ppc64le@0.15.18:
    esbuild-linux-ppc64le: private
  esbuild-linux-riscv64@0.15.18:
    esbuild-linux-riscv64: private
  esbuild-linux-s390x@0.15.18:
    esbuild-linux-s390x: private
  esbuild-netbsd-64@0.15.18:
    esbuild-netbsd-64: private
  esbuild-openbsd-64@0.15.18:
    esbuild-openbsd-64: private
  esbuild-register@3.6.0(esbuild@0.19.12):
    esbuild-register: private
  esbuild-sunos-64@0.15.18:
    esbuild-sunos-64: private
  esbuild-windows-32@0.15.18:
    esbuild-windows-32: private
  esbuild-windows-64@0.15.18:
    esbuild-windows-64: private
  esbuild-windows-arm64@0.15.18:
    esbuild-windows-arm64: private
  esbuild@0.15.18:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.31.0)(eslint@8.57.1):
    eslint-import-resolver-typescript: private
  eslint-module-utils@2.12.0(@typescript-eslint/parser@8.33.1(eslint@8.57.1)(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-module-utils: private
  eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.33.1(eslint@8.57.1)(typescript@5.8.3))(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-plugin-import: private
  eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: private
  eslint-plugin-react-hooks@5.2.0(eslint@8.57.1):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.5(eslint@8.57.1):
    eslint-plugin-react: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@3.0.3:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-stream@3.3.4:
    event-stream: private
  event-target-shim@5.0.1:
    event-target-shim: private
  events-intercept@2.0.0:
    events-intercept: private
  events@3.3.0:
    events: private
  execa@5.1.1:
    execa: private
  exit-hook@2.2.1:
    exit-hook: private
  expect-type@1.2.1:
    expect-type: private
  exsolve@1.0.5:
    exsolve: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fdir@6.4.5(picomatch@4.0.2):
    fdir: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  file-uri-to-path@1.0.0:
    file-uri-to-path: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  find-cache-dir@3.3.2:
    find-cache-dir: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9(debug@4.4.1):
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.3:
    form-data: private
  formdata-node@4.4.1:
    formdata-node: private
  formidable@3.5.4:
    formidable: private
  from@0.1.7:
    from: private
  fs-extra@8.1.0:
    fs-extra: private
  fs-minipass@1.2.7:
    fs-minipass: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gauge@3.0.2:
    gauge: private
  generic-pool@3.4.2:
    generic-pool: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-own-enumerable-property-symbols@3.0.2:
    get-own-enumerable-property-symbols: private
  get-proto@1.0.1:
    get-proto: private
  get-source@2.0.12:
    get-source: private
  get-stream@6.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@10.4.5:
    glob: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  globrex@0.1.2:
    globrex: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-unicode@2.0.1:
    has-unicode: private
  hasown@2.0.2:
    hasown: private
  http-errors@1.4.0:
    http-errors: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  humanize-ms@1.2.1:
    humanize-ms: private
  iconv-lite@0.4.24:
    iconv-lite: private
  idb@7.1.1:
    idb: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  internal-slot@1.1.0:
    internal-slot: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-module@1.0.0:
    is-module: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-obj@1.0.1:
    is-obj: private
  is-path-cwd@2.2.0:
    is-path-cwd: private
  is-path-in-cwd@2.1.0:
    is-path-in-cwd: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-regex@1.2.1:
    is-regex: private
  is-regexp@1.0.0:
    is-regexp: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@0.0.1:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jest-worker@27.5.1:
    jest-worker: private
  jiti@1.21.7:
    jiti: private
  joi@17.13.3:
    joi: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-to-ts@1.6.4:
    json-schema-to-ts: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonfile@4.0.0:
    jsonfile: private
  jsonpointer@5.0.1:
    jsonpointer: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  lazy-ass@1.6.0:
    lazy-ass: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@2.0.4:
    loader-utils: private
  locate-path@6.0.0:
    locate-path: private
  lodash.castarray@4.4.0:
    lodash.castarray: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lodash@4.17.21:
    lodash: private
  loose-envify@1.4.0:
    loose-envify: private
  loupe@3.1.3:
    loupe: private
  lru-cache@6.0.0:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  make-dir@3.1.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  map-stream@0.1.0:
    map-stream: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micro@9.3.5-canary.3:
    micro: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@3.0.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  miniflare@3.********.2:
    miniflare: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@1.3.3:
    minizlib: private
  mkdirp@0.5.6:
    mkdirp: private
  mri@1.2.0:
    mri: private
  ms@2.1.1:
    ms: private
  mustache@4.2.0:
    mustache: private
  mz@2.7.0:
    mz: private
  napi-postinstall@0.2.4:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  neo-async@2.6.2:
    neo-async: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch@2.7.0:
    node-fetch: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  node-releases@2.0.19:
    node-releases: private
  nopt@5.0.0:
    nopt: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@4.0.1:
    npm-run-path: private
  npmlog@5.0.1:
    npmlog: private
  oauth4webapi@3.5.1:
    oauth4webapi: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  ohash@2.0.11:
    ohash: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  os-paths@4.4.0:
    os-paths: private
  own-keys@1.0.1:
    own-keys: private
  p-finally@2.0.1:
    p-finally: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@2.1.0:
    p-map: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-manager-manager@0.2.0:
    package-manager-manager: private
  parent-module@1.0.1:
    parent-module: private
  parse-ms@2.1.0:
    parse-ms: private
  path-browserify@1.0.1:
    path-browserify: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-is-inside@1.0.2:
    path-is-inside: private
  path-key@3.1.1:
    path-key: private
  path-match@1.2.4:
    path-match: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pathe@2.0.3:
    pathe: private
  pathval@2.0.0:
    pathval: private
  pause-stream@0.0.11:
    pause-stream: private
  pcre-to-regexp@1.1.0:
    pcre-to-regexp: private
  pend@1.2.0:
    pend: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pify@4.0.1:
    pify: private
  pinkie-promise@2.0.1:
    pinkie-promise: private
  pinkie@2.0.4:
    pinkie: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-import@15.1.0(postcss@8.5.4):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.4):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.4)(ts-node@10.9.1(@types/node@20.19.0)(typescript@5.8.3)):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.4):
    postcss-nested: private
  postcss-selector-parser@6.0.10:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  preact-render-to-string@5.2.3(preact@10.11.3):
    preact-render-to-string: private
  preact@10.11.3:
    preact: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-bytes@5.6.0:
    pretty-bytes: private
  pretty-format@3.8.0:
    pretty-format: private
  pretty-ms@7.0.1:
    pretty-ms: private
  printable-characters@1.0.42:
    printable-characters: private
  promisepipe@3.0.0:
    promisepipe: private
  prop-types@15.8.1:
    prop-types: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  ps-tree@1.2.0:
    ps-tree: private
  pump@3.0.2:
    pump: private
  punycode@2.3.1:
    punycode: private
  qs@6.14.0:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  randombytes@2.1.0:
    randombytes: private
  raw-body@2.4.1:
    raw-body: private
  react-is@16.13.1:
    react-is: private
  react-remove-scroll-bar@2.3.8(@types/react@18.3.23)(react@19.0.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.1(@types/react@18.3.23)(react@19.0.0):
    react-remove-scroll: private
  react-style-singleton@2.2.3(@types/react@18.3.23)(react@19.0.0):
    react-style-singleton: private
  read-cache@1.0.0:
    read-cache: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpu-core@6.2.0:
    regexpu-core: private
  reghex@1.0.2:
    reghex: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rimraf@2.7.1:
    rimraf: private
  rollup-plugin-inject@3.0.2:
    rollup-plugin-inject: private
  rollup-plugin-node-polyfills@0.2.1:
    rollup-plugin-node-polyfills: private
  rollup-plugin-terser@7.0.2(rollup@2.79.2):
    rollup-plugin-terser: private
  rollup-pluginutils@2.8.2:
    rollup-pluginutils: private
  rollup@4.42.0:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.25.0:
    scheduler: private
  schema-utils@2.7.1:
    schema-utils: private
  semver@7.7.2:
    semver: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setprototypeof@1.1.1:
    setprototypeof: private
  sharp@0.33.5:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shellac@0.8.0:
    shellac: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  slash@3.0.0:
    slash: private
  source-list-map@2.0.1:
    source-list-map: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.8.0-beta.0:
    source-map: private
  sourcemap-codec@1.4.8:
    sourcemap-codec: private
  split@0.3.3:
    split: private
  stable-hash@0.0.5:
    stable-hash: private
  stackback@0.0.2:
    stackback: private
  stacktracey@2.1.8:
    stacktracey: private
  stat-mode@0.3.0:
    stat-mode: private
  statuses@1.5.0:
    statuses: private
  std-env@3.9.0:
    std-env: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  stoppable@1.1.0:
    stoppable: private
  stream-combiner@0.0.4:
    stream-combiner: private
  stream-to-array@2.3.0:
    stream-to-array: private
  stream-to-promise@2.2.0:
    stream-to-promise: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  stringify-object@3.3.0:
    stringify-object: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-comments@2.0.1:
    strip-comments: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  styled-jsx@5.1.6(@babel/core@7.27.4)(react@19.0.0):
    styled-jsx: private
  sucrase@3.35.0:
    sucrase: private
  superagent@10.2.1:
    superagent: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tapable@2.2.2:
    tapable: private
  tar@4.4.18:
    tar: private
  temp-dir@2.0.0:
    temp-dir: private
  tempy@0.6.0:
    tempy: private
  terser-webpack-plugin@5.3.14(esbuild@0.17.19)(webpack@5.99.9(esbuild@0.17.19)):
    terser-webpack-plugin: private
  terser@5.41.0:
    terser: private
  text-table@0.2.0:
    text-table: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  through@2.3.8:
    through: private
  time-span@4.0.0:
    time-span: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tinypool@1.1.0:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@4.0.3:
    tinyspy: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.0:
    toidentifier: private
  tr46@0.0.3:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  ts-morph@12.0.0:
    ts-morph: private
  ts-node@10.9.1(@types/node@16.18.11)(typescript@4.9.5):
    ts-node: private
  ts-toolbelt@6.15.5:
    ts-toolbelt: private
  tsconfck@3.1.6(typescript@5.8.3):
    tsconfck: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  ufo@1.6.1:
    ufo: private
  uid-promise@1.0.0:
    uid-promise: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.21.0:
    undici-types: private
  undici@5.28.4:
    undici: private
  unenv@2.0.0-rc.14:
    unenv: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  unique-string@2.0.0:
    unique-string: private
  universalify@0.1.2:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  unrs-resolver@1.7.11:
    unrs-resolver: private
  upath@1.2.0:
    upath: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.3(@types/react@18.3.23)(react@19.0.0):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@18.3.23)(react@19.0.0):
    use-sidecar: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@3.3.2:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  vite-node@3.2.2(@types/node@20.19.0)(jiti@1.21.7)(terser@5.41.0)(tsx@4.19.4)(yaml@2.8.0):
    vite-node: private
  vite@6.3.5(@types/node@20.19.0)(jiti@1.21.7)(terser@5.41.0)(tsx@4.19.4)(yaml@2.8.0):
    vite: private
  wait-on@8.0.3(debug@4.4.1):
    wait-on: private
  watchpack@2.4.0:
    watchpack: private
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: private
  web-vitals@0.2.4:
    web-vitals: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  webpack-sources@1.4.3:
    webpack-sources: private
  webpack@5.99.9(esbuild@0.17.19):
    webpack: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  wide-align@1.1.5:
    wide-align: private
  word-wrap@1.2.5:
    word-wrap: private
  workbox-background-sync@6.6.0:
    workbox-background-sync: private
  workbox-broadcast-update@6.6.0:
    workbox-broadcast-update: private
  workbox-build@6.6.0:
    workbox-build: private
  workbox-cacheable-response@6.6.0:
    workbox-cacheable-response: private
  workbox-core@6.6.0:
    workbox-core: private
  workbox-expiration@6.6.0:
    workbox-expiration: private
  workbox-google-analytics@6.6.0:
    workbox-google-analytics: private
  workbox-navigation-preload@6.6.0:
    workbox-navigation-preload: private
  workbox-precaching@6.6.0:
    workbox-precaching: private
  workbox-range-requests@6.6.0:
    workbox-range-requests: private
  workbox-recipes@6.6.0:
    workbox-recipes: private
  workbox-routing@6.6.0:
    workbox-routing: private
  workbox-strategies@6.6.0:
    workbox-strategies: private
  workbox-streams@6.6.0:
    workbox-streams: private
  workbox-sw@6.6.0:
    workbox-sw: private
  workbox-webpack-plugin@6.6.0(webpack@5.99.9(esbuild@0.17.19)):
    workbox-webpack-plugin: private
  workbox-window@6.6.0:
    workbox-window: private
  workerd@1.********.0:
    workerd: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.0:
    ws: private
  xdg-app-paths@5.1.0:
    xdg-app-paths: private
  xdg-portable@7.3.0:
    xdg-portable: private
  yallist@3.1.1:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yauzl-clone@1.0.4:
    yauzl-clone: private
  yauzl-promise@2.1.3:
    yauzl-promise: private
  yauzl@2.10.0:
    yauzl: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  youch@3.3.4:
    youch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.1
pendingBuilds: []
prunedAt: Sat, 07 Jun 2025 10:32:14 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@cloudflare/workerd-darwin-64@1.********.0'
  - '@cloudflare/workerd-darwin-arm64@1.********.0'
  - '@cloudflare/workerd-linux-64@1.********.0'
  - '@cloudflare/workerd-linux-arm64@1.********.0'
  - '@emnapi/core@1.4.3'
  - '@emnapi/runtime@1.4.3'
  - '@emnapi/wasi-threads@1.0.2'
  - '@esbuild/aix-ppc64@0.19.12'
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.17.19'
  - '@esbuild/android-arm64@0.18.20'
  - '@esbuild/android-arm64@0.19.12'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.15.18'
  - '@esbuild/android-arm@0.17.19'
  - '@esbuild/android-arm@0.18.20'
  - '@esbuild/android-arm@0.19.12'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.17.19'
  - '@esbuild/android-x64@0.18.20'
  - '@esbuild/android-x64@0.19.12'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.17.19'
  - '@esbuild/darwin-arm64@0.18.20'
  - '@esbuild/darwin-arm64@0.19.12'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.17.19'
  - '@esbuild/darwin-x64@0.18.20'
  - '@esbuild/darwin-x64@0.19.12'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.17.19'
  - '@esbuild/freebsd-arm64@0.18.20'
  - '@esbuild/freebsd-arm64@0.19.12'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.17.19'
  - '@esbuild/freebsd-x64@0.18.20'
  - '@esbuild/freebsd-x64@0.19.12'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.17.19'
  - '@esbuild/linux-arm64@0.18.20'
  - '@esbuild/linux-arm64@0.19.12'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.17.19'
  - '@esbuild/linux-arm@0.18.20'
  - '@esbuild/linux-arm@0.19.12'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.17.19'
  - '@esbuild/linux-ia32@0.18.20'
  - '@esbuild/linux-ia32@0.19.12'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.15.18'
  - '@esbuild/linux-loong64@0.17.19'
  - '@esbuild/linux-loong64@0.18.20'
  - '@esbuild/linux-loong64@0.19.12'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.17.19'
  - '@esbuild/linux-mips64el@0.18.20'
  - '@esbuild/linux-mips64el@0.19.12'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.17.19'
  - '@esbuild/linux-ppc64@0.18.20'
  - '@esbuild/linux-ppc64@0.19.12'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.17.19'
  - '@esbuild/linux-riscv64@0.18.20'
  - '@esbuild/linux-riscv64@0.19.12'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.17.19'
  - '@esbuild/linux-s390x@0.18.20'
  - '@esbuild/linux-s390x@0.19.12'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.17.19'
  - '@esbuild/linux-x64@0.18.20'
  - '@esbuild/linux-x64@0.19.12'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.17.19'
  - '@esbuild/netbsd-x64@0.18.20'
  - '@esbuild/netbsd-x64@0.19.12'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.17.19'
  - '@esbuild/openbsd-x64@0.18.20'
  - '@esbuild/openbsd-x64@0.19.12'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.17.19'
  - '@esbuild/sunos-x64@0.18.20'
  - '@esbuild/sunos-x64@0.19.12'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.17.19'
  - '@esbuild/win32-arm64@0.18.20'
  - '@esbuild/win32-arm64@0.19.12'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.17.19'
  - '@esbuild/win32-ia32@0.18.20'
  - '@esbuild/win32-ia32@0.19.12'
  - '@esbuild/win32-ia32@0.25.5'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@napi-rs/wasm-runtime@0.2.10'
  - '@next/swc-darwin-arm64@13.5.9'
  - '@next/swc-darwin-arm64@15.1.1'
  - '@next/swc-darwin-x64@13.5.9'
  - '@next/swc-darwin-x64@15.1.1'
  - '@next/swc-linux-arm64-gnu@13.5.9'
  - '@next/swc-linux-arm64-gnu@15.1.1'
  - '@next/swc-linux-arm64-musl@13.5.9'
  - '@next/swc-linux-arm64-musl@15.1.1'
  - '@next/swc-linux-x64-gnu@13.5.9'
  - '@next/swc-linux-x64-gnu@15.1.1'
  - '@next/swc-linux-x64-musl@13.5.9'
  - '@next/swc-linux-x64-musl@15.1.1'
  - '@next/swc-win32-arm64-msvc@13.5.9'
  - '@next/swc-win32-arm64-msvc@15.1.1'
  - '@next/swc-win32-ia32-msvc@13.5.9'
  - '@oven/bun-darwin-aarch64@1.2.15'
  - '@oven/bun-darwin-x64-baseline@1.2.15'
  - '@oven/bun-darwin-x64@1.2.15'
  - '@oven/bun-linux-aarch64-musl@1.2.15'
  - '@oven/bun-linux-aarch64@1.2.15'
  - '@oven/bun-linux-x64-baseline@1.2.15'
  - '@oven/bun-linux-x64-musl-baseline@1.2.15'
  - '@oven/bun-linux-x64-musl@1.2.15'
  - '@oven/bun-linux-x64@1.2.15'
  - '@rollup/rollup-android-arm-eabi@4.42.0'
  - '@rollup/rollup-android-arm64@4.42.0'
  - '@rollup/rollup-darwin-arm64@4.42.0'
  - '@rollup/rollup-darwin-x64@4.42.0'
  - '@rollup/rollup-freebsd-arm64@4.42.0'
  - '@rollup/rollup-freebsd-x64@4.42.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.42.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.42.0'
  - '@rollup/rollup-linux-arm64-gnu@4.42.0'
  - '@rollup/rollup-linux-arm64-musl@4.42.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.42.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.42.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.42.0'
  - '@rollup/rollup-linux-riscv64-musl@4.42.0'
  - '@rollup/rollup-linux-s390x-gnu@4.42.0'
  - '@rollup/rollup-linux-x64-gnu@4.42.0'
  - '@rollup/rollup-linux-x64-musl@4.42.0'
  - '@rollup/rollup-win32-arm64-msvc@4.42.0'
  - '@rollup/rollup-win32-ia32-msvc@4.42.0'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/resolver-binding-darwin-arm64@1.7.11'
  - '@unrs/resolver-binding-darwin-x64@1.7.11'
  - '@unrs/resolver-binding-freebsd-x64@1.7.11'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.7.11'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.7.11'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.7.11'
  - '@unrs/resolver-binding-linux-arm64-musl@1.7.11'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.7.11'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.7.11'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.7.11'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.7.11'
  - '@unrs/resolver-binding-linux-x64-gnu@1.7.11'
  - '@unrs/resolver-binding-linux-x64-musl@1.7.11'
  - '@unrs/resolver-binding-wasm32-wasi@1.7.11'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.7.11'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.7.11'
  - esbuild-android-64@0.14.47
  - esbuild-android-64@0.15.18
  - esbuild-android-arm64@0.14.47
  - esbuild-android-arm64@0.15.18
  - esbuild-darwin-64@0.14.47
  - esbuild-darwin-64@0.15.18
  - esbuild-darwin-arm64@0.14.47
  - esbuild-darwin-arm64@0.15.18
  - esbuild-freebsd-64@0.14.47
  - esbuild-freebsd-64@0.15.18
  - esbuild-freebsd-arm64@0.14.47
  - esbuild-freebsd-arm64@0.15.18
  - esbuild-linux-32@0.14.47
  - esbuild-linux-32@0.15.18
  - esbuild-linux-64@0.14.47
  - esbuild-linux-64@0.15.18
  - esbuild-linux-arm64@0.14.47
  - esbuild-linux-arm64@0.15.18
  - esbuild-linux-arm@0.14.47
  - esbuild-linux-arm@0.15.18
  - esbuild-linux-mips64le@0.14.47
  - esbuild-linux-mips64le@0.15.18
  - esbuild-linux-ppc64le@0.14.47
  - esbuild-linux-ppc64le@0.15.18
  - esbuild-linux-riscv64@0.14.47
  - esbuild-linux-riscv64@0.15.18
  - esbuild-linux-s390x@0.14.47
  - esbuild-linux-s390x@0.15.18
  - esbuild-netbsd-64@0.14.47
  - esbuild-netbsd-64@0.15.18
  - esbuild-openbsd-64@0.14.47
  - esbuild-openbsd-64@0.15.18
  - esbuild-sunos-64@0.14.47
  - esbuild-sunos-64@0.15.18
  - esbuild-windows-32@0.14.47
  - esbuild-windows-32@0.15.18
  - esbuild-windows-arm64@0.14.47
  - esbuild-windows-arm64@0.15.18
  - fsevents@2.3.3
storeDir: F:\.pnpm-store\v10
virtualStoreDir: F:\CODE\Project\Mail\moemail_source\node_modules\.pnpm
virtualStoreDirMaxLength: 60
