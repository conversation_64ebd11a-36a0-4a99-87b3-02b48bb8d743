import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { generateAddressCredential, verifyAddressCredential } from './jwt';
import { jwtVerify } from 'jose';
import { createDb } from './db';

// Mock the database
vi.mock('./db', () => ({
  createDb: vi.fn(() => ({
    query: {
      revoked_credentials: {
        findFirst: vi.fn(),
      },
    },
  })),
}));

const mockedCreateDb = vi.mocked(createDb);
const mockedFindFirst = mockedCreateDb()!.query.revoked_credentials.findFirst;

describe('JWT Address Credentials', () => {
  const testSecret = 'test-secret-string-for-jwt-address';
  const testAddress = '<EMAIL>';

  beforeEach(() => {
    vi.stubEnv('JWT_ADDRESS_SECRET', testSecret);
    mockedFindFirst.mockReset();
  });

  afterEach(() => {
    vi.unstubAllEnvs();
  });

  describe('generateAddressCredential', () => {
    it('should generate a valid JWT with correct subject and audience', async () => {
      const token = await generateAddressCredential(testAddress, 3600);
      const secret = new TextEncoder().encode(testSecret);
      const { payload } = await jwtVerify(token, secret);

      expect(payload.sub).toBe(testAddress);
      expect(payload.aud).toBe(testAddress);
      expect(payload.iss).toBe('moemail');
    });

    it('should generate a long-lived token when expiresIn is 0', async () => {
      const token = await generateAddressCredential(testAddress, 0);
      const secret = new TextEncoder().encode(testSecret);
      const { payload } = await jwtVerify(token, secret);
      
      const oneYearInSeconds = 365 * 24 * 60 * 60;
      expect(payload.exp! - payload.iat!).toBeGreaterThanOrEqual(oneYearInSeconds - 1);
    });
  });

  describe('verifyAddressCredential', () => {
    it('should return payload for a valid, non-revoked token', async () => {
      mockedFindFirst.mockResolvedValue(undefined);
      const token = await generateAddressCredential(testAddress, 3600);
      const payload = await verifyAddressCredential(token);
      expect(payload).not.toBeNull();
      expect(payload?.sub).toBe(testAddress);
    });

    it('should return null for a revoked token', async () => {
      const token = await generateAddressCredential(testAddress, 3600);
      const { payload: decodedPayload } = await jwtVerify(token, new TextEncoder().encode(testSecret));
      
      mockedFindFirst.mockResolvedValue({ jti: decodedPayload.jti!, expires_at: new Date() });
      
      const payload = await verifyAddressCredential(token);
      expect(payload).toBeNull();
      expect(mockedFindFirst).toHaveBeenCalledWith({
        where: expect.anything(),
      });
    });

    it('should return null for an expired token', async () => {
      const token = await generateAddressCredential(testAddress, -1); // Expires in the past
      const payload = await verifyAddressCredential(token);
      expect(payload).toBeNull();
    });

    it('should return null for a token with an invalid signature', async () => {
      const token = await generateAddressCredential(testAddress, 3600);
      vi.stubEnv('JWT_ADDRESS_SECRET', 'a-different-secret'); // Verify with wrong secret
      const payload = await verifyAddressCredential(token);
      expect(payload).toBeNull();
    });
  });
}); 