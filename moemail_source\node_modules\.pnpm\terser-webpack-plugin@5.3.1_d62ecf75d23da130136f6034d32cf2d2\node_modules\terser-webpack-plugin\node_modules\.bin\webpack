#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/webpack@5.97.1_esbuild@0.17.19/node_modules/webpack/bin/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/webpack@5.97.1_esbuild@0.17.19/node_modules/webpack/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/webpack@5.97.1_esbuild@0.17.19/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/webpack@5.97.1_esbuild@0.17.19/node_modules/webpack/bin/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/webpack@5.97.1_esbuild@0.17.19/node_modules/webpack/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/webpack@5.97.1_esbuild@0.17.19/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../webpack@5.97.1_esbuild@0.17.19/node_modules/webpack/bin/webpack.js" "$@"
else
  exec node  "$basedir/../../../../../webpack@5.97.1_esbuild@0.17.19/node_modules/webpack/bin/webpack.js" "$@"
fi
