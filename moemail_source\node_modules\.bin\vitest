#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/vitest@3.2.2_@edge-runtime+_3dc4917ec1ded1b0e4a023a5f2859cd4/node_modules/vitest/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/vitest@3.2.2_@edge-runtime+_3dc4917ec1ded1b0e4a023a5f2859cd4/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/vitest@3.2.2_@edge-runtime+_3dc4917ec1ded1b0e4a023a5f2859cd4/node_modules/vitest/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/vitest@3.2.2_@edge-runtime+_3dc4917ec1ded1b0e4a023a5f2859cd4/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../vitest/vitest.mjs" "$@"
fi
