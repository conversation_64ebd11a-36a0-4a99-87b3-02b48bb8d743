{"version": 3, "sources": ["../../../../src/build/webpack/plugins/next-trace-entrypoints-plugin.ts"], "sourcesContent": ["import nodePath from 'path'\nimport crypto from 'crypto'\nimport type { Span } from '../../../trace'\nimport { spans } from './profiling-plugin'\nimport isError from '../../../lib/is-error'\nimport { nodeFileTrace } from 'next/dist/compiled/@vercel/nft'\nimport type { SWCLoaderOptions } from '../loaders/next-swc-loader'\nimport type { NodeFileTraceReasons } from 'next/dist/compiled/@vercel/nft'\nimport {\n  CLIENT_REFERENCE_MANIFEST,\n  TRACE_OUTPUT_VERSION,\n  type CompilerNameValues,\n} from '../../../shared/lib/constants'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  NODE_ESM_RESOLVE_OPTIONS,\n  NODE_RESOLVE_OPTIONS,\n} from '../../webpack-config'\nimport type { NextConfigComplete } from '../../../server/config-shared'\nimport picomatch from 'next/dist/compiled/picomatch'\nimport { getModuleBuildInfo } from '../loaders/get-module-build-info'\nimport { getPageFilePath } from '../../entries'\nimport { resolveExternal } from '../../handle-externals'\nimport { isMetadataRoute } from '../../../lib/metadata/is-metadata-route'\n\nconst PLUGIN_NAME = 'TraceEntryPointsPlugin'\nexport const TRACE_IGNORES = [\n  '**/*/next/dist/server/next.js',\n  '**/*/next/dist/bin/next',\n]\n\nconst NOT_TRACEABLE = [\n  '.wasm',\n  '.png',\n  '.jpg',\n  '.jpeg',\n  '.gif',\n  '.webp',\n  '.avif',\n  '.ico',\n  '.bmp',\n  '.svg',\n]\n\nfunction getModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string; request?: string } {\n  return compilation.moduleGraph.getModule(dep)\n}\n\nexport function getFilesMapFromReasons(\n  fileList: Set<string>,\n  reasons: NodeFileTraceReasons,\n  ignoreFn?: (file: string, parent?: string) => Boolean\n) {\n  // this uses the reasons tree to collect files specific to a\n  // certain parent allowing us to not have to trace each parent\n  // separately\n  const parentFilesMap = new Map<string, Map<string, { ignored: boolean }>>()\n\n  function propagateToParents(\n    parents: Set<string>,\n    file: string,\n    seen = new Set<string>()\n  ) {\n    for (const parent of parents || []) {\n      if (!seen.has(parent)) {\n        seen.add(parent)\n        let parentFiles = parentFilesMap.get(parent)\n\n        if (!parentFiles) {\n          parentFiles = new Map()\n          parentFilesMap.set(parent, parentFiles)\n        }\n        const ignored = Boolean(ignoreFn?.(file, parent))\n        parentFiles.set(file, { ignored })\n\n        const parentReason = reasons.get(parent)\n\n        if (parentReason?.parents) {\n          propagateToParents(parentReason.parents, file, seen)\n        }\n      }\n    }\n  }\n\n  for (const file of fileList!) {\n    const reason = reasons!.get(file)\n    const isInitial =\n      reason?.type.length === 1 && reason.type.includes('initial')\n\n    if (\n      !reason ||\n      !reason.parents ||\n      (isInitial && reason.parents.size === 0)\n    ) {\n      continue\n    }\n    propagateToParents(reason.parents, file)\n  }\n  return parentFilesMap\n}\n\nexport interface TurbotraceAction {\n  action: 'print' | 'annotate'\n  input: string[]\n  contextDirectory: string\n  processCwd: string\n  showAll?: boolean\n  memoryLimit?: number\n}\n\nexport interface BuildTraceContext {\n  entriesTrace?: {\n    action: TurbotraceAction\n    appDir: string\n    outputPath: string\n    depModArray: string[]\n    entryNameMap: Record<string, string>\n  }\n  chunksTrace?: {\n    action: TurbotraceAction\n    outputPath: string\n    entryNameFilesMap: Record<string, Array<string>>\n  }\n}\n\nexport function getHash(content: string | Buffer): string {\n  return crypto.createHash('sha1').update(content).digest('hex')\n}\n\nexport class TraceEntryPointsPlugin implements webpack.WebpackPluginInstance {\n  public buildTraceContext: BuildTraceContext = {}\n\n  private rootDir: string\n  private appDir: string | undefined\n  private pagesDir: string | undefined\n  private optOutBundlingPackages: string[]\n  private appDirEnabled?: boolean\n  private tracingRoot: string\n  private entryTraces: Map<string, Map<string, { bundled: boolean }>>\n  private traceIgnores: string[]\n  private esmExternals?: NextConfigComplete['experimental']['esmExternals']\n  private traceHashes: Map<string, string>\n  private compilerType: CompilerNameValues\n  private swcLoaderConfig: {\n    loader: string\n    options: SWCLoaderOptions\n  }\n\n  constructor({\n    rootDir,\n    appDir,\n    pagesDir,\n    compilerType,\n    optOutBundlingPackages,\n    appDirEnabled,\n    traceIgnores,\n    esmExternals,\n    outputFileTracingRoot,\n    swcLoaderConfig,\n  }: {\n    rootDir: string\n    compilerType: CompilerNameValues\n    appDir: string | undefined\n    pagesDir: string | undefined\n    optOutBundlingPackages: string[]\n    appDirEnabled?: boolean\n    traceIgnores?: string[]\n    outputFileTracingRoot?: string\n    esmExternals?: NextConfigComplete['experimental']['esmExternals']\n    swcLoaderConfig: TraceEntryPointsPlugin['swcLoaderConfig']\n  }) {\n    this.rootDir = rootDir\n    this.appDir = appDir\n    this.pagesDir = pagesDir\n    this.entryTraces = new Map()\n    this.esmExternals = esmExternals\n    this.appDirEnabled = appDirEnabled\n    this.traceIgnores = traceIgnores || []\n    this.tracingRoot = outputFileTracingRoot || rootDir\n    this.optOutBundlingPackages = optOutBundlingPackages\n    this.traceHashes = new Map()\n    this.compilerType = compilerType\n    this.swcLoaderConfig = swcLoaderConfig\n  }\n\n  // Here we output all traced assets and webpack chunks to a\n  // ${page}.js.nft.json file\n  async createTraceAssets(\n    compilation: webpack.Compilation,\n    assets: any,\n    span: Span\n  ) {\n    const outputPath = compilation.outputOptions.path || ''\n\n    await span.traceChild('create-trace-assets').traceAsyncFn(async () => {\n      const entryFilesMap = new Map<any, Set<string>>()\n      const chunksToTrace = new Set<string>()\n      const entryNameFilesMap = new Map<string, Array<string>>()\n\n      const isTraceable = (file: string) =>\n        !NOT_TRACEABLE.some((suffix) => {\n          return file.endsWith(suffix)\n        })\n\n      for (const entrypoint of compilation.entrypoints.values()) {\n        const entryFiles = new Set<string>()\n\n        for (const chunk of entrypoint\n          .getEntrypointChunk()\n          .getAllReferencedChunks()) {\n          for (const file of chunk.files) {\n            if (isTraceable(file)) {\n              const filePath = nodePath.join(outputPath, file)\n              chunksToTrace.add(filePath)\n              entryFiles.add(filePath)\n            }\n          }\n          for (const file of chunk.auxiliaryFiles) {\n            if (isTraceable(file)) {\n              const filePath = nodePath.join(outputPath, file)\n              chunksToTrace.add(filePath)\n              entryFiles.add(filePath)\n            }\n          }\n        }\n        entryFilesMap.set(entrypoint, entryFiles)\n        entryNameFilesMap.set(entrypoint.name || '', [...entryFiles])\n      }\n\n      // startTrace existed and callable\n      this.buildTraceContext.chunksTrace = {\n        action: {\n          action: 'annotate',\n          input: [...chunksToTrace],\n          contextDirectory: this.tracingRoot,\n          processCwd: this.rootDir,\n        },\n        outputPath,\n        entryNameFilesMap: Object.fromEntries(entryNameFilesMap),\n      }\n\n      // server compiler outputs to `server/chunks` so we traverse up\n      // one, but edge-server does not so don't for that one\n      const outputPrefix = this.compilerType === 'server' ? '../' : ''\n\n      for (const [entrypoint, entryFiles] of entryFilesMap) {\n        const traceOutputName = `${outputPrefix}${entrypoint.name}.js.nft.json`\n        const traceOutputPath = nodePath.dirname(\n          nodePath.join(outputPath, traceOutputName)\n        )\n\n        // don't include the entry itself in the trace\n        entryFiles.delete(\n          nodePath.join(outputPath, `${outputPrefix}${entrypoint.name}.js`)\n        )\n\n        if (entrypoint.name.startsWith('app/')) {\n          // Include the client reference manifest for pages and route handlers,\n          // excluding metadata route handlers.\n          const clientManifestsForEntrypoint = isMetadataRoute(entrypoint.name)\n            ? null\n            : nodePath.join(\n                outputPath,\n                outputPrefix,\n                entrypoint.name.replace(/%5F/g, '_') +\n                  '_' +\n                  CLIENT_REFERENCE_MANIFEST +\n                  '.js'\n              )\n\n          if (clientManifestsForEntrypoint !== null) {\n            entryFiles.add(clientManifestsForEntrypoint)\n          }\n        }\n\n        const finalFiles: string[] = []\n\n        await Promise.all(\n          [\n            ...new Set([\n              ...entryFiles,\n              ...(this.entryTraces.get(entrypoint.name)?.keys() || []),\n            ]),\n          ].map(async (file) => {\n            const fileInfo = this.entryTraces.get(entrypoint.name)?.get(file)\n\n            const relativeFile = nodePath\n              .relative(traceOutputPath, file)\n              .replace(/\\\\/g, '/')\n\n            if (file) {\n              if (!fileInfo?.bundled) {\n                finalFiles.push(relativeFile)\n              }\n            }\n          })\n        )\n\n        assets[traceOutputName] = new sources.RawSource(\n          JSON.stringify({\n            version: TRACE_OUTPUT_VERSION,\n            files: finalFiles,\n          })\n        )\n      }\n    })\n  }\n\n  tapfinishModules(\n    compilation: webpack.Compilation,\n    traceEntrypointsPluginSpan: Span,\n    doResolve: (\n      request: string,\n      parent: string,\n      job: import('@vercel/nft/out/node-file-trace').Job,\n      isEsmRequested: boolean\n    ) => Promise<string>,\n    readlink: any,\n    stat: any\n  ) {\n    compilation.hooks.finishModules.tapAsync(\n      PLUGIN_NAME,\n      async (_stats: any, callback: any) => {\n        const finishModulesSpan =\n          traceEntrypointsPluginSpan.traceChild('finish-modules')\n        await finishModulesSpan\n          .traceAsyncFn(async () => {\n            // we create entry -> module maps so that we can\n            // look them up faster instead of having to iterate\n            // over the compilation modules list\n            const entryNameMap = new Map<string, string>()\n            const entryModMap = new Map<string, any>()\n            const additionalEntries = new Map<string, Map<string, any>>()\n\n            const depModMap = new Map<string, any>()\n\n            await finishModulesSpan\n              .traceChild('get-entries')\n              .traceAsyncFn(async () => {\n                for (const [name, entry] of compilation.entries.entries()) {\n                  const normalizedName = name?.replace(/\\\\/g, '/')\n\n                  const isPage = normalizedName.startsWith('pages/')\n                  const isApp =\n                    this.appDirEnabled && normalizedName.startsWith('app/')\n\n                  if (isApp || isPage) {\n                    for (const dep of entry.dependencies) {\n                      if (!dep) continue\n                      const entryMod = getModuleFromDependency(compilation, dep)\n\n                      // Handle case where entry is a loader coming from Next.js.\n                      // For example edge-loader or app-loader.\n                      if (entryMod && entryMod.resource === '') {\n                        const moduleBuildInfo = getModuleBuildInfo(entryMod)\n                        // All loaders that are used to create entries have a `route` property on the buildInfo.\n                        if (moduleBuildInfo.route) {\n                          const absolutePath = getPageFilePath({\n                            absolutePagePath:\n                              moduleBuildInfo.route.absolutePagePath,\n                            rootDir: this.rootDir,\n                            appDir: this.appDir,\n                            pagesDir: this.pagesDir,\n                          })\n\n                          // Ensures we don't handle non-pages.\n                          if (\n                            (this.pagesDir &&\n                              absolutePath.startsWith(this.pagesDir)) ||\n                            (this.appDir &&\n                              absolutePath.startsWith(this.appDir))\n                          ) {\n                            entryModMap.set(absolutePath, entryMod)\n                            entryNameMap.set(absolutePath, name)\n                          }\n                        }\n\n                        // If there was no `route` property, we can assume that it was something custom instead.\n                        // In order to trace these we add them to the additionalEntries map.\n                        if (entryMod.request) {\n                          let curMap = additionalEntries.get(name)\n\n                          if (!curMap) {\n                            curMap = new Map()\n                            additionalEntries.set(name, curMap)\n                          }\n                          depModMap.set(entryMod.request, entryMod)\n                          curMap.set(entryMod.resource, entryMod)\n                        }\n                      }\n\n                      if (entryMod && entryMod.resource) {\n                        entryNameMap.set(entryMod.resource, name)\n                        entryModMap.set(entryMod.resource, entryMod)\n\n                        let curMap = additionalEntries.get(name)\n\n                        if (!curMap) {\n                          curMap = new Map()\n                          additionalEntries.set(name, curMap)\n                        }\n                        depModMap.set(entryMod.resource, entryMod)\n                        curMap.set(entryMod.resource, entryMod)\n                      }\n                    }\n                  }\n                }\n              })\n\n            const readFile = async (\n              path: string\n            ): Promise<Buffer | string | null> => {\n              const mod = depModMap.get(path) || entryModMap.get(path)\n\n              // map the transpiled source when available to avoid\n              // parse errors in node-file-trace\n              let source: Buffer | string = mod?.originalSource?.()?.buffer()\n              return source || ''\n            }\n\n            const entryPaths = Array.from(entryModMap.keys())\n\n            const collectDependencies = async (mod: any, parent: string) => {\n              if (!mod || !mod.dependencies) return\n\n              for (const dep of mod.dependencies) {\n                const depMod = getModuleFromDependency(compilation, dep)\n\n                if (depMod?.resource && !depModMap.get(depMod.resource)) {\n                  depModMap.set(depMod.resource, depMod)\n                  await collectDependencies(depMod, parent)\n                }\n              }\n            }\n            const entriesToTrace = [...entryPaths]\n\n            for (const entry of entryPaths) {\n              await collectDependencies(entryModMap.get(entry), entry)\n              const entryName = entryNameMap.get(entry)!\n              const curExtraEntries = additionalEntries.get(entryName)\n\n              if (curExtraEntries) {\n                entriesToTrace.push(...curExtraEntries.keys())\n              }\n            }\n\n            const contextDirectory = this.tracingRoot\n            const chunks = [...entriesToTrace]\n\n            this.buildTraceContext.entriesTrace = {\n              action: {\n                action: 'print',\n                input: chunks,\n                contextDirectory,\n                processCwd: this.rootDir,\n              },\n              appDir: this.rootDir,\n              depModArray: Array.from(depModMap.keys()),\n              entryNameMap: Object.fromEntries(entryNameMap),\n              outputPath: compilation.outputOptions.path!,\n            }\n\n            let fileList: Set<string>\n            let reasons: NodeFileTraceReasons\n            const ignores = [\n              ...TRACE_IGNORES,\n              ...this.traceIgnores,\n              '**/node_modules/**',\n            ]\n\n            // pre-compile the ignore matcher to avoid repeating on every ignoreFn call\n            const isIgnoreMatcher = picomatch(ignores, {\n              contains: true,\n              dot: true,\n            })\n            const ignoreFn = (path: string) => {\n              return isIgnoreMatcher(path)\n            }\n\n            await finishModulesSpan\n              .traceChild('node-file-trace-plugin', {\n                traceEntryCount: entriesToTrace.length + '',\n              })\n              .traceAsyncFn(async () => {\n                const result = await nodeFileTrace(entriesToTrace, {\n                  base: this.tracingRoot,\n                  processCwd: this.rootDir,\n                  readFile,\n                  readlink,\n                  stat,\n                  resolve: doResolve\n                    ? async (id, parent, job, isCjs) => {\n                        return doResolve(id, parent, job, !isCjs)\n                      }\n                    : undefined,\n                  ignore: ignoreFn,\n                  mixedModules: true,\n                })\n                // @ts-ignore\n                fileList = result.fileList\n                result.esmFileList.forEach((file) => fileList.add(file))\n                reasons = result.reasons\n              })\n\n            await finishModulesSpan\n              .traceChild('collect-traced-files')\n              .traceAsyncFn(() => {\n                const parentFilesMap = getFilesMapFromReasons(\n                  fileList,\n                  reasons,\n                  (file) => {\n                    // if a file was imported and a loader handled it\n                    // we don't include it in the trace e.g.\n                    // static image imports, CSS imports\n                    file = nodePath.join(this.tracingRoot, file)\n                    const depMod = depModMap.get(file)\n                    const isAsset = reasons\n                      .get(nodePath.relative(this.tracingRoot, file))\n                      ?.type.includes('asset')\n\n                    return (\n                      !isAsset &&\n                      Array.isArray(depMod?.loaders) &&\n                      depMod.loaders.length > 0\n                    )\n                  }\n                )\n\n                for (const entry of entryPaths) {\n                  const entryName = entryNameMap.get(entry)!\n                  const normalizedEntry = nodePath.relative(\n                    this.tracingRoot,\n                    entry\n                  )\n                  const curExtraEntries = additionalEntries.get(entryName)\n                  const finalDeps = new Map<string, { bundled: boolean }>()\n\n                  // ensure we include entry source file as well for\n                  // hash comparison\n                  finalDeps.set(entry, {\n                    bundled: true,\n                  })\n\n                  for (const [dep, info] of parentFilesMap\n                    .get(normalizedEntry)\n                    ?.entries() || []) {\n                    finalDeps.set(nodePath.join(this.tracingRoot, dep), {\n                      bundled: info.ignored,\n                    })\n                  }\n\n                  if (curExtraEntries) {\n                    for (const extraEntry of curExtraEntries.keys()) {\n                      const normalizedExtraEntry = nodePath.relative(\n                        this.tracingRoot,\n                        extraEntry\n                      )\n                      finalDeps.set(extraEntry, { bundled: false })\n\n                      for (const [dep, info] of parentFilesMap\n                        .get(normalizedExtraEntry)\n                        ?.entries() || []) {\n                        finalDeps.set(nodePath.join(this.tracingRoot, dep), {\n                          bundled: info.ignored,\n                        })\n                      }\n                    }\n                  }\n                  this.entryTraces.set(entryName, finalDeps)\n                }\n              })\n          })\n          .then(\n            () => callback(),\n            (err) => callback(err)\n          )\n      }\n    )\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      const readlink = async (path: string): Promise<string | null> => {\n        try {\n          return await new Promise((resolve, reject) => {\n            ;(\n              compilation.inputFileSystem\n                .readlink as typeof import('fs').readlink\n            )(path, (err, link) => {\n              if (err) return reject(err)\n              resolve(link)\n            })\n          })\n        } catch (e) {\n          if (\n            isError(e) &&\n            (e.code === 'EINVAL' || e.code === 'ENOENT' || e.code === 'UNKNOWN')\n          ) {\n            return null\n          }\n          throw e\n        }\n      }\n      const stat = async (path: string): Promise<import('fs').Stats | null> => {\n        try {\n          return await new Promise((resolve, reject) => {\n            ;(compilation.inputFileSystem.stat as typeof import('fs').stat)(\n              path,\n              (err, stats) => {\n                if (err) return reject(err)\n                resolve(stats)\n              }\n            )\n          })\n        } catch (e) {\n          if (isError(e) && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) {\n            return null\n          }\n          throw e\n        }\n      }\n\n      const compilationSpan = spans.get(compilation) || spans.get(compiler)!\n      const traceEntrypointsPluginSpan = compilationSpan.traceChild(\n        'next-trace-entrypoint-plugin'\n      )\n      traceEntrypointsPluginSpan.traceFn(() => {\n        compilation.hooks.processAssets.tapAsync(\n          {\n            name: PLUGIN_NAME,\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_SUMMARIZE,\n          },\n          (assets: any, callback: any) => {\n            this.createTraceAssets(\n              compilation,\n              assets,\n              traceEntrypointsPluginSpan\n            )\n              .then(() => callback())\n              .catch((err) => callback(err))\n          }\n        )\n\n        let resolver = compilation.resolverFactory.get('normal')\n\n        function getPkgName(name: string) {\n          const segments = name.split('/')\n          if (name[0] === '@' && segments.length > 1)\n            return segments.length > 1 ? segments.slice(0, 2).join('/') : null\n          return segments.length ? segments[0] : null\n        }\n\n        const getResolve = (\n          options: Parameters<typeof resolver.withOptions>[0]\n        ) => {\n          const curResolver = resolver.withOptions(options)\n\n          return (\n            parent: string,\n            request: string,\n            job: import('@vercel/nft/out/node-file-trace').Job\n          ) =>\n            new Promise<[string, boolean]>((resolve, reject) => {\n              const context = nodePath.dirname(parent)\n\n              curResolver.resolve(\n                {},\n                context,\n                request,\n                {\n                  fileDependencies: compilation.fileDependencies,\n                  missingDependencies: compilation.missingDependencies,\n                  contextDependencies: compilation.contextDependencies,\n                },\n                async (err: any, result?, resContext?) => {\n                  if (err) return reject(err)\n\n                  if (!result) {\n                    return reject(new Error('module not found'))\n                  }\n\n                  // webpack resolver doesn't strip loader query info\n                  // from the result so use path instead\n                  if (result.includes('?') || result.includes('!')) {\n                    result = resContext?.path || result\n                  }\n\n                  try {\n                    // we need to collect all parent package.json's used\n                    // as webpack's resolve doesn't expose this and parent\n                    // package.json could be needed for resolving e.g. stylis\n                    // stylis/package.json -> stylis/dist/umd/package.json\n                    if (result.includes('node_modules')) {\n                      let requestPath = result\n                        .replace(/\\\\/g, '/')\n                        .replace(/\\0/g, '')\n\n                      if (\n                        !nodePath.isAbsolute(request) &&\n                        request.includes('/') &&\n                        resContext?.descriptionFileRoot\n                      ) {\n                        requestPath = (\n                          resContext.descriptionFileRoot +\n                          request.slice(getPkgName(request)?.length || 0) +\n                          nodePath.sep +\n                          'package.json'\n                        )\n                          .replace(/\\\\/g, '/')\n                          .replace(/\\0/g, '')\n                      }\n\n                      const rootSeparatorIndex = requestPath.indexOf('/')\n                      let separatorIndex: number\n                      while (\n                        (separatorIndex = requestPath.lastIndexOf('/')) >\n                        rootSeparatorIndex\n                      ) {\n                        requestPath = requestPath.slice(0, separatorIndex)\n                        const curPackageJsonPath = `${requestPath}/package.json`\n                        if (await job.isFile(curPackageJsonPath)) {\n                          await job.emitFile(\n                            await job.realpath(curPackageJsonPath),\n                            'resolve',\n                            parent\n                          )\n                        }\n                      }\n                    }\n                  } catch (_err) {\n                    // we failed to resolve the package.json boundary,\n                    // we don't block emitting the initial asset from this\n                  }\n                  resolve([result, options.dependencyType === 'esm'])\n                }\n              )\n            })\n        }\n\n        const CJS_RESOLVE_OPTIONS = {\n          ...NODE_RESOLVE_OPTIONS,\n          fullySpecified: undefined,\n          modules: undefined,\n          extensions: undefined,\n        }\n        const BASE_CJS_RESOLVE_OPTIONS = {\n          ...CJS_RESOLVE_OPTIONS,\n          alias: false,\n        }\n        const ESM_RESOLVE_OPTIONS = {\n          ...NODE_ESM_RESOLVE_OPTIONS,\n          fullySpecified: undefined,\n          modules: undefined,\n          extensions: undefined,\n        }\n        const BASE_ESM_RESOLVE_OPTIONS = {\n          ...ESM_RESOLVE_OPTIONS,\n          alias: false,\n        }\n\n        const doResolve = async (\n          request: string,\n          parent: string,\n          job: import('@vercel/nft/out/node-file-trace').Job,\n          isEsmRequested: boolean\n        ): Promise<string> => {\n          const context = nodePath.dirname(parent)\n          // When in esm externals mode, and using import, we resolve with\n          // ESM resolving options.\n          const { res } = await resolveExternal(\n            this.rootDir,\n            this.esmExternals,\n            context,\n            request,\n            isEsmRequested,\n            this.optOutBundlingPackages,\n            (options) => (_: string, resRequest: string) => {\n              return getResolve(options)(parent, resRequest, job)\n            },\n            undefined,\n            undefined,\n            ESM_RESOLVE_OPTIONS,\n            CJS_RESOLVE_OPTIONS,\n            BASE_ESM_RESOLVE_OPTIONS,\n            BASE_CJS_RESOLVE_OPTIONS\n          )\n\n          if (!res) {\n            throw new Error(`failed to resolve ${request} from ${parent}`)\n          }\n          return res.replace(/\\0/g, '')\n        }\n\n        this.tapfinishModules(\n          compilation,\n          traceEntrypointsPluginSpan,\n          doResolve,\n          readlink,\n          stat\n        )\n      })\n    })\n  }\n}\n"], "names": ["TRACE_IGNORES", "TraceEntryPointsPlugin", "getFilesMapFromReasons", "getHash", "PLUGIN_NAME", "NOT_TRACEABLE", "getModuleFromDependency", "compilation", "dep", "moduleGraph", "getModule", "fileList", "reasons", "ignoreFn", "parentFilesMap", "Map", "propagateToParents", "parents", "file", "seen", "Set", "parent", "has", "add", "parentFiles", "get", "set", "ignored", "Boolean", "parentReason", "reason", "isInitial", "type", "length", "includes", "size", "content", "crypto", "createHash", "update", "digest", "constructor", "rootDir", "appDir", "pagesDir", "compilerType", "optOutBundlingPackages", "appDirEnabled", "traceIgnores", "esmExternals", "outputFileTracingRoot", "swcLoaderConfig", "buildTraceContext", "entryTraces", "tracingRoot", "traceHashes", "createTraceAssets", "assets", "span", "outputPath", "outputOptions", "path", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "entryFilesMap", "chunksToTrace", "entryNameFilesMap", "isTraceable", "some", "suffix", "endsWith", "entrypoint", "entrypoints", "values", "entryFiles", "chunk", "getEntrypointChunk", "getAllReferencedChunks", "files", "filePath", "nodePath", "join", "auxiliaryFiles", "name", "chunksTrace", "action", "input", "contextDirectory", "processCwd", "Object", "fromEntries", "outputPrefix", "traceOutputName", "traceOutputPath", "dirname", "delete", "startsWith", "clientManifestsForEntrypoint", "isMetadataRoute", "replace", "CLIENT_REFERENCE_MANIFEST", "finalFiles", "Promise", "all", "keys", "map", "fileInfo", "relativeFile", "relative", "bundled", "push", "sources", "RawSource", "JSON", "stringify", "version", "TRACE_OUTPUT_VERSION", "tapfinishModules", "traceEntrypointsPluginSpan", "doResolve", "readlink", "stat", "hooks", "finishModules", "tapAsync", "_stats", "callback", "finishModulesSpan", "entryNameMap", "entryModMap", "additionalEntries", "depModMap", "entry", "entries", "normalizedName", "isPage", "isApp", "dependencies", "entryMod", "resource", "moduleBuildInfo", "getModuleBuildInfo", "route", "absolutePath", "getPageFilePath", "absolutePagePath", "request", "curMap", "readFile", "mod", "source", "originalSource", "buffer", "entryPaths", "Array", "from", "collectDependencies", "depMod", "entriesToTrace", "entryName", "curExtraEntries", "chunks", "entriesTrace", "depModArray", "ignores", "isIgnoreMatcher", "picomatch", "contains", "dot", "traceEntryCount", "result", "nodeFileTrace", "base", "resolve", "id", "job", "isCjs", "undefined", "ignore", "mixedModules", "esmFileList", "for<PERSON>ach", "isAsset", "isArray", "loaders", "normalizedEntry", "finalDeps", "info", "extraEntry", "normalizedExtraEntry", "then", "err", "apply", "compiler", "tap", "reject", "inputFileSystem", "link", "e", "isError", "code", "stats", "compilationSpan", "spans", "traceFn", "processAssets", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_SUMMARIZE", "catch", "resolver", "resolverFactory", "getPkgName", "segments", "split", "slice", "getResolve", "options", "curResolver", "withOptions", "context", "fileDependencies", "missingDependencies", "contextDependencies", "resContext", "Error", "requestPath", "isAbsolute", "descriptionFileRoot", "sep", "rootSeparatorIndex", "indexOf", "separatorIndex", "lastIndexOf", "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isFile", "emitFile", "realpath", "_err", "dependencyType", "CJS_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "fullySpecified", "modules", "extensions", "BASE_CJS_RESOLVE_OPTIONS", "alias", "ESM_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "BASE_ESM_RESOLVE_OPTIONS", "isEsmRequested", "res", "resolveExternal", "_", "resRequest"], "mappings": ";;;;;;;;;;;;;;;;;IA0BaA,aAAa;eAAbA;;IA0GAC,sBAAsB;eAAtBA;;IAjFGC,sBAAsB;eAAtBA;;IA6EAC,OAAO;eAAPA;;;6DAhIK;+DACF;iCAEG;gEACF;qBACU;2BAOvB;yBAC0B;+BAI1B;kEAEe;oCACa;yBACH;iCACA;iCACA;;;;;;AAEhC,MAAMC,cAAc;AACb,MAAMJ,gBAAgB;IAC3B;IACA;CACD;AAED,MAAMK,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,wBACPC,WAAgB,EAChBC,GAAQ;IAER,OAAOD,YAAYE,WAAW,CAACC,SAAS,CAACF;AAC3C;AAEO,SAASN,uBACdS,QAAqB,EACrBC,OAA6B,EAC7BC,QAAqD;IAErD,4DAA4D;IAC5D,8DAA8D;IAC9D,aAAa;IACb,MAAMC,iBAAiB,IAAIC;IAE3B,SAASC,mBACPC,OAAoB,EACpBC,IAAY,EACZC,OAAO,IAAIC,KAAa;QAExB,KAAK,MAAMC,UAAUJ,WAAW,EAAE,CAAE;YAClC,IAAI,CAACE,KAAKG,GAAG,CAACD,SAAS;gBACrBF,KAAKI,GAAG,CAACF;gBACT,IAAIG,cAAcV,eAAeW,GAAG,CAACJ;gBAErC,IAAI,CAACG,aAAa;oBAChBA,cAAc,IAAIT;oBAClBD,eAAeY,GAAG,CAACL,QAAQG;gBAC7B;gBACA,MAAMG,UAAUC,QAAQf,4BAAAA,SAAWK,MAAMG;gBACzCG,YAAYE,GAAG,CAACR,MAAM;oBAAES;gBAAQ;gBAEhC,MAAME,eAAejB,QAAQa,GAAG,CAACJ;gBAEjC,IAAIQ,gCAAAA,aAAcZ,OAAO,EAAE;oBACzBD,mBAAmBa,aAAaZ,OAAO,EAAEC,MAAMC;gBACjD;YACF;QACF;IACF;IAEA,KAAK,MAAMD,QAAQP,SAAW;QAC5B,MAAMmB,SAASlB,QAASa,GAAG,CAACP;QAC5B,MAAMa,YACJD,CAAAA,0BAAAA,OAAQE,IAAI,CAACC,MAAM,MAAK,KAAKH,OAAOE,IAAI,CAACE,QAAQ,CAAC;QAEpD,IACE,CAACJ,UACD,CAACA,OAAOb,OAAO,IACdc,aAAaD,OAAOb,OAAO,CAACkB,IAAI,KAAK,GACtC;YACA;QACF;QACAnB,mBAAmBc,OAAOb,OAAO,EAAEC;IACrC;IACA,OAAOJ;AACT;AA0BO,SAASX,QAAQiC,OAAwB;IAC9C,OAAOC,eAAM,CAACC,UAAU,CAAC,QAAQC,MAAM,CAACH,SAASI,MAAM,CAAC;AAC1D;AAEO,MAAMvC;IAmBXwC,YAAY,EACVC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,sBAAsB,EACtBC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,qBAAqB,EACrBC,eAAe,EAYhB,CAAE;aAxCIC,oBAAuC,CAAC;QAyC7C,IAAI,CAACV,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;QACd,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACS,WAAW,GAAG,IAAItC;QACvB,IAAI,CAACkC,YAAY,GAAGA;QACpB,IAAI,CAACF,aAAa,GAAGA;QACrB,IAAI,CAACC,YAAY,GAAGA,gBAAgB,EAAE;QACtC,IAAI,CAACM,WAAW,GAAGJ,yBAAyBR;QAC5C,IAAI,CAACI,sBAAsB,GAAGA;QAC9B,IAAI,CAACS,WAAW,GAAG,IAAIxC;QACvB,IAAI,CAAC8B,YAAY,GAAGA;QACpB,IAAI,CAACM,eAAe,GAAGA;IACzB;IAEA,2DAA2D;IAC3D,2BAA2B;IAC3B,MAAMK,kBACJjD,WAAgC,EAChCkD,MAAW,EACXC,IAAU,EACV;QACA,MAAMC,aAAapD,YAAYqD,aAAa,CAACC,IAAI,IAAI;QAErD,MAAMH,KAAKI,UAAU,CAAC,uBAAuBC,YAAY,CAAC;YACxD,MAAMC,gBAAgB,IAAIjD;YAC1B,MAAMkD,gBAAgB,IAAI7C;YAC1B,MAAM8C,oBAAoB,IAAInD;YAE9B,MAAMoD,cAAc,CAACjD,OACnB,CAACb,cAAc+D,IAAI,CAAC,CAACC;oBACnB,OAAOnD,KAAKoD,QAAQ,CAACD;gBACvB;YAEF,KAAK,MAAME,cAAchE,YAAYiE,WAAW,CAACC,MAAM,GAAI;gBACzD,MAAMC,aAAa,IAAItD;gBAEvB,KAAK,MAAMuD,SAASJ,WACjBK,kBAAkB,GAClBC,sBAAsB,GAAI;oBAC3B,KAAK,MAAM3D,QAAQyD,MAAMG,KAAK,CAAE;wBAC9B,IAAIX,YAAYjD,OAAO;4BACrB,MAAM6D,WAAWC,aAAQ,CAACC,IAAI,CAACtB,YAAYzC;4BAC3C+C,cAAc1C,GAAG,CAACwD;4BAClBL,WAAWnD,GAAG,CAACwD;wBACjB;oBACF;oBACA,KAAK,MAAM7D,QAAQyD,MAAMO,cAAc,CAAE;wBACvC,IAAIf,YAAYjD,OAAO;4BACrB,MAAM6D,WAAWC,aAAQ,CAACC,IAAI,CAACtB,YAAYzC;4BAC3C+C,cAAc1C,GAAG,CAACwD;4BAClBL,WAAWnD,GAAG,CAACwD;wBACjB;oBACF;gBACF;gBACAf,cAActC,GAAG,CAAC6C,YAAYG;gBAC9BR,kBAAkBxC,GAAG,CAAC6C,WAAWY,IAAI,IAAI,IAAI;uBAAIT;iBAAW;YAC9D;YAEA,kCAAkC;YAClC,IAAI,CAACtB,iBAAiB,CAACgC,WAAW,GAAG;gBACnCC,QAAQ;oBACNA,QAAQ;oBACRC,OAAO;2BAAIrB;qBAAc;oBACzBsB,kBAAkB,IAAI,CAACjC,WAAW;oBAClCkC,YAAY,IAAI,CAAC9C,OAAO;gBAC1B;gBACAiB;gBACAO,mBAAmBuB,OAAOC,WAAW,CAACxB;YACxC;YAEA,+DAA+D;YAC/D,sDAAsD;YACtD,MAAMyB,eAAe,IAAI,CAAC9C,YAAY,KAAK,WAAW,QAAQ;YAE9D,KAAK,MAAM,CAAC0B,YAAYG,WAAW,IAAIV,cAAe;oBAoC1C;gBAnCV,MAAM4B,kBAAkB,GAAGD,eAAepB,WAAWY,IAAI,CAAC,YAAY,CAAC;gBACvE,MAAMU,kBAAkBb,aAAQ,CAACc,OAAO,CACtCd,aAAQ,CAACC,IAAI,CAACtB,YAAYiC;gBAG5B,8CAA8C;gBAC9ClB,WAAWqB,MAAM,CACff,aAAQ,CAACC,IAAI,CAACtB,YAAY,GAAGgC,eAAepB,WAAWY,IAAI,CAAC,GAAG,CAAC;gBAGlE,IAAIZ,WAAWY,IAAI,CAACa,UAAU,CAAC,SAAS;oBACtC,sEAAsE;oBACtE,qCAAqC;oBACrC,MAAMC,+BAA+BC,IAAAA,gCAAe,EAAC3B,WAAWY,IAAI,IAChE,OACAH,aAAQ,CAACC,IAAI,CACXtB,YACAgC,cACApB,WAAWY,IAAI,CAACgB,OAAO,CAAC,QAAQ,OAC9B,MACAC,oCAAyB,GACzB;oBAGR,IAAIH,iCAAiC,MAAM;wBACzCvB,WAAWnD,GAAG,CAAC0E;oBACjB;gBACF;gBAEA,MAAMI,aAAuB,EAAE;gBAE/B,MAAMC,QAAQC,GAAG,CACf;uBACK,IAAInF,IAAI;2BACNsD;2BACC,EAAA,wBAAA,IAAI,CAACrB,WAAW,CAAC5B,GAAG,CAAC8C,WAAWY,IAAI,sBAApC,sBAAuCqB,IAAI,OAAM,EAAE;qBACxD;iBACF,CAACC,GAAG,CAAC,OAAOvF;wBACM;oBAAjB,MAAMwF,YAAW,wBAAA,IAAI,CAACrD,WAAW,CAAC5B,GAAG,CAAC8C,WAAWY,IAAI,sBAApC,sBAAuC1D,GAAG,CAACP;oBAE5D,MAAMyF,eAAe3B,aAAQ,CAC1B4B,QAAQ,CAACf,iBAAiB3E,MAC1BiF,OAAO,CAAC,OAAO;oBAElB,IAAIjF,MAAM;wBACR,IAAI,EAACwF,4BAAAA,SAAUG,OAAO,GAAE;4BACtBR,WAAWS,IAAI,CAACH;wBAClB;oBACF;gBACF;gBAGFlD,MAAM,CAACmC,gBAAgB,GAAG,IAAImB,gBAAO,CAACC,SAAS,CAC7CC,KAAKC,SAAS,CAAC;oBACbC,SAASC,+BAAoB;oBAC7BtC,OAAOuB;gBACT;YAEJ;QACF;IACF;IAEAgB,iBACE9G,WAAgC,EAChC+G,0BAAgC,EAChCC,SAKoB,EACpBC,QAAa,EACbC,IAAS,EACT;QACAlH,YAAYmH,KAAK,CAACC,aAAa,CAACC,QAAQ,CACtCxH,aACA,OAAOyH,QAAaC;YAClB,MAAMC,oBACJT,2BAA2BxD,UAAU,CAAC;YACxC,MAAMiE,kBACHhE,YAAY,CAAC;gBACZ,gDAAgD;gBAChD,mDAAmD;gBACnD,oCAAoC;gBACpC,MAAMiE,eAAe,IAAIjH;gBACzB,MAAMkH,cAAc,IAAIlH;gBACxB,MAAMmH,oBAAoB,IAAInH;gBAE9B,MAAMoH,YAAY,IAAIpH;gBAEtB,MAAMgH,kBACHjE,UAAU,CAAC,eACXC,YAAY,CAAC;oBACZ,KAAK,MAAM,CAACoB,MAAMiD,MAAM,IAAI7H,YAAY8H,OAAO,CAACA,OAAO,GAAI;wBACzD,MAAMC,iBAAiBnD,wBAAAA,KAAMgB,OAAO,CAAC,OAAO;wBAE5C,MAAMoC,SAASD,eAAetC,UAAU,CAAC;wBACzC,MAAMwC,QACJ,IAAI,CAACzF,aAAa,IAAIuF,eAAetC,UAAU,CAAC;wBAElD,IAAIwC,SAASD,QAAQ;4BACnB,KAAK,MAAM/H,OAAO4H,MAAMK,YAAY,CAAE;gCACpC,IAAI,CAACjI,KAAK;gCACV,MAAMkI,WAAWpI,wBAAwBC,aAAaC;gCAEtD,2DAA2D;gCAC3D,yCAAyC;gCACzC,IAAIkI,YAAYA,SAASC,QAAQ,KAAK,IAAI;oCACxC,MAAMC,kBAAkBC,IAAAA,sCAAkB,EAACH;oCAC3C,wFAAwF;oCACxF,IAAIE,gBAAgBE,KAAK,EAAE;wCACzB,MAAMC,eAAeC,IAAAA,wBAAe,EAAC;4CACnCC,kBACEL,gBAAgBE,KAAK,CAACG,gBAAgB;4CACxCvG,SAAS,IAAI,CAACA,OAAO;4CACrBC,QAAQ,IAAI,CAACA,MAAM;4CACnBC,UAAU,IAAI,CAACA,QAAQ;wCACzB;wCAEA,qCAAqC;wCACrC,IACE,AAAC,IAAI,CAACA,QAAQ,IACZmG,aAAa/C,UAAU,CAAC,IAAI,CAACpD,QAAQ,KACtC,IAAI,CAACD,MAAM,IACVoG,aAAa/C,UAAU,CAAC,IAAI,CAACrD,MAAM,GACrC;4CACAsF,YAAYvG,GAAG,CAACqH,cAAcL;4CAC9BV,aAAatG,GAAG,CAACqH,cAAc5D;wCACjC;oCACF;oCAEA,wFAAwF;oCACxF,oEAAoE;oCACpE,IAAIuD,SAASQ,OAAO,EAAE;wCACpB,IAAIC,SAASjB,kBAAkBzG,GAAG,CAAC0D;wCAEnC,IAAI,CAACgE,QAAQ;4CACXA,SAAS,IAAIpI;4CACbmH,kBAAkBxG,GAAG,CAACyD,MAAMgE;wCAC9B;wCACAhB,UAAUzG,GAAG,CAACgH,SAASQ,OAAO,EAAER;wCAChCS,OAAOzH,GAAG,CAACgH,SAASC,QAAQ,EAAED;oCAChC;gCACF;gCAEA,IAAIA,YAAYA,SAASC,QAAQ,EAAE;oCACjCX,aAAatG,GAAG,CAACgH,SAASC,QAAQ,EAAExD;oCACpC8C,YAAYvG,GAAG,CAACgH,SAASC,QAAQ,EAAED;oCAEnC,IAAIS,SAASjB,kBAAkBzG,GAAG,CAAC0D;oCAEnC,IAAI,CAACgE,QAAQ;wCACXA,SAAS,IAAIpI;wCACbmH,kBAAkBxG,GAAG,CAACyD,MAAMgE;oCAC9B;oCACAhB,UAAUzG,GAAG,CAACgH,SAASC,QAAQ,EAAED;oCACjCS,OAAOzH,GAAG,CAACgH,SAASC,QAAQ,EAAED;gCAChC;4BACF;wBACF;oBACF;gBACF;gBAEF,MAAMU,WAAW,OACfvF;wBAM8BwF,qBAAAA;oBAJ9B,MAAMA,MAAMlB,UAAU1G,GAAG,CAACoC,SAASoE,YAAYxG,GAAG,CAACoC;oBAEnD,oDAAoD;oBACpD,kCAAkC;oBAClC,IAAIyF,SAA0BD,wBAAAA,uBAAAA,IAAKE,cAAc,sBAAnBF,sBAAAA,0BAAAA,yBAAAA,oBAAyBG,MAAM;oBAC7D,OAAOF,UAAU;gBACnB;gBAEA,MAAMG,aAAaC,MAAMC,IAAI,CAAC1B,YAAYzB,IAAI;gBAE9C,MAAMoD,sBAAsB,OAAOP,KAAUhI;oBAC3C,IAAI,CAACgI,OAAO,CAACA,IAAIZ,YAAY,EAAE;oBAE/B,KAAK,MAAMjI,OAAO6I,IAAIZ,YAAY,CAAE;wBAClC,MAAMoB,SAASvJ,wBAAwBC,aAAaC;wBAEpD,IAAIqJ,CAAAA,0BAAAA,OAAQlB,QAAQ,KAAI,CAACR,UAAU1G,GAAG,CAACoI,OAAOlB,QAAQ,GAAG;4BACvDR,UAAUzG,GAAG,CAACmI,OAAOlB,QAAQ,EAAEkB;4BAC/B,MAAMD,oBAAoBC,QAAQxI;wBACpC;oBACF;gBACF;gBACA,MAAMyI,iBAAiB;uBAAIL;iBAAW;gBAEtC,KAAK,MAAMrB,SAASqB,WAAY;oBAC9B,MAAMG,oBAAoB3B,YAAYxG,GAAG,CAAC2G,QAAQA;oBAClD,MAAM2B,YAAY/B,aAAavG,GAAG,CAAC2G;oBACnC,MAAM4B,kBAAkB9B,kBAAkBzG,GAAG,CAACsI;oBAE9C,IAAIC,iBAAiB;wBACnBF,eAAehD,IAAI,IAAIkD,gBAAgBxD,IAAI;oBAC7C;gBACF;gBAEA,MAAMjB,mBAAmB,IAAI,CAACjC,WAAW;gBACzC,MAAM2G,SAAS;uBAAIH;iBAAe;gBAElC,IAAI,CAAC1G,iBAAiB,CAAC8G,YAAY,GAAG;oBACpC7E,QAAQ;wBACNA,QAAQ;wBACRC,OAAO2E;wBACP1E;wBACAC,YAAY,IAAI,CAAC9C,OAAO;oBAC1B;oBACAC,QAAQ,IAAI,CAACD,OAAO;oBACpByH,aAAaT,MAAMC,IAAI,CAACxB,UAAU3B,IAAI;oBACtCwB,cAAcvC,OAAOC,WAAW,CAACsC;oBACjCrE,YAAYpD,YAAYqD,aAAa,CAACC,IAAI;gBAC5C;gBAEA,IAAIlD;gBACJ,IAAIC;gBACJ,MAAMwJ,UAAU;uBACXpK;uBACA,IAAI,CAACgD,YAAY;oBACpB;iBACD;gBAED,2EAA2E;gBAC3E,MAAMqH,kBAAkBC,IAAAA,kBAAS,EAACF,SAAS;oBACzCG,UAAU;oBACVC,KAAK;gBACP;gBACA,MAAM3J,WAAW,CAACgD;oBAChB,OAAOwG,gBAAgBxG;gBACzB;gBAEA,MAAMkE,kBACHjE,UAAU,CAAC,0BAA0B;oBACpC2G,iBAAiBX,eAAe7H,MAAM,GAAG;gBAC3C,GACC8B,YAAY,CAAC;oBACZ,MAAM2G,SAAS,MAAMC,IAAAA,kBAAa,EAACb,gBAAgB;wBACjDc,MAAM,IAAI,CAACtH,WAAW;wBACtBkC,YAAY,IAAI,CAAC9C,OAAO;wBACxB0G;wBACA5B;wBACAC;wBACAoD,SAAStD,YACL,OAAOuD,IAAIzJ,QAAQ0J,KAAKC;4BACtB,OAAOzD,UAAUuD,IAAIzJ,QAAQ0J,KAAK,CAACC;wBACrC,IACAC;wBACJC,QAAQrK;wBACRsK,cAAc;oBAChB;oBACA,aAAa;oBACbxK,WAAW+J,OAAO/J,QAAQ;oBAC1B+J,OAAOU,WAAW,CAACC,OAAO,CAAC,CAACnK,OAASP,SAASY,GAAG,CAACL;oBAClDN,UAAU8J,OAAO9J,OAAO;gBAC1B;gBAEF,MAAMmH,kBACHjE,UAAU,CAAC,wBACXC,YAAY,CAAC;oBACZ,MAAMjD,iBAAiBZ,uBACrBS,UACAC,SACA,CAACM;4BAMiBN;wBALhB,iDAAiD;wBACjD,wCAAwC;wBACxC,oCAAoC;wBACpCM,OAAO8D,aAAQ,CAACC,IAAI,CAAC,IAAI,CAAC3B,WAAW,EAAEpC;wBACvC,MAAM2I,SAAS1B,UAAU1G,GAAG,CAACP;wBAC7B,MAAMoK,WAAU1K,eAAAA,QACba,GAAG,CAACuD,aAAQ,CAAC4B,QAAQ,CAAC,IAAI,CAACtD,WAAW,EAAEpC,2BAD3BN,aAEZoB,IAAI,CAACE,QAAQ,CAAC;wBAElB,OACE,CAACoJ,WACD5B,MAAM6B,OAAO,CAAC1B,0BAAAA,OAAQ2B,OAAO,KAC7B3B,OAAO2B,OAAO,CAACvJ,MAAM,GAAG;oBAE5B;oBAGF,KAAK,MAAMmG,SAASqB,WAAY;4BAeJ3I;wBAd1B,MAAMiJ,YAAY/B,aAAavG,GAAG,CAAC2G;wBACnC,MAAMqD,kBAAkBzG,aAAQ,CAAC4B,QAAQ,CACvC,IAAI,CAACtD,WAAW,EAChB8E;wBAEF,MAAM4B,kBAAkB9B,kBAAkBzG,GAAG,CAACsI;wBAC9C,MAAM2B,YAAY,IAAI3K;wBAEtB,kDAAkD;wBAClD,kBAAkB;wBAClB2K,UAAUhK,GAAG,CAAC0G,OAAO;4BACnBvB,SAAS;wBACX;wBAEA,KAAK,MAAM,CAACrG,KAAKmL,KAAK,IAAI7K,EAAAA,sBAAAA,eACvBW,GAAG,CAACgK,qCADmB3K,oBAEtBuH,OAAO,OAAM,EAAE,CAAE;4BACnBqD,UAAUhK,GAAG,CAACsD,aAAQ,CAACC,IAAI,CAAC,IAAI,CAAC3B,WAAW,EAAE9C,MAAM;gCAClDqG,SAAS8E,KAAKhK,OAAO;4BACvB;wBACF;wBAEA,IAAIqI,iBAAiB;4BACnB,KAAK,MAAM4B,cAAc5B,gBAAgBxD,IAAI,GAAI;oCAOrB1F;gCAN1B,MAAM+K,uBAAuB7G,aAAQ,CAAC4B,QAAQ,CAC5C,IAAI,CAACtD,WAAW,EAChBsI;gCAEFF,UAAUhK,GAAG,CAACkK,YAAY;oCAAE/E,SAAS;gCAAM;gCAE3C,KAAK,MAAM,CAACrG,KAAKmL,KAAK,IAAI7K,EAAAA,uBAAAA,eACvBW,GAAG,CAACoK,0CADmB/K,qBAEtBuH,OAAO,OAAM,EAAE,CAAE;oCACnBqD,UAAUhK,GAAG,CAACsD,aAAQ,CAACC,IAAI,CAAC,IAAI,CAAC3B,WAAW,EAAE9C,MAAM;wCAClDqG,SAAS8E,KAAKhK,OAAO;oCACvB;gCACF;4BACF;wBACF;wBACA,IAAI,CAAC0B,WAAW,CAAC3B,GAAG,CAACqI,WAAW2B;oBAClC;gBACF;YACJ,GACCI,IAAI,CACH,IAAMhE,YACN,CAACiE,MAAQjE,SAASiE;QAExB;IAEJ;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASvE,KAAK,CAACnH,WAAW,CAAC2L,GAAG,CAAC9L,aAAa,CAACG;YAC3C,MAAMiH,WAAW,OAAO3D;gBACtB,IAAI;oBACF,OAAO,MAAM,IAAIyC,QAAQ,CAACuE,SAASsB;;wBAE/B5L,YAAY6L,eAAe,CACxB5E,QAAQ,CACX3D,MAAM,CAACkI,KAAKM;4BACZ,IAAIN,KAAK,OAAOI,OAAOJ;4BACvBlB,QAAQwB;wBACV;oBACF;gBACF,EAAE,OAAOC,GAAG;oBACV,IACEC,IAAAA,gBAAO,EAACD,MACPA,CAAAA,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,SAAQ,GAClE;wBACA,OAAO;oBACT;oBACA,MAAMF;gBACR;YACF;YACA,MAAM7E,OAAO,OAAO5D;gBAClB,IAAI;oBACF,OAAO,MAAM,IAAIyC,QAAQ,CAACuE,SAASsB;;wBAC/B5L,YAAY6L,eAAe,CAAC3E,IAAI,CAChC5D,MACA,CAACkI,KAAKU;4BACJ,IAAIV,KAAK,OAAOI,OAAOJ;4BACvBlB,QAAQ4B;wBACV;oBAEJ;gBACF,EAAE,OAAOH,GAAG;oBACV,IAAIC,IAAAA,gBAAO,EAACD,MAAOA,CAAAA,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,SAAQ,GAAI;wBAC/D,OAAO;oBACT;oBACA,MAAMF;gBACR;YACF;YAEA,MAAMI,kBAAkBC,sBAAK,CAAClL,GAAG,CAAClB,gBAAgBoM,sBAAK,CAAClL,GAAG,CAACwK;YAC5D,MAAM3E,6BAA6BoF,gBAAgB5I,UAAU,CAC3D;YAEFwD,2BAA2BsF,OAAO,CAAC;gBACjCrM,YAAYmH,KAAK,CAACmF,aAAa,CAACjF,QAAQ,CACtC;oBACEzC,MAAM/E;oBACN0M,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;gBAC3D,GACA,CAACxJ,QAAaqE;oBACZ,IAAI,CAACtE,iBAAiB,CACpBjD,aACAkD,QACA6D,4BAECwE,IAAI,CAAC,IAAMhE,YACXoF,KAAK,CAAC,CAACnB,MAAQjE,SAASiE;gBAC7B;gBAGF,IAAIoB,WAAW5M,YAAY6M,eAAe,CAAC3L,GAAG,CAAC;gBAE/C,SAAS4L,WAAWlI,IAAY;oBAC9B,MAAMmI,WAAWnI,KAAKoI,KAAK,CAAC;oBAC5B,IAAIpI,IAAI,CAAC,EAAE,KAAK,OAAOmI,SAASrL,MAAM,GAAG,GACvC,OAAOqL,SAASrL,MAAM,GAAG,IAAIqL,SAASE,KAAK,CAAC,GAAG,GAAGvI,IAAI,CAAC,OAAO;oBAChE,OAAOqI,SAASrL,MAAM,GAAGqL,QAAQ,CAAC,EAAE,GAAG;gBACzC;gBAEA,MAAMG,aAAa,CACjBC;oBAEA,MAAMC,cAAcR,SAASS,WAAW,CAACF;oBAEzC,OAAO,CACLrM,QACA6H,SACA6B,MAEA,IAAIzE,QAA2B,CAACuE,SAASsB;4BACvC,MAAM0B,UAAU7I,aAAQ,CAACc,OAAO,CAACzE;4BAEjCsM,YAAY9C,OAAO,CACjB,CAAC,GACDgD,SACA3E,SACA;gCACE4E,kBAAkBvN,YAAYuN,gBAAgB;gCAC9CC,qBAAqBxN,YAAYwN,mBAAmB;gCACpDC,qBAAqBzN,YAAYyN,mBAAmB;4BACtD,GACA,OAAOjC,KAAUrB,QAASuD;gCACxB,IAAIlC,KAAK,OAAOI,OAAOJ;gCAEvB,IAAI,CAACrB,QAAQ;oCACX,OAAOyB,OAAO,IAAI+B,MAAM;gCAC1B;gCAEA,mDAAmD;gCACnD,sCAAsC;gCACtC,IAAIxD,OAAOxI,QAAQ,CAAC,QAAQwI,OAAOxI,QAAQ,CAAC,MAAM;oCAChDwI,SAASuD,CAAAA,8BAAAA,WAAYpK,IAAI,KAAI6G;gCAC/B;gCAEA,IAAI;oCACF,oDAAoD;oCACpD,sDAAsD;oCACtD,yDAAyD;oCACzD,sDAAsD;oCACtD,IAAIA,OAAOxI,QAAQ,CAAC,iBAAiB;wCACnC,IAAIiM,cAAczD,OACfvE,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCAElB,IACE,CAACnB,aAAQ,CAACoJ,UAAU,CAAClF,YACrBA,QAAQhH,QAAQ,CAAC,SACjB+L,8BAAAA,WAAYI,mBAAmB,GAC/B;gDAGgBhB;4CAFhBc,cAAc,AACZF,CAAAA,WAAWI,mBAAmB,GAC9BnF,QAAQsE,KAAK,CAACH,EAAAA,cAAAA,WAAWnE,6BAAXmE,YAAqBpL,MAAM,KAAI,KAC7C+C,aAAQ,CAACsJ,GAAG,GACZ,cAAa,EAEZnI,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCACpB;wCAEA,MAAMoI,qBAAqBJ,YAAYK,OAAO,CAAC;wCAC/C,IAAIC;wCACJ,MACE,AAACA,CAAAA,iBAAiBN,YAAYO,WAAW,CAAC,IAAG,IAC7CH,mBACA;4CACAJ,cAAcA,YAAYX,KAAK,CAAC,GAAGiB;4CACnC,MAAME,qBAAqB,GAAGR,YAAY,aAAa,CAAC;4CACxD,IAAI,MAAMpD,IAAI6D,MAAM,CAACD,qBAAqB;gDACxC,MAAM5D,IAAI8D,QAAQ,CAChB,MAAM9D,IAAI+D,QAAQ,CAACH,qBACnB,WACAtN;4CAEJ;wCACF;oCACF;gCACF,EAAE,OAAO0N,MAAM;gCACb,kDAAkD;gCAClD,sDAAsD;gCACxD;gCACAlE,QAAQ;oCAACH;oCAAQgD,QAAQsB,cAAc,KAAK;iCAAM;4BACpD;wBAEJ;gBACJ;gBAEA,MAAMC,sBAAsB;oBAC1B,GAAGC,mCAAoB;oBACvBC,gBAAgBlE;oBAChBmE,SAASnE;oBACToE,YAAYpE;gBACd;gBACA,MAAMqE,2BAA2B;oBAC/B,GAAGL,mBAAmB;oBACtBM,OAAO;gBACT;gBACA,MAAMC,sBAAsB;oBAC1B,GAAGC,uCAAwB;oBAC3BN,gBAAgBlE;oBAChBmE,SAASnE;oBACToE,YAAYpE;gBACd;gBACA,MAAMyE,2BAA2B;oBAC/B,GAAGF,mBAAmB;oBACtBD,OAAO;gBACT;gBAEA,MAAMhI,YAAY,OAChB2B,SACA7H,QACA0J,KACA4E;oBAEA,MAAM9B,UAAU7I,aAAQ,CAACc,OAAO,CAACzE;oBACjC,gEAAgE;oBAChE,yBAAyB;oBACzB,MAAM,EAAEuO,GAAG,EAAE,GAAG,MAAMC,IAAAA,gCAAe,EACnC,IAAI,CAACnN,OAAO,EACZ,IAAI,CAACO,YAAY,EACjB4K,SACA3E,SACAyG,gBACA,IAAI,CAAC7M,sBAAsB,EAC3B,CAAC4K,UAAY,CAACoC,GAAWC;4BACvB,OAAOtC,WAAWC,SAASrM,QAAQ0O,YAAYhF;wBACjD,GACAE,WACAA,WACAuE,qBACAP,qBACAS,0BACAJ;oBAGF,IAAI,CAACM,KAAK;wBACR,MAAM,IAAI1B,MAAM,CAAC,kBAAkB,EAAEhF,QAAQ,MAAM,EAAE7H,QAAQ;oBAC/D;oBACA,OAAOuO,IAAIzJ,OAAO,CAAC,OAAO;gBAC5B;gBAEA,IAAI,CAACkB,gBAAgB,CACnB9G,aACA+G,4BACAC,WACAC,UACAC;YAEJ;QACF;IACF;AACF"}