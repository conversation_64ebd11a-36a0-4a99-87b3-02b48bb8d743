{"version": 3, "file": "app/api/emails/[address]/credentials/route.js", "mappings": "mFAAA,6DCAA,mHGAA,wSFOO,IAAMA,EAAU,OAEhB,eAAeC,EACpBC,CAAgB,CAChB,QAAEC,CAAM,CAAmC,EAE3C,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAC1B,GAAI,CAACD,GAASE,MAAMC,GAClB,CADsB,MACfC,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEC,MAAO,cAAe,EAAG,CAAEC,OAAQ,GAAI,GAGpE,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACbC,EAAeC,mBAAmBZ,EAAOa,OAAO,EAEtD,GAAI,CACF,IAAMC,EAAQ,MAAML,EAAGM,KAAK,CAACC,MAAM,CAACC,SAAS,CAAC,CAC5CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACH,OAAO,CAAEF,GACnBS,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACK,MAAM,CAAEpB,EAAQE,IAAI,CAACC,EAAE,EAErC,GAEA,GAAI,CAACU,EACH,KADU,EACHT,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,kCAAmC,EAC5C,CAAEC,OAAQ,GAAI,GAIlB,GAAM,WAAEc,EAAY,IAAI,CAAE,CAAI,MAAMvB,EAAQO,IAAI,GAAGiB,KAAK,CAAC,IAAO,GAAC,GAI3DC,EAAYF,MAAkB,EAAIA,MAElCG,EAAa,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAyBA,CAACZ,EAAMD,OAAO,CAAES,GAE5DK,MACEC,KAD4B,IAChC,EAAS,2BACAA,GAAT,EAAcC,EAAVD,CAAa,GAAKJ,GAE1B,OAAOnB,EAAAA,EAAYA,CAACC,IAAI,CAAC,YACvBmB,EACAD,UAAWG,EAAcG,WAAW,EACtC,EACF,CAAE,MAAOvB,EAAO,CAEd,OADAwB,QAAQxB,KAAK,CAAC,iCAAkCA,GACzCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,+BAAgC,EACzC,CAAEC,OAAQ,GAAI,EAElB,CACF,CCpDA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,+CACA,6CACA,iBACA,uDACA,CAAK,CACL,+GACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,wFACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,+CACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,uIAiBnxJ,CAAC,CAAC,EAAC", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/emails/[address]/credentials/route.ts", "webpack://_N_E/./app/api/emails/[address]/credentials/route.ts?c601", "webpack://_N_E/?c5e8"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { NextResponse } from \"next/server\";\r\nimport { auth } from \"@/lib/auth\";\r\nimport { createDb } from \"@/lib/db\";\r\nimport { emails } from \"@/lib/schema\";\r\nimport { eq, and } from \"drizzle-orm\";\r\nimport { generateAddressCredential } from \"@/lib/jwt\";\r\n\r\nexport const runtime = \"edge\";\r\n\r\nexport async function POST(\r\n  request: Request,\r\n  { params }: { params: { address: string } }\r\n) {\r\n  const session = await auth();\r\n  if (!session?.user?.id) {\r\n    return NextResponse.json({ error: \"Unauthorized\" }, { status: 401 });\r\n  }\r\n\r\n  const db = createDb();\r\n  const emailAddress = decodeURIComponent(params.address);\r\n\r\n  try {\r\n    const email = await db.query.emails.findFirst({\r\n      where: and(\r\n        eq(emails.address, emailAddress),\r\n        eq(emails.userId, session.user.id)\r\n      ),\r\n    });\r\n\r\n    if (!email) {\r\n      return NextResponse.json(\r\n        { error: \"Email not found or access denied\" },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    const { expiresIn = 3600 } = (await request.json().catch(() => ({}))) as {\r\n      expiresIn?: number;\r\n    };\r\n\r\n    const expiresAt = expiresIn === 0 ? 0 : expiresIn * 1000;\r\n    \r\n    const credential = await generateAddressCredential(email.address, expiresIn);\r\n\r\n    const expiresAtDate = expiresIn === 0 \r\n      ? new Date('9999-01-01T00:00:00.000Z')\r\n      : new Date(Date.now() + expiresAt);\r\n\r\n    return NextResponse.json({\r\n      credential,\r\n      expiresAt: expiresAtDate.toISOString(),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Failed to generate credential:\", error);\r\n    return NextResponse.json(\r\n      { error: \"Failed to generate credential\" },\r\n      { status: 500 }\r\n    );\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\emails\\\\[address]\\\\credentials\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/emails/[address]/credentials/route\",\n        pathname: \"/api/emails/[address]/credentials\",\n        filename: \"route\",\n        bundlePath: \"app/api/emails/[address]/credentials/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\emails\\\\[address]\\\\credentials\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Femails%2F%5Baddress%5D%2Fcredentials%2Froute&page=%2Fapi%2Femails%2F%5Baddress%5D%2Fcredentials%2Froute&pagePath=private-next-app-dir%2Fapi%2Femails%2F%5Baddress%5D%2Fcredentials%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fapi%2Femails%2F%5Baddress%5D%2Fcredentials%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/emails/[address]/credentials/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/emails/[address]/credentials/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/emails/[address]/credentials/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map"], "names": ["runtime", "POST", "request", "params", "session", "auth", "user", "id", "NextResponse", "json", "error", "status", "db", "createDb", "emailAddress", "decodeURIComponent", "address", "email", "query", "emails", "<PERSON><PERSON><PERSON><PERSON>", "where", "and", "eq", "userId", "expiresIn", "catch", "expiresAt", "credential", "generateAddressCredential", "expiresAtDate", "Date", "now", "toISOString", "console"], "sourceRoot": "", "ignoreList": []}