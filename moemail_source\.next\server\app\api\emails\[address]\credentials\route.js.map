{"version": 3, "file": "app/api/emails/[address]/credentials/route.js", "mappings": "mFAAA,6DCAA,kEkBIA,yDUJA,sS3BAO,sBACM,EAAO,gBEmBb,EFnBa,OEmBb,KACP,cAIA,CAHA,oBACA,GAAoB,EAAO,WAE3B,+BACA,YAAoC,oCAA0C,EAEnE,CD5BJ,WC4BgB,CD3BvB,iCACA,oBAGA,SACA,YAAoB,WAAkB,GAFtC,MAGA,qDAHA,SAKA,uBACA,GCkBuB,yDACvB,CC7BO,sBACP,+BACA,oCACA,KACA,WACA,gCACA,gDACA,CACA,CA+BO,kBACP,qCACA,8BAaO,kBACP,6BACA,wBAEO,MAAM,UAAU,EACvB,KADuB,EACvB,uBACA,uBAiBO,kBACP,8BACA,uCACA,sCACA,yEACA,UACA,CACA,CCnFA,MAAe,QACf,aAAwB,YAAc,EACtC,UACA,YACA,YACA,YACA,YAAqB,cACrB,aACA,YACA,YACA,YAAqB,wDACrB,aACA,YACA,YACA,OAAqB,gCACrB,aACA,YACA,YACA,YAAqB,uCACrB,eACA,YACA,OAAqB,eACrB,SACA,UAAsB,EAAgB,OAAQ,GAAK,IAAb,sDAAa,EACnD,CACA,CAAC,CC1BD,CD0BE,CC1Ba,QACf,2CACA,IAAgB,iBAAgB,YAChC,8BACA,mBAAmC,GAAK,sDAExC,CACA,CAAC,CCPD,CDOE,QCPF,wBACA,mEAA2E,GAAM,UAAU,EAAK,EAChG,CACA,gBACA,iBACA,CACA,cACA,mCACA,CCRA,qBAEA,IADA,qBACA,UACA,cACA,kBAA8B,aAAiB,OAAO,EAAK,QAE3D,aACA,kBAA8B,MAAU,KAAK,KAAS,GAGtD,cAA0B,KAAS,GAanC,OAXA,QACA,gBAA4B,EAAO,EAEnC,6BACA,yBAAqC,OAAY,EAEjD,6BACA,qBACA,gCAA+C,oBAAwB,EAGvE,CACA,CACA,MAAe,UACf,yBAEO,qBACP,wBAAkC,GAAK,2BACvC,CC5BA,MAAe,eACf,4BACA,uBACA,gBAAgC,EAAe,2CAE/C,wCAAqD,YAAa,YAAc,cAAiB,QACjG,CAEA,OFgBO,gBACP,CElBqB,MFkBrB,GACA,YACA,YACA,aACA,0BACA,gBACA,8BAEA,GADA,sBACA,EACA,eAAsC,EAAS,oBAC/C,KACA,CACA,YACA,YACA,aACA,uCACA,6BACA,8BAEA,GADA,sBACA,EACA,eAAsC,EAAS,oBAC/C,KACA,CACA,YACA,YACA,aACA,6BACA,mBACA,8BAEA,KADA,oBACA,EACA,eAAsC,EAAS,oBAC/C,KACA,CACA,cACA,YACA,6BACA,mBACA,KAEA,aACA,YACA,aACA,2BACA,iBACA,MA/DA,YACA,UACA,YACA,aACA,aACA,aACA,aACA,aACA,SACA,0BACA,CACA,EAoDA,GAEA,KADA,uBACA,EACA,kCACA,KACA,CACA,QACA,4DACA,CACA,CA5DA,cACA,4BACA,sFAAkG,EAAM,IAExG,EAwDA,IACA,EEzEqB,OACrB,CACA,CAAC,CCRD,CDQE,CCRa,eACf,YAA4B,EAAU,YAGtC,KAHsC,EAClC,EAAc,KAElB,SAFkB,MAClB,yBAA+C,EAAe,oBAE9D,CAAC,CCRD,CDQE,CCRa,aAKf,EAJA,wBACA,8BACA,SAGA,gBACA,qBACA,mBACA,aACA,QACA,CACA,gBACA,YACA,SAEA,QACA,CACA,CACA,QACA,CAAC,CCfM,CDeL,QCfK,KACP,4CACA,CACO,cACP,4CACA,CACA,MAAe,GACf,WCTA,EAAe,IACf,IAJA,YACA,kCACA,EAEA,0DACA,SAEA,mCACA,SAEA,QACA,sCACA,2BAEA,mCACA,CAAC,CCdM,CDcL,QCdK,KACP,OAAW,EAAQ,2BCCnB,iCACA,YACA,mBACA,MACA,UACA,WACA,aACA,QACA,KACA,eACA,cACA,OAEA,CACA,aACA,sEAAsF,EAAS,gBAE/F,CACA,6BACA,sEAAkF,EAAI,iBAEtF,6BACA,MACA,WACA,6BACA,cACA,0BACA,IACA,KACA,4BACA,eACA,KACA,WAAuB,EAAE,0BAEzB,EADA,qCACA,oCAGA,EAEA,KACA,wCACA,YACA,KACA,mBACA,8CAEA,CACA,oCACA,+EAA+F,EAAc,gBAE7G,CACA,QACA,EACA,YACA,+BAEA,GAAQ,EAAS,IACjB,GDlDO,WCkDoB,CDjD3B,2CCiD2B,aAC3B,MACA,2IACA,CACA,IAAS,EAAS,GAClB,MADkB,UACU,EAAe,0DAE3C,qBACA,mBAA+B,MAAU,8DAEzC,EACA,YACA,GAAQ,EAAS,UACjB,GACA,cACA,WACA,GDxEO,YACP,2CCuEoC,aACpC,MACA,oEACA,eACA,aACA,GAAoB,SD1Eb,EC0E4B,CDzEnC,mCCyEmC,aACnC,MACA,mEACA,CAEA,IAAS,EAAS,GAClB,MADkB,UACU,EAAe,6CAE3C,qBACA,mBAA+B,MAAU,mEAEzC,qBACA,UACA,WACA,mBAAuC,MAAU,sEACjD,eACA,mBAAuC,MAAU,yEAGjD,CAEA,sBACA,UACA,aACA,mBAAuC,MAAU,uEACjD,eACA,mBAAuC,MAAU,wEAGjD,CAEA,EACA,EAAe,UACf,oBACA,WACA,uBACA,6CACA,kDAEA,SAGA,QAEA,CAAC,CC5HD,CD4HE,CC5Ha,kBAYf,EAXA,qCACA,8EAEA,uBACA,eAEA,2BACA,mBACA,iDACA,qGASA,aALA,EADA,WACA,+CAGA,EAEA,SACA,aACA,UAAsB,EAAgB,+BAAgC,EAAU,sBAEhF,iBACA,2CAAyD,EAAU,eAEnE,2BACA,2CAAyD,EAAU,+BAEnE,CACA,sBACA,CAAC,CCoDD,CDpDE,CCoDa,UACf,UACA,4EAEA,cAAY,eAvFZ,YACA,MACA,EACA,cACA,UACA,cACA,YACA,YACA,YACA,GAAkC,2BAA8B,gBAAkB,GAClF,0BACA,KACA,aACA,YACA,YACA,GAAkC,qCAAwC,gBAAkB,GAC5F,0BACA,KACA,gBACA,mBACA,mBACA,mBACA,GACA,gBACA,YAAqC,gCAAqC,GAE1E,oDACA,KACA,SACA,UAA8B,EAAgB,+DAC9C,CACA,KAEA,UACA,cACA,YACA,GAAkC,iCAClC,0BACA,KACA,aACA,GAAkC,iCAClC,0BACA,KACA,aACA,GAAkC,iCAClC,0BACA,KACA,eACA,qBACA,qBACA,qBACA,GAAkC,8BAClC,wBACA,KACA,SACA,UAA8B,EAAgB,+DAC9C,CACA,KAEA,WACA,cACA,cACA,YACA,GAAkC,gBAClC,0BACA,KACA,eACA,qBACA,qBACA,qBACA,GAAkC,YAClC,wBACA,KACA,SACA,UAA8B,EAAgB,+DAC9C,CACA,KAEA,SACA,UAAsB,EAAgB,8DACtC,CACA,iBAAa,cACb,EAKmC,GACnC,GAAsB,MAGtB,OAFA,aACA,aACA,2DACA,CAAC,CCxFD,CDwFE,CCxFF,oBAEA,MADA,kBACA,OACA,UACA,YAEA,YAA4B,EAAS,CAAG,OAAH,EAAG,EAAa,EASrD,OARA,GACA,iBACA,EAIA,OAHA,SAAyB,MAAkB,EAK3C,CACA,EACA,cAQA,EANA,OADA,iBACA,OACA,UACA,YAEA,wBACA,MAEA,mCACA,UACA,cACA,qBACA,qBACA,qBACA,KACA,SACA,6EACA,CACA,0DACA,CACA,oCACA,8BACA,8EAEA,uCACA,kBACA,CACA,CACA,gCACA,MACA,UACA,eACA,UACA,KACA,aACA,YACA,mBACA,YACA,KACA,aACA,YACA,mBACA,YACA,KACA,aACA,YACA,mBACA,YACA,KACA,SACA,6EACA,CACA,4BACA,sBACA,gBACA,MACA,CAAa,8BAEb,iBACA,sDACA,MACA,CAAS,uBACT,CACA,+BAMA,MALA,SACA,uBACA,sBACA,sBACA,EACA,wCACA,MACA,6EAEA,2BACA,kBACA,aACA,YACA,CAAa,yBAEb,0BACA,kBACA,aACA,YACA,CAAa,yBAEb,0BACA,kBACA,aACA,YACA,CAAa,yBAEb,yBACA,kBACA,YACA,YACA,CAAa,wBAEb,CACA,MACA,8EAQA,OANA,EAIA,OAHA,SAA+B,MAAkB,EAKjD,CACA,EACA,EAAe,aACf,4BAGQ,EAAW,GAFnB,MAEmB,CAFnB,EAKA,GAAQ,EAAW,IACnB,KADmB,SACnB,OACA,kBAEA,uDACA,IACA,aACA,CACA,SACA,0BACA,OAEA,CAEA,gBAA+B,aAAe,EAC9C,eACA,CACA,GAAQ,EAAK,UACb,If3JO,Me4JkB,Gf5JlB,GACP,yBACA,kDAAyE,EAAO,WAChF,OADgF,EAChF,WACA,CAAS,EAET,QACA,yBACA,GAAkB,EAAO,WAEzB,OAFyB,IAEzB,+CACA,IACA,ODHO,YACP,yBACA,gCAEA,cACA,2BACA,YAAoB,WAAmB,IACvC,qBAEA,QACA,ECP2B,EAC3B,CACA,MACA,oEACA,CACA,Ee2IyB,KAEzB,WAEA,2BACA,CAAC,EAAC,KC3JK,EACP,IACA,GACA,cACA,GACA,8BACA,4DAEA,UACA,CACA,sBACA,WACA,8DAGA,OADA,UACA,IACA,CACA,wBACA,WACA,gEAGA,OADA,UACA,KAEA,oBA4BA,EA3BA,sBACA,UAAsB,EAAU,mFAEhC,IAAa,EAAU,iBACvB,UAAsB,EAAU,6EAEhC,OACA,WACA,YAEA,EAA2B,EAAa,EAAU,SAAX,CAAW,+BAClD,KACA,iBAEA,iBADA,gBAEA,UAA0B,EAAU,2EAGpC,QAAgB,GAAM,EACtB,0BACA,UAAsB,EAAU,6DAExB,EAAY,gBACpB,UACA,GACA,GAAsB,EAAO,OAAQ,EAAI,KASzC,MlBhEO,MkBgEoB,GlBhEpB,MAEP,qBADA,oBAAwC,EAAQ,WAEhD,IACA,eACA,WACA,YAEA,QACA,EkBkDA,EADA,QAC8B,EAAO,OAAQ,EAAI,0BAGnB,EAAO,WAEQ,EAAO,eACpD,QAAwB,EAAY,KAEpC,GACA,GAHoC,OAGb,EAFvB,IAE2B,EAFK,EAAI,QAGpC,UACA,EAUA,OATA,GACA,WAA0B,EAAO,WAEjC,OAFiC,EAGjC,mBAEA,SACA,aAA4B,EAAO,WAEnC,CACA,CACA,CCpFO,IDgF4B,EChF5B,EACP,eACA,GACA,YAA8B,EAAa,EAC3C,CACA,QAF2C,WAE3C,GAEA,OADA,8BACA,KAEA,gBACA,8BACA,sBACA,6EAEA,SAAkB,YAAc,GAAG,UAAY,GAAG,YAAc,EAEhE,CCjBA,OAAe,+BAA2C,EAAC,CCK3D,oIACA,GAAe,QAOf,EANA,iBACA,kBACA,8CAEA,uBAGA,OAFA,oBAGA,UACA,WACA,aACA,cACA,QACA,gBACA,KACA,cACA,cACA,UACA,WACA,QACA,mBACA,KACA,YACA,YACA,SACA,UACA,QACA,qBACA,KACA,WACA,WACA,QACA,sBACA,KACA,YACA,YACA,QACA,aAzCA,OAyCA,GACA,KACA,SACA,yBAEA,OACA,yBACA,GAEA,CACA,CAAC,CChDD,CDgDE,QChDF,QACA,uBACA,2BAAuC,GAAO,QAE9C,QACA,CAyGO,SACP,eACA,GACA,IAAa,EAAQ,GACrB,IADqB,EACrB,6CAEA,2BACA,CACA,OACA,OAAe,EAAO,+BACtB,CACA,UACA,kBACA,CACA,WACA,aACA,CACA,UACA,mBAEA,WACA,aACA,CACA,UACA,mBAEA,WACA,aACA,CACA,WACA,aACA,CACA,WACA,mBACA,iCAEA,kBACA,8BAA8D,GAAK,IAGnE,EAHmE,EAGnE,QAAgC,GAAK,UAAe,GAAI,EAExD,CACA,EAHwD,EAGxD,OACA,mBACA,sCAEA,kBACA,mCAAmE,GAAK,IAGxE,EAHwE,EAGxE,QAAgC,GAAK,UAAe,GAAI,EAExD,CACA,EAHwD,EAGxD,OACA,WACA,YAAgC,GAAK,UAErC,kBACA,6BAA6D,GAAK,IAElE,EAFkE,QAElE,SACA,6BAA6D,GAAK,UAAe,GAAI,IAGrF,CAHqF,GAGrF,2BAEA,CACA,CCrLO,SACP,IACA,cACA,IAA4B,EAC5B,YAAwB,GAAgB,EACxC,CACA,UAFwC,CAExC,EAEA,OADA,cACA,KAEA,cAEA,OADA,cACA,KAEA,eAEA,OADA,cACA,KAEA,UAEA,OADA,cACA,KAEA,gBAEA,OADA,cACA,IACA,CACA,qBAEA,OADA,cACA,IACA,CACA,eAEA,OADA,cACA,KAEA,sBAEA,OADA,UACA,KAEA,gBACA,UAAwB,EAAW,gBAEnC,GADA,8BACA,8BACA,8BACA,iBACA,UAAsB,EAAU,uCAEhC,kBACA,CACA,gBC7CA,IAAMA,GAAqBC,QAAQC,GAAG,CAACF,kBAAkB,CAUlD,eAAeG,GAA0BC,CAAoB,CAAEC,CAAiB,EACrF,GAAI,CAACL,GACH,MAAM,MAAU,KADO,oCAIzB,IAAMM,EAAS,IAAIC,cAAcC,MAAM,CAACR,IAWxC,OAVY,MAAM,IAAIS,GAAQ,CAAC,GAAFA,kBACR,CAAC,CAAEC,IAAK,OAAQ,GAClCC,UAAU,CAACP,GACXQ,MAAM,CAACC,CAAAA,EAAAA,GAAAA,EAAAA,CAAMA,IACbC,WAAW,GACXC,SAAS,CAACC,WACVC,WAAW,CAACb,GACZc,iBAAiB,CAAe,IAAdb,EAAkB,OAAS,GAAGA,EAAU,CAAC,CAAC,EAC5Dc,IAAI,CAACb,EAGV,CC1BO,IAAMc,GAAU,OAAO,eAERC,GACpBC,CAAgB,CAChB,QAAEC,CAAM,CAAmC,EAE3C,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAC1B,GAAI,CAACD,GAASE,MAAMC,GAClB,CADsB,MACfC,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEC,MAAO,cAAe,EAAG,CAAEC,OAAQ,GAAI,GAGpE,IAAMC,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACb7B,EAAe8B,mBAAmBX,EAAOY,OAAO,EAEtD,GAAI,CACF,IAAMC,EAAQ,MAAMJ,EAAGK,KAAK,CAACC,MAAM,CAACC,SAAS,CAAC,CAC5CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACRC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACH,OAAO,CAAE/B,GACnBsC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,MAAMA,CAACK,MAAM,CAAEnB,EAAQE,IAAI,CAACC,EAAE,EAErC,GAEA,GAAI,CAACS,EACH,KADU,EACHR,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,kCAAmC,EAC5C,CAAEC,OAAQ,GAAI,GAIlB,GAAM,WAAE1B,EAAY,IAAI,CAAE,CAAI,MAAMiB,EAAQO,IAAI,GAAGe,KAAK,CAAC,IAAO,GAAC,GAI3DC,EAA0B,IAAdxC,EAAkB,EAAgB,IAAZA,EAElCyC,EAAa,MAAM3C,GAA0BiC,EAAMD,OAAO,CAAE9B,GAE5D0C,MACEC,GAH0C7C,EAEd,IAAdE,EACT,2BACA2C,GAAT,EAAcC,EAAVD,CAAa,GAAKH,GAE1B,OAAOjB,EAAAA,EAAYA,CAACC,IAAI,CAAC,YACvBiB,EACAD,UAAWE,EAAcG,WAAW,EACtC,EACF,CAAE,MAAOpB,EAAO,CAEd,OADAqB,QAAQrB,KAAK,CAAC,iCAAkCA,GACzCF,EAAAA,EAAYA,CAACC,IAAI,CACtB,CAAEC,MAAO,+BAAgC,EACzC,CAAEC,OAAQ,GAAI,EAElB,CACF,CCpDA,WAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,+CACA,6CACA,iBACA,uDACA,CAAK,CACL,+GACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,2CAAsD,GAC9D,cACA,MAAW,QAAW,EACtB,oBACA,uBACA,CAAK,CACL,CCpBA,yFACA,GAFA,4BAEA,4BACA,QACI,QAA8B,EAClC,+CACA,2BACA,yBACA,gBAAyB,OAAqB,EAC9C,wBACA,CAAS,CACT,CAAK,EAEE,OAAqB,EAC5B,GAAe,GAAsB,MAAM,GAAkB,CAC7D,OAD6D,IAf7D,CAAoB,MAAQ,KAcM,GAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,uIAiBnxJ,CAAC,CAAC,EAAC,kHCwBI,IAAMqB,EAAY,UAEvB,IAAMT,EAASU,CADK,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,EAAAA,EACNC,GAAG,CAAC,aAE/B,GAAIZ,EAAQ,OAAOA,EAEnB,IAAMnB,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAE1B,OAAOD,GAASE,KAAKC,EACvB,EAAC,2NCxDD,IAAM6B,EAAS,CACb,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACD,eCGD,IAAMC,EAA0C,CAC9C,CAACC,EAAAA,EAAKA,CAACC,OAAO,CAAC,CAAE,YACjB,CAACD,EAAAA,EAAKA,CAACE,IAAI,CAAC,CAAE,WACd,CAACF,EAAAA,EAAKA,CAACG,MAAM,CAAC,CAAE,WAChB,CAACH,EAAAA,EAAKA,CAACI,QAAQ,CAAC,CAAE,UACpB,EAEMC,EAAiB,UACrB,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAG/D,GAAG,CAACgE,WAAW,CAACX,GAAG,CAAC,uBAElE,IACkBG,EAAAA,EAAKA,CAACE,IAAI,EAC1BI,IAAgBN,EAAAA,EAAKA,CAACG,MAAM,EAC5BG,IAAgBN,EAAAA,EAAKA,CAACI,QAAQ,CAEvBE,CADP,CAIKN,EAAAA,EAAKA,CAACI,QAAQ,EAGvB,eAAeK,EAAiBnC,CAAM,CAAEoC,CAAc,EACpD,IAAIC,EAAO,MAAMrC,EAAGK,KAAK,CAACiC,KAAK,CAAC/B,SAAS,CAAC,CACxCC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC4B,EAAAA,KAAKA,CAACC,IAAI,CAAEH,EACxB,GAEA,GAAI,CAACC,EAAM,CACT,GAAM,CAACG,EAAQ,CAAG,MAAMxC,EAAGyC,MAAM,CAACH,EAAAA,KAAKA,EACpCI,MAAM,CAAC,CACNH,KAAMH,EACNO,YAAalB,CAAiB,CAACW,EAAS,GAEzCQ,SAAS,GACZP,EAAOG,CACT,CAEA,OAAOH,CACT,CAEO,eAAeQ,EAAiB7C,CAAM,CAAEW,CAAc,CAAEmC,CAAc,EAC3E,MAAM9C,EAAG+C,MAAM,CAACC,EAAAA,SAASA,EACtBxC,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACsC,EAAAA,SAASA,CAACrC,MAAM,CAAEA,IAE9B,MAAMX,EAAGyC,MAAM,CAACO,EAAAA,SAASA,EACtBN,MAAM,CAAC,QACN/B,SACAmC,CACF,EACJ,CAWO,eAAeG,EAAgBC,CAAsB,EAC1D,IAAMvC,EAAS,MAAMS,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE9B,GAAI,CAACT,EAAQ,OAAO,EAEpB,IAAMX,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMbkD,EAAgBC,CALE,MAAMpD,EAAGK,KAAK,CAAC2C,SAAS,CAACK,QAAQ,CAAC,CACxD7C,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACsC,EAAAA,SAASA,CAACrC,MAAM,CAAEA,GAC5B2C,KAAM,CAAEjB,MAAM,CAAK,CACrB,IAEsCkB,GAAG,CAACC,GAAMA,EAAGnB,IAAI,CAACE,IAAI,EAC5D,MAAOkB,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAACN,EAAyBD,EAChD,CAEO,GAAM,CACXQ,SAAU,KAAEC,CAAG,MAAEtE,CAAI,CAAE,MACvBI,CAAI,QACJmE,CAAM,SACNC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,IAAO,EAClBxF,OAAQL,QAAQC,GAAG,CAAC6F,WAAW,CAC/BC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAAChE,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAAI,CAClCiE,WAAYC,EAAAA,KAAKA,CACjBC,cAAeC,EAAAA,QAAQA,GAEzBC,UAAW,CACTC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC,CACLC,SAAUvG,QAAQC,GAAG,CAACuG,cAAc,CACpCC,aAAczG,QAAQC,GAAG,CAACyG,kBAC5B,GACAC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CAClBrC,KAAM,cACNsC,YAAa,CACXC,SAAU,CAAEC,MAAO,MAAOC,KAAM,OAAQC,YAAa,QAAS,EAC9DC,SAAU,CAAEH,MAAO,KAAMC,KAAM,WAAYC,YAAa,OAAQ,CAClE,EACA,MAAME,UAAUN,CAAW,EACzB,GAAI,CAACA,EACH,MAAM,KADU,CACA,aAGlB,GAAM,UAAEC,CAAQ,UAAEI,CAAQ,CAAE,CAAGL,EAE/B,GAAI,CACFO,EAAAA,CAAUA,CAACC,KAAK,CAAC,UAAEP,WAAUI,CAAS,EAExC,CAAE,MAAOpF,EAAO,CACd,MAAM,MAAU,UAClB,CAEA,IAAME,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEbP,EAAO,MAAMM,EAAGK,KAAK,CAAC8D,KAAK,CAAC5D,SAAS,CAAC,CAC1CC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACyD,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAEA,GAAI,CAACpF,GAKD,CADY,EAJL,IAIW4F,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAACJ,EAAoBxF,EAAKwF,QAAQ,EAHrE,MAAM,MAAU,YAQlB,MAAO,CACL,GAAGxF,CAAI,CACPwF,cAAUK,CACZ,CACF,CACF,GACD,CACDC,OAAQ,CACN,MAAM5B,OAAO,MAAElE,CAAI,CAAE,EACnB,GAAKA,CAAD,CAAMC,EAAE,CAEZ,CAFc,EAEV,CACF,IAAMK,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,GAJqB,CAIjBwF,KAJuBzF,EAAGK,KAAK,CAAC2C,SAAS,CAACzC,SAAS,CAAC,CACtDC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACsC,EAAAA,SAASA,CAACrC,MAAM,CAAEjB,EAAKC,EAAE,CACrC,GAEkB,OAElB,IAAMqC,EAAc,MAAMD,IACpBM,EAAO,MAAMF,EAAiBnC,EAAIgC,EACxC,OAAMa,EAAiB7C,EAAIN,EAAKC,EAAE,CAAE0C,EAAK1C,EAAE,CAC7C,CAAE,MAAOG,EAAO,CACdqB,QAAQrB,KAAK,CAAC,wBAAyBA,EACzC,CACF,CACF,EACA4F,UAAW,CACHC,IAAN,MAAU,OAAEC,CAAK,MAAElG,CAAI,CAAE,IACnBA,IACFkG,EADQ,EACA,CAAGlG,EAAKC,EAAE,CAClBiG,EAAMrD,IAAI,CAAG7C,EAAK6C,IAAI,EAAI7C,EAAKoF,QAAQ,CACvCc,EAAMd,QAAQ,CAAGpF,EAAKoF,QAAQ,CAC9Bc,EAAMC,KAAK,CAAGnG,EAAKmG,KAAK,ED/JzB,SAA2BtD,CAAY,EAC5C,IAAMuD,CC8J6CC,CD9JnCxD,CAAI,CAAC,EAAE,CAACyD,WAAW,GAE7BC,EAAaC,MAAMC,IAAI,CAAC5D,GAAM6D,MAAM,CACxC,CAACC,EAAKC,IAASD,EAAMC,EAAKC,UAAU,CAAC,GAAI,GACvC/E,EAAOgF,MAAM,CAEXC,EAAkBjF,CAAM,CAACyE,EAAW,CAEpCS,EAAM,CAAC;;2CAE4B,EAAED,EAAgB;;;;;;;;;;;;;QAarD,EAAEX,QAAQ;;;EAGhB,CAAC,CAACa,IAAI,GAGAC,EADU,IAAIrI,cACEC,MAAM,CAACkI,GACvBG,EAASC,EAAOX,IAADW,CAAMF,GAAOG,QAAQ,CAAC,UAE3C,MAAO,CAAC,0BAA0B,EAAEF,EAAAA,CACtC,EC6HsDjB,EAAMrD,IAAI,GAEnDqD,GAET,MAAMpG,QAAQ,SAAEA,CAAO,OAAEoG,CAAK,CAAE,EAC9B,GAAIA,GAASpG,EAAQE,IAAI,CAAE,CACzBF,EAAQE,IAAI,CAACC,EAAE,CAAGiG,EAAMjG,EAAE,CAC1BH,EAAQE,IAAI,CAAC6C,IAAI,CAAGqD,EAAMrD,IAAI,CAC9B/C,EAAQE,IAAI,CAACoF,QAAQ,CAAGc,EAAMd,QAAQ,CACtCtF,EAAQE,IAAI,CAACmG,KAAK,CAAGD,EAAMC,KAAK,CAEhC,IAAM7F,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACfmD,EAAkB,MAAMpD,EAAGK,KAAK,CAAC2C,SAAS,CAACK,QAAQ,CAAC,CACtD7C,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACsC,EAAAA,SAASA,CAACrC,MAAM,CAAEnB,EAAQE,IAAI,CAACC,EAAE,EAC3C2D,KAAM,CAAEjB,MAAM,CAAK,CACrB,GAEA,GAAI,CAACe,EAAgBoD,MAAM,CAAE,CAC3B,IAAMxE,EAAc,MAAMD,IACpBM,EAAO,MAAMF,EAAiBnC,EAAIgC,EACxC,OAAMa,EAAiB7C,EAAIR,EAAQE,IAAI,CAACC,EAAE,CAAE0C,EAAK1C,EAAE,EACnDyD,EAAkB,CAAC,CACjBzC,OAAQnB,EAAQE,IAAI,CAACC,EAAE,CACvBmD,OAAQT,EAAK1C,EAAE,CACfqH,UAAW,IAAIhG,KACfqB,KAAMA,CACR,EAAE,CAGJ7C,EAAQE,IAAI,CAAC4C,KAAK,CAAGc,EAAgBG,GAAG,CAACC,GAAO,OACxCA,EAAGnB,IAAI,CAACE,IAAI,CACpB,EACF,CAEA,OAAO/C,CACT,CACF,EACAA,QAAS,CACPyH,SAAU,KACZ,EACF,GAEO,CAFJ,cAEmBC,EAASpC,CAAgB,CAAEI,CAAgB,EAC/D,IAAMlF,EAAKC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,GAJiB,CAIbkH,KAJmBnH,EAAGK,GAIZ,EAJiB,CAAC8D,KAAK,CAAC5D,SAAS,CAAC,CAC9CC,MAAOE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACyD,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAGE,MAAM,MAAU,UAGlB,IAAMsC,EAAiB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACnC,GAEpC,CAACxF,EAAK,CAAG,MAAMM,EAAGyC,MAAM,CAAC0B,EAAAA,KAAKA,EACjCzB,MAAM,CAAC,UACNoC,EACAI,SAAUkC,CACZ,GACCxE,SAAS,GAEZ,OAAOlD,CACT,gFCvOO,IAAMO,EAAW,IAAMqH,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAACrF,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAG/D,GAAG,CAACqJ,EAAE,CAAE,CAAEC,MAAMA,CAAAA,CAAC,GAAE,gCAAHA,gCCJnE,IAAM9F,EAAQ,CACnBC,QAAS,UACTC,KAAM,OACNC,OAAQ,SACRC,SAAU,UACZ,EAAW,EAIgB,CACzB2F,aAAc,eACdC,eAAgB,iBAChBC,aAAc,eACdC,cAAe,gBACfC,eAAgB,gBAClB,EAAW,EAIiD,CAC1D,CAACnG,EAAMC,OAAO,CAAC,CAAEmG,OAAOpF,MAAM,CAACqF,GAC/B,CAACrG,EAAME,IAAI,CAAC,CAAE,CACZmG,EAAYN,YAAY,CACxBM,EAAYL,cAAc,CAC1BK,EAAYF,cAAc,CAC3B,CACD,CAACnG,EAAMG,MAAM,CAAC,CAAE,CACdkG,EAAYN,YAAY,CACxBM,EAAYL,cAAc,CAC3B,CACD,CAAChG,EAAMI,QAAQ,CAAC,CAAE,EAAE,EACX,SAEK2B,EAAcT,CAAiB,CAAEE,CAAsB,EACrE,OAAOF,EAAUgF,IAAI,CAAC3F,GAAQ4F,CAAgB,CAAC5F,EAAK,EAAE6F,SAAShF,GACjE,kVC9BO,IAAMiB,EAAQgE,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvCxI,GAAIyI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MACNC,UAAU,GACVC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IACrCjG,KAAM6F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXhI,MAAOgI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASK,MAAM,GAC3BC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,gBAAiB,CAAEC,KAAM,cAAe,GAC/D/C,MAAOuC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZtD,SAAUsD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYK,MAAM,GACjCvD,SAAUkD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACjB,GAAE,EACsBD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CACjC,UACA,CACExH,OAAQyH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UACVS,OAAO,GACPC,UAAU,CAAC,IAAM3E,EAAMxE,EAAE,CAAE,CAAEoJ,SAAU,SAAU,GACpD/D,KAAMoD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQY,KAAK,GAAuBH,OAAO,GACtDI,SAAUb,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYS,OAAO,GAClCK,kBAAmBd,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,qBAAqBS,OAAO,GACpDM,cAAef,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBACpBgB,aAAchB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACnBiB,WAAYV,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cACpBW,WAAYlB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,cACjBmB,MAAOnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZoB,SAAUpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YACfqB,cAAerB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACtB,EACA,GAAc,EACZsB,KADY,OACCrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CACtBsB,QAAS,CAACC,EAAQX,QAAQ,CAAEW,EAAQV,iBAAiB,CAAC,GAE1D,GAGW5I,CAFZ,CAEqB6H,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,QAAS,CACzCxI,GAAIyI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DrI,QAASiI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAAGJ,MAAM,GACzC9H,OAAQyH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UAAUU,UAAU,CAAC,IAAM3E,EAAMxE,EAAE,CAAE,CAAEoJ,SAAU,SAAU,GACxE/B,UAAW2B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPP,UAAU,CAAC,IAAM,IAAItH,MACxBH,UAAW8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbgB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAMnJ,SAAS,EAChE,GAEaoJ,CAFV,CAEqB9B,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7CxI,GAAIyI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D0B,QAAS9B,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACXS,OAAO,GACPC,UAAU,CAAC,IAAMxI,EAAOX,EAAE,CAAE,CAAEoJ,SAAU,SAAU,GACrDoB,YAAa/B,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBAAgBS,OAAO,GACzCuB,QAAShC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAChCwB,QAASjC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAChCyB,KAAMlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXmC,WAAY5B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cAAe,CAAEC,KAAM,cAAe,GACvDC,OAAO,GACPP,UAAU,CAAC,IAAM,IAAItH,KAC1B,EAAG,GAAY,EACbwJ,GADa,QACDV,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAME,OAAO,EAC5D,GAEaO,CAFV,CAEqBtC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7CxI,GAAIyI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D7H,OAAQyH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACVS,OAAO,GACPC,UAAU,CAAC,IAAM3E,EAAMxE,EAAE,CAAE,CAAEoJ,SAAU,SAAU,GACpD2B,IAAKtC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOS,OAAO,GACxB8B,QAAShC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAG+B,OAAO,EAAC,GACnE5D,UAAW2B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPP,UAAU,CAAC,IAAM,IAAItH,MACxB6J,UAAWlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPP,UAAU,CAAC,IAAM,IAAItH,KAC1B,GAAE,EAEmBmH,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvCxI,GAAIyI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DjG,KAAM6F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQS,OAAO,GAC1BlG,YAAayF,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,eAClBpB,UAAW2B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAItH,MAC7E6J,UAAWlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAItH,KAC/E,GAAG,EAEsBmH,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,YAAa,CAChDxH,OAAQyH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAAGC,UAAU,CAAC,IAAM3E,EAAMxE,EAAE,CAAE,CAAEoJ,SAAU,SAAU,GACnFjG,OAAQsF,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAAGC,UAAU,CAAC,IAAMxG,EAAM3C,EAAE,CAAE,CAAEoJ,SAAU,SAAU,GACnF/B,UAAW2B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAItH,KAC/E,EAAG,GAAY,EACb8J,GADa,CACTzC,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CAAEsB,QAAS,CAACK,EAAMrJ,MAAM,CAAEqJ,EAAMlH,MAAM,CAAC,GACxD,GAEaiI,CAFT,CAEmB5C,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,WAAY,CAC7CxI,GAAIyI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D7H,OAAQyH,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWS,OAAO,GAAGC,UAAU,CAAC,IAAM3E,EAAMxE,EAAE,EAC3D4C,KAAM6F,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQS,OAAO,GAC1BmC,IAAK5C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOS,OAAO,GAAGJ,MAAM,GACjCzB,UAAW2B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGN,UAAU,CAAC,IAAM,IAAItH,MAC7EH,UAAW8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrD+B,QAAShC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAG+B,OAAO,CAAC,GACrE,EAAG,GAAY,EACbK,GADa,cACKC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,uBAAuBnB,EAAE,CAACC,EAAMzH,IAAI,CAAEyH,EAAMrJ,MAAM,EAClF,GAEawK,CAFT,CAE+BhD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,qBAAsB,CACnEiD,IAAKhD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOC,UAAU,GAC3BxH,UAAW8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbgB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,0BAA0BC,EAAE,CAACC,EAAMnJ,SAAS,EAClE,GAEawK,CAFT,CAE4BC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACP,EAAS,CAAC,KAAEQ,CAAG,CAAE,GAAM,EAC/D7L,KAAM6L,EAAIpH,EAAO,CACfqH,OAAQ,CAACT,EAAQpK,MAAM,CAAC,CACxBmI,WAAY,CAAC3E,EAAMxE,EAAE,CACvB,GACF,GAEa8L,CAFT,CAE8BH,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACtI,EAAW,CAAC,KAAEuI,CAAG,CAAE,GAAM,EACnE7L,KAAM6L,EAAIpH,EAAO,CACfqH,OAAQ,CAACxI,EAAUrC,MAAM,CAAC,CAC1BmI,WAAY,CAAC3E,EAAMxE,EAAE,CAAC,GAExB0C,KAAMkJ,EAAIjJ,EAAO,CACfkJ,OAAQ,CAACxI,EAAUF,MAAM,CAAC,CAC1BgG,WAAY,CAACxG,EAAM3C,EAAE,CAAC,GAE1B,GAEa+L,CAFT,CAE0BJ,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACnH,EAAO,CAAC,MAAEwH,CAAI,CAAE,GAAM,EAC5D3I,UAAW2I,EAAK3I,GAChB+H,QAASY,EAAKZ,GAChB,GAEaa,CAFT,CAE0BN,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAChJ,EAAO,CAAC,MAAEqJ,CAAI,CAAE,GAAM,EAC5D3I,UAAW2I,EAAK3I,EAClB,IAAI,uFC5IG,SAAS6I,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,CAEO,eAAezE,EAAanC,CAAgB,EACjD,IAAM+G,EAAU,IAAI1N,YACd2N,EAAOjO,QAAQC,GAAG,CAAC6F,WAAW,EAAI,GAClCoI,EAAOF,EAAQzN,MAAM,CAAC0G,EAAWgH,GAEvC,OAAOE,KAAKC,OAAOC,YAAY,IAAI,IAAIC,WAD1B,MAAMhE,OAAOiE,MAAM,CAACC,MAAM,CAAC,UAAWN,KAErD,CAEO,eAAe7G,EAAgBJ,CAAgB,CAAEkC,CAAsB,EAE5E,OADa,MAAMC,EAAanC,KAChBkC,CAClB,8DChBO,IAAMhC,EAAasH,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACjC5H,SAAU4H,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,WACPC,GAAG,CAAC,GAAI,gBACRC,KAAK,CAAC,mBAAoB,uBAC1BC,MAAM,CAACC,GAAO,CAACA,EAAI7E,QAAQ,CAAC,KAAM,cACrChD,SAAUwH,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,eACZ,GAAE,+CEWK,eACP,SACA,+CACA,UACA,GDxBA,kECwB2B,UAE3B,QACA", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/buffer_utils.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/base64.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/util/base64url.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/util/errors.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/subtle_dsa.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/check_key_length.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/crypto_key.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/invalid_key_input.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/get_sign_verify_key.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/sign.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/is_disjoint.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/is_key_like.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/is_object.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/is_jwk.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/check_key_type.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/validate_crit.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/jwk_to_key.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/normalize_key.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/jws/flattened/sign.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/jws/compact/sign.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/epoch.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/secs.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/lib/jwt_claims_set.js", "webpack://_N_E/./node_modules/.pnpm/jose@6.0.11/node_modules/jose/dist/webapi/jwt/sign.js", "webpack://_N_E/./app/lib/jwt.ts", "webpack://_N_E/./app/api/emails/[address]/credentials/route.ts", "webpack://_N_E/./app/api/emails/[address]/credentials/route.ts?c601", "webpack://_N_E/?c5e8", "webpack://_N_E/./app/lib/apiKey.ts", "webpack://_N_E/./app/lib/avatar.ts", "webpack://_N_E/./app/lib/auth.ts", "webpack://_N_E/./app/lib/db.ts", "webpack://_N_E/./app/lib/permissions.ts", "webpack://_N_E/./app/lib/schema.ts", "webpack://_N_E/./app/lib/utils.ts", "webpack://_N_E/./app/lib/validation.ts", "webpack://_N_E/./node_modules/.pnpm/nanoid@5.1.5/node_modules/nanoid/url-alphabet/index.js", "webpack://_N_E/./node_modules/.pnpm/nanoid@5.1.5/node_modules/nanoid/index.browser.js"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "export const encoder = new TextEncoder();\nexport const decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nexport function concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nexport function uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nexport function uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\n", "export function encodeBase64(input) {\n    if (Uint8Array.prototype.toBase64) {\n        return input.toBase64();\n    }\n    const CHUNK_SIZE = 0x8000;\n    const arr = [];\n    for (let i = 0; i < input.length; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join(''));\n}\nexport function decodeBase64(encoded) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(encoded);\n    }\n    const binary = atob(encoded);\n    const bytes = new Uint8Array(binary.length);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return bytes;\n}\n", "import { encoder, decoder } from '../lib/buffer_utils.js';\nimport { encodeBase64, decodeBase64 } from '../lib/base64.js';\nexport function decode(input) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(typeof input === 'string' ? input : decoder.decode(input), {\n            alphabet: 'base64url',\n        });\n    }\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = decoder.decode(encoded);\n    }\n    encoded = encoded.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, '');\n    try {\n        return decodeBase64(encoded);\n    }\n    catch {\n        throw new TypeError('The input to be decoded is not correctly encoded.');\n    }\n}\nexport function encode(input) {\n    let unencoded = input;\n    if (typeof unencoded === 'string') {\n        unencoded = encoder.encode(unencoded);\n    }\n    if (Uint8Array.prototype.toBase64) {\n        return unencoded.toBase64({ alphabet: 'base64url', omitPadding: true });\n    }\n    return encodeBase64(unencoded).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "export class JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nexport class JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JOSEAlgNotAllowed extends J<PERSON><PERSON><PERSON>r {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nexport class JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nexport class JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nexport class JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nexport class JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nexport class JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nexport class JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nexport class JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nexport class JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nexport class JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n", "import { JOSENotSupported } from '../util/errors.js';\nexport default (alg, algorithm) => {\n    const hash = `SHA-${alg.slice(-3)}`;\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512':\n            return { hash, name: '<PERSON><PERSON>' };\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { hash, name: 'RSA-PSS', saltLength: parseInt(alg.slice(-3), 10) >> 3 };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { hash, name: 'RSASSA-PKCS1-v1_5' };\n        case 'ES256':\n        case 'ES384':\n        case 'ES512':\n            return { hash, name: 'ECDS<PERSON>', namedCurve: algorithm.namedCurve };\n        case 'Ed25519':\n        case 'EdDSA':\n            return { name: 'Ed25519' };\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n};\n", "export default (alg, key) => {\n    if (alg.startsWith('RS') || alg.startsWith('PS')) {\n        const { modulusLength } = key.algorithm;\n        if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n            throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n        }\n    }\n};\n", "function unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usage) {\n    if (usage && !key.usages.includes(usage)) {\n        throw new TypeError(`CryptoKey does not support this operation, its usages must include ${usage}.`);\n    }\n}\nexport function checkSigCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'Ed25519':\n        case 'EdDSA': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\nexport function checkEncCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                    break;\n                default:\n                    throw unusable('ECDH or X25519');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\n", "function message(msg, actual, ...types) {\n    types = types.filter(<PERSON><PERSON><PERSON>);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\nexport default (actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n};\nexport function withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n", "import { checkSig<PERSON>rypt<PERSON><PERSON><PERSON> } from './crypto_key.js';\nimport invalidKeyInput from './invalid_key_input.js';\nexport default async (alg, key, usage) => {\n    if (key instanceof Uint8Array) {\n        if (!alg.startsWith('HS')) {\n            throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n        }\n        return crypto.subtle.importKey('raw', key, { hash: `SHA-${alg.slice(-3)}`, name: 'HM<PERSON>' }, false, [usage]);\n    }\n    checkSigCryptoKey(key, alg, usage);\n    return key;\n};\n", "import subtleAlgorithm from './subtle_dsa.js';\nimport checkKey<PERSON>ength from './check_key_length.js';\nimport getSignKey from './get_sign_verify_key.js';\nexport default async (alg, key, data) => {\n    const cryptoKey = await getSignKey(alg, key, 'sign');\n    checkKeyLength(alg, cryptoKey);\n    const signature = await crypto.subtle.sign(subtleAlgorithm(alg, cryptoKey.algorithm), cryptoKey, data);\n    return new Uint8Array(signature);\n};\n", "export default (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\n", "export function assertCrypto<PERSON><PERSON>(key) {\n    if (!isCrypto<PERSON>ey(key)) {\n        throw new Error('CryptoKey instance expected');\n    }\n}\nexport function isCrypto<PERSON>ey(key) {\n    return key?.[Symbol.toStringTag] === 'CryptoKey';\n}\nexport function isKeyObject(key) {\n    return key?.[Symbol.toStringTag] === 'KeyObject';\n}\nexport default (key) => {\n    return isCryptoKey(key) || isKeyObject(key);\n};\n", "function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nexport default (input) => {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n};\n", "import isObject from './is_object.js';\nexport function isJWK(key) {\n    return isObject(key) && typeof key.kty === 'string';\n}\nexport function isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nexport function isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nexport function isSecretJWK(key) {\n    return key.kty === 'oct' && typeof key.k === 'string';\n}\n", "import { withAlg as invalidKeyInput } from './invalid_key_input.js';\nimport isKey<PERSON>ike from './is_key_like.js';\nimport * as jwk from './is_jwk.js';\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined) {\n        let expected;\n        switch (usage) {\n            case 'sign':\n            case 'verify':\n                expected = 'sig';\n                break;\n            case 'encrypt':\n            case 'decrypt':\n                expected = 'enc';\n                break;\n        }\n        if (key.use !== expected) {\n            throw new TypeError(`Invalid key for this operation, its \"use\" must be \"${expected}\" when present`);\n        }\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, its \"alg\" must be \"${alg}\" when present`);\n    }\n    if (Array.isArray(key.key_ops)) {\n        let expectedKeyOp;\n        switch (true) {\n            case usage === 'sign' || usage === 'verify':\n            case alg === 'dir':\n            case alg.includes('CBC-HS'):\n                expectedKeyOp = usage;\n                break;\n            case alg.startsWith('PBES2'):\n                expectedKeyOp = 'deriveBits';\n                break;\n            case /^A\\d{3}(?:GCM)?(?:KW)?$/.test(alg):\n                if (!alg.includes('GCM') && alg.endsWith('KW')) {\n                    expectedKeyOp = usage === 'encrypt' ? 'wrapKey' : 'unwrapKey';\n                }\n                else {\n                    expectedKeyOp = usage;\n                }\n                break;\n            case usage === 'encrypt' && alg.startsWith('RSA'):\n                expectedKeyOp = 'wrapKey';\n                break;\n            case usage === 'decrypt':\n                expectedKeyOp = alg.startsWith('RSA') ? 'unwrapKey' : 'deriveBits';\n                break;\n        }\n        if (expectedKeyOp && key.key_ops?.includes?.(expectedKeyOp) === false) {\n            throw new TypeError(`Invalid key for this operation, its \"key_ops\" must include \"${expectedKeyOp}\" when present`);\n        }\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (jwk.isJWK(key)) {\n        if (jwk.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key', 'Uint8Array'));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage) => {\n    if (jwk.isJWK(key)) {\n        switch (usage) {\n            case 'decrypt':\n            case 'sign':\n                if (jwk.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'encrypt':\n            case 'verify':\n                if (jwk.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (key.type === 'public') {\n        switch (usage) {\n            case 'sign':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n            case 'decrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n            default:\n                break;\n        }\n    }\n    if (key.type === 'private') {\n        switch (usage) {\n            case 'verify':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n            case 'encrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n            default:\n                break;\n        }\n    }\n};\nexport default (alg, key, usage) => {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(alg) ||\n        /^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage);\n    }\n};\n", "import { JOSENotSupported, JWEInvalid, JWSInvalid } from '../util/errors.js';\nexport default (Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) => {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n};\n", "import { JOSENotSupported } from '../util/errors.js';\nfunction subtleMapping(jwk) {\n    let algorithm;\n    let keyUsages;\n    switch (jwk.kty) {\n        case 'RSA': {\n            switch (jwk.alg) {\n                case 'PS256':\n                case 'PS384':\n                case 'PS512':\n                    algorithm = { name: 'RSA-PSS', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RS256':\n                case 'RS384':\n                case 'RS512':\n                    algorithm = { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RSA-OAEP':\n                case 'RSA-OAEP-256':\n                case 'RSA-OAEP-384':\n                case 'RSA-OAEP-512':\n                    algorithm = {\n                        name: 'RSA-OAEP',\n                        hash: `SHA-${parseInt(jwk.alg.slice(-3), 10) || 1}`,\n                    };\n                    keyUsages = jwk.d ? ['decrypt', 'unwrapKey'] : ['encrypt', 'wrapKey'];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'EC': {\n            switch (jwk.alg) {\n                case 'ES256':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-256' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES384':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-384' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES512':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-521' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: 'ECDH', namedCurve: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'OKP': {\n            switch (jwk.alg) {\n                case 'Ed25519':\n                case 'EdDSA':\n                    algorithm = { name: 'Ed25519' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        default:\n            throw new JOSENotSupported('Invalid or unsupported JWK \"kty\" (Key Type) Parameter value');\n    }\n    return { algorithm, keyUsages };\n}\nexport default async (jwk) => {\n    if (!jwk.alg) {\n        throw new TypeError('\"alg\" argument is required when \"jwk.alg\" is not present');\n    }\n    const { algorithm, keyUsages } = subtleMapping(jwk);\n    const keyData = { ...jwk };\n    delete keyData.alg;\n    delete keyData.use;\n    return crypto.subtle.importKey('jwk', keyData, algorithm, jwk.ext ?? (jwk.d ? false : true), jwk.key_ops ?? keyUsages);\n};\n", "import { isJWK } from './is_jwk.js';\nimport { decode } from '../util/base64url.js';\nimport importJWK from './jwk_to_key.js';\nimport { isCryptoKey, isKeyObject } from './is_key_like.js';\nlet cache;\nconst handleJWK = async (key, jwk, alg, freeze = false) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(key);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const cryptoKey = await importJWK({ ...jwk, alg });\n    if (freeze)\n        Object.freeze(key);\n    if (!cached) {\n        cache.set(key, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nconst handleKeyObject = (keyObject, alg) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(keyObject);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const isPublic = keyObject.type === 'public';\n    const extractable = isPublic ? true : false;\n    let cryptoKey;\n    if (keyObject.asymmetricKeyType === 'x25519') {\n        switch (alg) {\n            case 'ECDH-ES':\n            case 'ECDH-ES+A128KW':\n            case 'ECDH-ES+A192KW':\n            case 'ECDH-ES+A256KW':\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, isPublic ? [] : ['deriveBits']);\n    }\n    if (keyObject.asymmetricKeyType === 'ed25519') {\n        if (alg !== 'EdDSA' && alg !== 'Ed25519') {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, [\n            isPublic ? 'verify' : 'sign',\n        ]);\n    }\n    if (keyObject.asymmetricKeyType === 'rsa') {\n        let hash;\n        switch (alg) {\n            case 'RSA-OAEP':\n                hash = 'SHA-1';\n                break;\n            case 'RS256':\n            case 'PS256':\n            case 'RSA-OAEP-256':\n                hash = 'SHA-256';\n                break;\n            case 'RS384':\n            case 'PS384':\n            case 'RSA-OAEP-384':\n                hash = 'SHA-384';\n                break;\n            case 'RS512':\n            case 'PS512':\n            case 'RSA-OAEP-512':\n                hash = 'SHA-512';\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg.startsWith('RSA-OAEP')) {\n            return keyObject.toCryptoKey({\n                name: 'RSA-OAEP',\n                hash,\n            }, extractable, isPublic ? ['encrypt'] : ['decrypt']);\n        }\n        cryptoKey = keyObject.toCryptoKey({\n            name: alg.startsWith('PS') ? 'RSA-PSS' : 'RSASSA-PKCS1-v1_5',\n            hash,\n        }, extractable, [isPublic ? 'verify' : 'sign']);\n    }\n    if (keyObject.asymmetricKeyType === 'ec') {\n        const nist = new Map([\n            ['prime256v1', 'P-256'],\n            ['secp384r1', 'P-384'],\n            ['secp521r1', 'P-521'],\n        ]);\n        const namedCurve = nist.get(keyObject.asymmetricKeyDetails?.namedCurve);\n        if (!namedCurve) {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg === 'ES256' && namedCurve === 'P-256') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES384' && namedCurve === 'P-384') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES512' && namedCurve === 'P-521') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg.startsWith('ECDH-ES')) {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDH',\n                namedCurve,\n            }, extractable, isPublic ? [] : ['deriveBits']);\n        }\n    }\n    if (!cryptoKey) {\n        throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n    }\n    if (!cached) {\n        cache.set(keyObject, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nexport default async (key, alg) => {\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        return key;\n    }\n    if (isKeyObject(key)) {\n        if (key.type === 'secret') {\n            return key.export();\n        }\n        if ('toCryptoKey' in key && typeof key.toCryptoKey === 'function') {\n            try {\n                return handleKeyObject(key, alg);\n            }\n            catch (err) {\n                if (err instanceof TypeError) {\n                    throw err;\n                }\n            }\n        }\n        let jwk = key.export({ format: 'jwk' });\n        return handleJWK(key, jwk, alg);\n    }\n    if (isJWK(key)) {\n        if (key.k) {\n            return decode(key.k);\n        }\n        return handleJWK(key, key, alg, true);\n    }\n    throw new Error('unreachable');\n};\n", "import { encode as b64u } from '../../util/base64url.js';\nimport sign from '../../lib/sign.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nexport class FlattenedSign {\n    #payload;\n    #protectedHeader;\n    #unprotectedHeader;\n    constructor(payload) {\n        if (!(payload instanceof Uint8Array)) {\n            throw new TypeError('payload must be an instance of Uint8Array');\n        }\n        this.#payload = payload;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.#protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.#unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.#unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        if (!this.#protectedHeader && !this.#unprotectedHeader) {\n            throw new JWSInvalid('either setProtectedHeader or setUnprotectedHeader must be called before #sign()');\n        }\n        if (!isDisjoint(this.#protectedHeader, this.#unprotectedHeader)) {\n            throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this.#protectedHeader,\n            ...this.#unprotectedHeader,\n        };\n        const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, this.#protectedHeader, joseHeader);\n        let b64 = true;\n        if (extensions.has('b64')) {\n            b64 = this.#protectedHeader.b64;\n            if (typeof b64 !== 'boolean') {\n                throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n            }\n        }\n        const { alg } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        checkKeyType(alg, key, 'sign');\n        let payload = this.#payload;\n        if (b64) {\n            payload = encoder.encode(b64u(payload));\n        }\n        let protectedHeader;\n        if (this.#protectedHeader) {\n            protectedHeader = encoder.encode(b64u(JSON.stringify(this.#protectedHeader)));\n        }\n        else {\n            protectedHeader = encoder.encode('');\n        }\n        const data = concat(protectedHeader, encoder.encode('.'), payload);\n        const k = await normalizeKey(key, alg);\n        const signature = await sign(alg, k, data);\n        const jws = {\n            signature: b64u(signature),\n            payload: '',\n        };\n        if (b64) {\n            jws.payload = decoder.decode(payload);\n        }\n        if (this.#unprotectedHeader) {\n            jws.header = this.#unprotectedHeader;\n        }\n        if (this.#protectedHeader) {\n            jws.protected = decoder.decode(protectedHeader);\n        }\n        return jws;\n    }\n}\n", "import { FlattenedSign } from '../flattened/sign.js';\nexport class CompactSign {\n    #flattened;\n    constructor(payload) {\n        this.#flattened = new FlattenedSign(payload);\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    async sign(key, options) {\n        const jws = await this.#flattened.sign(key, options);\n        if (jws.payload === undefined) {\n            throw new TypeError('use the flattened module for creating JWS with b64: false');\n        }\n        return `${jws.protected}.${jws.payload}.${jws.signature}`;\n    }\n}\n", "export default (date) => Math.floor(date.getTime() / 1000);\n", "const minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\nexport default (str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n};\n", "import { JWTClaimValidationFailed, JW<PERSON>xpired, JWTInvalid } from '../util/errors.js';\nimport { decoder } from './buffer_utils.js';\nimport epoch from './epoch.js';\nimport secs from './secs.js';\nimport isObject from './is_object.js';\nimport { encoder } from './buffer_utils.js';\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nconst normalizeTyp = (value) => {\n    if (value.includes('/')) {\n        return value.toLowerCase();\n    }\n    return `application/${value.toLowerCase()}`;\n};\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\nexport function validateClaimsSet(protectedHeader, encodedPayload, options = {}) {\n    let payload;\n    try {\n        payload = JSON.parse(decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!isObject(payload)) {\n        throw new JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new JWTClaimValidationFailed('unexpected \"typ\" JWT header value', payload, 'typ', 'check_failed');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    const presenceCheck = [...requiredClaims];\n    if (maxTokenAge !== undefined)\n        presenceCheck.push('iat');\n    if (audience !== undefined)\n        presenceCheck.push('aud');\n    if (subject !== undefined)\n        presenceCheck.push('sub');\n    if (issuer !== undefined)\n        presenceCheck.push('iss');\n    for (const claim of new Set(presenceCheck.reverse())) {\n        if (!(claim in payload)) {\n            throw new JWTClaimValidationFailed(`missing required \"${claim}\" claim`, payload, claim, 'missing');\n        }\n    }\n    if (issuer &&\n        !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new JWTClaimValidationFailed('unexpected \"iss\" claim value', payload, 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new JWTClaimValidationFailed('unexpected \"sub\" claim value', payload, 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new JWTClaimValidationFailed('unexpected \"aud\" claim value', payload, 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = secs(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = epoch(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new JWTClaimValidationFailed('\"iat\" claim must be a number', payload, 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new JWTClaimValidationFailed('\"nbf\" claim must be a number', payload, 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', payload, 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new JWTClaimValidationFailed('\"exp\" claim must be a number', payload, 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new JWTExpired('\"exp\" claim timestamp check failed', payload, 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : secs(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', payload, 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', payload, 'iat', 'check_failed');\n        }\n    }\n    return payload;\n}\nexport class JWTClaimsBuilder {\n    #payload;\n    constructor(payload) {\n        if (!isObject(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this.#payload = structuredClone(payload);\n    }\n    data() {\n        return encoder.encode(JSON.stringify(this.#payload));\n    }\n    get iss() {\n        return this.#payload.iss;\n    }\n    set iss(value) {\n        this.#payload.iss = value;\n    }\n    get sub() {\n        return this.#payload.sub;\n    }\n    set sub(value) {\n        this.#payload.sub = value;\n    }\n    get aud() {\n        return this.#payload.aud;\n    }\n    set aud(value) {\n        this.#payload.aud = value;\n    }\n    set jti(value) {\n        this.#payload.jti = value;\n    }\n    set nbf(value) {\n        if (typeof value === 'number') {\n            this.#payload.nbf = validateInput('setNotBefore', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.nbf = validateInput('setNotBefore', epoch(value));\n        }\n        else {\n            this.#payload.nbf = epoch(new Date()) + secs(value);\n        }\n    }\n    set exp(value) {\n        if (typeof value === 'number') {\n            this.#payload.exp = validateInput('setExpirationTime', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.exp = validateInput('setExpirationTime', epoch(value));\n        }\n        else {\n            this.#payload.exp = epoch(new Date()) + secs(value);\n        }\n    }\n    set iat(value) {\n        if (typeof value === 'undefined') {\n            this.#payload.iat = epoch(new Date());\n        }\n        else if (value instanceof Date) {\n            this.#payload.iat = validateInput('setIssuedAt', epoch(value));\n        }\n        else if (typeof value === 'string') {\n            this.#payload.iat = validateInput('setIssuedAt', epoch(new Date()) + secs(value));\n        }\n        else {\n            this.#payload.iat = validateInput('setIssuedAt', value);\n        }\n    }\n}\n", "import { CompactSign } from '../jws/compact/sign.js';\nimport { JWTInvalid } from '../util/errors.js';\nimport { JWTClaimsBuilder } from '../lib/jwt_claims_set.js';\nexport class SignJWT {\n    #protectedHeader;\n    #jwt;\n    constructor(payload = {}) {\n        this.#jwt = new JWTClaimsBuilder(payload);\n    }\n    setIssuer(issuer) {\n        this.#jwt.iss = issuer;\n        return this;\n    }\n    setSubject(subject) {\n        this.#jwt.sub = subject;\n        return this;\n    }\n    setAudience(audience) {\n        this.#jwt.aud = audience;\n        return this;\n    }\n    setJti(jwtId) {\n        this.#jwt.jti = jwtId;\n        return this;\n    }\n    setNotBefore(input) {\n        this.#jwt.nbf = input;\n        return this;\n    }\n    setExpirationTime(input) {\n        this.#jwt.exp = input;\n        return this;\n    }\n    setIssuedAt(input) {\n        this.#jwt.iat = input;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        const sig = new CompactSign(this.#jwt.data());\n        sig.setProtectedHeader(this.#protectedHeader);\n        if (Array.isArray(this.#protectedHeader?.crit) &&\n            this.#protectedHeader.crit.includes('b64') &&\n            this.#protectedHeader.b64 === false) {\n            throw new JWTInvalid('JWTs MUST NOT use unencoded payload');\n        }\n        return sig.sign(key, options);\n    }\n}\n", "import { SignJWT, jwtVerify } from 'jose';\r\nimport { nanoid } from 'nanoid';\r\nimport { createDb } from './db';\r\nimport { revoked_credentials } from './schema';\r\nimport { eq } from 'drizzle-orm';\r\n\r\nconst JWT_ADDRESS_SECRET = process.env.JWT_ADDRESS_SECRET;\r\nconst ISSUER = 'moemail';\r\n\r\n/**\r\n * Generates a JWT (Address Credential) for a given email address.\r\n *\r\n * @param emailAddress The email address to be the subject of the JWT.\r\n * @param expiresIn The lifetime of the token in seconds. If 0, it creates a \"permanent\" token (expires in 100 years).\r\n * @returns A signed JWT string.\r\n */\r\nexport async function generateAddressCredential(emailAddress: string, expiresIn: number): Promise<string> {\r\n  if (!JWT_ADDRESS_SECRET) {\r\n    throw new Error('JWT_ADDRESS_SECRET is not set in .env');\r\n  }\r\n\r\n  const secret = new TextEncoder().encode(JWT_ADDRESS_SECRET);\r\n  const jwt = await new SignJWT({})\r\n    .setProtectedHeader({ alg: 'HS256' })\r\n    .setSubject(emailAddress)\r\n    .setJti(nanoid())\r\n    .setIssuedAt()\r\n    .setIssuer(ISSUER)\r\n    .setAudience(emailAddress)\r\n    .setExpirationTime(expiresIn === 0 ? '365d' : `${expiresIn}s`)\r\n    .sign(secret);\r\n\r\n  return jwt;\r\n}\r\n\r\nexport async function verifyAddressCredential(token: string) {\r\n  if (!JWT_ADDRESS_SECRET) {\r\n    throw new Error('JWT_ADDRESS_SECRET is not set in .env');\r\n  }\r\n\r\n  const secret = new TextEncoder().encode(JWT_ADDRESS_SECRET);\r\n\r\n  try {\r\n    const { payload } = await jwtVerify(token, secret, {\r\n      issuer: ISSUER,\r\n    });\r\n\r\n    // Check for revocation\r\n    const db = createDb();\r\n    const isRevoked = await db.query.revoked_credentials.findFirst({\r\n      where: eq(revoked_credentials.jti, payload.jti!),\r\n    });\r\n\r\n    if (isRevoked) {\r\n      return null;\r\n    }\r\n\r\n    return payload;\r\n  } catch (error) {\r\n    // This will catch expired tokens, invalid signatures, etc.\r\n    return null;\r\n  }\r\n} ", "import { NextResponse } from \"next/server\";\r\nimport { auth } from \"@/lib/auth\";\r\nimport { createDb } from \"@/lib/db\";\r\nimport { emails } from \"@/lib/schema\";\r\nimport { eq, and } from \"drizzle-orm\";\r\nimport { generateAddressCredential } from \"@/lib/jwt\";\r\n\r\nexport const runtime = \"edge\";\r\n\r\nexport async function POST(\r\n  request: Request,\r\n  { params }: { params: { address: string } }\r\n) {\r\n  const session = await auth();\r\n  if (!session?.user?.id) {\r\n    return NextResponse.json({ error: \"Unauthorized\" }, { status: 401 });\r\n  }\r\n\r\n  const db = createDb();\r\n  const emailAddress = decodeURIComponent(params.address);\r\n\r\n  try {\r\n    const email = await db.query.emails.findFirst({\r\n      where: and(\r\n        eq(emails.address, emailAddress),\r\n        eq(emails.userId, session.user.id)\r\n      ),\r\n    });\r\n\r\n    if (!email) {\r\n      return NextResponse.json(\r\n        { error: \"Email not found or access denied\" },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    const { expiresIn = 3600 } = (await request.json().catch(() => ({}))) as {\r\n      expiresIn?: number;\r\n    };\r\n\r\n    const expiresAt = expiresIn === 0 ? 0 : expiresIn * 1000;\r\n    \r\n    const credential = await generateAddressCredential(email.address, expiresIn);\r\n\r\n    const expiresAtDate = expiresIn === 0 \r\n      ? new Date('9999-01-01T00:00:00.000Z')\r\n      : new Date(Date.now() + expiresAt);\r\n\r\n    return NextResponse.json({\r\n      credential,\r\n      expiresAt: expiresAtDate.toISOString(),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Failed to generate credential:\", error);\r\n    return NextResponse.json(\r\n      { error: \"Failed to generate credential\" },\r\n      { status: 500 }\r\n    );\r\n  }\r\n} ", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\emails\\\\[address]\\\\credentials\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/emails/[address]/credentials/route\",\n        pathname: \"/api/emails/[address]/credentials\",\n        filename: \"route\",\n        bundlePath: \"app/api/emails/[address]/credentials/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\emails\\\\[address]\\\\credentials\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Femails%2F%5Baddress%5D%2Fcredentials%2Froute&page=%2Fapi%2Femails%2F%5Baddress%5D%2Fcredentials%2Froute&pagePath=private-next-app-dir%2Fapi%2Femails%2F%5Baddress%5D%2Fcredentials%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fapi%2Femails%2F%5Baddress%5D%2Fcredentials%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/emails/[address]/credentials/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/emails/[address]/credentials/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/emails/[address]/credentials/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { createDb } from \"./db\"\r\nimport { api<PERSON><PERSON>s } from \"./schema\"\r\nimport { eq, and, gt } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport type { User } from \"next-auth\"\r\nimport { auth } from \"./auth\"\r\nimport { headers } from \"next/headers\"\r\n\r\nasync function getUserByApiKey(key: string): Promise<User | null> {\r\n  const db = createDb()\r\n  const apiKey = await db.query.apiKeys.findFirst({\r\n    where: and(\r\n      eq(apiKeys.key, key),\r\n      eq(apiKeys.enabled, true),\r\n      gt(apiKeys.expiresAt, new Date())\r\n    ),\r\n    with: {\r\n      user: true\r\n    }\r\n  })\r\n\r\n  if (!apiKey) return null\r\n\r\n  return apiKey.user\r\n}\r\n\r\nexport async function handleApiKeyAuth(apiKey: string, pathname: string) {\r\n  if (!pathname.startsWith('/api/emails') && !pathname.startsWith('/api/config')) {\r\n    return NextResponse.json(\r\n      { error: \"无权限查看\" },\r\n      { status: 403 }\r\n    )\r\n  }\r\n\r\n  const user = await getUserByApiKey(apiKey)\r\n  if (!user?.id) {\r\n    return NextResponse.json(\r\n      { error: \"无效的 API Key\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  const response = NextResponse.next()\r\n  response.headers.set(\"X-User-Id\", user.id)\r\n  return response\r\n}\r\n\r\nexport const getUserId = async () => {\r\n  const headersList = await headers()\r\n  const userId = headersList.get(\"X-User-Id\")\r\n  \r\n  if (userId) return userId\r\n\r\n  const session = await auth()\r\n\r\n  return session?.user.id\r\n}\r\n", "const COLORS = [\r\n  '#2196F3', // 蓝色\r\n  '#009688', // 青色\r\n  '#9C27B0', // 紫色\r\n  '#F44336', // 红色\r\n  '#673AB7', // 深紫色\r\n  '#3F51B5', // 靛蓝\r\n  '#4CAF50', // 绿色\r\n  '#FF5722', // 深橙\r\n  '#795548', // 棕色\r\n  '#607D8B', // 蓝灰\r\n];\r\n\r\nexport function generateAvatarUrl(name: string): string {\r\n  const initial = name[0].toUpperCase();\r\n  \r\n  const colorIndex = Array.from(name).reduce(\r\n    (acc, char) => acc + char.charCodeAt(0), 0\r\n  ) % COLORS.length;\r\n  \r\n  const backgroundColor = COLORS[colorIndex];\r\n  \r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\" width=\"100\" height=\"100\">\r\n      <rect width=\"100\" height=\"100\" fill=\"${backgroundColor}\"/>\r\n      <text \r\n        x=\"50%\" \r\n        y=\"50%\" \r\n        fill=\"white\" \r\n        font-family=\"system-ui, -apple-system, sans-serif\" \r\n        font-size=\"45\" \r\n        font-weight=\"500\"\r\n        text-anchor=\"middle\"\r\n        alignment-baseline=\"central\"\r\n        dominant-baseline=\"central\"\r\n        style=\"text-transform: uppercase\"\r\n      >\r\n        ${initial}\r\n      </text>\r\n    </svg>\r\n  `.trim();\r\n\r\n  const encoder = new TextEncoder();\r\n  const bytes = encoder.encode(svg);\r\n  const base64 = Buffer.from(bytes).toString('base64');\r\n  \r\n  return `data:image/svg+xml;base64,${base64}`;\r\n} ", "import NextAuth from \"next-auth\"\r\nimport GitHub from \"next-auth/providers/github\"\r\nimport { Dr<PERSON>zleAdapter } from \"@auth/drizzle-adapter\"\r\nimport { createDb, Db } from \"./db\"\r\nimport { accounts, users, roles, userRoles } from \"./schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { Permission, hasPermission, ROLES, Role } from \"./permissions\"\r\nimport CredentialsProvider from \"next-auth/providers/credentials\"\r\nimport { hashPassword, comparePassword } from \"@/lib/utils\"\r\nimport { authSchema } from \"@/lib/validation\"\r\nimport { generateAvatarUrl } from \"./avatar\"\r\nimport { getUserId } from \"./apiKey\"\r\n\r\nconst ROLE_DESCRIPTIONS: Record<Role, string> = {\r\n  [ROLES.EMPEROR]: \"皇帝（网站所有者）\",\r\n  [ROLES.DUKE]: \"公爵（超级用户）\",\r\n  [ROLES.KNIGHT]: \"骑士（高级用户）\",\r\n  [ROLES.CIVILIAN]: \"平民（普通用户）\",\r\n}\r\n\r\nconst getDefaultRole = async (): Promise<Role> => {\r\n  const defaultRole = await getRequestContext().env.SITE_CONFIG.get(\"DEFAULT_ROLE\")\r\n\r\n  if (\r\n    defaultRole === ROLES.DUKE ||\r\n    defaultRole === ROLES.KNIGHT ||\r\n    defaultRole === ROLES.CIVILIAN\r\n  ) {\r\n    return defaultRole as Role\r\n  }\r\n  \r\n  return ROLES.CIVILIAN\r\n}\r\n\r\nasync function findOrCreateRole(db: Db, roleName: Role) {\r\n  let role = await db.query.roles.findFirst({\r\n    where: eq(roles.name, roleName),\r\n  })\r\n\r\n  if (!role) {\r\n    const [newRole] = await db.insert(roles)\r\n      .values({\r\n        name: roleName,\r\n        description: ROLE_DESCRIPTIONS[roleName],\r\n      })\r\n      .returning()\r\n    role = newRole\r\n  }\r\n\r\n  return role\r\n}\r\n\r\nexport async function assignRoleToUser(db: Db, userId: string, roleId: string) {\r\n  await db.delete(userRoles)\r\n    .where(eq(userRoles.userId, userId))\r\n\r\n  await db.insert(userRoles)\r\n    .values({\r\n      userId,\r\n      roleId,\r\n    })\r\n}\r\n\r\nexport async function getUserRole(userId: string) {\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n  return userRoleRecords[0].role.name\r\n}\r\n\r\nexport async function checkPermission(permission: Permission) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) return false\r\n\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n\r\n  const userRoleNames = userRoleRecords.map(ur => ur.role.name)\r\n  return hasPermission(userRoleNames as Role[], permission)\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  auth,\r\n  signIn,\r\n  signOut\r\n} = NextAuth(() => ({\r\n  secret: process.env.AUTH_SECRET,\r\n  adapter: DrizzleAdapter(createDb(), {\r\n    usersTable: users,\r\n    accountsTable: accounts,\r\n  }),\r\n  providers: [\r\n    GitHub({\r\n      clientId: process.env.AUTH_GITHUB_ID,\r\n      clientSecret: process.env.AUTH_GITHUB_SECRET,\r\n    }),\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        username: { label: \"用户名\", type: \"text\", placeholder: \"请输入用户名\" },\r\n        password: { label: \"密码\", type: \"password\", placeholder: \"请输入密码\" },\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials) {\r\n          throw new Error(\"请输入用户名和密码\")\r\n        }\r\n\r\n        const { username, password } = credentials\r\n\r\n        try {\r\n          authSchema.parse({ username, password })\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        } catch (error) {\r\n          throw new Error(\"输入格式不正确\")\r\n        }\r\n\r\n        const db = createDb()\r\n\r\n        const user = await db.query.users.findFirst({\r\n          where: eq(users.username, username as string),\r\n        })\r\n\r\n        if (!user) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        const isValid = await comparePassword(password as string, user.password as string)\r\n        if (!isValid) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        return {\r\n          ...user,\r\n          password: undefined,\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  events: {\r\n    async signIn({ user }) {\r\n      if (!user.id) return\r\n\r\n      try {\r\n        const db = createDb()\r\n        const existingRole = await db.query.userRoles.findFirst({\r\n          where: eq(userRoles.userId, user.id),\r\n        })\r\n\r\n        if (existingRole) return\r\n\r\n        const defaultRole = await getDefaultRole()\r\n        const role = await findOrCreateRole(db, defaultRole)\r\n        await assignRoleToUser(db, user.id, role.id)\r\n      } catch (error) {\r\n        console.error('Error assigning role:', error)\r\n      }\r\n    },\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id\r\n        token.name = user.name || user.username\r\n        token.username = user.username\r\n        token.image = user.image || generateAvatarUrl(token.name as string)\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token && session.user) {\r\n        session.user.id = token.id as string\r\n        session.user.name = token.name as string\r\n        session.user.username = token.username as string\r\n        session.user.image = token.image as string\r\n\r\n        const db = createDb()\r\n        let userRoleRecords = await db.query.userRoles.findMany({\r\n          where: eq(userRoles.userId, session.user.id),\r\n          with: { role: true },\r\n        })\r\n  \r\n        if (!userRoleRecords.length) {\r\n          const defaultRole = await getDefaultRole()\r\n          const role = await findOrCreateRole(db, defaultRole)\r\n          await assignRoleToUser(db, session.user.id, role.id)\r\n          userRoleRecords = [{\r\n            userId: session.user.id,\r\n            roleId: role.id,\r\n            createdAt: new Date(),\r\n            role: role\r\n          }]\r\n        }\r\n  \r\n        session.user.roles = userRoleRecords.map(ur => ({\r\n          name: ur.role.name,\r\n        }))\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: \"jwt\",\r\n  },\r\n}))\r\n\r\nexport async function register(username: string, password: string) {\r\n  const db = createDb()\r\n  \r\n  const existing = await db.query.users.findFirst({\r\n    where: eq(users.username, username)\r\n  })\r\n\r\n  if (existing) {\r\n    throw new Error(\"用户名已存在\")\r\n  }\r\n\r\n  const hashedPassword = await hashPassword(password)\r\n  \r\n  const [user] = await db.insert(users)\r\n    .values({\r\n      username,\r\n      password: hashedPassword,\r\n    })\r\n    .returning()\r\n\r\n  return user\r\n}\r\n", "import { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { drizzle } from \"drizzle-orm/d1\"\r\nimport * as schema from \"./schema\"\r\n\r\nexport const createDb = () => drizzle(getRequestContext().env.DB, { schema })\r\n\r\nexport type Db = ReturnType<typeof createDb>\r\n", "export const ROLES = {\r\n  EMPEROR: 'emperor',\r\n  <PERSON>U<PERSON>: 'duke',\r\n  <PERSON><PERSON><PERSON><PERSON>: 'knight',\r\n  CIVILIAN: 'civilian',\r\n} as const;\r\n\r\nexport type Role = typeof ROLES[keyof typeof ROLES];\r\n\r\nexport const PERMISSIONS = {\r\n  MANAGE_EMAIL: 'manage_email',\r\n  <PERSON><PERSON>GE_WEBHOOK: 'manage_webhook',\r\n  PROMOTE_USER: 'promote_user',\r\n  MANAGE_CONFIG: 'manage_config',\r\n  MANAGE_API_KEY: 'manage_api_key',\r\n} as const;\r\n\r\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\r\n\r\nexport const ROLE_PERMISSIONS: Record<Role, Permission[]> = {\r\n  [ROLES.EMPEROR]: Object.values(PERMISSIONS),\r\n  [ROLES.DUKE]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_API_KEY,\r\n  ],\r\n  [ROLES.KNIGHT]: [\r\n    PERMISSIONS.<PERSON>NAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n  ],\r\n  [ROLES.CIVILIAN]: [],\r\n} as const;\r\n\r\nexport function hasPermission(userRoles: Role[], permission: Permission): boolean {\r\n  return userRoles.some(role => ROLE_PERMISSIONS[role]?.includes(permission));\r\n} ", "import { integer, sqliteTable, text, primaryKey, uniqueIndex, index } from \"drizzle-orm/sqlite-core\"\r\nimport type { AdapterAccountType } from \"next-auth/adapters\"\r\nimport { relations } from 'drizzle-orm';\r\n\r\n// https://authjs.dev/getting-started/adapters/drizzle\r\nexport const users = sqliteTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: integer(\"emailVerified\", { mode: \"timestamp_ms\" }),\r\n  image: text(\"image\"),\r\n  username: text(\"username\").unique(),\r\n  password: text(\"password\"),\r\n})\r\nexport const accounts = sqliteTable(\r\n  \"account\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => users.id, { onDelete: \"cascade\" }),\r\n    type: text(\"type\").$type<AdapterAccountType>().notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n  },\r\n  (account) => ({\r\n    compoundKey: primaryKey({\r\n      columns: [account.provider, account.providerAccountId],\r\n    }),\r\n  })\r\n)\r\n\r\nexport const emails = sqliteTable(\"email\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  address: text(\"address\").notNull().unique(),\r\n  userId: text(\"userId\").references(() => users.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp_ms\" }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"email_expires_at_idx\").on(table.expiresAt),\r\n}))\r\n\r\nexport const messages = sqliteTable(\"message\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  emailId: text(\"emailId\")\r\n    .notNull()\r\n    .references(() => emails.id, { onDelete: \"cascade\" }),\r\n  fromAddress: text(\"from_address\").notNull(),\r\n  subject: text(\"subject\").notNull(),\r\n  content: text(\"content\").notNull(),\r\n  html: text(\"html\"),\r\n  receivedAt: integer(\"received_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  emailIdIdx: index(\"message_email_id_idx\").on(table.emailId),\r\n}))\r\n\r\nexport const webhooks = sqliteTable('webhook', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id')\r\n    .notNull()\r\n    .references(() => users.id, { onDelete: \"cascade\" }),\r\n  url: text('url').notNull(),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n})\r\n\r\nexport const roles = sqliteTable(\"role\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\").notNull(),\r\n  description: text(\"description\"),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n});\r\n\r\nexport const userRoles = sqliteTable(\"user_role\", {\r\n  userId: text(\"user_id\").notNull().references(() => users.id, { onDelete: \"cascade\" }),\r\n  roleId: text(\"role_id\").notNull().references(() => roles.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  pk: primaryKey({ columns: [table.userId, table.roleId] }),\r\n}));\r\n\r\nexport const apiKeys = sqliteTable('api_keys', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id),\r\n  name: text('name').notNull(),\r\n  key: text('key').notNull().unique(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n}, (table) => ({\r\n  nameUserIdUnique: uniqueIndex('name_user_id_unique').on(table.name, table.userId)\r\n}));\r\n\r\nexport const revoked_credentials = sqliteTable('revoked_credential', {\r\n  jti: text('jti').primaryKey(),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp_ms' }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"revoked_expires_at_idx\").on(table.expiresAt),\r\n}));\r\n\r\nexport const apiKeysRelations = relations(apiKeys, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [apiKeys.userId],\r\n    references: [users.id],\r\n  }),\r\n}));\r\n\r\nexport const userRolesRelations = relations(userRoles, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userRoles.userId],\r\n    references: [users.id],\r\n  }),\r\n  role: one(roles, {\r\n    fields: [userRoles.roleId],\r\n    references: [roles.id],\r\n  }),\r\n}));\r\n\r\nexport const usersRelations = relations(users, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n  apiKeys: many(apiKeys),\r\n}));\r\n\r\nexport const rolesRelations = relations(roles, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n}));", "import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport async function hashPassword(password: string): Promise<string> {\r\n  const encoder = new TextEncoder()\r\n  const salt = process.env.AUTH_SECRET || ''\r\n  const data = encoder.encode(password + salt)\r\n  const hash = await crypto.subtle.digest('SHA-256', data)\r\n  return btoa(String.fromCharCode(...new Uint8Array(hash)))\r\n}\r\n\r\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\r\n  const hash = await hashPassword(password)\r\n  return hash === hashedPassword\r\n}", "import { z } from \"zod\"\r\n\r\nexport const authSchema = z.object({\r\n  username: z.string()\r\n    .min(1, \"用户名不能为空\")\r\n    .max(20, \"用户名不能超过20个字符\")\r\n    .regex(/^[a-zA-Z0-9_-]+$/, \"用户名只能包含字母、数字、下划线和横杠\")\r\n    .refine(val => !val.includes('@'), \"用户名不能是邮箱格式\"),\r\n  password: z.string()\r\n    .min(8, \"密码长度必须大于等于8位\")\r\n})\r\n\r\nexport type AuthSchema = z.infer<typeof authSchema>", "export const urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n", "/* @ts-self-types=\"./index.d.ts\" */\nimport { url<PERSON>lphabet as scopedUrlAlphabet } from './url-alphabet/index.js'\nexport { urlAlphabet } from './url-alphabet/index.js'\nexport let random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nexport let customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << Math.log2(alphabet.length - 1)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length >= size) return id\n      }\n    }\n  }\n}\nexport let customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size | 0, random)\nexport let nanoid = (size = 21) => {\n  let id = ''\n  let bytes = crypto.getRandomValues(new Uint8Array((size |= 0)))\n  while (size--) {\n    id += scopedUrlAlphabet[bytes[size] & 63]\n  }\n  return id\n}\n"], "names": ["JWT_ADDRESS_SECRET", "process", "env", "generateAddressCredential", "emailAddress", "expiresIn", "secret", "TextEncoder", "encode", "SignJWT", "alg", "setSubject", "<PERSON><PERSON><PERSON>", "nanoid", "setIssuedAt", "<PERSON><PERSON><PERSON><PERSON>", "ISSUER", "setAudience", "setExpirationTime", "sign", "runtime", "POST", "request", "params", "session", "auth", "user", "id", "NextResponse", "json", "error", "status", "db", "createDb", "decodeURIComponent", "address", "email", "query", "emails", "<PERSON><PERSON><PERSON><PERSON>", "where", "and", "eq", "userId", "catch", "expiresAt", "credential", "expiresAtDate", "Date", "now", "toISOString", "console", "getUserId", "headersList", "headers", "get", "COLORS", "ROLE_DESCRIPTIONS", "ROLES", "EMPEROR", "DUKE", "KNIGHT", "CIVILIAN", "getDefaultRole", "defaultRole", "getRequestContext", "SITE_CONFIG", "findOrCreateRole", "<PERSON><PERSON><PERSON>", "role", "roles", "name", "newRole", "insert", "values", "description", "returning", "assignRoleToUser", "roleId", "delete", "userRoles", "checkPermission", "permission", "userRoleNames", "userRoleRecords", "find<PERSON>any", "with", "map", "ur", "hasPermission", "handlers", "GET", "signIn", "signOut", "NextAuth", "AUTH_SECRET", "adapter", "DrizzleAdapter", "usersTable", "users", "accountsTable", "accounts", "providers", "GitHub", "clientId", "AUTH_GITHUB_ID", "clientSecret", "AUTH_GITHUB_SECRET", "CredentialsProvider", "credentials", "username", "label", "type", "placeholder", "password", "authorize", "authSchema", "parse", "comparePassword", "undefined", "events", "existingRole", "callbacks", "jwt", "token", "image", "initial", "generateAvatarUrl", "toUpperCase", "colorIndex", "Array", "from", "reduce", "acc", "char", "charCodeAt", "length", "backgroundColor", "svg", "trim", "bytes", "base64", "<PERSON><PERSON><PERSON>", "toString", "createdAt", "strategy", "register", "existing", "hashedPassword", "hashPassword", "drizzle", "DB", "schema", "MANAGE_EMAIL", "MANAGE_WEBHOOK", "PROMOTE_USER", "MANAGE_CONFIG", "MANAGE_API_KEY", "Object", "PERMISSIONS", "some", "ROLE_PERMISSIONS", "includes", "sqliteTable", "text", "<PERSON><PERSON><PERSON>", "$defaultFn", "crypto", "randomUUID", "unique", "emailVerified", "integer", "mode", "notNull", "references", "onDelete", "$type", "provider", "providerAccountId", "refresh_token", "access_token", "expires_at", "token_type", "scope", "id_token", "session_state", "<PERSON><PERSON><PERSON>", "columns", "account", "expiresAtIdx", "index", "on", "table", "messages", "emailId", "fromAddress", "subject", "content", "html", "receivedAt", "emailIdIdx", "webhooks", "url", "enabled", "default", "updatedAt", "pk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "nameUserIdUnique", "uniqueIndex", "revoked_credentials", "jti", "apiKeysRelations", "relations", "one", "fields", "userRolesRelations", "usersRelations", "many", "rolesRelations", "cn", "inputs", "twMerge", "clsx", "encoder", "salt", "data", "btoa", "String", "fromCharCode", "Uint8Array", "subtle", "digest", "z", "min", "max", "regex", "refine", "val"], "sourceRoot": "", "ignoreList": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 38, 39]}