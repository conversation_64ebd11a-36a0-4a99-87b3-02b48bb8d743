# Moemail Credential System - Architectural Design

*   **Version:** 1.2
*   **Created_At:** 2025-06-07 16:28:27 +08:00
*   **Last_Updated:** 2025-06-07 16:50:14 +08:00
*   **Author:** AR (Architect)
*   **Status:** Revised based on user feedback

---

## Update Log
*   **2025-06-07 16:49:47 +08:00 (v1.2):** Corrected section numbering. Added new sections for Testing Strategy, Deployment, Migration, and recommended libraries based on user feedback.
*   **2025-06-07 16:34:12 +08:00 (v1.1):** Added sections for Error Handling, Revocation, Monitoring, etc.
*   **2025-06-07 16:28:27 +08:00 (v1.0):** Initial draft.

---

## 1. Overview

This document outlines the architecture for the **Temporary Email Credential System**. The chosen approach is a **JWT-based Bearer Token**. This revision (v1.2) incorporates detailed user feedback on error handling, revocation, monitoring, testing strategy, deployment considerations, and other critical areas.

## 2. Core Components

### 2.1. Recommended Libraries
For all JWT operations (signing, verifying, decoding), we recommend using the **`jose`** library. It is a robust, modern, zero-dependency library that supports a wide range of cryptographic algorithms and is well-suited for environments like Cloudflare Workers and Next.js.

### 2.2. JWT Structure (The "Address Credential")

The JWT payload will be updated to include a JWT ID (`jti`) for revocation purposes.

```json
{
  "iat": 1672531200, // Issued At (Unix timestamp)
  "exp": 1672617600, // Expiration Time (Unix timestamp)
  "aud": "moemail:mailbox:access", // Audience
  "iss": "moemail:api", // Issuer
  "sub": "<EMAIL>", // Subject (The email address itself)
  "jti": "d5f6e7b8-c9a0-4d1e-8f2a-1b3c4d5e6f7a" // JWT ID, for revocation
}
```

### 2.3. API Endpoints

1.  **Modify `POST /api/emails/generate` (Existing Endpoint)**
    *   **Description:** When a new temporary email address is created, the response body will be augmented to include the newly generated "Address Credential".
    *   **Response Body (Addition):**
        ```json
        {
          // ... existing fields (id, email)
          "credential": "ey..." // The JWT string
        }
        ```

2.  **Create `POST /api/emails/{emailAddress}/credentials` (New Endpoint)**
    *   **Description:** Allows an authenticated user (via the primary GitHub OAuth session) to generate a new "Address Credential" for an email address they own. This is useful if they lose the original credential or it expires.
    *   **Path Parameter:** `emailAddress` - The url-encoded email address.
    *   **Request Body:**
        ```json
        {
          "expiresIn": 3600 // Optional: TTL in seconds. Defaults to a system-wide value. 0 for permanent.
        }
        ```
    *   **Response Body:**
        ```json
        {
          "credential": "ey...", // The JWT string
          "expiresAt": "2025-01-01T12:00:00Z"
        }
        ```

### 2.4. Next.js API Middleware

The middleware is the core enforcement mechanism. Here is a pseudo-code representation of its logic:

```typescript
// moemail_source/src/middleware.ts (illustrative pseudo-code)
import { NextRequest, NextResponse } from 'next/server';
import { jwtVerify } from 'jose';
import { isTokenRevoked } from './lib/db/revocation'; // DB check function

const JWT_ADDRESS_SECRET = new TextEncoder().encode(process.env.JWT_ADDRESS_SECRET);

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  if (pathname.startsWith('/api/emails/') && !pathname.endsWith('/credentials')) {
    const authHeader = req.headers.get('authorization');
    const token = authHeader?.split(' ')[1];

    if (!token) {
      return new NextResponse(JSON.stringify({ error: 'missing_token', message: 'Authentication token not provided.' }), { status: 401 });
    }

    try {
      const { payload } = await jwtVerify(token, JWT_ADDRESS_SECRET, {
        audience: 'moemail:mailbox:access',
        issuer: 'moemail:api',
      });

      // Check revocation
      if (await isTokenRevoked(payload.jti)) {
        return new NextResponse(JSON.stringify({ error: 'token_revoked', message: 'Token has been revoked.' }), { status: 401 });
      }

      // Match subject with URL
      const emailFromUrl = pathname.split('/')[3];
      if (payload.sub !== decodeURIComponent(emailFromUrl)) {
        return new NextResponse(JSON.stringify({ error: 'permission_denied', message: 'Token not valid for this mailbox.' }), { status: 403 });
      }

      // All checks passed
      return NextResponse.next();

    } catch (err) {
      // Catches expired tokens, invalid signatures, etc.
      return new NextResponse(JSON.stringify({ error: 'invalid_token', message: 'Invalid or expired token.' }), { status: 401 });
    }
  }

  return NextResponse.next();
}
```

#### 2.4.1 Error Handling

Middleware and API endpoints will return standardized error responses.

- **401 Unauthorized**: When no credential is provided, the token is malformed, expired, or revoked.
- **403 Forbidden**: When the credential is valid but not authorized for the requested resource (e.g., `sub` mismatch).

The error response body will follow this JSON format:
```json
{
  "error": "invalid_token", // A machine-readable error code
  "message": "The provided access token is invalid or has expired.", // A human-readable message (to be internationalized)
  "details": {} // Optional additional information
}
```

### 2.5 Credential Management

#### 2.5.1 Client-Side Credential Storage
The client-side application should store the credential in `sessionStorage` by default. `sessionStorage` is preferred over `localStorage` for sensitive tokens as it's cleared when the browser tab is closed, reducing the risk of the token persisting longer than necessary on a shared machine. The UI should offer a "remember me" option that moves the token to `localStorage` for convenience, with a clear security warning.

#### 2.5.2 Credential Revocation
To support revocation, the system will implement a lightweight revocation list using a new table in the D1 database.
*   **Table `revoked_credentials`:** `(jti TEXT PRIMARY KEY, expires_at INTEGER)`
*   When a user requests to revoke a token, its `jti` and `exp` claims are added to this table.
*   A scheduled worker will periodically clean up expired `jti` entries from the table.

### 2.6 Integration with Existing Authentication

The two systems will coexist:
1.  **User Authentication (GitHub OAuth):** Used for user-centric actions: creating new addresses, managing account settings, and generating/revoking Address Credentials. This authenticates the **person**.
2.  **Address Credential (JWT):** Used for direct, programmatic, or shared access to a specific **mailbox**. It authenticates the **request** for mailbox data.
*   An endpoint like `POST /api/emails/{emailAddress}/credentials` **requires** User Authentication to prove ownership before it will issue a new Address Credential.

## 3. Security Considerations

(This section is expanded based on feedback.)

*   **Secret Management:** A new secret (`JWT_ADDRESS_SECRET`) is required.
*   **Token Expiration (TTL):** Default TTL will be short (24 hours).
*   **Transport Security:** Must use HTTPS.

### 3.1 Monitoring and Auditing

The system will log the following credential-related events to provide an audit trail for security monitoring:
-   **Credential Generation:** Log the `jti`, associated `sub` (email address), and `exp`. Do NOT log the credential itself.
-   **Credential Validation Failure:** Log the reason for failure (e.g., "expired", "invalid_signature", "revoked"), the `jti` if available, and the source IP address.
-   **Credential Revocation:** Log the `jti` of the token that was revoked.

## 4. Performance Considerations

The JWT validation process itself is computationally cheap. The main performance consideration introduced in v1.1 is the revocation check, which requires a database query.
*   **Benchmark:** We will benchmark the D1 query time for the revocation check under load.
*   **Optimization:** The `revoked_credentials` table will be indexed on `jti`. For very high-traffic scenarios, a Cloudflare KV store could be considered for faster lookups, though D1 is sufficient for the expected initial load.

## 5. Testing Strategy

A multi-layered testing approach is required:
*   **Unit Tests:** Each new utility function (e.g., `generateAddressCredential`, `isTokenRevoked`) will have dedicated unit tests covering its logic and edge cases. API route handlers will be unit-tested with mocked services.
*   **Integration Tests:** We will write integration tests for the API endpoints. These tests will make real HTTP requests to the endpoints and verify the entire flow, including middleware execution, database interaction (for revocation), and the final response. We will test both success and failure paths extensively.
*   **End-to-End (E2E) Tests:** Using Playwright, we will simulate a full user journey: logging in, creating an email, copying the credential, logging out, and then accessing the mailbox using only the credential. We will also test the "Revoke" button functionality from the UI.

## 6. Deployment & Configuration

*   **Environment Variables:** The `JWT_ADDRESS_SECRET` is a critical secret. It must be configured as an environment variable in the deployment platform (e.g., Cloudflare Pages settings). It must never be hardcoded or committed to the repository.
*   **Database Migrations:** The Drizzle migration to create the `revoked_credentials` table must be run as part of the deployment process. This can be scripted and executed via the Cloudflare Wrangler CLI.

## 7. Migration Strategy

Since the existing system has no credential concept, the migration path is straightforward. This is a net-new feature.
*   **Phase 1 (Implementation):** The new credential system is built and deployed. Existing user sessions and mailboxes are unaffected.
*   **Phase 2 (Rollout):** The UI is updated to expose the credential generation and usage features. Users can now generate credentials for their existing mailboxes. No data migration is required. Access to mailboxes via the standard authenticated UI remains unchanged.

## 8. Architectural Principles Compliance

*   **KISS:** The solution uses a standard, well-understood technology (JWT) in a straightforward way.
*   **Statelessness:** The validation is primarily stateless. The revocation check adds a minimal stateful component, which is a necessary trade-off for the added security.
*   **High Cohesion / Low Coupling:** The authentication/authorization logic is encapsulated within the middleware, cleanly separating it from the API business logic.

