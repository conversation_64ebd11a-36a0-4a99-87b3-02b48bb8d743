# Moemail Credential System - Architectural Design

*   **Version:** 1.1
*   **Created_At:** 2025-06-07 16:28:27 +08:00
*   **Last_Updated:** 2025-06-07 16:34:12 +08:00
*   **Author:** AR (Architect)
*   **Status:** Revised based on user feedback

---

## Update Log
*   **2025-06-07 16:34:12 +08:00 (v1.1):** Revised based on user feedback. Added detailed sections for Error Handling, Credential Management (including revocation), Monitoring, and clarified integration with existing auth systems.
*   **2025-06-07 16:28:27 +08:00 (v1.0):** Initial draft created by AR.

---

## 1. Overview

This document outlines the architecture for the **Temporary Email Credential System**. The chosen approach is a **JWT-based Bear<PERSON>**. This revision (v1.1) incorporates detailed user feedback on error handling, revocation, monitoring, and other critical areas.

## 2. Core Components

### 2.1. JWT Structure (The "Address Credential")

The JWT payload will be updated to include a JWT ID (`jti`) for revocation purposes.

```json
{
  "iat": 1672531200, // Issued At (Unix timestamp)
  "exp": 1672617600, // Expiration Time (Unix timestamp)
  "aud": "moemail:mailbox:access", // Audience
  "iss": "moemail:api", // Issuer
  "sub": "<EMAIL>", // Subject (The email address itself)
  "jti": "d5f6e7b8-c9a0-4d1e-8f2a-1b3c4d5e6f7a" // JWT ID, for revocation
}
```

### 2.2. API Endpoints

1.  **Modify `POST /api/emails/generate` (Existing Endpoint)**
    *   **Description:** When a new temporary email address is created, the response body will be augmented to include the newly generated "Address Credential".
    *   **Response Body (Addition):**
        ```json
        {
          // ... existing fields (id, email)
          "credential": "ey..." // The JWT string
        }
        ```

2.  **Create `POST /api/emails/{emailAddress}/credentials` (New Endpoint)**
    *   **Description:** Allows an authenticated user (via the primary GitHub OAuth session) to generate a new "Address Credential" for an email address they own. This is useful if they lose the original credential or it expires.
    *   **Path Parameter:** `emailAddress` - The url-encoded email address.
    *   **Request Body:**
        ```json
        {
          "expiresIn": 3600 // Optional: TTL in seconds. Defaults to a system-wide value. 0 for permanent.
        }
        ```
    *   **Response Body:**
        ```json
        {
          "credential": "ey...", // The JWT string
          "expiresAt": "2025-01-01T12:00:00Z"
        }
        ```

### 2.3. Next.js API Middleware

The middleware logic is expanded to include revocation checks and standardized error responses.

*   **Logic:**
    1.  The middleware will apply to routes matching `/api/emails/{emailAddress}/*`.
    2.  It will extract the Bearer token.
    3.  If present, it will:
        a. Decode the JWT (without full verification) to read the `jti` claim.
        b. **(New)** Check if the `jti` exists in a revocation list (e.g., in D1 or a KV store). If so, immediately reject with a 401.
        c. Fully verify the JWT's signature, `exp`, `aud`, `iss`.
        d. Verify the `sub` claim matches the `{emailAddress}` from the URL.
    4.  If validation succeeds, proceed. Otherwise, return a standardized error.

#### 2.3.1 Error Handling

Middleware and API endpoints will return standardized error responses.

- **401 Unauthorized**: When no credential is provided, the token is malformed, expired, or revoked.
- **403 Forbidden**: When the credential is valid but not authorized for the requested resource (e.g., `sub` mismatch).

The error response body will follow this JSON format:
```json
{
  "error": "invalid_token", // A machine-readable error code
  "message": "The provided access token is invalid or has expired.", // A human-readable message (to be internationalized)
  "details": {} // Optional additional information
}
```

### 2.4 Credential Management

#### 2.4.1 Client-Side Credential Storage
The client-side application should store the credential in `sessionStorage` by default. `sessionStorage` is preferred over `localStorage` for sensitive tokens as it's cleared when the browser tab is closed, reducing the risk of the token persisting longer than necessary on a shared machine. The UI should offer a "remember me" option that moves the token to `localStorage` for convenience, with a clear security warning.

#### 2.4.2 Credential Revocation
To support revocation, the system will implement a lightweight revocation list using a new table in the D1 database.
*   **Table `revoked_credentials`:** `(jti TEXT PRIMARY KEY, expires_at INTEGER)`
*   When a user requests to revoke a token, its `jti` and `exp` claims are added to this table.
*   A scheduled worker will periodically clean up expired `jti` entries from the table.

### 2.5 Integration with Existing Authentication

The two systems will coexist:
1.  **User Authentication (GitHub OAuth):** Used for user-centric actions: creating new addresses, managing account settings, and generating/revoking Address Credentials. This authenticates the **person**.
2.  **Address Credential (JWT):** Used for direct, programmatic, or shared access to a specific **mailbox**. It authenticates the **request** for mailbox data.
*   An endpoint like `POST /api/emails/{emailAddress}/credentials` **requires** User Authentication to prove ownership before it will issue a new Address Credential.

## 3. Security Considerations

(This section is expanded based on feedback.)

*   **Secret Management:** A new secret (`JWT_ADDRESS_SECRET`) is required.
*   **Token Expiration (TTL):** Default TTL will be short (24 hours).
*   **Transport Security:** Must use HTTPS.

### 3.1 Monitoring and Auditing

The system will log the following credential-related events to provide an audit trail for security monitoring:
-   **Credential Generation:** Log the `jti`, associated `sub` (email address), and `exp`. Do NOT log the credential itself.
-   **Credential Validation Failure:** Log the reason for failure (e.g., "expired", "invalid_signature", "revoked"), the `jti` if available, and the source IP address.
-   **Credential Revocation:** Log the `jti` of the token that was revoked.

## 4. Performance Considerations

The JWT validation process itself is computationally cheap. The main performance consideration introduced in v1.1 is the revocation check, which requires a database query.
*   **Benchmark:** We will benchmark the D1 query time for the revocation check under load.
*   **Optimization:** The `revoked_credentials` table will be indexed on `jti`. For very high-traffic scenarios, a Cloudflare KV store could be considered for faster lookups, though D1 is sufficient for the expected initial load.

## 5. Internationalization

All user-facing error messages (like those in the standardized error response) will be mapped to i18n keys, allowing the frontend to display translated messages based on the user's language preference.

## 4. Architectural Principles Compliance

*   **KISS:** The solution uses a standard, well-understood technology (JWT) in a straightforward way.
*   **Statelessness:** The validation is stateless, requiring no database lookups, which is ideal for a serverless environment.
*   **High Cohesion / Low Coupling:** The authentication/authorization logic is encapsulated within the middleware, cleanly separating it from the API business logic.

---
## Update Log
*   **2025-06-07 16:28:27 +08:00:** Initial draft (v1.0) created by AR. 