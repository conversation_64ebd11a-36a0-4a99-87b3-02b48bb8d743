(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{5521:e=>{"use strict";e.exports=require("node:async_hooks")},5356:e=>{"use strict";e.exports=require("node:buffer")},6347:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>k,default:()=>_});var a,s={};r.r(s),r.d(s,{ClientPageRoot:()=>p.Fy,ClientSegmentRoot:()=>p.pl,GlobalError:()=>m.default,HTTPAccessFallbackBoundary:()=>p.nQ,LayoutRouter:()=>p.C3,MetadataBoundary:()=>p.qB,OutletBoundary:()=>p.Cr,Postpone:()=>p.fK,RenderFromTemplateContext:()=>p.IY,ViewportBoundary:()=>p.PX,__next_app__:()=>f,actionAsyncStorage:()=>p.sc,collectSegmentData:()=>p.Uy,createMetadataComponents:()=>p.IB,createPrerenderParamsForClientSegment:()=>p.lu,createPrerenderSearchParamsForClientPage:()=>p.jO,createServerParamsForMetadata:()=>p.Kx,createServerParamsForServerSegment:()=>p.LV,createServerSearchParamsForMetadata:()=>p.mh,createServerSearchParamsForServerPage:()=>p.Vv,createTemporaryReferenceSet:()=>p.XI,decodeAction:()=>p.Jk,decodeFormState:()=>p.Am,decodeReply:()=>p.X$,pages:()=>h,patchFetch:()=>p.V5,preconnect:()=>p.kZ,preloadFont:()=>p.PY,preloadStyle:()=>p.vI,prerender:()=>p.CR,renderToReadableStream:()=>p.WK,routeModule:()=>g,serverHooks:()=>p.ge,taintObjectReference:()=>p.N2,tree:()=>u,workAsyncStorage:()=>p.J_,workUnitAsyncStorage:()=>p.FP}),r(9569);var i=r(981),o=r(9040),n=r(7598),l=r(5717),c=r(3307),d=r(6292),m=r(3094),p=r(6953);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,341)),"F:\\CODE\\Project\\Mail\\moemail_source\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4452))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,189)),"F:\\CODE\\Project\\Mail\\moemail_source\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4962)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.bind(r,1648)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.bind(r,6408)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,4452))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],h=["F:\\CODE\\Project\\Mail\\moemail_source\\app\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},g=new c.AppPageRouteModule({definition:{kind:d.A.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}});var x=r(5711),v=r(4194),b=r(7285);globalThis.__nextCacheHandlers||(globalThis.__nextCacheHandlers={});let y=e=>e?JSON.parse(e):void 0,j=self.__BUILD_MANIFEST,N=y(self.__REACT_LOADABLE_MANIFEST),C=null==(a=self.__RSC_MANIFEST)?void 0:a["/page"],P=y(self.__RSC_SERVER_MANIFEST),w=y(self.__NEXT_FONT_MANIFEST),M=y(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST)??[];C&&P&&(0,v.fQ)({page:"/page",clientReferenceManifest:C,serverActionsManifest:P,serverModuleMap:(0,b.e)({serverActionsManifest:P})});let S=(0,o.R)({pagesType:x.g.APP,dev:!1,page:"/page",appMod:null,pageMod:s,errorMod:null,error500Mod:null,Document:null,buildManifest:j,renderToHTML:l.W,reactLoadableManifest:N,clientReferenceManifest:C,serverActionsManifest:P,serverActions:void 0,subresourceIntegrityManifest:void 0,config:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\Mail\\moemail_source",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\Mail\\moemail_source"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\Mail\\moemail_source\\next.config.ts",configFileName:"next.config.ts"},buildId:process.env.__NEXT_BUILD_ID,nextFontManifest:w,incrementalCacheHandler:null,interceptionRouteRewrites:M}),k=s;function _(e){return(0,i.O)({...e,IncrementalCache:n.N,handler:S})}},395:(e,t,r)=>{Promise.resolve().then(r.bind(r,2483)),Promise.resolve().then(r.bind(r,3699)),Promise.resolve().then(r.bind(r,38)),Promise.resolve().then(r.bind(r,9134))},6419:(e,t,r)=>{Promise.resolve().then(r.bind(r,2396)),Promise.resolve().then(r.bind(r,3183)),Promise.resolve().then(r.bind(r,5354)),Promise.resolve().then(r.bind(r,4794))},2396:(e,t,r)=>{"use strict";r.d(t,{SignButton:()=>m});var a=r(9796),s=r(1511),i=r(1813),o=r(4123),n=r(7149),l=r(6079),c=r(772),d=r(2304);function m({size:e="default"}){let t=(0,l.rd)(),{data:r,status:m}=(0,o.wV)();return"loading"===m?(0,a.jsx)("div",{className:"h-9"}):r?.user?(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(c.A,{href:"/profile",className:"flex items-center gap-2 hover:opacity-80 transition-opacity",children:[r.user.image&&(0,a.jsx)(i.A,{src:r.user.image,alt:r.user.name||"用户头像",width:24,height:24,className:"rounded-full"}),(0,a.jsx)("span",{className:"text-sm",children:r.user.name})]}),(0,a.jsx)(s.$,{onClick:()=>(0,o.CI)({callbackUrl:"/"}),variant:"outline",className:(0,d.cn)("flex-shrink-0","lg"===e?"px-8":""),size:e,children:"登出"})]}):(0,a.jsxs)(s.$,{onClick:()=>t.push("/login"),className:(0,d.cn)("gap-2","lg"===e?"px-8":""),size:e,children:[(0,a.jsx)(n.A,{className:"lg"===e?"w-5 h-5":"w-4 h-4"}),"登录/注册"]})}},3183:(e,t,r)=>{"use strict";r.d(t,{ActionButton:()=>l});var a=r(9796),s=r(1511),i=r(5139),o=r(6079),n=r(2396);function l({isLoggedIn:e}){let t=(0,o.rd)();return e?(0,a.jsxs)(s.$,{size:"lg",onClick:()=>t.push("/moe"),className:"gap-2 bg-primary hover:bg-primary/90 text-white px-8",children:[(0,a.jsx)(i.A,{className:"w-5 h-5"}),"进入邮箱"]}):(0,a.jsx)(n.SignButton,{size:"lg"})}},5354:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>l});var a=r(9796),s=r(9205),i=r(7322),o=r(9845),n=r(1511);function l(){let{theme:e,setTheme:t}=(0,o.D)();return(0,a.jsxs)(n.$,{variant:"ghost",size:"icon",onClick:()=>t("light"===e?"dark":"light"),className:"rounded-full",children:[(0,a.jsx)(s.A,{className:"h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(i.A,{className:"absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"切换主题"})]})}},4794:(e,t,r)=>{"use strict";r.d(t,{Logo:()=>i});var a=r(9796),s=r(772);function i(){return(0,a.jsxs)(s.A,{href:"/",className:"flex items-center gap-2 hover:opacity-80 transition-opacity",children:[(0,a.jsx)("div",{className:"relative w-8 h-8",children:(0,a.jsx)("div",{className:"absolute inset-0 grid grid-cols-8 grid-rows-8 gap-px",children:(0,a.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-primary",children:[(0,a.jsx)("path",{d:"M4 8h24v16H4V8z",className:"fill-primary/20"}),(0,a.jsx)("path",{d:"M4 8h24v2H4V8zM4 22h24v2H4v-2z",className:"fill-primary"}),(0,a.jsx)("path",{d:"M14 12h4v4h-4v-4zM12 14h2v4h-2v-4zM18 14h2v4h-2v-4zM14 18h4v2h-4v-2z",className:"fill-primary"}),(0,a.jsx)("path",{d:"M4 8l12 8 12-8",className:"stroke-primary stroke-2",fill:"none"}),(0,a.jsx)("path",{d:"M8 18h2v2H8v-2zM22 18h2v2h-2v-2z",className:"fill-primary/60"}),(0,a.jsx)("path",{d:"M8 14h2v2H8v-2zM22 14h2v2h-2v-2z",className:"fill-primary/40"})]})})}),(0,a.jsx)("span",{className:"font-bold tracking-wider bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600",children:"MoeMail"})]})}},2483:(e,t,r)=>{"use strict";r.d(t,{SignButton:()=>a});let a=(0,r(6853).YR)(function(){throw Error("Attempted to call SignButton() from the server but SignButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\auth\\sign-button.tsx","SignButton")},3699:(e,t,r)=>{"use strict";r.d(t,{ActionButton:()=>a});let a=(0,r(6853).YR)(function(){throw Error("Attempted to call ActionButton() from the server but ActionButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\home\\action-button.tsx","ActionButton")},1024:(e,t,r)=>{"use strict";r.d(t,{Y:()=>n});var a=r(861),s=r(2483),i=r(38),o=r(9134);function n(){return(0,a.jsx)("header",{className:"fixed top-0 left-0 right-0 z-50 h-16 bg-background/80 backdrop-blur-sm border-b",children:(0,a.jsx)("div",{className:"container mx-auto h-full px-4",children:(0,a.jsxs)("div",{className:"h-full flex items-center justify-between",children:[(0,a.jsx)(o.Logo,{}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(i.ThemeToggle,{}),(0,a.jsx)(s.SignButton,{})]})]})})})}},38:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>a});let a=(0,r(6853).YR)(function(){throw Error("Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\theme\\theme-toggle.tsx","ThemeToggle")},9134:(e,t,r)=>{"use strict";r.d(t,{Logo:()=>a});let a=(0,r(6853).YR)(function(){throw Error("Attempted to call Logo() from the server but Logo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\CODE\\Project\\Mail\\moemail_source\\app\\components\\ui\\logo.tsx","Logo")},341:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v,runtime:()=>x});var a=r(861),s=r(1024),i=r(1639),o=r(2136);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,o.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:s="",children:i,iconNode:n,...d},m)=>(0,o.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:l("lucide",s),...d},[...n.map(([e,t])=>(0,o.createElement)(e,t)),...Array.isArray(i)?i:[i]])),m=(e,t)=>{let r=(0,o.forwardRef)(({className:r,...a},s)=>(0,o.createElement)(d,{ref:s,iconNode:t,className:l(`lucide-${n(e)}`,r),...a}));return r.displayName=`${e}`,r},p=m("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),u=m("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),h=m("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var f=r(3699);function g({icon:e,title:t,description:r}){return(0,a.jsx)("div",{className:"p-4 rounded border-2 border-primary/20 hover:border-primary/40 transition-colors bg-white/5 backdrop-blur",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"rounded-lg bg-primary/10 text-primary p-2",children:e}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("h3",{className:"font-bold",children:t}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r})]})]})})}let x="edge";async function v(){let e=await (0,i.j2)();return(0,a.jsx)("div",{className:"bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 h-screen",children:(0,a.jsxs)("div",{className:"container mx-auto h-full px-4 lg:px-8 max-w-[1600px]",children:[(0,a.jsx)(s.Y,{}),(0,a.jsx)("main",{className:"h-full",children:(0,a.jsxs)("div",{className:"h-[calc(100vh-4rem)] flex flex-col items-center justify-center text-center px-4 relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 -z-10 bg-grid-primary/5"}),(0,a.jsxs)("div",{className:"w-full max-w-3xl mx-auto space-y-12 py-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-4xl md:text-5xl font-bold tracking-wider",children:(0,a.jsx)("span",{className:"bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600",children:"MoeMail"})}),(0,a.jsx)("p",{className:"text-lg sm:text-xl text-gray-600 dark:text-gray-300 tracking-wide",children:"萌萌哒临时邮箱服务"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 px-4 sm:px-0",children:[(0,a.jsx)(g,{icon:(0,a.jsx)(p,{className:"w-5 h-5"}),title:"隐私保护",description:"保护您的真实邮箱地址"}),(0,a.jsx)(g,{icon:(0,a.jsx)(u,{className:"w-5 h-5"}),title:"即时收件",description:"实时接收邮件通知"}),(0,a.jsx)(g,{icon:(0,a.jsx)(h,{className:"w-5 h-5"}),title:"自动过期",description:"到期自动失效"})]}),(0,a.jsx)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-4 px-4 sm:px-0",children:(0,a.jsx)(f.ActionButton,{isLoggedIn:!!e})})]})]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[810,702,267,658,51,886,293,559],()=>t(6347));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/page"]=r}]);
//# sourceMappingURL=page.js.map