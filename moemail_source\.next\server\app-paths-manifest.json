{"/_not-found/page": "app/_not-found/page.js", "/api/emails/generate/route": "app/api/emails/generate/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/api/auth/[...auth]/route": "app/api/auth/[...auth]/route.js", "/api/auth/register/route": "app/api/auth/register/route.js", "/api/config/route": "app/api/config/route.js", "/api/api-keys/route": "app/api/api-keys/route.js", "/api/emails/[address]/messages/route": "app/api/emails/[address]/messages/route.js", "/api/api-keys/[id]/route": "app/api/api-keys/[id]/route.js", "/api/emails/[address]/credentials/route": "app/api/emails/[address]/credentials/route.js", "/api/emails/[address]/[messageId]/route": "app/api/emails/[address]/[messageId]/route.js", "/api/emails/route": "app/api/emails/route.js", "/api/roles/promote/route": "app/api/roles/promote/route.js", "/api/roles/users/route": "app/api/roles/users/route.js", "/api/roles/init-emperor/route": "app/api/roles/init-emperor/route.js", "/api/webhook/route": "app/api/webhook/route.js", "/api/emails/[address]/route": "app/api/emails/[address]/route.js", "/api/webhook/test/route": "app/api/webhook/test/route.js", "/login/page": "app/login/page.js", "/profile/page": "app/profile/page.js", "/moe/page": "app/moe/page.js", "/page": "app/page.js"}