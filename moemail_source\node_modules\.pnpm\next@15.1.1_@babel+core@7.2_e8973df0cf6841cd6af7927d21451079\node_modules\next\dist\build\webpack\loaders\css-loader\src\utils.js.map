{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/css-loader/src/utils.ts"], "sourcesContent": ["/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n*/\nimport { fileURLToPath } from 'url'\nimport path from 'path'\n\nimport { urlToRequest } from 'next/dist/compiled/loader-utils3'\nimport modulesValues from 'next/dist/compiled/postcss-modules-values'\nimport localByDefault from 'next/dist/compiled/postcss-modules-local-by-default'\nimport extractImports from 'next/dist/compiled/postcss-modules-extract-imports'\nimport modulesScope from 'next/dist/compiled/postcss-modules-scope'\nimport camelCase from './camelcase'\n\nconst whitespace = '[\\\\x20\\\\t\\\\r\\\\n\\\\f]'\nconst unescapeRegExp = new RegExp(\n  `\\\\\\\\([\\\\da-f]{1,6}${whitespace}?|(${whitespace})|.)`,\n  'ig'\n)\nconst matchNativeWin32Path = /^[A-Z]:[/\\\\]|^\\\\\\\\/i\n\nfunction unescape(str: string) {\n  return str.replace(unescapeRegExp, (_, escaped, escapedWhitespace) => {\n    const high = (`0x${escaped}` as any) - 0x10000\n\n    /* eslint-disable line-comment-position */\n    // NaN means non-codepoint\n    // Workaround erroneous numeric interpretation of +\"0x\"\n    // eslint-disable-next-line no-self-compare\n    return high !== high || escapedWhitespace\n      ? escaped\n      : high < 0\n        ? // BMP codepoint\n          String.fromCharCode(high + 0x10000)\n        : // Supplemental Plane codepoint (surrogate pair)\n          // eslint-disable-next-line no-bitwise\n          String.fromCharCode((high >> 10) | 0xd800, (high & 0x3ff) | 0xdc00)\n    /* eslint-enable line-comment-position */\n  })\n}\n\nfunction normalizePath(file: string) {\n  return path.sep === '\\\\' ? file.replace(/\\\\/g, '/') : file\n}\n\nfunction fixedEncodeURIComponent(str: string) {\n  return str.replace(/[!'()*]/g, (c) => `%${c.charCodeAt(0).toString(16)}`)\n}\n\nfunction normalizeUrl(url: string, isStringValue: boolean) {\n  let normalizedUrl = url\n\n  if (isStringValue && /\\\\(\\n|\\r\\n|\\r|\\f)/.test(normalizedUrl)) {\n    normalizedUrl = normalizedUrl.replace(/\\\\(\\n|\\r\\n|\\r|\\f)/g, '')\n  }\n\n  if (matchNativeWin32Path.test(url)) {\n    try {\n      normalizedUrl = decodeURIComponent(normalizedUrl)\n    } catch (error) {\n      // Ignores invalid and broken URLs and try to resolve them as is\n    }\n\n    return normalizedUrl\n  }\n\n  normalizedUrl = unescape(normalizedUrl)\n\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  if (isDataUrl(url)) {\n    return fixedEncodeURIComponent(normalizedUrl)\n  }\n\n  try {\n    normalizedUrl = decodeURI(normalizedUrl)\n  } catch (error) {\n    // Ignores invalid and broken URLs and try to resolve them as is\n  }\n\n  return normalizedUrl\n}\n\nfunction requestify(url: string, rootContext: string) {\n  if (/^file:/i.test(url)) {\n    return fileURLToPath(url)\n  }\n\n  if (/^[a-z][a-z0-9+.-]*:/i.test(url)) {\n    return url\n  }\n\n  return url.charAt(0) === '/'\n    ? urlToRequest(url, rootContext)\n    : urlToRequest(url)\n}\n\nfunction getFilter(filter: any, resourcePath: string) {\n  return (...args: any[]) => {\n    if (typeof filter === 'function') {\n      return filter(...args, resourcePath)\n    }\n\n    return true\n  }\n}\n\nfunction shouldUseImportPlugin(options: any) {\n  if (options.modules.exportOnlyLocals) {\n    return false\n  }\n\n  if (typeof options.import === 'boolean') {\n    return options.import\n  }\n\n  return true\n}\n\nfunction shouldUseURLPlugin(options: any) {\n  if (options.modules.exportOnlyLocals) {\n    return false\n  }\n\n  if (typeof options.url === 'boolean') {\n    return options.url\n  }\n\n  return true\n}\n\nfunction shouldUseModulesPlugins(options: any) {\n  return options.modules.compileType === 'module'\n}\n\nfunction shouldUseIcssPlugin(options: any) {\n  return options.icss === true || Boolean(options.modules)\n}\n\nfunction getModulesPlugins(options: any, loaderContext: any, meta: any) {\n  const {\n    mode,\n    getLocalIdent,\n    localIdentName,\n    localIdentContext,\n    localIdentHashPrefix,\n    localIdentRegExp,\n  } = options.modules\n\n  let plugins: any[] = []\n\n  try {\n    plugins = [\n      modulesValues,\n      localByDefault({ mode }),\n      extractImports(),\n      modulesScope({\n        generateScopedName(exportName: any) {\n          return getLocalIdent(\n            loaderContext,\n            localIdentName,\n            exportName,\n            {\n              context: localIdentContext,\n              hashPrefix: localIdentHashPrefix,\n              regExp: localIdentRegExp,\n            },\n            meta\n          )\n        },\n        exportGlobals: options.modules.exportGlobals,\n      }),\n    ]\n  } catch (error) {\n    loaderContext.emitError(error)\n  }\n\n  return plugins\n}\n\nconst IS_NATIVE_WIN32_PATH = /^[a-z]:[/\\\\]|^\\\\\\\\/i\nconst ABSOLUTE_SCHEME = /^[a-z0-9+\\-.]+:/i\n\nfunction getURLType(source: string) {\n  if (source[0] === '/') {\n    if (source[1] === '/') {\n      return 'scheme-relative'\n    }\n\n    return 'path-absolute'\n  }\n\n  if (IS_NATIVE_WIN32_PATH.test(source)) {\n    return 'path-absolute'\n  }\n\n  return ABSOLUTE_SCHEME.test(source) ? 'absolute' : 'path-relative'\n}\n\nfunction normalizeSourceMap(map: any, resourcePath: string) {\n  let newMap = map\n\n  // Some loader emit source map as string\n  // Strip any JSON XSSI avoidance prefix from the string (as documented in the source maps specification), and then parse the string as JSON.\n  if (typeof newMap === 'string') {\n    newMap = JSON.parse(newMap)\n  }\n\n  delete newMap.file\n\n  const { sourceRoot } = newMap\n\n  delete newMap.sourceRoot\n\n  if (newMap.sources) {\n    // Source maps should use forward slash because it is URLs (https://github.com/mozilla/source-map/issues/91)\n    // We should normalize path because previous loaders like `sass-loader` using backslash when generate source map\n    newMap.sources = newMap.sources.map((source: string) => {\n      // Non-standard syntax from `postcss`\n      if (source.startsWith('<')) {\n        return source\n      }\n\n      const sourceType = getURLType(source)\n\n      // Do no touch `scheme-relative` and `absolute` URLs\n      if (sourceType === 'path-relative' || sourceType === 'path-absolute') {\n        const absoluteSource =\n          sourceType === 'path-relative' && sourceRoot\n            ? path.resolve(sourceRoot, normalizePath(source))\n            : normalizePath(source)\n\n        return path.relative(path.dirname(resourcePath), absoluteSource)\n      }\n\n      return source\n    })\n  }\n\n  return newMap\n}\n\nfunction getPreRequester({ loaders, loaderIndex }: any) {\n  const cache = Object.create(null)\n\n  return (number: any) => {\n    if (cache[number]) {\n      return cache[number]\n    }\n\n    if (number === false) {\n      cache[number] = ''\n    } else {\n      const loadersRequest = loaders\n        .slice(\n          loaderIndex,\n          loaderIndex + 1 + (typeof number !== 'number' ? 0 : number)\n        )\n        .map((x: any) => x.request)\n        .join('!')\n\n      cache[number] = `-!${loadersRequest}!`\n    }\n\n    return cache[number]\n  }\n}\n\nfunction getImportCode(imports: any, options: any) {\n  let code = ''\n\n  for (const item of imports) {\n    const { importName, url, icss } = item\n\n    if (options.esModule) {\n      if (icss && options.modules.namedExport) {\n        code += `import ${\n          options.modules.exportOnlyLocals ? '' : `${importName}, `\n        }* as ${importName}_NAMED___ from ${url};\\n`\n      } else {\n        code += `import ${importName} from ${url};\\n`\n      }\n    } else {\n      code += `var ${importName} = require(${url});\\n`\n    }\n  }\n\n  return code ? `// Imports\\n${code}` : ''\n}\n\nfunction normalizeSourceMapForRuntime(map: any, loaderContext: any) {\n  const resultMap = map ? map.toJSON() : null\n\n  if (resultMap) {\n    delete resultMap.file\n\n    resultMap.sourceRoot = ''\n\n    resultMap.sources = resultMap.sources.map((source: string) => {\n      // Non-standard syntax from `postcss`\n      if (source.startsWith('<')) {\n        return source\n      }\n\n      const sourceType = getURLType(source)\n\n      if (sourceType !== 'path-relative') {\n        return source\n      }\n\n      const resourceDirname = path.dirname(loaderContext.resourcePath)\n      const absoluteSource = path.resolve(resourceDirname, source)\n      const contextifyPath = normalizePath(\n        path.relative(loaderContext.rootContext, absoluteSource)\n      )\n\n      return `webpack://${contextifyPath}`\n    })\n  }\n\n  return JSON.stringify(resultMap)\n}\n\nfunction getModuleCode(\n  result: { map: any; css: any },\n  api: any,\n  replacements: any,\n  options: {\n    modules: { exportOnlyLocals: boolean; namedExport: any }\n    sourceMap: any\n  },\n  loaderContext: any\n) {\n  if (options.modules.exportOnlyLocals === true) {\n    return ''\n  }\n\n  const sourceMapValue = options.sourceMap\n    ? `,${normalizeSourceMapForRuntime(result.map, loaderContext)}`\n    : ''\n\n  let code = JSON.stringify(result.css)\n  let beforeCode = `var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(${options.sourceMap});\\n`\n\n  for (const item of api) {\n    const { url, media, dedupe } = item\n\n    beforeCode += url\n      ? `___CSS_LOADER_EXPORT___.push([module.id, ${JSON.stringify(\n          `@import url(${url});`\n        )}${media ? `, ${JSON.stringify(media)}` : ''}]);\\n`\n      : `___CSS_LOADER_EXPORT___.i(${item.importName}${\n          media ? `, ${JSON.stringify(media)}` : dedupe ? ', \"\"' : ''\n        }${dedupe ? ', true' : ''});\\n`\n  }\n\n  for (const item of replacements) {\n    const { replacementName, importName, localName } = item\n\n    if (localName) {\n      code = code.replace(new RegExp(replacementName, 'g'), () =>\n        options.modules.namedExport\n          ? `\" + ${importName}_NAMED___[${JSON.stringify(\n              camelCase(localName)\n            )}] + \"`\n          : `\" + ${importName}.locals[${JSON.stringify(localName)}] + \"`\n      )\n    } else {\n      const { hash, needQuotes } = item\n      const getUrlOptions = [\n        ...(hash ? [`hash: ${JSON.stringify(hash)}`] : []),\n        ...(needQuotes ? 'needQuotes: true' : []),\n      ]\n      const preparedOptions =\n        getUrlOptions.length > 0 ? `, { ${getUrlOptions.join(', ')} }` : ''\n\n      beforeCode += `var ${replacementName} = ___CSS_LOADER_GET_URL_IMPORT___(${importName}${preparedOptions});\\n`\n      code = code.replace(\n        new RegExp(replacementName, 'g'),\n        () => `\" + ${replacementName} + \"`\n      )\n    }\n  }\n\n  return `${beforeCode}// Module\\n___CSS_LOADER_EXPORT___.push([module.id, ${code}, \"\"${sourceMapValue}]);\\n`\n}\n\nfunction dashesCamelCase(str: string) {\n  return str.replace(/-+(\\w)/g, (_match: any, firstLetter: string) =>\n    firstLetter.toUpperCase()\n  )\n}\n\nfunction getExportCode(\n  exports: any,\n  replacements: any,\n  options: {\n    modules: {\n      namedExport: any\n      exportLocalsConvention: any\n      exportOnlyLocals: any\n    }\n    esModule: any\n  }\n) {\n  let code = '// Exports\\n'\n  let localsCode = ''\n\n  const addExportToLocalsCode = (name: string, value: any) => {\n    if (options.modules.namedExport) {\n      localsCode += `export const ${camelCase(name)} = ${JSON.stringify(\n        value\n      )};\\n`\n    } else {\n      if (localsCode) {\n        localsCode += `,\\n`\n      }\n\n      localsCode += `\\t${JSON.stringify(name)}: ${JSON.stringify(value)}`\n    }\n  }\n\n  for (const { name, value } of exports) {\n    switch (options.modules.exportLocalsConvention) {\n      case 'camelCase': {\n        addExportToLocalsCode(name, value)\n\n        const modifiedName = camelCase(name)\n\n        if (modifiedName !== name) {\n          addExportToLocalsCode(modifiedName, value)\n        }\n        break\n      }\n      case 'camelCaseOnly': {\n        addExportToLocalsCode(camelCase(name), value)\n        break\n      }\n      case 'dashes': {\n        addExportToLocalsCode(name, value)\n\n        const modifiedName = dashesCamelCase(name)\n\n        if (modifiedName !== name) {\n          addExportToLocalsCode(modifiedName, value)\n        }\n        break\n      }\n      case 'dashesOnly': {\n        addExportToLocalsCode(dashesCamelCase(name), value)\n        break\n      }\n      case 'asIs':\n      default:\n        addExportToLocalsCode(name, value)\n        break\n    }\n  }\n\n  for (const item of replacements) {\n    const { replacementName, localName } = item\n\n    if (localName) {\n      const { importName } = item\n\n      localsCode = localsCode.replace(new RegExp(replacementName, 'g'), () => {\n        if (options.modules.namedExport) {\n          return `\" + ${importName}_NAMED___[${JSON.stringify(\n            camelCase(localName)\n          )}] + \"`\n        } else if (options.modules.exportOnlyLocals) {\n          return `\" + ${importName}[${JSON.stringify(localName)}] + \"`\n        }\n\n        return `\" + ${importName}.locals[${JSON.stringify(localName)}] + \"`\n      })\n    } else {\n      localsCode = localsCode.replace(\n        new RegExp(replacementName, 'g'),\n        () => `\" + ${replacementName} + \"`\n      )\n    }\n  }\n\n  if (options.modules.exportOnlyLocals) {\n    code += options.modules.namedExport\n      ? localsCode\n      : `${\n          options.esModule ? 'export default' : 'module.exports ='\n        } {\\n${localsCode}\\n};\\n`\n\n    return code\n  }\n\n  if (localsCode) {\n    code += options.modules.namedExport\n      ? localsCode\n      : `___CSS_LOADER_EXPORT___.locals = {\\n${localsCode}\\n};\\n`\n  }\n\n  code += `${\n    options.esModule ? 'export default' : 'module.exports ='\n  } ___CSS_LOADER_EXPORT___;\\n`\n\n  return code\n}\n\nasync function resolveRequests(\n  resolve: (arg0: any, arg1: any) => Promise<any>,\n  context: any,\n  possibleRequests: any[]\n): Promise<any> {\n  return resolve(context, possibleRequests[0])\n    .then((result: any) => {\n      return result\n    })\n    .catch((error: any) => {\n      const [, ...tailPossibleRequests] = possibleRequests\n\n      if (tailPossibleRequests.length === 0) {\n        throw error\n      }\n\n      return resolveRequests(resolve, context, tailPossibleRequests)\n    })\n}\n\nfunction isUrlRequestable(url: string) {\n  // Protocol-relative URLs\n  if (/^\\/\\//.test(url)) {\n    return false\n  }\n\n  // `file:` protocol\n  if (/^file:/i.test(url)) {\n    return true\n  }\n\n  // Absolute URLs\n  if (/^[a-z][a-z0-9+.-]*:/i.test(url)) {\n    return true\n  }\n\n  // `#` URLs\n  if (/^#/.test(url)) {\n    return false\n  }\n\n  return true\n}\n\nfunction sort(a: { index: number }, b: { index: number }) {\n  return a.index - b.index\n}\n\nfunction isDataUrl(url: string) {\n  if (/^data:/i.test(url)) {\n    return true\n  }\n\n  return false\n}\n\nexport {\n  isDataUrl,\n  shouldUseModulesPlugins,\n  shouldUseImportPlugin,\n  shouldUseURLPlugin,\n  shouldUseIcssPlugin,\n  normalizeUrl,\n  requestify,\n  getFilter,\n  getModulesPlugins,\n  normalizeSourceMap,\n  getPreRequester,\n  getImportCode,\n  getModuleCode,\n  getExportCode,\n  resolveRequests,\n  isUrlRequestable,\n  sort,\n  // For lightningcss-loader\n  normalizeSourceMapForRuntime,\n  dashesCamelCase,\n}\n"], "names": ["dashesCamelCase", "getExportCode", "getFilter", "getImportCode", "getModuleCode", "getModulesPlugins", "getPreRequester", "isDataUrl", "isUrlRequestable", "normalizeSourceMap", "normalizeSourceMapForRuntime", "normalizeUrl", "requestify", "resolveRequests", "shouldUseIcssPlugin", "shouldUseImportPlugin", "shouldUseModulesPlugins", "shouldUseURLPlugin", "sort", "whitespace", "unescapeRegExp", "RegExp", "matchNativeWin32Path", "unescape", "str", "replace", "_", "escaped", "escapedWhitespace", "high", "String", "fromCharCode", "normalizePath", "file", "path", "sep", "fixedEncodeURIComponent", "c", "charCodeAt", "toString", "url", "isStringValue", "normalizedUrl", "test", "decodeURIComponent", "error", "decodeURI", "rootContext", "fileURLToPath", "char<PERSON>t", "urlToRequest", "filter", "resourcePath", "args", "options", "modules", "exportOnlyLocals", "import", "compileType", "icss", "Boolean", "loaderContext", "meta", "mode", "getLocalIdent", "localIdentName", "localIdentContext", "localIdentHashPrefix", "localIdentRegExp", "plugins", "modulesValues", "localByDefault", "extractImports", "modulesScope", "generateScopedName", "exportName", "context", "hashPrefix", "regExp", "exportGlobals", "emitError", "IS_NATIVE_WIN32_PATH", "ABSOLUTE_SCHEME", "getURLType", "source", "map", "newMap", "JSON", "parse", "sourceRoot", "sources", "startsWith", "sourceType", "absoluteSource", "resolve", "relative", "dirname", "loaders", "loaderIndex", "cache", "Object", "create", "number", "loadersRequest", "slice", "x", "request", "join", "imports", "code", "item", "importName", "esModule", "namedExport", "resultMap", "toJSON", "resourceDirname", "contextifyPath", "stringify", "result", "api", "replacements", "sourceMapValue", "sourceMap", "css", "beforeCode", "media", "dedupe", "replacement<PERSON>ame", "localName", "camelCase", "hash", "needQuotes", "getUrlOptions", "preparedOptions", "length", "_match", "firstLetter", "toUpperCase", "exports", "localsCode", "addExportToLocalsCode", "name", "value", "exportLocalsConvention", "modifiedName", "possibleRequests", "then", "catch", "tailPossibleRequests", "a", "b", "index"], "mappings": "AAAA;;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmkBEA,eAAe;eAAfA;;IANAC,aAAa;eAAbA;;IANAC,SAAS;eAATA;;IAIAC,aAAa;eAAbA;;IACAC,aAAa;eAAbA;;IAJAC,iBAAiB;eAAjBA;;IAEAC,eAAe;eAAfA;;IAVAC,SAAS;eAATA;;IAeAC,gBAAgB;eAAhBA;;IANAC,kBAAkB;eAAlBA;;IAQA,0BAA0B;IAC1BC,4BAA4B;eAA5BA;;IAbAC,YAAY;eAAZA;;IACAC,UAAU;eAAVA;;IAQAC,eAAe;eAAfA;;IAVAC,mBAAmB;eAAnBA;;IAFAC,qBAAqB;eAArBA;;IADAC,uBAAuB;eAAvBA;;IAEAC,kBAAkB;eAAlBA;;IAaAC,IAAI;eAAJA;;;qBA/jB4B;6DACb;8BAEY;6EACH;qFACC;qFACA;4EACF;kEACH;;;;;;AAEtB,MAAMC,aAAa;AACnB,MAAMC,iBAAiB,IAAIC,OACzB,CAAC,kBAAkB,EAAEF,WAAW,GAAG,EAAEA,WAAW,IAAI,CAAC,EACrD;AAEF,MAAMG,uBAAuB;AAE7B,SAASC,SAASC,GAAW;IAC3B,OAAOA,IAAIC,OAAO,CAACL,gBAAgB,CAACM,GAAGC,SAASC;QAC9C,MAAMC,OAAO,AAAC,CAAC,EAAE,EAAEF,SAAS,GAAW;QAEvC,wCAAwC,GACxC,0BAA0B;QAC1B,uDAAuD;QACvD,2CAA2C;QAC3C,OAAOE,SAASA,QAAQD,oBACpBD,UACAE,OAAO,IAELC,OAAOC,YAAY,CAACF,OAAO,WAE3B,sCAAsC;QACtCC,OAAOC,YAAY,CAAC,AAACF,QAAQ,KAAM,QAAQ,AAACA,OAAO,QAAS;IAClE,uCAAuC,GACzC;AACF;AAEA,SAASG,cAAcC,IAAY;IACjC,OAAOC,aAAI,CAACC,GAAG,KAAK,OAAOF,KAAKR,OAAO,CAAC,OAAO,OAAOQ;AACxD;AAEA,SAASG,wBAAwBZ,GAAW;IAC1C,OAAOA,IAAIC,OAAO,CAAC,YAAY,CAACY,IAAM,CAAC,CAAC,EAAEA,EAAEC,UAAU,CAAC,GAAGC,QAAQ,CAAC,KAAK;AAC1E;AAEA,SAAS5B,aAAa6B,GAAW,EAAEC,aAAsB;IACvD,IAAIC,gBAAgBF;IAEpB,IAAIC,iBAAiB,oBAAoBE,IAAI,CAACD,gBAAgB;QAC5DA,gBAAgBA,cAAcjB,OAAO,CAAC,sBAAsB;IAC9D;IAEA,IAAIH,qBAAqBqB,IAAI,CAACH,MAAM;QAClC,IAAI;YACFE,gBAAgBE,mBAAmBF;QACrC,EAAE,OAAOG,OAAO;QACd,gEAAgE;QAClE;QAEA,OAAOH;IACT;IAEAA,gBAAgBnB,SAASmB;IAEzB,mEAAmE;IACnE,IAAInC,UAAUiC,MAAM;QAClB,OAAOJ,wBAAwBM;IACjC;IAEA,IAAI;QACFA,gBAAgBI,UAAUJ;IAC5B,EAAE,OAAOG,OAAO;IACd,gEAAgE;IAClE;IAEA,OAAOH;AACT;AAEA,SAAS9B,WAAW4B,GAAW,EAAEO,WAAmB;IAClD,IAAI,UAAUJ,IAAI,CAACH,MAAM;QACvB,OAAOQ,IAAAA,kBAAa,EAACR;IACvB;IAEA,IAAI,uBAAuBG,IAAI,CAACH,MAAM;QACpC,OAAOA;IACT;IAEA,OAAOA,IAAIS,MAAM,CAAC,OAAO,MACrBC,IAAAA,0BAAY,EAACV,KAAKO,eAClBG,IAAAA,0BAAY,EAACV;AACnB;AAEA,SAAStC,UAAUiD,MAAW,EAAEC,YAAoB;IAClD,OAAO,CAAC,GAAGC;QACT,IAAI,OAAOF,WAAW,YAAY;YAChC,OAAOA,UAAUE,MAAMD;QACzB;QAEA,OAAO;IACT;AACF;AAEA,SAASrC,sBAAsBuC,OAAY;IACzC,IAAIA,QAAQC,OAAO,CAACC,gBAAgB,EAAE;QACpC,OAAO;IACT;IAEA,IAAI,OAAOF,QAAQG,MAAM,KAAK,WAAW;QACvC,OAAOH,QAAQG,MAAM;IACvB;IAEA,OAAO;AACT;AAEA,SAASxC,mBAAmBqC,OAAY;IACtC,IAAIA,QAAQC,OAAO,CAACC,gBAAgB,EAAE;QACpC,OAAO;IACT;IAEA,IAAI,OAAOF,QAAQd,GAAG,KAAK,WAAW;QACpC,OAAOc,QAAQd,GAAG;IACpB;IAEA,OAAO;AACT;AAEA,SAASxB,wBAAwBsC,OAAY;IAC3C,OAAOA,QAAQC,OAAO,CAACG,WAAW,KAAK;AACzC;AAEA,SAAS5C,oBAAoBwC,OAAY;IACvC,OAAOA,QAAQK,IAAI,KAAK,QAAQC,QAAQN,QAAQC,OAAO;AACzD;AAEA,SAASlD,kBAAkBiD,OAAY,EAAEO,aAAkB,EAAEC,IAAS;IACpE,MAAM,EACJC,IAAI,EACJC,aAAa,EACbC,cAAc,EACdC,iBAAiB,EACjBC,oBAAoB,EACpBC,gBAAgB,EACjB,GAAGd,QAAQC,OAAO;IAEnB,IAAIc,UAAiB,EAAE;IAEvB,IAAI;QACFA,UAAU;YACRC,6BAAa;YACbC,IAAAA,qCAAc,EAAC;gBAAER;YAAK;YACtBS,IAAAA,qCAAc;YACdC,IAAAA,4BAAY,EAAC;gBACXC,oBAAmBC,UAAe;oBAChC,OAAOX,cACLH,eACAI,gBACAU,YACA;wBACEC,SAASV;wBACTW,YAAYV;wBACZW,QAAQV;oBACV,GACAN;gBAEJ;gBACAiB,eAAezB,QAAQC,OAAO,CAACwB,aAAa;YAC9C;SACD;IACH,EAAE,OAAOlC,OAAO;QACdgB,cAAcmB,SAAS,CAACnC;IAC1B;IAEA,OAAOwB;AACT;AAEA,MAAMY,uBAAuB;AAC7B,MAAMC,kBAAkB;AAExB,SAASC,WAAWC,MAAc;IAChC,IAAIA,MAAM,CAAC,EAAE,KAAK,KAAK;QACrB,IAAIA,MAAM,CAAC,EAAE,KAAK,KAAK;YACrB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,IAAIH,qBAAqBtC,IAAI,CAACyC,SAAS;QACrC,OAAO;IACT;IAEA,OAAOF,gBAAgBvC,IAAI,CAACyC,UAAU,aAAa;AACrD;AAEA,SAAS3E,mBAAmB4E,GAAQ,EAAEjC,YAAoB;IACxD,IAAIkC,SAASD;IAEb,wCAAwC;IACxC,4IAA4I;IAC5I,IAAI,OAAOC,WAAW,UAAU;QAC9BA,SAASC,KAAKC,KAAK,CAACF;IACtB;IAEA,OAAOA,OAAOrD,IAAI;IAElB,MAAM,EAAEwD,UAAU,EAAE,GAAGH;IAEvB,OAAOA,OAAOG,UAAU;IAExB,IAAIH,OAAOI,OAAO,EAAE;QAClB,4GAA4G;QAC5G,gHAAgH;QAChHJ,OAAOI,OAAO,GAAGJ,OAAOI,OAAO,CAACL,GAAG,CAAC,CAACD;YACnC,qCAAqC;YACrC,IAAIA,OAAOO,UAAU,CAAC,MAAM;gBAC1B,OAAOP;YACT;YAEA,MAAMQ,aAAaT,WAAWC;YAE9B,oDAAoD;YACpD,IAAIQ,eAAe,mBAAmBA,eAAe,iBAAiB;gBACpE,MAAMC,iBACJD,eAAe,mBAAmBH,aAC9BvD,aAAI,CAAC4D,OAAO,CAACL,YAAYzD,cAAcoD,WACvCpD,cAAcoD;gBAEpB,OAAOlD,aAAI,CAAC6D,QAAQ,CAAC7D,aAAI,CAAC8D,OAAO,CAAC5C,eAAeyC;YACnD;YAEA,OAAOT;QACT;IACF;IAEA,OAAOE;AACT;AAEA,SAAShF,gBAAgB,EAAE2F,OAAO,EAAEC,WAAW,EAAO;IACpD,MAAMC,QAAQC,OAAOC,MAAM,CAAC;IAE5B,OAAO,CAACC;QACN,IAAIH,KAAK,CAACG,OAAO,EAAE;YACjB,OAAOH,KAAK,CAACG,OAAO;QACtB;QAEA,IAAIA,WAAW,OAAO;YACpBH,KAAK,CAACG,OAAO,GAAG;QAClB,OAAO;YACL,MAAMC,iBAAiBN,QACpBO,KAAK,CACJN,aACAA,cAAc,IAAK,CAAA,OAAOI,WAAW,WAAW,IAAIA,MAAK,GAE1DjB,GAAG,CAAC,CAACoB,IAAWA,EAAEC,OAAO,EACzBC,IAAI,CAAC;YAERR,KAAK,CAACG,OAAO,GAAG,CAAC,EAAE,EAAEC,eAAe,CAAC,CAAC;QACxC;QAEA,OAAOJ,KAAK,CAACG,OAAO;IACtB;AACF;AAEA,SAASnG,cAAcyG,OAAY,EAAEtD,OAAY;IAC/C,IAAIuD,OAAO;IAEX,KAAK,MAAMC,QAAQF,QAAS;QAC1B,MAAM,EAAEG,UAAU,EAAEvE,GAAG,EAAEmB,IAAI,EAAE,GAAGmD;QAElC,IAAIxD,QAAQ0D,QAAQ,EAAE;YACpB,IAAIrD,QAAQL,QAAQC,OAAO,CAAC0D,WAAW,EAAE;gBACvCJ,QAAQ,CAAC,OAAO,EACdvD,QAAQC,OAAO,CAACC,gBAAgB,GAAG,KAAK,GAAGuD,WAAW,EAAE,CAAC,CAC1D,KAAK,EAAEA,WAAW,eAAe,EAAEvE,IAAI,GAAG,CAAC;YAC9C,OAAO;gBACLqE,QAAQ,CAAC,OAAO,EAAEE,WAAW,MAAM,EAAEvE,IAAI,GAAG,CAAC;YAC/C;QACF,OAAO;YACLqE,QAAQ,CAAC,IAAI,EAAEE,WAAW,WAAW,EAAEvE,IAAI,IAAI,CAAC;QAClD;IACF;IAEA,OAAOqE,OAAO,CAAC,YAAY,EAAEA,MAAM,GAAG;AACxC;AAEA,SAASnG,6BAA6B2E,GAAQ,EAAExB,aAAkB;IAChE,MAAMqD,YAAY7B,MAAMA,IAAI8B,MAAM,KAAK;IAEvC,IAAID,WAAW;QACb,OAAOA,UAAUjF,IAAI;QAErBiF,UAAUzB,UAAU,GAAG;QAEvByB,UAAUxB,OAAO,GAAGwB,UAAUxB,OAAO,CAACL,GAAG,CAAC,CAACD;YACzC,qCAAqC;YACrC,IAAIA,OAAOO,UAAU,CAAC,MAAM;gBAC1B,OAAOP;YACT;YAEA,MAAMQ,aAAaT,WAAWC;YAE9B,IAAIQ,eAAe,iBAAiB;gBAClC,OAAOR;YACT;YAEA,MAAMgC,kBAAkBlF,aAAI,CAAC8D,OAAO,CAACnC,cAAcT,YAAY;YAC/D,MAAMyC,iBAAiB3D,aAAI,CAAC4D,OAAO,CAACsB,iBAAiBhC;YACrD,MAAMiC,iBAAiBrF,cACrBE,aAAI,CAAC6D,QAAQ,CAAClC,cAAcd,WAAW,EAAE8C;YAG3C,OAAO,CAAC,UAAU,EAAEwB,gBAAgB;QACtC;IACF;IAEA,OAAO9B,KAAK+B,SAAS,CAACJ;AACxB;AAEA,SAAS9G,cACPmH,MAA8B,EAC9BC,GAAQ,EACRC,YAAiB,EACjBnE,OAGC,EACDO,aAAkB;IAElB,IAAIP,QAAQC,OAAO,CAACC,gBAAgB,KAAK,MAAM;QAC7C,OAAO;IACT;IAEA,MAAMkE,iBAAiBpE,QAAQqE,SAAS,GACpC,CAAC,CAAC,EAAEjH,6BAA6B6G,OAAOlC,GAAG,EAAExB,gBAAgB,GAC7D;IAEJ,IAAIgD,OAAOtB,KAAK+B,SAAS,CAACC,OAAOK,GAAG;IACpC,IAAIC,aAAa,CAAC,0DAA0D,EAAEvE,QAAQqE,SAAS,CAAC,IAAI,CAAC;IAErG,KAAK,MAAMb,QAAQU,IAAK;QACtB,MAAM,EAAEhF,GAAG,EAAEsF,KAAK,EAAEC,MAAM,EAAE,GAAGjB;QAE/Be,cAAcrF,MACV,CAAC,yCAAyC,EAAE+C,KAAK+B,SAAS,CACxD,CAAC,YAAY,EAAE9E,IAAI,EAAE,CAAC,IACpBsF,QAAQ,CAAC,EAAE,EAAEvC,KAAK+B,SAAS,CAACQ,QAAQ,GAAG,GAAG,KAAK,CAAC,GACpD,CAAC,0BAA0B,EAAEhB,KAAKC,UAAU,GAC1Ce,QAAQ,CAAC,EAAE,EAAEvC,KAAK+B,SAAS,CAACQ,QAAQ,GAAGC,SAAS,SAAS,KACxDA,SAAS,WAAW,GAAG,IAAI,CAAC;IACrC;IAEA,KAAK,MAAMjB,QAAQW,aAAc;QAC/B,MAAM,EAAEO,eAAe,EAAEjB,UAAU,EAAEkB,SAAS,EAAE,GAAGnB;QAEnD,IAAImB,WAAW;YACbpB,OAAOA,KAAKpF,OAAO,CAAC,IAAIJ,OAAO2G,iBAAiB,MAAM,IACpD1E,QAAQC,OAAO,CAAC0D,WAAW,GACvB,CAAC,IAAI,EAAEF,WAAW,UAAU,EAAExB,KAAK+B,SAAS,CAC1CY,IAAAA,kBAAS,EAACD,YACV,KAAK,CAAC,GACR,CAAC,IAAI,EAAElB,WAAW,QAAQ,EAAExB,KAAK+B,SAAS,CAACW,WAAW,KAAK,CAAC;QAEpE,OAAO;YACL,MAAM,EAAEE,IAAI,EAAEC,UAAU,EAAE,GAAGtB;YAC7B,MAAMuB,gBAAgB;mBAChBF,OAAO;oBAAC,CAAC,MAAM,EAAE5C,KAAK+B,SAAS,CAACa,OAAO;iBAAC,GAAG,EAAE;mBAC7CC,aAAa,qBAAqB,EAAE;aACzC;YACD,MAAME,kBACJD,cAAcE,MAAM,GAAG,IAAI,CAAC,IAAI,EAAEF,cAAc1B,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG;YAEnEkB,cAAc,CAAC,IAAI,EAAEG,gBAAgB,mCAAmC,EAAEjB,aAAauB,gBAAgB,IAAI,CAAC;YAC5GzB,OAAOA,KAAKpF,OAAO,CACjB,IAAIJ,OAAO2G,iBAAiB,MAC5B,IAAM,CAAC,IAAI,EAAEA,gBAAgB,IAAI,CAAC;QAEtC;IACF;IAEA,OAAO,GAAGH,WAAW,oDAAoD,EAAEhB,KAAK,IAAI,EAAEa,eAAe,KAAK,CAAC;AAC7G;AAEA,SAAS1H,gBAAgBwB,GAAW;IAClC,OAAOA,IAAIC,OAAO,CAAC,WAAW,CAAC+G,QAAaC,cAC1CA,YAAYC,WAAW;AAE3B;AAEA,SAASzI,cACP0I,QAAY,EACZlB,YAAiB,EACjBnE,OAOC;IAED,IAAIuD,OAAO;IACX,IAAI+B,aAAa;IAEjB,MAAMC,wBAAwB,CAACC,MAAcC;QAC3C,IAAIzF,QAAQC,OAAO,CAAC0D,WAAW,EAAE;YAC/B2B,cAAc,CAAC,aAAa,EAAEV,IAAAA,kBAAS,EAACY,MAAM,GAAG,EAAEvD,KAAK+B,SAAS,CAC/DyB,OACA,GAAG,CAAC;QACR,OAAO;YACL,IAAIH,YAAY;gBACdA,cAAc,CAAC,GAAG,CAAC;YACrB;YAEAA,cAAc,CAAC,EAAE,EAAErD,KAAK+B,SAAS,CAACwB,MAAM,EAAE,EAAEvD,KAAK+B,SAAS,CAACyB,QAAQ;QACrE;IACF;IAEA,KAAK,MAAM,EAAED,IAAI,EAAEC,KAAK,EAAE,IAAIJ,SAAS;QACrC,OAAQrF,QAAQC,OAAO,CAACyF,sBAAsB;YAC5C,KAAK;gBAAa;oBAChBH,sBAAsBC,MAAMC;oBAE5B,MAAME,eAAef,IAAAA,kBAAS,EAACY;oBAE/B,IAAIG,iBAAiBH,MAAM;wBACzBD,sBAAsBI,cAAcF;oBACtC;oBACA;gBACF;YACA,KAAK;gBAAiB;oBACpBF,sBAAsBX,IAAAA,kBAAS,EAACY,OAAOC;oBACvC;gBACF;YACA,KAAK;gBAAU;oBACbF,sBAAsBC,MAAMC;oBAE5B,MAAME,eAAejJ,gBAAgB8I;oBAErC,IAAIG,iBAAiBH,MAAM;wBACzBD,sBAAsBI,cAAcF;oBACtC;oBACA;gBACF;YACA,KAAK;gBAAc;oBACjBF,sBAAsB7I,gBAAgB8I,OAAOC;oBAC7C;gBACF;YACA,KAAK;YACL;gBACEF,sBAAsBC,MAAMC;gBAC5B;QACJ;IACF;IAEA,KAAK,MAAMjC,QAAQW,aAAc;QAC/B,MAAM,EAAEO,eAAe,EAAEC,SAAS,EAAE,GAAGnB;QAEvC,IAAImB,WAAW;YACb,MAAM,EAAElB,UAAU,EAAE,GAAGD;YAEvB8B,aAAaA,WAAWnH,OAAO,CAAC,IAAIJ,OAAO2G,iBAAiB,MAAM;gBAChE,IAAI1E,QAAQC,OAAO,CAAC0D,WAAW,EAAE;oBAC/B,OAAO,CAAC,IAAI,EAAEF,WAAW,UAAU,EAAExB,KAAK+B,SAAS,CACjDY,IAAAA,kBAAS,EAACD,YACV,KAAK,CAAC;gBACV,OAAO,IAAI3E,QAAQC,OAAO,CAACC,gBAAgB,EAAE;oBAC3C,OAAO,CAAC,IAAI,EAAEuD,WAAW,CAAC,EAAExB,KAAK+B,SAAS,CAACW,WAAW,KAAK,CAAC;gBAC9D;gBAEA,OAAO,CAAC,IAAI,EAAElB,WAAW,QAAQ,EAAExB,KAAK+B,SAAS,CAACW,WAAW,KAAK,CAAC;YACrE;QACF,OAAO;YACLW,aAAaA,WAAWnH,OAAO,CAC7B,IAAIJ,OAAO2G,iBAAiB,MAC5B,IAAM,CAAC,IAAI,EAAEA,gBAAgB,IAAI,CAAC;QAEtC;IACF;IAEA,IAAI1E,QAAQC,OAAO,CAACC,gBAAgB,EAAE;QACpCqD,QAAQvD,QAAQC,OAAO,CAAC0D,WAAW,GAC/B2B,aACA,GACEtF,QAAQ0D,QAAQ,GAAG,mBAAmB,mBACvC,IAAI,EAAE4B,WAAW,MAAM,CAAC;QAE7B,OAAO/B;IACT;IAEA,IAAI+B,YAAY;QACd/B,QAAQvD,QAAQC,OAAO,CAAC0D,WAAW,GAC/B2B,aACA,CAAC,oCAAoC,EAAEA,WAAW,MAAM,CAAC;IAC/D;IAEA/B,QAAQ,GACNvD,QAAQ0D,QAAQ,GAAG,mBAAmB,mBACvC,2BAA2B,CAAC;IAE7B,OAAOH;AACT;AAEA,eAAehG,gBACbiF,OAA+C,EAC/ClB,OAAY,EACZsE,gBAAuB;IAEvB,OAAOpD,QAAQlB,SAASsE,gBAAgB,CAAC,EAAE,EACxCC,IAAI,CAAC,CAAC5B;QACL,OAAOA;IACT,GACC6B,KAAK,CAAC,CAACvG;QACN,MAAM,GAAG,GAAGwG,qBAAqB,GAAGH;QAEpC,IAAIG,qBAAqBd,MAAM,KAAK,GAAG;YACrC,MAAM1F;QACR;QAEA,OAAOhC,gBAAgBiF,SAASlB,SAASyE;IAC3C;AACJ;AAEA,SAAS7I,iBAAiBgC,GAAW;IACnC,yBAAyB;IACzB,IAAI,QAAQG,IAAI,CAACH,MAAM;QACrB,OAAO;IACT;IAEA,mBAAmB;IACnB,IAAI,UAAUG,IAAI,CAACH,MAAM;QACvB,OAAO;IACT;IAEA,gBAAgB;IAChB,IAAI,uBAAuBG,IAAI,CAACH,MAAM;QACpC,OAAO;IACT;IAEA,WAAW;IACX,IAAI,KAAKG,IAAI,CAACH,MAAM;QAClB,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAStB,KAAKoI,CAAoB,EAAEC,CAAoB;IACtD,OAAOD,EAAEE,KAAK,GAAGD,EAAEC,KAAK;AAC1B;AAEA,SAASjJ,UAAUiC,GAAW;IAC5B,IAAI,UAAUG,IAAI,CAACH,MAAM;QACvB,OAAO;IACT;IAEA,OAAO;AACT"}