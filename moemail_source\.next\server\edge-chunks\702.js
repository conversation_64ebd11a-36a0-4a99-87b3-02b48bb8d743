(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[702],{888:(e,t,i)=>{"use strict";var s=Object.create,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,l=Object.getOwnPropertyNames,a=Object.getPrototypeOf,o=Object.prototype.hasOwnProperty,u=(e,t,i,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of l(t))o.call(e,a)||a===i||r(e,a,{get:()=>t[a],enumerable:!(s=n(t,a))||s.enumerable});return e},c=((e,t)=>function(){return t||(0,e[l(e)[0]])((t={exports:{}}).exports,t),t.exports})({"../../node_modules/dedent-tabs/dist/dedent-tabs.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(e){for(var t="string"==typeof e?[e]:e.raw,i="",s=0;s<t.length;s++)if(i+=t[s].replace(/\\\n[ \t]*/g,"").replace(/\\`/g,"`").replace(/\\\$/g,"$").replace(/\\\{/g,"{"),s<(1>=arguments.length?0:arguments.length-1)){var r=i.substring(i.lastIndexOf("\n")+1).match(/^(\s*)\S?/);i+=((1>s+1||arguments.length<=s+1?void 0:arguments[s+1])+"").replace(/\n/g,"\n"+r[1])}var n=i.split("\n"),l=null;if(n.forEach(function(e){var t=Math.min,i=e.match(/^(\s+)\S+/);if(i){var s=i[1].length;l=l?t(l,s):s}}),null!==l){var a=l;i=n.map(function(e){return" "===e[0]||"	"===e[0]?e.slice(a):e}).join("\n")}return i.trim().replace(/\\n/g,"\n")}}}),h={};((e,t)=>{for(var i in t)r(e,i,{get:t[i],enumerable:!0})})(h,{getOptionalRequestContext:()=>m,getRequestContext:()=>g}),e.exports=u(r({},"__esModule",{value:!0}),h),i(2870);var d=((e,t,i)=>(i=null!=e?s(a(e)):{},u(!t&&e&&e.__esModule?i:r(i,"default",{value:e,enumerable:!0}),e)))(c()),f=Symbol.for("__cloudflare-request-context__");function m(){let e=globalThis[f];if("nodejs"==(process?.release?.name==="node"?"nodejs":"edge"))throw Error(d.default`
			\`getRequestContext\` and \`getOptionalRequestContext\` can only be run
			inside the edge runtime, so please make sure to have included
			\`export const runtime = 'edge'\` in all the routes using such functions
			(regardless of whether they are used directly or indirectly through imports).
		`);return e}function g(){let e=m();if(!e){if(process?.env?.NEXT_PHASE==="phase-production-build")throw Error(d.default`
				\n\`getRequestContext\` is being called at the top level of a route file, this is not supported
				for more details see https://developers.cloudflare.com/pages/framework-guides/nextjs/ssr/troubleshooting/#top-level-getrequestcontext \n
			`);throw Error("Failed to retrieve the Cloudflare request context.")}return e}},2870:()=>{},8421:(e,t,i)=>{"use strict";i.d(t,{Hs:()=>f,Ht:()=>o,h_:()=>u,oG:()=>h,ug:()=>d,yY:()=>m});var s=i(823),r=i(66),n=i(7536),l=i(8685),a=i(4996);class o{constructor(e){this.table=e}static [r.i]="ColumnAliasProxyHandler";get(e,t){return"table"===t?this.table:e[t]}}class u{constructor(e,t){this.alias=e,this.replaceOriginalName=t}static [r.i]="TableAliasProxyHandler";get(e,t){if(t===l.XI.Symbol.IsAlias)return!0;if(t===l.XI.Symbol.Name||this.replaceOriginalName&&t===l.XI.Symbol.OriginalName)return this.alias;if(t===a.n)return{...e[a.n],name:this.alias,isAlias:!0};if(t===l.XI.Symbol.Columns){let t=e[l.XI.Symbol.Columns];if(!t)return t;let i={};return Object.keys(t).map(s=>{i[s]=new Proxy(t[s],new o(new Proxy(e,this)))}),i}let i=e[t];return(0,r.is)(i,s.V)?new Proxy(i,new o(new Proxy(e,this))):i}}class c{constructor(e){this.alias=e}static [r.i]=null;get(e,t){return"sourceTable"===t?h(e.sourceTable,this.alias):e[t]}}function h(e,t){return new Proxy(e,new u(t,!1))}function d(e,t){return new Proxy(e,new o(new Proxy(e.table,new u(t,!1))))}function f(e,t){return new n.Xs.Aliased(m(e.sql,t),e.fieldAlias)}function m(e,t){return n.ll.join(e.queryChunks.map(e=>(0,r.is)(e,s.V)?d(e,t):(0,r.is)(e,n.Xs)?m(e,t):(0,r.is)(e,n.Xs.Aliased)?f(e,t):e))}},4650:(e,t,i)=>{"use strict";i.d(t,{Yn:()=>o});var s=i(66),r=i(8685);function n(e){return(e.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).map(e=>e.toLowerCase()).join("_")}function l(e){return(e.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).reduce((e,t,i)=>e+(0===i?t.toLowerCase():`${t[0].toUpperCase()}${t.slice(1)}`),"")}function a(e){return e}class o{static [s.i]="CasingCache";cache={};cachedTables={};convert;constructor(e){this.convert="snake_case"===e?n:"camelCase"===e?l:a}getColumnCasing(e){if(!e.keyAsName)return e.name;let t=e.table[r.XI.Symbol.Schema]??"public",i=e.table[r.XI.Symbol.OriginalName],s=`${t}.${i}.${e.name}`;return this.cache[s]||this.cacheTable(e.table),this.cache[s]}cacheTable(e){let t=e[r.XI.Symbol.Schema]??"public",i=e[r.XI.Symbol.OriginalName],s=`${t}.${i}`;if(!this.cachedTables[s]){for(let t of Object.values(e[r.XI.Symbol.Columns])){let e=`${s}.${t.name}`;this.cache[e]=this.convert(t.name)}this.cachedTables[s]=!0}}clearCache(){this.cache={},this.cachedTables={}}}},9877:(e,t,i)=>{"use strict";i.d(t,{Q:()=>r});var s=i(66);class r{static [s.i]="ColumnBuilder";config;constructor(e,t,i){this.config={name:e,keyAsName:""===e,notNull:!1,default:void 0,hasDefault:!1,primaryKey:!1,isUnique:!1,uniqueName:void 0,uniqueType:void 0,dataType:t,columnType:i,generated:void 0}}$type(){return this}notNull(){return this.config.notNull=!0,this}default(e){return this.config.default=e,this.config.hasDefault=!0,this}$defaultFn(e){return this.config.defaultFn=e,this.config.hasDefault=!0,this}$default=this.$defaultFn;$onUpdateFn(e){return this.config.onUpdateFn=e,this.config.hasDefault=!0,this}$onUpdate=this.$onUpdateFn;primaryKey(){return this.config.primaryKey=!0,this.config.notNull=!0,this}setName(e){""===this.config.name&&(this.config.name=e)}}},823:(e,t,i)=>{"use strict";i.d(t,{V:()=>r});var s=i(66);class r{constructor(e,t){this.table=e,this.config=t,this.name=t.name,this.keyAsName=t.keyAsName,this.notNull=t.notNull,this.default=t.default,this.defaultFn=t.defaultFn,this.onUpdateFn=t.onUpdateFn,this.hasDefault=t.hasDefault,this.primary=t.primaryKey,this.isUnique=t.isUnique,this.uniqueName=t.uniqueName,this.uniqueType=t.uniqueType,this.dataType=t.dataType,this.columnType=t.columnType,this.generated=t.generated,this.generatedIdentity=t.generatedIdentity}static [s.i]="Column";name;keyAsName;primary;notNull;default;defaultFn;onUpdateFn;hasDefault;isUnique;uniqueName;uniqueType;dataType;columnType;enumValues=void 0;generated=void 0;generatedIdentity=void 0;config;mapFromDriverValue(e){return e}mapToDriverValue(e){return e}shouldDisableInsert(){return void 0!==this.config.generated&&"byDefault"!==this.config.generated.type}}},7384:(e,t,i)=>{"use strict";i.d(t,{f:()=>T});var s=i(66);class r{static [s.i]="ConsoleLogWriter";write(e){console.log(e)}}class n{static [s.i]="DefaultLogger";writer;constructor(e){this.writer=e?.writer??new r}logQuery(e,t){let i=t.map(e=>{try{return JSON.stringify(e)}catch{return String(e)}}),s=i.length?` -- params: [${i.join(", ")}]`:"";this.writer.write(`Query: ${e}${s}`)}}class l{static [s.i]="NoopLogger";logQuery(){}}var a=i(652),o=i(4986),u=i(2148),c=i(7536),h=i(7430),d=i(9389);class f extends d.k{constructor(e){super(),this.resultCb=e}static [s.i]="ExecuteResultSync";async execute(){return this.resultCb()}sync(){return this.resultCb()}}class m{constructor(e,t,i){this.mode=e,this.executeMethod=t,this.query=i}static [s.i]="PreparedQuery";joinsNotNullableMap;getQuery(){return this.query}mapRunResult(e,t){return e}mapAllResult(e,t){throw Error("Not implemented")}mapGetResult(e,t){throw Error("Not implemented")}execute(e){return"async"===this.mode?this[this.executeMethod](e):new f(()=>this[this.executeMethod](e))}mapResult(e,t){switch(this.executeMethod){case"run":return this.mapRunResult(e,t);case"all":return this.mapAllResult(e,t);case"get":return this.mapGetResult(e,t)}}}class g{constructor(e){this.dialect=e}static [s.i]="SQLiteSession";prepareOneTimeQuery(e,t,i,s){return this.prepareQuery(e,t,i,s)}run(e){let t=this.dialect.sqlToQuery(e);try{return this.prepareOneTimeQuery(t,void 0,"run",!1).run()}catch(e){throw new h.n({cause:e,message:`Failed to run the query '${t.sql}'`})}}extractRawRunValueFromBatchResult(e){return e}all(e){return this.prepareOneTimeQuery(this.dialect.sqlToQuery(e),void 0,"run",!1).all()}extractRawAllValueFromBatchResult(e){throw Error("Not implemented")}get(e){return this.prepareOneTimeQuery(this.dialect.sqlToQuery(e),void 0,"run",!1).get()}extractRawGetValueFromBatchResult(e){throw Error("Not implemented")}values(e){return this.prepareOneTimeQuery(this.dialect.sqlToQuery(e),void 0,"run",!1).values()}async count(e){return(await this.values(e))[0][0]}extractRawValuesValueFromBatchResult(e){throw Error("Not implemented")}}class p extends o.N{constructor(e,t,i,s,r=0){super(e,t,i,s),this.schema=s,this.nestedIndex=r}static [s.i]="SQLiteTransaction";rollback(){throw new h.j}}var y=i(6104);class b extends g{constructor(e,t,i,s={}){super(t),this.client=e,this.schema=i,this.options=s,this.logger=s.logger??new l}static [s.i]="SQLiteD1Session";logger;prepareQuery(e,t,i,s,r){return new v(this.client.prepare(e.sql),e,this.logger,t,i,s,r)}async batch(e){let t=[],i=[];for(let s of e){let e=s._prepare(),r=e.getQuery();if(t.push(e),r.params.length>0)i.push(e.stmt.bind(...r.params));else{let t=e.getQuery();i.push(this.client.prepare(t.sql).bind(...t.params))}}return(await this.client.batch(i)).map((e,i)=>t[i].mapResult(e,!0))}extractRawAllValueFromBatchResult(e){return e.results}extractRawGetValueFromBatchResult(e){return e.results[0]}extractRawValuesValueFromBatchResult(e){return S(e.results)}async transaction(e,t){let i=new w("async",this.dialect,this,this.schema);await this.run(c.ll.raw(`begin${t?.behavior?" "+t.behavior:""}`));try{let t=await e(i);return await this.run((0,c.ll)`commit`),t}catch(e){throw await this.run((0,c.ll)`rollback`),e}}}class w extends p{static [s.i]="D1Transaction";async transaction(e){let t=`sp${this.nestedIndex}`,i=new w("async",this.dialect,this.session,this.schema,this.nestedIndex+1);await this.session.run(c.ll.raw(`savepoint ${t}`));try{let s=await e(i);return await this.session.run(c.ll.raw(`release savepoint ${t}`)),s}catch(e){throw await this.session.run(c.ll.raw(`rollback to savepoint ${t}`)),e}}}function S(e){let t=[];for(let i of e){let e=Object.keys(i).map(e=>i[e]);t.push(e)}return t}class v extends m{constructor(e,t,i,s,r,n,l){super("async",r,t),this.logger=i,this._isResponseInArrayMode=n,this.customResultMapper=l,this.fields=s,this.stmt=e}static [s.i]="D1PreparedQuery";customResultMapper;fields;stmt;run(e){let t=(0,c.Ct)(this.query.params,e??{});return this.logger.logQuery(this.query.sql,t),this.stmt.bind(...t).run()}async all(e){let{fields:t,query:i,logger:s,stmt:r,customResultMapper:n}=this;if(!t&&!n){let t=(0,c.Ct)(i.params,e??{});return s.logQuery(i.sql,t),r.bind(...t).all().then(({results:e})=>this.mapAllResult(e))}let l=await this.values(e);return this.mapAllResult(l)}mapAllResult(e,t){return(t&&(e=S(e.results)),this.fields||this.customResultMapper)?this.customResultMapper?this.customResultMapper(e):e.map(e=>(0,y.a6)(this.fields,e,this.joinsNotNullableMap)):e}async get(e){let{fields:t,joinsNotNullableMap:i,query:s,logger:r,stmt:n,customResultMapper:l}=this;if(!t&&!l){let t=(0,c.Ct)(s.params,e??{});return r.logQuery(s.sql,t),n.bind(...t).all().then(({results:e})=>e[0])}let a=await this.values(e);return a[0]?l?l(a):(0,y.a6)(t,a[0],i):void 0}mapGetResult(e,t){return(t&&(e=S(e.results)[0]),this.fields||this.customResultMapper)?this.customResultMapper?this.customResultMapper([e]):(0,y.a6)(this.fields,e,this.joinsNotNullableMap):e}values(e){let t=(0,c.Ct)(this.query.params,e??{});return this.logger.logQuery(this.query.sql,t),this.stmt.bind(...t).raw()}isResponseInArrayMode(){return this._isResponseInArrayMode}}class x extends o.N{static [s.i]="D1Database";async batch(e){return this.session.batch(e)}}function T(e,t={}){let i,s;let r=new u.ZR({casing:t.casing});if(!0===t.logger?i=new n:!1!==t.logger&&(i=t.logger),t.schema){let e=(0,a._k)(t.schema,a.DZ);s={fullSchema:t.schema,schema:e.tables,tableNamesMap:e.tableNamesMap}}let l=new b(e,r,s,{logger:i}),o=new x("async",r,l,s);return o.$client=e,o}},66:(e,t,i)=>{"use strict";i.d(t,{i:()=>s,is:()=>r});let s=Symbol.for("drizzle:entityKind");function r(e,t){if(!e||"object"!=typeof e)return!1;if(e instanceof t)return!0;if(!Object.prototype.hasOwnProperty.call(t,s))throw Error(`Class "${t.name??"<unknown>"}" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Drizzle, please report this as a bug.`);let i=Object.getPrototypeOf(e).constructor;if(i)for(;i;){if(s in i&&i[s]===t[s])return!0;i=Object.getPrototypeOf(i)}return!1}Symbol.for("drizzle:hasOwnEntityKind")},7430:(e,t,i)=>{"use strict";i.d(t,{j:()=>n,n:()=>r});var s=i(66);class r extends Error{static [s.i]="DrizzleError";constructor({message:e,cause:t}){super(e),this.name="DrizzleError",this.cause=t}}class n extends r{static [s.i]="TransactionRollbackError";constructor(){super({message:"Rollback"})}}},3829:(e,t,i)=>{"use strict";i.d(t,{zM:()=>a});var s=i(66),r=i(277);class n extends r.pe{static [s.i]="PgBooleanBuilder";constructor(e){super(e,"boolean","PgBoolean")}build(e){return new l(e,this.config)}}class l extends r.Kl{static [s.i]="PgBoolean";getSQLType(){return"boolean"}}function a(e){return new n(e??"")}},277:(e,t,i)=>{"use strict";i.d(t,{Kl:()=>p,pe:()=>g});var s=i(9877),r=i(823),n=i(66),l=i(326);class a{static [n.i]="PgForeignKeyBuilder";reference;_onUpdate="no action";_onDelete="no action";constructor(e,t){this.reference=()=>{let{name:t,columns:i,foreignColumns:s}=e();return{name:t,columns:i,foreignTable:s[0].table,foreignColumns:s}},t&&(this._onUpdate=t.onUpdate,this._onDelete=t.onDelete)}onUpdate(e){return this._onUpdate=void 0===e?"no action":e,this}onDelete(e){return this._onDelete=void 0===e?"no action":e,this}build(e){return new o(e,this)}}class o{constructor(e,t){this.table=e,this.reference=t.reference,this.onUpdate=t._onUpdate,this.onDelete=t._onDelete}static [n.i]="PgForeignKey";reference;onUpdate;onDelete;getName(){let{name:e,columns:t,foreignColumns:i}=this.reference(),s=t.map(e=>e.name),r=i.map(e=>e.name),n=[this.table[l.E],...s,i[0].table[l.E],...r];return e??`${n.join("_")}_fk`}}var u=i(9903);function c(e,t){return`${e[l.E]}_${t.join("_")}_unique`}class h{constructor(e,t){this.name=t,this.columns=e}static [n.i]=null;columns;nullsNotDistinctConfig=!1;nullsNotDistinct(){return this.nullsNotDistinctConfig=!0,this}build(e){return new f(e,this.columns,this.nullsNotDistinctConfig,this.name)}}class d{static [n.i]=null;name;constructor(e){this.name=e}on(...e){return new h(e,this.name)}}class f{constructor(e,t,i,s){this.table=e,this.columns=t,this.name=s??c(this.table,this.columns.map(e=>e.name)),this.nullsNotDistinct=i}static [n.i]=null;columns;name;nullsNotDistinct=!1;getName(){return this.name}}function m(e,t,i){for(let s=t;s<e.length;s++){let r=e[s];if("\\"===r){s++;continue}if('"'===r)return[e.slice(t,s).replace(/\\/g,""),s+1];if(!i&&(","===r||"}"===r))return[e.slice(t,s).replace(/\\/g,""),s]}return[e.slice(t).replace(/\\/g,""),e.length]}class g extends s.Q{foreignKeyConfigs=[];static [n.i]="PgColumnBuilder";array(e){return new w(this.config.name,this,e)}references(e,t={}){return this.foreignKeyConfigs.push({ref:e,actions:t}),this}unique(e,t){return this.config.isUnique=!0,this.config.uniqueName=e,this.config.uniqueType=t?.nulls,this}generatedAlwaysAs(e){return this.config.generated={as:e,type:"always",mode:"stored"},this}buildForeignKeys(e,t){return this.foreignKeyConfigs.map(({ref:i,actions:s})=>(0,u.i)((i,s)=>{let r=new a(()=>({columns:[e],foreignColumns:[i()]}));return s.onUpdate&&r.onUpdate(s.onUpdate),s.onDelete&&r.onDelete(s.onDelete),r.build(t)},i,s))}buildExtraConfigColumn(e){return new y(e,this.config)}}class p extends r.V{constructor(e,t){t.uniqueName||(t.uniqueName=c(e,[t.name])),super(e,t),this.table=e}static [n.i]="PgColumn"}class y extends p{static [n.i]="ExtraConfigColumn";getSQLType(){return this.getSQLType()}indexConfig={order:this.config.order??"asc",nulls:this.config.nulls??"last",opClass:this.config.opClass};defaultConfig={order:"asc",nulls:"last",opClass:void 0};asc(){return this.indexConfig.order="asc",this}desc(){return this.indexConfig.order="desc",this}nullsFirst(){return this.indexConfig.nulls="first",this}nullsLast(){return this.indexConfig.nulls="last",this}op(e){return this.indexConfig.opClass=e,this}}class b{static [n.i]=null;constructor(e,t,i,s){this.name=e,this.keyAsName=t,this.type=i,this.indexConfig=s}name;keyAsName;type;indexConfig}class w extends g{static [n.i]="PgArrayBuilder";constructor(e,t,i){super(e,"array","PgArray"),this.config.baseBuilder=t,this.config.size=i}build(e){let t=this.config.baseBuilder.build(e);return new S(e,this.config,t)}}class S extends p{constructor(e,t,i,s){super(e,t),this.baseColumn=i,this.range=s,this.size=t.size}size;static [n.i]="PgArray";getSQLType(){return`${this.baseColumn.getSQLType()}[${"number"==typeof this.size?this.size:""}]`}mapFromDriverValue(e){return"string"==typeof e&&(e=function(e){let[t]=function e(t,i=0){let s=[],r=i,n=!1;for(;r<t.length;){let l=t[r];if(","===l){(n||r===i)&&s.push(""),n=!0,r++;continue}if(n=!1,"\\"===l){r+=2;continue}if('"'===l){let[e,i]=m(t,r+1,!0);s.push(e),r=i;continue}if("}"===l)return[s,r+1];if("{"===l){let[i,n]=e(t,r+1);s.push(i),r=n;continue}let[a,o]=m(t,r,!1);s.push(a),r=o}return[s,r]}(e,1);return t}(e)),e.map(e=>this.baseColumn.mapFromDriverValue(e))}mapToDriverValue(e,t=!1){let i=e.map(e=>null===e?null:(0,n.is)(this.baseColumn,S)?this.baseColumn.mapToDriverValue(e,!0):this.baseColumn.mapToDriverValue(e));return t?i:function e(t){return`{${t.map(t=>Array.isArray(t)?e(t):"string"==typeof t?`"${t.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`:`${t}`).join(",")}}`}(i)}}},9254:(e,t,i)=>{"use strict";i.d(t,{u:()=>l});var s=i(66),r=i(7536),n=i(277);class l extends n.pe{static [s.i]="PgDateColumnBaseBuilder";defaultNow(){return this.default((0,r.ll)`now()`)}}},4777:(e,t,i)=>{"use strict";i.d(t,{dw:()=>c,p6:()=>h,qw:()=>o});var s=i(66),r=i(6104),n=i(277),l=i(9254);class a extends l.u{static [s.i]="PgDateBuilder";constructor(e){super(e,"date","PgDate")}build(e){return new o(e,this.config)}}class o extends n.Kl{static [s.i]="PgDate";getSQLType(){return"date"}mapFromDriverValue(e){return new Date(e)}mapToDriverValue(e){return e.toISOString()}}class u extends l.u{static [s.i]="PgDateStringBuilder";constructor(e){super(e,"string","PgDateString")}build(e){return new c(e,this.config)}}class c extends n.Kl{static [s.i]="PgDateString";getSQLType(){return"date"}}function h(e,t){let{name:i,config:s}=(0,r.Ll)(e,t);return s?.mode==="date"?new a(i):new u(i)}},9681:(e,t,i)=>{"use strict";i.d(t,{p:()=>n});var s=i(66),r=i(277);class n extends r.pe{static [s.i]="PgIntColumnBaseBuilder";generatedAlwaysAsIdentity(e){if(e){let{name:t,...i}=e;this.config.generatedIdentity={type:"always",sequenceName:t,sequenceOptions:i}}else this.config.generatedIdentity={type:"always"};return this.config.hasDefault=!0,this.config.notNull=!0,this}generatedByDefaultAsIdentity(e){if(e){let{name:t,...i}=e;this.config.generatedIdentity={type:"byDefault",sequenceName:t,sequenceOptions:i}}else this.config.generatedIdentity={type:"byDefault"};return this.config.hasDefault=!0,this.config.notNull=!0,this}}},6071:(e,t,i)=>{"use strict";i.d(t,{nd:()=>o});var s=i(66),r=i(277),n=i(9681);class l extends n.p{static [s.i]="PgIntegerBuilder";constructor(e){super(e,"number","PgInteger")}build(e){return new a(e,this.config)}}class a extends r.Kl{static [s.i]="PgInteger";getSQLType(){return"integer"}mapFromDriverValue(e){return"string"==typeof e?Number.parseInt(e):e}}function o(e){return new l(e??"")}},2583:(e,t,i)=>{"use strict";i.d(t,{Pq:()=>a,iX:()=>l});var s=i(66),r=i(277);class n extends r.pe{static [s.i]="PgJsonBuilder";constructor(e){super(e,"json","PgJson")}build(e){return new l(e,this.config)}}class l extends r.Kl{static [s.i]="PgJson";constructor(e,t){super(e,t)}getSQLType(){return"json"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function a(e){return new n(e??"")}},4061:(e,t,i)=>{"use strict";i.d(t,{Fx:()=>a,kn:()=>l});var s=i(66),r=i(277);class n extends r.pe{static [s.i]="PgJsonbBuilder";constructor(e){super(e,"json","PgJsonb")}build(e){return new l(e,this.config)}}class l extends r.Kl{static [s.i]="PgJsonb";constructor(e,t){super(e,t)}getSQLType(){return"jsonb"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function a(e){return new n(e??"")}},8266:(e,t,i)=>{"use strict";i.d(t,{Z5:()=>a,sH:()=>o});var s=i(66),r=i(6104),n=i(277);class l extends n.pe{static [s.i]="PgNumericBuilder";constructor(e,t,i){super(e,"string","PgNumeric"),this.config.precision=t,this.config.scale=i}build(e){return new a(e,this.config)}}class a extends n.Kl{static [s.i]="PgNumeric";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}function o(e,t){let{name:i,config:s}=(0,r.Ll)(e,t);return new l(i,s?.precision,s?.scale)}},3606:(e,t,i)=>{"use strict";i.d(t,{Qq:()=>o});var s=i(66),r=i(6104),n=i(277);class l extends n.pe{static [s.i]="PgTextBuilder";constructor(e,t){super(e,"string","PgText"),this.config.enumValues=t.enum}build(e){return new a(e,this.config)}}class a extends n.Kl{static [s.i]="PgText";enumValues=this.config.enumValues;getSQLType(){return"text"}}function o(e,t={}){let{name:i,config:s}=(0,r.Ll)(e,t);return new l(i,s)}},3704:(e,t,i)=>{"use strict";i.d(t,{Xd:()=>o,kB:()=>u});var s=i(66),r=i(6104),n=i(277),l=i(9254);class a extends l.u{constructor(e,t,i){super(e,"string","PgTime"),this.withTimezone=t,this.precision=i,this.config.withTimezone=t,this.config.precision=i}static [s.i]="PgTimeBuilder";build(e){return new o(e,this.config)}}class o extends n.Kl{static [s.i]="PgTime";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`time${e}${this.withTimezone?" with time zone":""}`}}function u(e,t={}){let{name:i,config:s}=(0,r.Ll)(e,t);return new a(i,s.withTimezone??!1,s.precision)}},6197:(e,t,i)=>{"use strict";i.d(t,{KM:()=>o,vE:()=>h,xQ:()=>c});var s=i(66),r=i(6104),n=i(277),l=i(9254);class a extends l.u{static [s.i]="PgTimestampBuilder";constructor(e,t,i){super(e,"date","PgTimestamp"),this.config.withTimezone=t,this.config.precision=i}build(e){return new o(e,this.config)}}class o extends n.Kl{static [s.i]="PgTimestamp";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":` (${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}mapFromDriverValue=e=>new Date(this.withTimezone?e:e+"+0000");mapToDriverValue=e=>e.toISOString()}class u extends l.u{static [s.i]="PgTimestampStringBuilder";constructor(e,t,i){super(e,"string","PgTimestampString"),this.config.withTimezone=t,this.config.precision=i}build(e){return new c(e,this.config)}}class c extends n.Kl{static [s.i]="PgTimestampString";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}}function h(e,t={}){let{name:i,config:s}=(0,r.Ll)(e,t);return s?.mode==="string"?new u(i,s.withTimezone??!1,s.precision):new a(i,s?.withTimezone??!1,s?.precision)}},5594:(e,t,i)=>{"use strict";i.d(t,{dL:()=>a,uR:()=>o});var s=i(66),r=i(7536),n=i(277);class l extends n.pe{static [s.i]="PgUUIDBuilder";constructor(e){super(e,"string","PgUUID")}defaultRandom(){return this.default((0,r.ll)`gen_random_uuid()`)}build(e){return new a(e,this.config)}}class a extends n.Kl{static [s.i]="PgUUID";getSQLType(){return"uuid"}}function o(e){return new l(e??"")}},344:(e,t,i)=>{"use strict";i.d(t,{hv:()=>l,ie:()=>n});var s=i(66),r=i(3345);function n(...e){return e[0].columns?new l(e[0].columns,e[0].name):new l(e)}class l{static [s.i]="PgPrimaryKeyBuilder";columns;name;constructor(e,t){this.columns=e,this.name=t}build(e){return new a(e,this.columns,this.name)}}class a{constructor(e,t,i){this.table=e,this.columns=t,this.name=i}static [s.i]="PgPrimaryKey";columns;name;getName(){return this.name??`${this.table[r.mu.Symbol.Name]}_${this.columns.map(e=>e.name).join("_")}_pk`}}},3345:(e,t,i)=>{"use strict";i.d(t,{mu:()=>ez,cJ:()=>ek});var s=i(66),r=i(8685),n=i(6104),l=i(277),a=i(9681);class o extends a.p{static [s.i]="PgBigInt53Builder";constructor(e){super(e,"number","PgBigInt53")}build(e){return new u(e,this.config)}}class u extends l.Kl{static [s.i]="PgBigInt53";getSQLType(){return"bigint"}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class c extends a.p{static [s.i]="PgBigInt64Builder";constructor(e){super(e,"bigint","PgBigInt64")}build(e){return new h(e,this.config)}}class h extends l.Kl{static [s.i]="PgBigInt64";getSQLType(){return"bigint"}mapFromDriverValue(e){return BigInt(e)}}function d(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return"number"===s.mode?new o(i):new c(i)}class f extends l.pe{static [s.i]="PgBigSerial53Builder";constructor(e){super(e,"number","PgBigSerial53"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new m(e,this.config)}}class m extends l.Kl{static [s.i]="PgBigSerial53";getSQLType(){return"bigserial"}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class g extends l.pe{static [s.i]="PgBigSerial64Builder";constructor(e){super(e,"bigint","PgBigSerial64"),this.config.hasDefault=!0}build(e){return new p(e,this.config)}}class p extends l.Kl{static [s.i]="PgBigSerial64";getSQLType(){return"bigserial"}mapFromDriverValue(e){return BigInt(e)}}function y(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return"number"===s.mode?new f(i):new g(i)}var b=i(3829);class w extends l.pe{static [s.i]="PgCharBuilder";constructor(e,t){super(e,"string","PgChar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new S(e,this.config)}}class S extends l.Kl{static [s.i]="PgChar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"char":`char(${this.length})`}}function v(e,t={}){let{name:i,config:s}=(0,n.Ll)(e,t);return new w(i,s)}class x extends l.pe{static [s.i]="PgCidrBuilder";constructor(e){super(e,"string","PgCidr")}build(e){return new T(e,this.config)}}class T extends l.Kl{static [s.i]="PgCidr";getSQLType(){return"cidr"}}function $(e){return new x(e??"")}class Q extends l.pe{static [s.i]="PgCustomColumnBuilder";constructor(e,t,i){super(e,"custom","PgCustomColumn"),this.config.fieldConfig=t,this.config.customTypeParams=i}build(e){return new L(e,this.config)}}class L extends l.Kl{static [s.i]="PgCustomColumn";sqlName;mapTo;mapFrom;constructor(e,t){super(e,t),this.sqlName=t.customTypeParams.dataType(t.fieldConfig),this.mapTo=t.customTypeParams.toDriver,this.mapFrom=t.customTypeParams.fromDriver}getSQLType(){return this.sqlName}mapFromDriverValue(e){return"function"==typeof this.mapFrom?this.mapFrom(e):e}mapToDriverValue(e){return"function"==typeof this.mapTo?this.mapTo(e):e}}function N(e){return(t,i)=>{let{name:s,config:r}=(0,n.Ll)(t,i);return new Q(s,r,e)}}var P=i(4777);class C extends l.pe{static [s.i]="PgDoublePrecisionBuilder";constructor(e){super(e,"number","PgDoublePrecision")}build(e){return new q(e,this.config)}}class q extends l.Kl{static [s.i]="PgDoublePrecision";getSQLType(){return"double precision"}mapFromDriverValue(e){return"string"==typeof e?Number.parseFloat(e):e}}function B(e){return new C(e??"")}class I extends l.pe{static [s.i]="PgInetBuilder";constructor(e){super(e,"string","PgInet")}build(e){return new D(e,this.config)}}class D extends l.Kl{static [s.i]="PgInet";getSQLType(){return"inet"}}function O(e){return new I(e??"")}var j=i(6071);class F extends l.pe{static [s.i]="PgIntervalBuilder";constructor(e,t){super(e,"string","PgInterval"),this.config.intervalConfig=t}build(e){return new _(e,this.config)}}class _ extends l.Kl{static [s.i]="PgInterval";fields=this.config.intervalConfig.fields;precision=this.config.intervalConfig.precision;getSQLType(){let e=this.fields?` ${this.fields}`:"",t=this.precision?`(${this.precision})`:"";return`interval${e}${t}`}}function V(e,t={}){let{name:i,config:s}=(0,n.Ll)(e,t);return new F(i,s)}var A=i(2583),E=i(4061);class R extends l.pe{static [s.i]="PgLineBuilder";constructor(e){super(e,"array","PgLine")}build(e){return new K(e,this.config)}}class K extends l.Kl{static [s.i]="PgLine";getSQLType(){return"line"}mapFromDriverValue(e){let[t,i,s]=e.slice(1,-1).split(",");return[Number.parseFloat(t),Number.parseFloat(i),Number.parseFloat(s)]}mapToDriverValue(e){return`{${e[0]},${e[1]},${e[2]}}`}}class X extends l.pe{static [s.i]="PgLineABCBuilder";constructor(e){super(e,"json","PgLineABC")}build(e){return new z(e,this.config)}}class z extends l.Kl{static [s.i]="PgLineABC";getSQLType(){return"line"}mapFromDriverValue(e){let[t,i,s]=e.slice(1,-1).split(",");return{a:Number.parseFloat(t),b:Number.parseFloat(i),c:Number.parseFloat(s)}}mapToDriverValue(e){return`{${e.a},${e.b},${e.c}}`}}function k(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return s?.mode&&"tuple"!==s.mode?new X(i):new R(i)}class M extends l.pe{static [s.i]="PgMacaddrBuilder";constructor(e){super(e,"string","PgMacaddr")}build(e){return new U(e,this.config)}}class U extends l.Kl{static [s.i]="PgMacaddr";getSQLType(){return"macaddr"}}function J(e){return new M(e??"")}class H extends l.pe{static [s.i]="PgMacaddr8Builder";constructor(e){super(e,"string","PgMacaddr8")}build(e){return new W(e,this.config)}}class W extends l.Kl{static [s.i]="PgMacaddr8";getSQLType(){return"macaddr8"}}function G(e){return new H(e??"")}var Y=i(8266);class Z extends l.pe{static [s.i]="PgPointTupleBuilder";constructor(e){super(e,"array","PgPointTuple")}build(e){return new ee(e,this.config)}}class ee extends l.Kl{static [s.i]="PgPointTuple";getSQLType(){return"point"}mapFromDriverValue(e){if("string"==typeof e){let[t,i]=e.slice(1,-1).split(",");return[Number.parseFloat(t),Number.parseFloat(i)]}return[e.x,e.y]}mapToDriverValue(e){return`(${e[0]},${e[1]})`}}class et extends l.pe{static [s.i]="PgPointObjectBuilder";constructor(e){super(e,"json","PgPointObject")}build(e){return new ei(e,this.config)}}class ei extends l.Kl{static [s.i]="PgPointObject";getSQLType(){return"point"}mapFromDriverValue(e){if("string"==typeof e){let[t,i]=e.slice(1,-1).split(",");return{x:Number.parseFloat(t),y:Number.parseFloat(i)}}return e}mapToDriverValue(e){return`(${e.x},${e.y})`}}function es(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return s?.mode&&"tuple"!==s.mode?new et(i):new Z(i)}function er(e,t){let i=new DataView(new ArrayBuffer(8));for(let s=0;s<8;s++)i.setUint8(s,e[t+s]);return i.getFloat64(0,!0)}function en(e){let t=function(e){let t=[];for(let i=0;i<e.length;i+=2)t.push(Number.parseInt(e.slice(i,i+2),16));return new Uint8Array(t)}(e),i=0,s=t[0];i+=1;let r=new DataView(t.buffer),n=r.getUint32(i,1===s);if(i+=4,0x20000000&n&&(r.getUint32(i,1===s),i+=4),(65535&n)==1){let e=er(t,i),s=er(t,i+=8);return i+=8,[e,s]}throw Error("Unsupported geometry type")}class el extends l.pe{static [s.i]="PgGeometryBuilder";constructor(e){super(e,"array","PgGeometry")}build(e){return new ea(e,this.config)}}class ea extends l.Kl{static [s.i]="PgGeometry";getSQLType(){return"geometry(point)"}mapFromDriverValue(e){return en(e)}mapToDriverValue(e){return`point(${e[0]} ${e[1]})`}}class eo extends l.pe{static [s.i]="PgGeometryObjectBuilder";constructor(e){super(e,"json","PgGeometryObject")}build(e){return new eu(e,this.config)}}class eu extends l.Kl{static [s.i]="PgGeometryObject";getSQLType(){return"geometry(point)"}mapFromDriverValue(e){let t=en(e);return{x:t[0],y:t[1]}}mapToDriverValue(e){return`point(${e.x} ${e.y})`}}function ec(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return s?.mode&&"tuple"!==s.mode?new eo(i):new el(i)}class eh extends l.pe{static [s.i]="PgRealBuilder";constructor(e,t){super(e,"number","PgReal"),this.config.length=t}build(e){return new ed(e,this.config)}}class ed extends l.Kl{static [s.i]="PgReal";constructor(e,t){super(e,t)}getSQLType(){return"real"}mapFromDriverValue=e=>"string"==typeof e?Number.parseFloat(e):e}function ef(e){return new eh(e??"")}class em extends l.pe{static [s.i]="PgSerialBuilder";constructor(e){super(e,"number","PgSerial"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new eg(e,this.config)}}class eg extends l.Kl{static [s.i]="PgSerial";getSQLType(){return"serial"}}function ep(e){return new em(e??"")}class ey extends a.p{static [s.i]="PgSmallIntBuilder";constructor(e){super(e,"number","PgSmallInt")}build(e){return new eb(e,this.config)}}class eb extends l.Kl{static [s.i]="PgSmallInt";getSQLType(){return"smallint"}mapFromDriverValue=e=>"string"==typeof e?Number(e):e}function ew(e){return new ey(e??"")}class eS extends l.pe{static [s.i]="PgSmallSerialBuilder";constructor(e){super(e,"number","PgSmallSerial"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new ev(e,this.config)}}class ev extends l.Kl{static [s.i]="PgSmallSerial";getSQLType(){return"smallserial"}}function ex(e){return new eS(e??"")}var eT=i(3606),e$=i(3704),eQ=i(6197),eL=i(5594);class eN extends l.pe{static [s.i]="PgVarcharBuilder";constructor(e,t){super(e,"string","PgVarchar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new eP(e,this.config)}}class eP extends l.Kl{static [s.i]="PgVarchar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"varchar":`varchar(${this.length})`}}function eC(e,t={}){let{name:i,config:s}=(0,n.Ll)(e,t);return new eN(i,s)}class eq extends l.pe{static [s.i]="PgBinaryVectorBuilder";constructor(e,t){super(e,"string","PgBinaryVector"),this.config.dimensions=t.dimensions}build(e){return new eB(e,this.config)}}class eB extends l.Kl{static [s.i]="PgBinaryVector";dimensions=this.config.dimensions;getSQLType(){return`bit(${this.dimensions})`}}function eI(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return new eq(i,s)}class eD extends l.pe{static [s.i]="PgHalfVectorBuilder";constructor(e,t){super(e,"array","PgHalfVector"),this.config.dimensions=t.dimensions}build(e){return new eO(e,this.config)}}class eO extends l.Kl{static [s.i]="PgHalfVector";dimensions=this.config.dimensions;getSQLType(){return`halfvec(${this.dimensions})`}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){return e.slice(1,-1).split(",").map(e=>Number.parseFloat(e))}}function ej(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return new eD(i,s)}class eF extends l.pe{static [s.i]="PgSparseVectorBuilder";constructor(e,t){super(e,"string","PgSparseVector"),this.config.dimensions=t.dimensions}build(e){return new e_(e,this.config)}}class e_ extends l.Kl{static [s.i]="PgSparseVector";dimensions=this.config.dimensions;getSQLType(){return`sparsevec(${this.dimensions})`}}function eV(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return new eF(i,s)}class eA extends l.pe{static [s.i]="PgVectorBuilder";constructor(e,t){super(e,"array","PgVector"),this.config.dimensions=t.dimensions}build(e){return new eE(e,this.config)}}class eE extends l.Kl{static [s.i]="PgVector";dimensions=this.config.dimensions;getSQLType(){return`vector(${this.dimensions})`}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){return e.slice(1,-1).split(",").map(e=>Number.parseFloat(e))}}function eR(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return new eA(i,s)}let eK=Symbol.for("drizzle:PgInlineForeignKeys"),eX=Symbol.for("drizzle:EnableRLS");class ez extends r.XI{static [s.i]="PgTable";static Symbol=Object.assign({},r.XI.Symbol,{InlineForeignKeys:eK,EnableRLS:eX});[eK]=[];[eX]=!1;[r.XI.Symbol.ExtraConfigBuilder]=void 0}let ek=(e,t,i)=>(function(e,t,i,s,n=e){let l=new ez(e,s,n),a="function"==typeof t?t({bigint:d,bigserial:y,boolean:b.zM,char:v,cidr:$,customType:N,date:P.p6,doublePrecision:B,inet:O,integer:j.nd,interval:V,json:A.Pq,jsonb:E.Fx,line:k,macaddr:J,macaddr8:G,numeric:Y.sH,point:es,geometry:ec,real:ef,serial:ep,smallint:ew,smallserial:ex,text:eT.Qq,time:e$.kB,timestamp:eQ.vE,uuid:eL.uR,varchar:eC,bit:eI,halfvec:ej,sparsevec:eV,vector:eR}):t,o=Object.fromEntries(Object.entries(a).map(([e,t])=>{t.setName(e);let i=t.build(l);return l[eK].push(...t.buildForeignKeys(i,l)),[e,i]})),u=Object.fromEntries(Object.entries(a).map(([e,t])=>(t.setName(e),[e,t.buildExtraConfigColumn(l)]))),c=Object.assign(l,o);return c[r.XI.Symbol.Columns]=o,c[r.XI.Symbol.ExtraConfigColumns]=u,i&&(c[ez.Symbol.ExtraConfigBuilder]=i),Object.assign(c,{enableRLS:()=>(c[ez.Symbol.EnableRLS]=!0,c)})})(e,t,i,void 0)},8471:(e,t,i)=>{"use strict";i.d(t,{O:()=>r});var s=i(66);class r{static [s.i]="TypedQueryBuilder";getSelectedFields(){return this._.selectedFields}}},9389:(e,t,i)=>{"use strict";i.d(t,{k:()=>r});var s=i(66);class r{static [s.i]="QueryPromise";[Symbol.toStringTag]="QueryPromise";catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}then(e,t){return this.execute().then(e,t)}}},652:(e,t,i)=>{"use strict";i.d(t,{DZ:()=>w,I$:()=>function e(t,i,s,l,a=e=>e){let o={};for(let[c,h]of l.entries())if(h.isJson){let r=i.relations[h.tsKey],l=s[c],u="string"==typeof l?JSON.parse(l):l;o[h.tsKey]=(0,n.is)(r,d)?u&&e(t,t[h.relationTableTsKey],u,h.selection,a):u.map(i=>e(t,t[h.relationTableTsKey],i,h.selection,a))}else{let e;let t=a(s[c]),i=h.field;e=(0,n.is)(i,r.V)?i:(0,n.is)(i,u.Xs)?i.decoder:i.sql.decoder,o[h.tsKey]=null===t?null:e.mapFromDriverValue(t)}return o},K1:()=>y,W0:()=>b,_k:()=>p,iv:()=>f,mm:()=>m,pD:()=>d,rl:()=>g});var s=i(8685),r=i(823),n=i(66),l=i(344),a=i(9066),o=i(5888),u=i(7536);class c{constructor(e,t,i){this.sourceTable=e,this.referencedTable=t,this.relationName=i,this.referencedTableName=t[s.XI.Symbol.Name]}static [n.i]="Relation";referencedTableName;fieldName}class h{constructor(e,t){this.table=e,this.config=t}static [n.i]="Relations"}class d extends c{constructor(e,t,i,s){super(e,t,i?.relationName),this.config=i,this.isNullable=s}static [n.i]="One";withFieldName(e){let t=new d(this.sourceTable,this.referencedTable,this.config,this.isNullable);return t.fieldName=e,t}}class f extends c{constructor(e,t,i){super(e,t,i?.relationName),this.config=i}static [n.i]="Many";withFieldName(e){let t=new f(this.sourceTable,this.referencedTable,this.config);return t.fieldName=e,t}}function m(){return{and:a.Uo,between:a.Tq,eq:a.eq,exists:a.t2,gt:a.gt,gte:a.RO,ilike:a.B3,inArray:a.RV,isNull:a.kZ,isNotNull:a.Pe,like:a.mj,lt:a.lt,lte:a.wJ,ne:a.ne,not:a.AU,notBetween:a.o8,notExists:a.KJ,notLike:a.RK,notIlike:a.q1,notInArray:a.KL,or:a.or,sql:u.ll}}function g(){return{sql:u.ll,asc:o.Y,desc:o.i}}function p(e,t){1===Object.keys(e).length&&"default"in e&&!(0,n.is)(e.default,s.XI)&&(e=e.default);let i={},r={},a={};for(let[o,u]of Object.entries(e))if((0,n.is)(u,s.XI)){let e=(0,s.Lf)(u),t=r[e];for(let r of(i[e]=o,a[o]={tsName:o,dbName:u[s.XI.Symbol.Name],schema:u[s.XI.Symbol.Schema],columns:u[s.XI.Symbol.Columns],relations:t?.relations??{},primaryKey:t?.primaryKey??[]},Object.values(u[s.XI.Symbol.Columns])))r.primary&&a[o].primaryKey.push(r);let c=u[s.XI.Symbol.ExtraConfigBuilder]?.(u[s.XI.Symbol.ExtraConfigColumns]);if(c)for(let e of Object.values(c))(0,n.is)(e,l.hv)&&a[o].primaryKey.push(...e.columns)}else if((0,n.is)(u,h)){let e;let n=(0,s.Lf)(u.table),l=i[n];for(let[i,s]of Object.entries(u.config(t(u.table))))if(l){let t=a[l];t.relations[i]=s,e&&t.primaryKey.push(...e)}else n in r||(r[n]={relations:{},primaryKey:e}),r[n].relations[i]=s}return{tables:a,tableNamesMap:i}}function y(e,t){return new h(e,e=>Object.fromEntries(Object.entries(t(e)).map(([e,t])=>[e,t.withFieldName(e)])))}function b(e,t,i){if((0,n.is)(i,d)&&i.config)return{fields:i.config.fields,references:i.config.references};let r=t[(0,s.Lf)(i.referencedTable)];if(!r)throw Error(`Table "${i.referencedTable[s.XI.Symbol.Name]}" not found in schema`);let l=e[r];if(!l)throw Error(`Table "${r}" not found in schema`);let a=i.sourceTable,o=t[(0,s.Lf)(a)];if(!o)throw Error(`Table "${a[s.XI.Symbol.Name]}" not found in schema`);let u=[];for(let e of Object.values(l.relations))(i.relationName&&i!==e&&e.relationName===i.relationName||!i.relationName&&e.referencedTable===i.sourceTable)&&u.push(e);if(u.length>1)throw i.relationName?Error(`There are multiple relations with name "${i.relationName}" in table "${r}"`):Error(`There are multiple relations between "${r}" and "${i.sourceTable[s.XI.Symbol.Name]}". Please specify relation name`);if(u[0]&&(0,n.is)(u[0],d)&&u[0].config)return{fields:u[0].config.references,references:u[0].config.fields};throw Error(`There is not enough information to infer relation "${o}.${i.fieldName}"`)}function w(e){return{one:function(t,i){return new d(e,t,i,i?.fields.reduce((e,t)=>e&&t.notNull,!0)??!1)},many:function(t,i){return new f(e,t,i)}}}},4294:(e,t,i)=>{"use strict";i.d(t,{b:()=>u});var s=i(8421),r=i(823),n=i(66),l=i(7536),a=i(2923),o=i(4996);class u{static [n.i]="SelectionProxyHandler";config;constructor(e){this.config={...e}}get(e,t){if("_"===t)return{...e._,selectedFields:new Proxy(e._.selectedFields,this)};if(t===o.n)return{...e[o.n],selectedFields:new Proxy(e[o.n].selectedFields,this)};if("symbol"==typeof t)return e[t];let i=((0,n.is)(e,a.n)?e._.selectedFields:(0,n.is)(e,l.Ss)?e[o.n].selectedFields:e)[t];if((0,n.is)(i,l.Xs.Aliased)){if("sql"===this.config.sqlAliasedBehavior&&!i.isSelectionField)return i.sql;let e=i.clone();return e.isSelectionField=!0,e}if((0,n.is)(i,l.Xs)){if("sql"===this.config.sqlBehavior)return i;throw Error(`You tried to reference "${t}" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using ".as('alias')" method.`)}return(0,n.is)(i,r.V)?this.config.alias?new Proxy(i,new s.Ht(new Proxy(i.table,new s.h_(this.config.alias,this.config.replaceOriginalName??!1)))):i:"object"!=typeof i||null===i?i:new Proxy(i,new u(this.config))}}},9066:(e,t,i)=>{"use strict";i.d(t,{AU:()=>d,B3:()=>N,KJ:()=>x,KL:()=>b,Pe:()=>S,RK:()=>L,RO:()=>m,RV:()=>y,Tq:()=>T,Uo:()=>c,eq:()=>o,gt:()=>f,kZ:()=>w,lt:()=>g,mj:()=>Q,ne:()=>u,o8:()=>$,or:()=>h,q1:()=>P,t2:()=>v,wJ:()=>p});var s=i(823),r=i(66),n=i(8685),l=i(7536);function a(e,t){return!(0,l.eG)(t)||(0,l.qt)(e)||(0,r.is)(e,l.Iw)||(0,r.is)(e,l.Or)||(0,r.is)(e,s.V)||(0,r.is)(e,n.XI)||(0,r.is)(e,l.Ss)?e:new l.Iw(e,t)}let o=(e,t)=>(0,l.ll)`${e} = ${a(t,e)}`,u=(e,t)=>(0,l.ll)`${e} <> ${a(t,e)}`;function c(...e){let t=e.filter(e=>void 0!==e);return 0===t.length?void 0:new l.Xs(1===t.length?t:[new l.DJ("("),l.ll.join(t,new l.DJ(" and ")),new l.DJ(")")])}function h(...e){let t=e.filter(e=>void 0!==e);return 0===t.length?void 0:new l.Xs(1===t.length?t:[new l.DJ("("),l.ll.join(t,new l.DJ(" or ")),new l.DJ(")")])}function d(e){return(0,l.ll)`not ${e}`}let f=(e,t)=>(0,l.ll)`${e} > ${a(t,e)}`,m=(e,t)=>(0,l.ll)`${e} >= ${a(t,e)}`,g=(e,t)=>(0,l.ll)`${e} < ${a(t,e)}`,p=(e,t)=>(0,l.ll)`${e} <= ${a(t,e)}`;function y(e,t){return Array.isArray(t)?0===t.length?(0,l.ll)`false`:(0,l.ll)`${e} in ${t.map(t=>a(t,e))}`:(0,l.ll)`${e} in ${a(t,e)}`}function b(e,t){return Array.isArray(t)?0===t.length?(0,l.ll)`true`:(0,l.ll)`${e} not in ${t.map(t=>a(t,e))}`:(0,l.ll)`${e} not in ${a(t,e)}`}function w(e){return(0,l.ll)`${e} is null`}function S(e){return(0,l.ll)`${e} is not null`}function v(e){return(0,l.ll)`exists ${e}`}function x(e){return(0,l.ll)`not exists ${e}`}function T(e,t,i){return(0,l.ll)`${e} between ${a(t,e)} and ${a(i,e)}`}function $(e,t,i){return(0,l.ll)`${e} not between ${a(t,e)} and ${a(i,e)}`}function Q(e,t){return(0,l.ll)`${e} like ${t}`}function L(e,t){return(0,l.ll)`${e} not like ${t}`}function N(e,t){return(0,l.ll)`${e} ilike ${t}`}function P(e,t){return(0,l.ll)`${e} not ilike ${t}`}},5888:(e,t,i)=>{"use strict";i.d(t,{Y:()=>r,i:()=>n});var s=i(7536);function r(e){return(0,s.ll)`${e} asc`}function n(e){return(0,s.ll)`${e} desc`}},7536:(e,t,i)=>{"use strict";i.d(t,{Iw:()=>v,Or:()=>T,Xs:()=>p,DJ:()=>g,Ss:()=>Q,Ct:()=>$,eG:()=>b,qt:()=>m,ll:()=>x});var s=i(66),r=i(277);let n=Symbol.for("drizzle:isPgEnum");class l extends r.pe{static [s.i]="PgEnumColumnBuilder";constructor(e,t){super(e,"string","PgEnumColumn"),this.config.enum=t}build(e){return new a(e,this.config)}}class a extends r.Kl{static [s.i]="PgEnumColumn";enum=this.config.enum;enumValues=this.config.enum.enumValues;constructor(e,t){super(e,t),this.enum=t.enum}getSQLType(){return this.enum.enumName}}var o=i(2923),u=i(7424),c=i(4996),h=i(823),d=i(8685);class f{static [s.i]=null}function m(e){return null!=e&&"function"==typeof e.getSQL}class g{static [s.i]="StringChunk";value;constructor(e){this.value=Array.isArray(e)?e:[e]}getSQL(){return new p([this])}}class p{constructor(e){this.queryChunks=e}static [s.i]="SQL";decoder=w;shouldInlineParams=!1;append(e){return this.queryChunks.push(...e.queryChunks),this}toQuery(e){return u.k.startActiveSpan("drizzle.buildSQL",t=>{let i=this.buildQueryFromSourceParams(this.queryChunks,e);return t?.setAttributes({"drizzle.query.text":i.sql,"drizzle.query.params":JSON.stringify(i.params)}),i})}buildQueryFromSourceParams(e,t){let i=Object.assign({},t,{inlineParams:t.inlineParams||this.shouldInlineParams,paramStartIndex:t.paramStartIndex||{value:0}}),{casing:r,escapeName:l,escapeParam:a,prepareTyping:u,inlineParams:f,paramStartIndex:b}=i;return function(e){let t={sql:"",params:[]};for(let i of e)t.sql+=i.sql,t.params.push(...i.params),i.typings?.length&&(t.typings||(t.typings=[]),t.typings.push(...i.typings));return t}(e.map(e=>{if((0,s.is)(e,g))return{sql:e.value.join(""),params:[]};if((0,s.is)(e,y))return{sql:l(e.value),params:[]};if(void 0===e)return{sql:"",params:[]};if(Array.isArray(e)){let t=[new g("(")];for(let[i,s]of e.entries())t.push(s),i<e.length-1&&t.push(new g(", "));return t.push(new g(")")),this.buildQueryFromSourceParams(t,i)}if((0,s.is)(e,p))return this.buildQueryFromSourceParams(e.queryChunks,{...i,inlineParams:f||e.shouldInlineParams});if((0,s.is)(e,d.XI)){let t=e[d.XI.Symbol.Schema],i=e[d.XI.Symbol.Name];return{sql:void 0===t?l(i):l(t)+"."+l(i),params:[]}}if((0,s.is)(e,h.V)){let i=r.getColumnCasing(e);if("indexes"===t.invokeSource)return{sql:l(i),params:[]};let s=e.table[d.XI.Symbol.Schema];return{sql:e.table[d.HE]||void 0===s?l(e.table[d.XI.Symbol.Name])+"."+l(i):l(s)+"."+l(e.table[d.XI.Symbol.Name])+"."+l(i),params:[]}}if((0,s.is)(e,Q)){let t=e[c.n].schema,i=e[c.n].name;return{sql:void 0===t?l(i):l(t)+"."+l(i),params:[]}}if((0,s.is)(e,v)){if((0,s.is)(e.value,T))return{sql:a(b.value++,e),params:[e],typings:["none"]};let t=null===e.value?null:e.encoder.mapToDriverValue(e.value);if((0,s.is)(t,p))return this.buildQueryFromSourceParams([t],i);if(f)return{sql:this.mapInlineParam(t,i),params:[]};let r=["none"];return u&&(r=[u(e.encoder)]),{sql:a(b.value++,t),params:[t],typings:r}}return(0,s.is)(e,T)?{sql:a(b.value++,e),params:[e],typings:["none"]}:(0,s.is)(e,p.Aliased)&&void 0!==e.fieldAlias?{sql:l(e.fieldAlias),params:[]}:(0,s.is)(e,o.n)?e._.isWith?{sql:l(e._.alias),params:[]}:this.buildQueryFromSourceParams([new g("("),e._.sql,new g(") "),new y(e._.alias)],i):e&&"function"==typeof e&&n in e&&!0===e[n]?e.schema?{sql:l(e.schema)+"."+l(e.enumName),params:[]}:{sql:l(e.enumName),params:[]}:m(e)?e.shouldOmitSQLParens?.()?this.buildQueryFromSourceParams([e.getSQL()],i):this.buildQueryFromSourceParams([new g("("),e.getSQL(),new g(")")],i):f?{sql:this.mapInlineParam(e,i),params:[]}:{sql:a(b.value++,e),params:[e],typings:["none"]}}))}mapInlineParam(e,{escapeString:t}){if(null===e)return"null";if("number"==typeof e||"boolean"==typeof e)return e.toString();if("string"==typeof e)return t(e);if("object"==typeof e){let i=e.toString();return"[object Object]"===i?t(JSON.stringify(e)):t(i)}throw Error("Unexpected param value: "+e)}getSQL(){return this}as(e){return void 0===e?this:new p.Aliased(this,e)}mapWith(e){return this.decoder="function"==typeof e?{mapFromDriverValue:e}:e,this}inlineParams(){return this.shouldInlineParams=!0,this}if(e){return e?this:void 0}}class y{constructor(e){this.value=e}static [s.i]="Name";brand;getSQL(){return new p([this])}}function b(e){return"object"==typeof e&&null!==e&&"mapToDriverValue"in e&&"function"==typeof e.mapToDriverValue}let w={mapFromDriverValue:e=>e},S={mapToDriverValue:e=>e};({...w,...S});class v{constructor(e,t=S){this.value=e,this.encoder=t}static [s.i]="Param";brand;getSQL(){return new p([this])}}function x(e,...t){let i=[];for(let[s,r]of((t.length>0||e.length>0&&""!==e[0])&&i.push(new g(e[0])),t.entries()))i.push(r,new g(e[s+1]));return new p(i)}(e=>{e.empty=function(){return new p([])},e.fromList=function(e){return new p(e)},e.raw=function(e){return new p([new g(e)])},e.join=function(e,t){let i=[];for(let[s,r]of e.entries())s>0&&void 0!==t&&i.push(t),i.push(r);return new p(i)},e.identifier=function(e){return new y(e)},e.placeholder=function(e){return new T(e)},e.param=function(e,t){return new v(e,t)}})(x||(x={})),(e=>{class t{constructor(e,t){this.sql=e,this.fieldAlias=t}static [s.i]="SQL.Aliased";isSelectionField=!1;getSQL(){return this.sql}clone(){return new t(this.sql,this.fieldAlias)}}e.Aliased=t})(p||(p={}));class T{constructor(e){this.name=e}static [s.i]="Placeholder";getSQL(){return new p([this])}}function $(e,t){return e.map(e=>{if((0,s.is)(e,T)){if(!(e.name in t))throw Error(`No value for placeholder "${e.name}" was provided`);return t[e.name]}if((0,s.is)(e,v)&&(0,s.is)(e.value,T)){if(!(e.value.name in t))throw Error(`No value for placeholder "${e.value.name}" was provided`);return e.encoder.mapToDriverValue(t[e.value.name])}return e})}class Q{static [s.i]="View";[c.n];constructor({name:e,schema:t,selectedFields:i,query:s}){this[c.n]={name:e,originalName:e,schema:t,selectedFields:i,query:s,isExisting:!s,isAlias:!1}}getSQL(){return new p([this])}}h.V.prototype.getSQL=function(){return new p([this])},d.XI.prototype.getSQL=function(){return new p([this])},o.n.prototype.getSQL=function(){return new p([this])}},6946:(e,t,i)=>{"use strict";i.d(t,{v:()=>m,o:()=>f});var s=i(9877),r=i(823),n=i(66),l=i(326);class a{static [n.i]="SQLiteForeignKeyBuilder";reference;_onUpdate;_onDelete;constructor(e,t){this.reference=()=>{let{name:t,columns:i,foreignColumns:s}=e();return{name:t,columns:i,foreignTable:s[0].table,foreignColumns:s}},t&&(this._onUpdate=t.onUpdate,this._onDelete=t.onDelete)}onUpdate(e){return this._onUpdate=e,this}onDelete(e){return this._onDelete=e,this}build(e){return new o(e,this)}}class o{constructor(e,t){this.table=e,this.reference=t.reference,this.onUpdate=t._onUpdate,this.onDelete=t._onDelete}static [n.i]="SQLiteForeignKey";reference;onUpdate;onDelete;getName(){let{name:e,columns:t,foreignColumns:i}=this.reference(),s=t.map(e=>e.name),r=i.map(e=>e.name),n=[this.table[l.E],...s,i[0].table[l.E],...r];return e??`${n.join("_")}_fk`}}function u(e,t){return`${e[l.E]}_${t.join("_")}_unique`}class c{constructor(e,t){this.name=t,this.columns=e}static [n.i]=null;columns;build(e){return new d(e,this.columns,this.name)}}class h{static [n.i]=null;name;constructor(e){this.name=e}on(...e){return new c(e,this.name)}}class d{constructor(e,t,i){this.table=e,this.columns=t,this.name=i??u(this.table,this.columns.map(e=>e.name))}static [n.i]=null;columns;name;getName(){return this.name}}class f extends s.Q{static [n.i]="SQLiteColumnBuilder";foreignKeyConfigs=[];references(e,t={}){return this.foreignKeyConfigs.push({ref:e,actions:t}),this}unique(e){return this.config.isUnique=!0,this.config.uniqueName=e,this}generatedAlwaysAs(e,t){return this.config.generated={as:e,type:"always",mode:t?.mode??"virtual"},this}buildForeignKeys(e,t){return this.foreignKeyConfigs.map(({ref:i,actions:s})=>((i,s)=>{let r=new a(()=>({columns:[e],foreignColumns:[i()]}));return s.onUpdate&&r.onUpdate(s.onUpdate),s.onDelete&&r.onDelete(s.onDelete),r.build(t)})(i,s))}}class m extends r.V{constructor(e,t){t.uniqueName||(t.uniqueName=u(e,[t.name])),super(e,t),this.table=e}static [n.i]="SQLiteColumn"}},3268:(e,t,i)=>{"use strict";i.d(t,{nd:()=>g});var s=i(66),r=i(7536),n=i(6104),l=i(6946);class a extends l.o{static [s.i]="SQLiteBaseIntegerBuilder";constructor(e,t,i){super(e,t,i),this.config.autoIncrement=!1}primaryKey(e){return e?.autoIncrement&&(this.config.autoIncrement=!0),this.config.hasDefault=!0,super.primaryKey()}}class o extends l.v{static [s.i]="SQLiteBaseInteger";autoIncrement=this.config.autoIncrement;getSQLType(){return"integer"}}class u extends a{static [s.i]="SQLiteIntegerBuilder";constructor(e){super(e,"number","SQLiteInteger")}build(e){return new c(e,this.config)}}class c extends o{static [s.i]="SQLiteInteger"}class h extends a{static [s.i]="SQLiteTimestampBuilder";constructor(e,t){super(e,"date","SQLiteTimestamp"),this.config.mode=t}defaultNow(){return this.default((0,r.ll)`(cast((julianday('now') - 2440587.5)*86400000 as integer))`)}build(e){return new d(e,this.config)}}class d extends o{static [s.i]="SQLiteTimestamp";mode=this.config.mode;mapFromDriverValue(e){return new Date("timestamp"===this.config.mode?1e3*e:e)}mapToDriverValue(e){let t=e.getTime();return"timestamp"===this.config.mode?Math.floor(t/1e3):t}}class f extends a{static [s.i]="SQLiteBooleanBuilder";constructor(e,t){super(e,"boolean","SQLiteBoolean"),this.config.mode=t}build(e){return new m(e,this.config)}}class m extends o{static [s.i]="SQLiteBoolean";mode=this.config.mode;mapFromDriverValue(e){return 1===Number(e)}mapToDriverValue(e){return e?1:0}}function g(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return s?.mode==="timestamp"||s?.mode==="timestamp_ms"?new h(i,s.mode):s?.mode==="boolean"?new f(i,s.mode):new u(i)}},1939:(e,t,i)=>{"use strict";i.d(t,{Qq:()=>c});var s=i(66),r=i(6104),n=i(6946);class l extends n.o{static [s.i]="SQLiteTextBuilder";constructor(e,t){super(e,"string","SQLiteText"),this.config.enumValues=t.enum,this.config.length=t.length}build(e){return new a(e,this.config)}}class a extends n.v{static [s.i]="SQLiteText";enumValues=this.config.enumValues;length=this.config.length;constructor(e,t){super(e,t)}getSQLType(){return`text${this.config.length?`(${this.config.length})`:""}`}}class o extends n.o{static [s.i]="SQLiteTextJsonBuilder";constructor(e){super(e,"json","SQLiteTextJson")}build(e){return new u(e,this.config)}}class u extends n.v{static [s.i]="SQLiteTextJson";getSQLType(){return"text"}mapFromDriverValue(e){return JSON.parse(e)}mapToDriverValue(e){return JSON.stringify(e)}}function c(e,t={}){let{name:i,config:s}=(0,r.Ll)(e,t);return"json"===s.mode?new o(i):new l(i,s)}},4986:(e,t,i)=>{"use strict";i.d(t,{N:()=>F});var s=i(66),r=i(4294),n=i(7536),l=i(2148),a=i(2923),o=i(8471),u=i(9389),c=i(8685),h=i(6104),d=i(4996),f=i(3707);class m{static [s.i]="SQLiteSelectBuilder";fields;session;dialect;withList;distinct;constructor(e){this.fields=e.fields,this.session=e.session,this.dialect=e.dialect,this.withList=e.withList,this.distinct=e.distinct}from(e){let t;let i=!!this.fields;return t=this.fields?this.fields:(0,s.is)(e,a.n)?Object.fromEntries(Object.keys(e._.selectedFields).map(t=>[t,e[t]])):(0,s.is)(e,f.V)?e[d.n].selectedFields:(0,s.is)(e,n.Xs)?{}:(0,h.YD)(e),new p({table:e,fields:t,isPartialSelect:i,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct})}}class g extends o.O{static [s.i]="SQLiteSelectQueryBuilder";_;config;joinsNotNullableMap;tableName;isPartialSelect;session;dialect;constructor({table:e,fields:t,isPartialSelect:i,session:s,dialect:r,withList:n,distinct:l}){super(),this.config={withList:n,table:e,fields:{...t},distinct:l,setOperators:[]},this.isPartialSelect=i,this.session=s,this.dialect=r,this._={selectedFields:t},this.tableName=(0,h.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}createJoin(e){return(t,i)=>{let l=this.tableName,o=(0,h.zN)(t);if("string"==typeof o&&this.config.joins?.some(e=>e.alias===o))throw Error(`Alias "${o}" is already used in this query`);if(!this.isPartialSelect&&(1===Object.keys(this.joinsNotNullableMap).length&&"string"==typeof l&&(this.config.fields={[l]:this.config.fields}),"string"==typeof o&&!(0,s.is)(t,n.Xs))){let e=(0,s.is)(t,a.n)?t._.selectedFields:(0,s.is)(t,n.Ss)?t[d.n].selectedFields:t[c.XI.Symbol.Columns];this.config.fields[o]=e}if("function"==typeof i&&(i=i(new Proxy(this.config.fields,new r.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.joins||(this.config.joins=[]),this.config.joins.push({on:i,table:t,joinType:e,alias:o}),"string"==typeof o)switch(e){case"left":this.joinsNotNullableMap[o]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[o]=!0;break;case"inner":this.joinsNotNullableMap[o]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[o]=!1}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");createSetOperator(e,t){return i=>{let s="function"==typeof i?i(b()):i;if(!(0,h.DV)(this.getSelectedFields(),s.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return this.config.setOperators.push({type:e,isAll:t,rightSelect:s}),this}}union=this.createSetOperator("union",!1);unionAll=this.createSetOperator("union",!0);intersect=this.createSetOperator("intersect",!1);except=this.createSetOperator("except",!1);addSetOperators(e){return this.config.setOperators.push(...e),this}where(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new r.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.where=e,this}having(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new r.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.having=e,this}groupBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new r.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"})));this.config.groupBy=Array.isArray(t)?t:[t]}else this.config.groupBy=e;return this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new r.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),i=Array.isArray(t)?t:[t];this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=i:this.config.orderBy=i}else this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=e:this.config.orderBy=e;return this}limit(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).limit=e:this.config.limit=e,this}offset(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).offset=e:this.config.offset=e,this}getSQL(){return this.dialect.buildSelectQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}as(e){return new Proxy(new a.n(this.getSQL(),this.config.fields,e),new r.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}getSelectedFields(){return new Proxy(this.config.fields,new r.b({alias:this.tableName,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}$dynamic(){return this}}class p extends g{static [s.i]="SQLiteSelect";_prepare(e=!0){if(!this.session)throw Error("Cannot execute a query on a query builder. Please use a database instance instead.");let t=(0,h.He)(this.config.fields),i=this.session[e?"prepareOneTimeQuery":"prepareQuery"](this.dialect.sqlToQuery(this.getSQL()),t,"all",!0);return i.joinsNotNullableMap=this.joinsNotNullableMap,i}prepare(){return this._prepare(!1)}run=e=>this._prepare().run(e);all=e=>this._prepare().all(e);get=e=>this._prepare().get(e);values=e=>this._prepare().values(e);async execute(){return this.all()}}function y(e,t){return(i,s,...r)=>{let n=[s,...r].map(i=>({type:e,isAll:t,rightSelect:i}));for(let e of n)if(!(0,h.DV)(i.getSelectedFields(),e.rightSelect.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return i.addSetOperators(n)}}(0,h.XJ)(p,[u.k]);let b=()=>({union:w,unionAll:S,intersect:v,except:x}),w=y("union",!1),S=y("union",!0),v=y("intersect",!1),x=y("except",!1);class T{static [s.i]="SQLiteQueryBuilder";dialect;dialectConfig;constructor(e){this.dialect=(0,s.is)(e,l.vV)?e:void 0,this.dialectConfig=(0,s.is)(e,l.vV)?void 0:e}$with(e){let t=this;return{as:i=>("function"==typeof i&&(i=i(t)),new Proxy(new a.J(i.getSQL(),i.getSelectedFields(),e,!0),new r.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}}with(...e){let t=this;return{select:function(i){return new m({fields:i??void 0,session:void 0,dialect:t.getDialect(),withList:e})},selectDistinct:function(i){return new m({fields:i??void 0,session:void 0,dialect:t.getDialect(),withList:e,distinct:!0})}}}select(e){return new m({fields:e??void 0,session:void 0,dialect:this.getDialect()})}selectDistinct(e){return new m({fields:e??void 0,session:void 0,dialect:this.getDialect(),distinct:!0})}getDialect(){return this.dialect||(this.dialect=new l._K(this.dialectConfig)),this.dialect}}var $=i(7243);class Q{constructor(e,t,i,s){this.table=e,this.session=t,this.dialect=i,this.withList=s}static [s.i]="SQLiteUpdateBuilder";set(e){return new L(this.table,(0,h.q)(this.table,e),this.session,this.dialect,this.withList)}}class L extends u.k{constructor(e,t,i,s,r){super(),this.session=i,this.dialect=s,this.config={set:t,table:e,withList:r,joins:[]}}static [s.i]="SQLiteUpdate";config;from(e){return this.config.from=e,this}createJoin(e){return(t,i)=>{let n=(0,h.zN)(t);if("string"==typeof n&&this.config.joins.some(e=>e.alias===n))throw Error(`Alias "${n}" is already used in this query`);if("function"==typeof i){let e=this.config.from?(0,s.is)(t,$.jo)?t[c.XI.Symbol.Columns]:(0,s.is)(t,a.n)?t._.selectedFields:(0,s.is)(t,f.V)?t[d.n].selectedFields:void 0:void 0;i=i(new Proxy(this.config.table[c.XI.Symbol.Columns],new r.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})),e&&new Proxy(e,new r.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))}return this.config.joins.push({on:i,table:t,joinType:e,alias:n}),this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");where(e){return this.config.where=e,this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.table[c.XI.Symbol.Columns],new r.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),i=Array.isArray(t)?t:[t];this.config.orderBy=i}else this.config.orderBy=e;return this}limit(e){return this.config.limit=e,this}returning(e=this.config.table[$.jo.Symbol.Columns]){return this.config.returning=(0,h.He)(e),this}getSQL(){return this.dialect.buildUpdateQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e=!0){return this.session[e?"prepareOneTimeQuery":"prepareQuery"](this.dialect.sqlToQuery(this.getSQL()),this.config.returning,this.config.returning?"all":"run",!0)}prepare(){return this._prepare(!1)}run=e=>this._prepare().run(e);all=e=>this._prepare().all(e);get=e=>this._prepare().get(e);values=e=>this._prepare().values(e);async execute(){return this.config.returning?this.all():this.run()}$dynamic(){return this}}class N{constructor(e,t,i,s){this.table=e,this.session=t,this.dialect=i,this.withList=s}static [s.i]="SQLiteInsertBuilder";values(e){if(0===(e=Array.isArray(e)?e:[e]).length)throw Error("values() must be called with at least one value");let t=e.map(e=>{let t={},i=this.table[c.XI.Symbol.Columns];for(let r of Object.keys(e)){let l=e[r];t[r]=(0,s.is)(l,n.Xs)?l:new n.Iw(l,i[r])}return t});return new P(this.table,t,this.session,this.dialect,this.withList)}select(e){let t="function"==typeof e?e(new T):e;if(!(0,s.is)(t,n.Xs)&&!(0,h.DV)(this.table[c.e],t._.selectedFields))throw Error("Insert select error: selected fields are not the same or are in a different order compared to the table definition");return new P(this.table,t,this.session,this.dialect,this.withList,!0)}}class P extends u.k{constructor(e,t,i,s,r,n){super(),this.session=i,this.dialect=s,this.config={table:e,values:t,withList:r,select:n}}static [s.i]="SQLiteInsert";config;returning(e=this.config.table[$.jo.Symbol.Columns]){return this.config.returning=(0,h.He)(e),this}onConflictDoNothing(e={}){if(void 0===e.target)this.config.onConflict=(0,n.ll)`do nothing`;else{let t=Array.isArray(e.target)?(0,n.ll)`${e.target}`:(0,n.ll)`${[e.target]}`,i=e.where?(0,n.ll)` where ${e.where}`:(0,n.ll)``;this.config.onConflict=(0,n.ll)`${t} do nothing${i}`}return this}onConflictDoUpdate(e){if(e.where&&(e.targetWhere||e.setWhere))throw Error('You cannot use both "where" and "targetWhere"/"setWhere" at the same time - "where" is deprecated, use "targetWhere" or "setWhere" instead.');let t=e.where?(0,n.ll)` where ${e.where}`:void 0,i=e.targetWhere?(0,n.ll)` where ${e.targetWhere}`:void 0,s=e.setWhere?(0,n.ll)` where ${e.setWhere}`:void 0,r=Array.isArray(e.target)?(0,n.ll)`${e.target}`:(0,n.ll)`${[e.target]}`,l=this.dialect.buildUpdateSet(this.config.table,(0,h.q)(this.config.table,e.set));return this.config.onConflict=(0,n.ll)`${r}${i} do update set ${l}${t}${s}`,this}getSQL(){return this.dialect.buildInsertQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e=!0){return this.session[e?"prepareOneTimeQuery":"prepareQuery"](this.dialect.sqlToQuery(this.getSQL()),this.config.returning,this.config.returning?"all":"run",!0)}prepare(){return this._prepare(!1)}run=e=>this._prepare().run(e);all=e=>this._prepare().all(e);get=e=>this._prepare().get(e);values=e=>this._prepare().values(e);async execute(){return this.config.returning?this.all():this.run()}$dynamic(){return this}}class C extends u.k{constructor(e,t,i,s){super(),this.table=e,this.session=t,this.dialect=i,this.config={table:e,withList:s}}static [s.i]="SQLiteDelete";config;where(e){return this.config.where=e,this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.table[c.XI.Symbol.Columns],new r.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),i=Array.isArray(t)?t:[t];this.config.orderBy=i}else this.config.orderBy=e;return this}limit(e){return this.config.limit=e,this}returning(e=this.table[$.jo.Symbol.Columns]){return this.config.returning=(0,h.He)(e),this}getSQL(){return this.dialect.buildDeleteQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e=!0){return this.session[e?"prepareOneTimeQuery":"prepareQuery"](this.dialect.sqlToQuery(this.getSQL()),this.config.returning,this.config.returning?"all":"run",!0)}prepare(){return this._prepare(!1)}run=e=>this._prepare().run(e);all=e=>this._prepare().all(e);get=e=>this._prepare().get(e);values=e=>this._prepare().values(e);async execute(e){return this._prepare().execute(e)}$dynamic(){return this}}class q extends n.Xs{constructor(e){super(q.buildEmbeddedCount(e.source,e.filters).queryChunks),this.params=e,this.session=e.session,this.sql=q.buildCount(e.source,e.filters)}sql;static [s.i]="SQLiteCountBuilderAsync";[Symbol.toStringTag]="SQLiteCountBuilderAsync";session;static buildEmbeddedCount(e,t){return(0,n.ll)`(select count(*) from ${e}${n.ll.raw(" where ").if(t)}${t})`}static buildCount(e,t){return(0,n.ll)`select count(*) from ${e}${n.ll.raw(" where ").if(t)}${t}`}then(e,t){return Promise.resolve(this.session.count(this.sql)).then(e,t)}catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}}var B=i(652);class I{constructor(e,t,i,s,r,n,l,a){this.mode=e,this.fullSchema=t,this.schema=i,this.tableNamesMap=s,this.table=r,this.tableConfig=n,this.dialect=l,this.session=a}static [s.i]="SQLiteAsyncRelationalQueryBuilder";findMany(e){return"sync"===this.mode?new O(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many"):new D(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many")}findFirst(e){return"sync"===this.mode?new O(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first"):new D(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first")}}class D extends u.k{constructor(e,t,i,s,r,n,l,a,o){super(),this.fullSchema=e,this.schema=t,this.tableNamesMap=i,this.table=s,this.tableConfig=r,this.dialect=n,this.session=l,this.config=a,this.mode=o}static [s.i]="SQLiteAsyncRelationalQuery";mode;getSQL(){return this.dialect.buildRelationalQuery({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName}).sql}_prepare(e=!1){let{query:t,builtQuery:i}=this._toSQL();return this.session[e?"prepareOneTimeQuery":"prepareQuery"](i,void 0,"first"===this.mode?"get":"all",!0,(e,i)=>{let s=e.map(e=>(0,B.I$)(this.schema,this.tableConfig,e,t.selection,i));return"first"===this.mode?s[0]:s})}prepare(){return this._prepare(!1)}_toSQL(){let e=this.dialect.buildRelationalQuery({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName}),t=this.dialect.sqlToQuery(e.sql);return{query:e,builtQuery:t}}toSQL(){return this._toSQL().builtQuery}executeRaw(){return"first"===this.mode?this._prepare(!1).get():this._prepare(!1).all()}async execute(){return this.executeRaw()}}class O extends D{static [s.i]="SQLiteSyncRelationalQuery";sync(){return this.executeRaw()}}class j extends u.k{constructor(e,t,i,s,r){super(),this.execute=e,this.getSQL=t,this.dialect=s,this.mapBatchResult=r,this.config={action:i}}static [s.i]="SQLiteRaw";config;getQuery(){return{...this.dialect.sqlToQuery(this.getSQL()),method:this.config.action}}mapResult(e,t){return t?this.mapBatchResult(e):e}_prepare(){return this}isResponseInArrayMode(){return!1}}class F{constructor(e,t,i,s){this.resultKind=e,this.dialect=t,this.session=i,this._=s?{schema:s.schema,fullSchema:s.fullSchema,tableNamesMap:s.tableNamesMap}:{schema:void 0,fullSchema:{},tableNamesMap:{}},this.query={};let r=this.query;if(this._.schema)for(let[n,l]of Object.entries(this._.schema))r[n]=new I(e,s.fullSchema,this._.schema,this._.tableNamesMap,s.fullSchema[n],l,t,i)}static [s.i]="BaseSQLiteDatabase";query;$with(e){let t=this;return{as:i=>("function"==typeof i&&(i=i(new T(t.dialect))),new Proxy(new a.J(i.getSQL(),i.getSelectedFields(),e,!0),new r.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}}$count(e,t){return new q({source:e,filters:t,session:this.session})}with(...e){let t=this;return{select:function(i){return new m({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e})},selectDistinct:function(i){return new m({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:!0})},update:function(i){return new Q(i,t.session,t.dialect,e)},insert:function(i){return new N(i,t.session,t.dialect,e)},delete:function(i){return new C(i,t.session,t.dialect,e)}}}select(e){return new m({fields:e??void 0,session:this.session,dialect:this.dialect})}selectDistinct(e){return new m({fields:e??void 0,session:this.session,dialect:this.dialect,distinct:!0})}update(e){return new Q(e,this.session,this.dialect)}insert(e){return new N(e,this.session,this.dialect)}delete(e){return new C(e,this.session,this.dialect)}run(e){let t="string"==typeof e?n.ll.raw(e):e.getSQL();return"async"===this.resultKind?new j(async()=>this.session.run(t),()=>t,"run",this.dialect,this.session.extractRawRunValueFromBatchResult.bind(this.session)):this.session.run(t)}all(e){let t="string"==typeof e?n.ll.raw(e):e.getSQL();return"async"===this.resultKind?new j(async()=>this.session.all(t),()=>t,"all",this.dialect,this.session.extractRawAllValueFromBatchResult.bind(this.session)):this.session.all(t)}get(e){let t="string"==typeof e?n.ll.raw(e):e.getSQL();return"async"===this.resultKind?new j(async()=>this.session.get(t),()=>t,"get",this.dialect,this.session.extractRawGetValueFromBatchResult.bind(this.session)):this.session.get(t)}values(e){let t="string"==typeof e?n.ll.raw(e):e.getSQL();return"async"===this.resultKind?new j(async()=>this.session.values(t),()=>t,"values",this.dialect,this.session.extractRawValuesValueFromBatchResult.bind(this.session)):this.session.values(t)}transaction(e,t){return this.session.transaction(e,t)}}},2148:(e,t,i)=>{"use strict";i.d(t,{ZR:()=>S,_K:()=>w,vV:()=>b});var s=i(8421),r=i(4650),n=i(823),l=i(66),a=i(7430),o=i(652),u=i(9066),c=i(7536),h=i(6946),d=i(7243),f=i(2923),m=i(8685),g=i(6104),p=i(4996),y=i(3707);class b{static [l.i]="SQLiteDialect";casing;constructor(e){this.casing=new r.Yn(e?.casing)}escapeName(e){return`"${e}"`}escapeParam(e){return"?"}escapeString(e){return`'${e.replace(/'/g,"''")}'`}buildWithCTE(e){if(!e?.length)return;let t=[(0,c.ll)`with `];for(let[i,s]of e.entries())t.push((0,c.ll)`${c.ll.identifier(s._.alias)} as (${s._.sql})`),i<e.length-1&&t.push((0,c.ll)`, `);return t.push((0,c.ll)` `),c.ll.join(t)}buildDeleteQuery({table:e,where:t,returning:i,withList:s,limit:r,orderBy:n}){let l=this.buildWithCTE(s),a=i?(0,c.ll)` returning ${this.buildSelection(i,{isSingleTable:!0})}`:void 0,o=t?(0,c.ll)` where ${t}`:void 0,u=this.buildOrderBy(n),h=this.buildLimit(r);return(0,c.ll)`${l}delete from ${e}${o}${a}${u}${h}`}buildUpdateSet(e,t){let i=e[m.XI.Symbol.Columns],s=Object.keys(i).filter(e=>void 0!==t[e]||i[e]?.onUpdateFn!==void 0),r=s.length;return c.ll.join(s.flatMap((e,s)=>{let n=i[e],l=t[e]??c.ll.param(n.onUpdateFn(),n),a=(0,c.ll)`${c.ll.identifier(this.casing.getColumnCasing(n))} = ${l}`;return s<r-1?[a,c.ll.raw(", ")]:[a]}))}buildUpdateQuery({table:e,set:t,where:i,returning:s,withList:r,joins:n,from:l,limit:a,orderBy:o}){let u=this.buildWithCTE(r),h=this.buildUpdateSet(e,t),d=l&&c.ll.join([c.ll.raw(" from "),this.buildFromTable(l)]),f=this.buildJoins(n),m=s?(0,c.ll)` returning ${this.buildSelection(s,{isSingleTable:!0})}`:void 0,g=i?(0,c.ll)` where ${i}`:void 0,p=this.buildOrderBy(o),y=this.buildLimit(a);return(0,c.ll)`${u}update ${e} set ${h}${d}${f}${g}${m}${p}${y}`}buildSelection(e,{isSingleTable:t=!1}={}){let i=e.length,s=e.flatMap(({field:e},s)=>{let r=[];if((0,l.is)(e,c.Xs.Aliased)&&e.isSelectionField)r.push(c.ll.identifier(e.fieldAlias));else if((0,l.is)(e,c.Xs.Aliased)||(0,l.is)(e,c.Xs)){let i=(0,l.is)(e,c.Xs.Aliased)?e.sql:e;t?r.push(new c.Xs(i.queryChunks.map(e=>(0,l.is)(e,n.V)?c.ll.identifier(this.casing.getColumnCasing(e)):e))):r.push(i),(0,l.is)(e,c.Xs.Aliased)&&r.push((0,c.ll)` as ${c.ll.identifier(e.fieldAlias)}`)}else if((0,l.is)(e,n.V)){let i=e.table[m.XI.Symbol.Name];t?r.push(c.ll.identifier(this.casing.getColumnCasing(e))):r.push((0,c.ll)`${c.ll.identifier(i)}.${c.ll.identifier(this.casing.getColumnCasing(e))}`)}return s<i-1&&r.push((0,c.ll)`, `),r});return c.ll.join(s)}buildJoins(e){if(!e||0===e.length)return;let t=[];if(e)for(let[i,s]of e.entries()){0===i&&t.push((0,c.ll)` `);let r=s.table;if((0,l.is)(r,d.jo)){let e=r[d.jo.Symbol.Name],i=r[d.jo.Symbol.Schema],n=r[d.jo.Symbol.OriginalName],l=e===n?void 0:s.alias;t.push((0,c.ll)`${c.ll.raw(s.joinType)} join ${i?(0,c.ll)`${c.ll.identifier(i)}.`:void 0}${c.ll.identifier(n)}${l&&(0,c.ll)` ${c.ll.identifier(l)}`} on ${s.on}`)}else t.push((0,c.ll)`${c.ll.raw(s.joinType)} join ${r} on ${s.on}`);i<e.length-1&&t.push((0,c.ll)` `)}return c.ll.join(t)}buildLimit(e){return"object"==typeof e||"number"==typeof e&&e>=0?(0,c.ll)` limit ${e}`:void 0}buildOrderBy(e){let t=[];if(e)for(let[i,s]of e.entries())t.push(s),i<e.length-1&&t.push((0,c.ll)`, `);return t.length>0?(0,c.ll)` order by ${c.ll.join(t)}`:void 0}buildFromTable(e){return(0,l.is)(e,m.XI)&&e[m.XI.Symbol.OriginalName]!==e[m.XI.Symbol.Name]?(0,c.ll)`${c.ll.identifier(e[m.XI.Symbol.OriginalName])} ${c.ll.identifier(e[m.XI.Symbol.Name])}`:e}buildSelectQuery({withList:e,fields:t,fieldsFlat:i,where:s,having:r,table:a,joins:o,orderBy:u,groupBy:h,limit:d,offset:b,distinct:w,setOperators:S}){let v=i??(0,g.He)(t);for(let e of v){let t;if((0,l.is)(e.field,n.V)&&(0,m.Io)(e.field.table)!==((0,l.is)(a,f.n)?a._.alias:(0,l.is)(a,y.V)?a[p.n].name:(0,l.is)(a,c.Xs)?void 0:(0,m.Io)(a))&&(t=e.field.table,!o?.some(({alias:e})=>e===(t[m.XI.Symbol.IsAlias]?m.Io(t):t[m.XI.Symbol.BaseName])))){let t=(0,m.Io)(e.field.table);throw Error(`Your "${e.path.join("->")}" field references a column "${t}"."${e.field.name}", but the table "${t}" is not part of the query! Did you forget to join it?`)}}let x=!o||0===o.length,T=this.buildWithCTE(e),$=w?(0,c.ll)` distinct`:void 0,Q=this.buildSelection(v,{isSingleTable:x}),L=this.buildFromTable(a),N=this.buildJoins(o),P=s?(0,c.ll)` where ${s}`:void 0,C=r?(0,c.ll)` having ${r}`:void 0,q=[];if(h)for(let[e,t]of h.entries())q.push(t),e<h.length-1&&q.push((0,c.ll)`, `);let B=q.length>0?(0,c.ll)` group by ${c.ll.join(q)}`:void 0,I=this.buildOrderBy(u),D=this.buildLimit(d),O=b?(0,c.ll)` offset ${b}`:void 0,j=(0,c.ll)`${T}select${$} ${Q} from ${L}${N}${P}${B}${C}${I}${D}${O}`;return S.length>0?this.buildSetOperations(j,S):j}buildSetOperations(e,t){let[i,...s]=t;if(!i)throw Error("Cannot pass undefined values to any set operator");return 0===s.length?this.buildSetOperationQuery({leftSelect:e,setOperator:i}):this.buildSetOperations(this.buildSetOperationQuery({leftSelect:e,setOperator:i}),s)}buildSetOperationQuery({leftSelect:e,setOperator:{type:t,isAll:i,rightSelect:s,limit:r,orderBy:n,offset:a}}){let o;let u=(0,c.ll)`${e.getSQL()} `,d=(0,c.ll)`${s.getSQL()}`;if(n&&n.length>0){let e=[];for(let t of n)if((0,l.is)(t,h.v))e.push(c.ll.identifier(t.name));else if((0,l.is)(t,c.Xs)){for(let e=0;e<t.queryChunks.length;e++){let i=t.queryChunks[e];(0,l.is)(i,h.v)&&(t.queryChunks[e]=c.ll.identifier(this.casing.getColumnCasing(i)))}e.push((0,c.ll)`${t}`)}else e.push((0,c.ll)`${t}`);o=(0,c.ll)` order by ${c.ll.join(e,(0,c.ll)`, `)}`}let f="object"==typeof r||"number"==typeof r&&r>=0?(0,c.ll)` limit ${r}`:void 0,m=c.ll.raw(`${t} ${i?"all ":""}`),g=a?(0,c.ll)` offset ${a}`:void 0;return(0,c.ll)`${u}${m}${d}${o}${f}${g}`}buildInsertQuery({table:e,values:t,onConflict:i,returning:s,withList:r,select:n}){let a=[],o=Object.entries(e[m.XI.Symbol.Columns]).filter(([e,t])=>!t.shouldDisableInsert()),u=o.map(([,e])=>c.ll.identifier(this.casing.getColumnCasing(e)));if(n)(0,l.is)(t,c.Xs)?a.push(t):a.push(t.getSQL());else for(let[e,i]of(a.push(c.ll.raw("values ")),t.entries())){let s=[];for(let[e,t]of o){let r=i[e];if(void 0===r||(0,l.is)(r,c.Iw)&&void 0===r.value){let e;if(null!==t.default&&void 0!==t.default)e=(0,l.is)(t.default,c.Xs)?t.default:c.ll.param(t.default,t);else if(void 0!==t.defaultFn){let i=t.defaultFn();e=(0,l.is)(i,c.Xs)?i:c.ll.param(i,t)}else if(t.default||void 0===t.onUpdateFn)e=(0,c.ll)`null`;else{let i=t.onUpdateFn();e=(0,l.is)(i,c.Xs)?i:c.ll.param(i,t)}s.push(e)}else s.push(r)}a.push(s),e<t.length-1&&a.push((0,c.ll)`, `)}let h=this.buildWithCTE(r),d=c.ll.join(a),f=s?(0,c.ll)` returning ${this.buildSelection(s,{isSingleTable:!0})}`:void 0,g=i?(0,c.ll)` on conflict ${i}`:void 0;return(0,c.ll)`${h}insert into ${e} ${u} ${d}${g}${f}`}sqlToQuery(e,t){return e.toQuery({casing:this.casing,escapeName:this.escapeName,escapeParam:this.escapeParam,escapeString:this.escapeString,invokeSource:t})}buildRelationalQuery({fullSchema:e,schema:t,tableNamesMap:i,table:r,tableConfig:g,queryConfig:p,tableAlias:y,nestedQueryRelation:b,joinOn:w}){let S,v=[],x,T,$=[],Q,L=[];if(!0===p)v=Object.entries(g.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:(0,s.ug)(t,y),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let r=Object.fromEntries(Object.entries(g.columns).map(([e,t])=>[e,(0,s.ug)(t,y)]));if(p.where){let e="function"==typeof p.where?p.where(r,(0,o.mm)()):p.where;Q=e&&(0,s.yY)(e,y)}let a=[],h=[];if(p.columns){let e=!1;for(let[t,i]of Object.entries(p.columns))void 0!==i&&t in g.columns&&(e||!0!==i||(e=!0),h.push(t));h.length>0&&(h=e?h.filter(e=>p.columns?.[e]===!0):Object.keys(g.columns).filter(e=>!h.includes(e)))}else h=Object.keys(g.columns);for(let e of h){let t=g.columns[e];a.push({tsKey:e,value:t})}let d=[];if(p.with&&(d=Object.entries(p.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:g.relations[e]}))),p.extras)for(let[e,t]of Object.entries("function"==typeof p.extras?p.extras(r,{sql:c.ll}):p.extras))a.push({tsKey:e,value:(0,s.Hs)(t,y)});for(let{tsKey:e,value:t}of a)v.push({dbKey:(0,l.is)(t,c.Xs.Aliased)?t.fieldAlias:g.columns[e].name,tsKey:e,field:(0,l.is)(t,n.V)?(0,s.ug)(t,y):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let f="function"==typeof p.orderBy?p.orderBy(r,(0,o.rl)()):p.orderBy??[];for(let{tsKey:r,queryConfig:a,relation:h}of(Array.isArray(f)||(f=[f]),$=f.map(e=>(0,l.is)(e,n.V)?(0,s.ug)(e,y):(0,s.yY)(e,y)),x=p.limit,T=p.offset,d)){let n=(0,o.W0)(t,i,h),d=i[(0,m.Lf)(h.referencedTable)],f=`${y}_${r}`,g=(0,u.Uo)(...n.fields.map((e,t)=>(0,u.eq)((0,s.ug)(n.references[t],f),(0,s.ug)(e,y)))),p=this.buildRelationalQuery({fullSchema:e,schema:t,tableNamesMap:i,table:e[d],tableConfig:t[d],queryConfig:(0,l.is)(h,o.pD)?!0===a?{limit:1}:{...a,limit:1}:a,tableAlias:f,joinOn:g,nestedQueryRelation:h}),b=(0,c.ll)`(${p.sql})`.as(r);v.push({dbKey:r,tsKey:r,field:b,relationTableTsKey:d,isJson:!0,selection:p.selection})}}if(0===v.length)throw new a.n({message:`No fields selected for table "${g.tsName}" ("${y}"). You need to have at least one item in "columns", "with" or "extras". If you need to select all columns, omit the "columns" key or set it to undefined.`});if(Q=(0,u.Uo)(w,Q),b){let e=(0,c.ll)`json_array(${c.ll.join(v.map(({field:e})=>(0,l.is)(e,h.v)?c.ll.identifier(this.casing.getColumnCasing(e)):(0,l.is)(e,c.Xs.Aliased)?e.sql:e),(0,c.ll)`, `)})`;(0,l.is)(b,o.iv)&&(e=(0,c.ll)`coalesce(json_group_array(${e}), json_array())`);let t=[{dbKey:"data",tsKey:"data",field:e.as("data"),isJson:!0,relationTableTsKey:g.tsName,selection:v}];void 0!==x||void 0!==T||$.length>0?(S=this.buildSelectQuery({table:(0,s.oG)(r,y),fields:{},fieldsFlat:[{path:[],field:c.ll.raw("*")}],where:Q,limit:x,offset:T,orderBy:$,setOperators:[]}),Q=void 0,x=void 0,T=void 0,$=void 0):S=(0,s.oG)(r,y),S=this.buildSelectQuery({table:(0,l.is)(S,d.jo)?S:new f.n(S,{},y),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:(0,l.is)(e,n.V)?(0,s.ug)(e,y):e})),joins:L,where:Q,limit:x,offset:T,orderBy:$,setOperators:[]})}else S=this.buildSelectQuery({table:(0,s.oG)(r,y),fields:{},fieldsFlat:v.map(({field:e})=>({path:[],field:(0,l.is)(e,n.V)?(0,s.ug)(e,y):e})),joins:L,where:Q,limit:x,offset:T,orderBy:$,setOperators:[]});return{tableTsKey:g.tsName,sql:S,selection:v}}}class w extends b{static [l.i]="SQLiteSyncDialect";migrate(e,t,i){let s=void 0===i?"__drizzle_migrations":"string"==typeof i?"__drizzle_migrations":i.migrationsTable??"__drizzle_migrations",r=(0,c.ll)`
			CREATE TABLE IF NOT EXISTS ${c.ll.identifier(s)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at numeric
			)
		`;t.run(r);let n=t.values((0,c.ll)`SELECT id, hash, created_at FROM ${c.ll.identifier(s)} ORDER BY created_at DESC LIMIT 1`)[0]??void 0;t.run((0,c.ll)`BEGIN`);try{for(let i of e)if(!n||Number(n[2])<i.folderMillis){for(let e of i.sql)t.run(c.ll.raw(e));t.run((0,c.ll)`INSERT INTO ${c.ll.identifier(s)} ("hash", "created_at") VALUES(${i.hash}, ${i.folderMillis})`)}t.run((0,c.ll)`COMMIT`)}catch(e){throw t.run((0,c.ll)`ROLLBACK`),e}}}class S extends b{static [l.i]="SQLiteAsyncDialect";async migrate(e,t,i){let s=void 0===i?"__drizzle_migrations":"string"==typeof i?"__drizzle_migrations":i.migrationsTable??"__drizzle_migrations",r=(0,c.ll)`
			CREATE TABLE IF NOT EXISTS ${c.ll.identifier(s)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at numeric
			)
		`;await t.run(r);let n=(await t.values((0,c.ll)`SELECT id, hash, created_at FROM ${c.ll.identifier(s)} ORDER BY created_at DESC LIMIT 1`))[0]??void 0;await t.transaction(async t=>{for(let i of e)if(!n||Number(n[2])<i.folderMillis){for(let e of i.sql)await t.run(c.ll.raw(e));await t.run((0,c.ll)`INSERT INTO ${c.ll.identifier(s)} ("hash", "created_at") VALUES(${i.hash}, ${i.folderMillis})`)}})}}},1870:(e,t,i)=>{"use strict";i.d(t,{GL:()=>o,Pe:()=>a});var s=i(66);class r{constructor(e,t){this.name=e,this.unique=t}static [s.i]="SQLiteIndexBuilderOn";on(...e){return new n(this.name,e,this.unique)}}class n{static [s.i]="SQLiteIndexBuilder";config;constructor(e,t,i){this.config={name:e,columns:t,unique:i,where:void 0}}where(e){return this.config.where=e,this}build(e){return new l(this.config,e)}}class l{static [s.i]="SQLiteIndex";config;constructor(e,t){this.config={...e,table:t}}}function a(e){return new r(e,!1)}function o(e){return new r(e,!0)}},3797:(e,t,i)=>{"use strict";i.d(t,{ie:()=>n});var s=i(66),r=i(7243);function n(...e){return e[0].columns?new l(e[0].columns,e[0].name):new l(e)}class l{static [s.i]="SQLitePrimaryKeyBuilder";columns;name;constructor(e,t){this.columns=e,this.name=t}build(e){return new a(e,this.columns,this.name)}}class a{constructor(e,t,i){this.table=e,this.columns=t,this.name=i}static [s.i]="SQLitePrimaryKey";columns;name;getName(){return this.name??`${this.table[r.jo.Symbol.Name]}_${this.columns.map(e=>e.name).join("_")}_pk`}}},7243:(e,t,i)=>{"use strict";i.d(t,{jo:()=>N,D:()=>P});var s=i(66),r=i(8685),n=i(6104),l=i(6946),a=i(5356).Buffer;class o extends l.o{static [s.i]="SQLiteBigIntBuilder";constructor(e){super(e,"bigint","SQLiteBigInt")}build(e){return new u(e,this.config)}}class u extends l.v{static [s.i]="SQLiteBigInt";getSQLType(){return"blob"}mapFromDriverValue(e){return BigInt(a.isBuffer(e)?e.toString():String.fromCodePoint(...e))}mapToDriverValue(e){return a.from(e.toString())}}class c extends l.o{static [s.i]="SQLiteBlobJsonBuilder";constructor(e){super(e,"json","SQLiteBlobJson")}build(e){return new h(e,this.config)}}class h extends l.v{static [s.i]="SQLiteBlobJson";getSQLType(){return"blob"}mapFromDriverValue(e){return JSON.parse(a.isBuffer(e)?e.toString():String.fromCodePoint(...e))}mapToDriverValue(e){return a.from(JSON.stringify(e))}}class d extends l.o{static [s.i]="SQLiteBlobBufferBuilder";constructor(e){super(e,"buffer","SQLiteBlobBuffer")}build(e){return new f(e,this.config)}}class f extends l.v{static [s.i]="SQLiteBlobBuffer";getSQLType(){return"blob"}}function m(e,t){let{name:i,config:s}=(0,n.Ll)(e,t);return s?.mode==="json"?new c(i):s?.mode==="bigint"?new o(i):new d(i)}class g extends l.o{static [s.i]="SQLiteCustomColumnBuilder";constructor(e,t,i){super(e,"custom","SQLiteCustomColumn"),this.config.fieldConfig=t,this.config.customTypeParams=i}build(e){return new p(e,this.config)}}class p extends l.v{static [s.i]="SQLiteCustomColumn";sqlName;mapTo;mapFrom;constructor(e,t){super(e,t),this.sqlName=t.customTypeParams.dataType(t.fieldConfig),this.mapTo=t.customTypeParams.toDriver,this.mapFrom=t.customTypeParams.fromDriver}getSQLType(){return this.sqlName}mapFromDriverValue(e){return"function"==typeof this.mapFrom?this.mapFrom(e):e}mapToDriverValue(e){return"function"==typeof this.mapTo?this.mapTo(e):e}}function y(e){return(t,i)=>{let{name:s,config:r}=(0,n.Ll)(t,i);return new g(s,r,e)}}var b=i(3268);class w extends l.o{static [s.i]="SQLiteNumericBuilder";constructor(e){super(e,"string","SQLiteNumeric")}build(e){return new S(e,this.config)}}class S extends l.v{static [s.i]="SQLiteNumeric";getSQLType(){return"numeric"}}function v(e){return new w(e??"")}class x extends l.o{static [s.i]="SQLiteRealBuilder";constructor(e){super(e,"number","SQLiteReal")}build(e){return new T(e,this.config)}}class T extends l.v{static [s.i]="SQLiteReal";getSQLType(){return"real"}}function $(e){return new x(e??"")}var Q=i(1939);let L=Symbol.for("drizzle:SQLiteInlineForeignKeys");class N extends r.XI{static [s.i]="SQLiteTable";static Symbol=Object.assign({},r.XI.Symbol,{InlineForeignKeys:L});[r.XI.Symbol.Columns];[L]=[];[r.XI.Symbol.ExtraConfigBuilder]=void 0}let P=(e,t,i)=>(function(e,t,i,s,n=e){let l=new N(e,void 0,n),a=Object.fromEntries(Object.entries("function"==typeof t?t({blob:m,customType:y,integer:b.nd,numeric:v,real:$,text:Q.Qq}):t).map(([e,t])=>{t.setName(e);let i=t.build(l);return l[L].push(...t.buildForeignKeys(i,l)),[e,i]})),o=Object.assign(l,a);return o[r.XI.Symbol.Columns]=a,o[r.XI.Symbol.ExtraConfigColumns]=a,i&&(o[N.Symbol.ExtraConfigBuilder]=i),o})(e,t,i)},3707:(e,t,i)=>{"use strict";i.d(t,{V:()=>n});var s=i(66),r=i(7536);class n extends r.Ss{static [s.i]="SQLiteViewBase"}},2923:(e,t,i)=>{"use strict";i.d(t,{J:()=>n,n:()=>r});var s=i(66);class r{static [s.i]="Subquery";constructor(e,t,i,s=!1){this._={brand:"Subquery",sql:e,selectedFields:t,alias:i,isWith:s}}}class n extends r{static [s.i]="WithSubquery"}},8685:(e,t,i)=>{"use strict";i.d(t,{HE:()=>c,Io:()=>m,Lf:()=>g,XI:()=>f,e:()=>l});var s=i(66),r=i(326);let n=Symbol.for("drizzle:Schema"),l=Symbol.for("drizzle:Columns"),a=Symbol.for("drizzle:ExtraConfigColumns"),o=Symbol.for("drizzle:OriginalName"),u=Symbol.for("drizzle:BaseName"),c=Symbol.for("drizzle:IsAlias"),h=Symbol.for("drizzle:ExtraConfigBuilder"),d=Symbol.for("drizzle:IsDrizzleTable");class f{static [s.i]="Table";static Symbol={Name:r.E,Schema:n,OriginalName:o,Columns:l,ExtraConfigColumns:a,BaseName:u,IsAlias:c,ExtraConfigBuilder:h};[r.E];[o];[n];[l];[a];[u];[c]=!1;[d]=!0;[h]=void 0;constructor(e,t,i){this[r.E]=this[o]=e,this[n]=t,this[u]=i}}function m(e){return e[r.E]}function g(e){return`${e[n]??"public"}.${e[r.E]}`}},326:(e,t,i)=>{"use strict";i.d(t,{E:()=>s});let s=Symbol.for("drizzle:Name")},9903:(e,t,i)=>{"use strict";function s(e,...t){return e(...t)}i.d(t,{i:()=>s})},7424:(e,t,i)=>{"use strict";let s,r;i.d(t,{k:()=>l});var n=i(9903);let l={startActiveSpan:(e,t)=>s?(r||(r=s.trace.getTracer("drizzle-orm","0.36.4")),(0,n.i)((i,s)=>s.startActiveSpan(e,e=>{try{return t(e)}catch(t){throw e.setStatus({code:i.SpanStatusCode.ERROR,message:t instanceof Error?t.message:"Unknown error"}),t}finally{e.end()}}),s,r)):t()}},6104:(e,t,i)=>{"use strict";i.d(t,{DV:()=>c,He:()=>function e(t,i){return Object.entries(t).reduce((t,[l,o])=>{if("string"!=typeof l)return t;let u=i?[...i,l]:[l];return(0,r.is)(o,s.V)||(0,r.is)(o,n.Xs)||(0,r.is)(o,n.Xs.Aliased)?t.push({path:u,field:o}):(0,r.is)(o,a.XI)?t.push(...e(o[a.XI.Symbol.Columns],u)):t.push(...e(o,u)),t},[])},Ll:()=>g,XJ:()=>d,YD:()=>f,a6:()=>u,q:()=>h,zN:()=>m});var s=i(823),r=i(66),n=i(7536),l=i(2923),a=i(8685),o=i(4996);function u(e,t,i){let l={},o=e.reduce((e,{path:o,field:u},c)=>{let h;h=(0,r.is)(u,s.V)?u:(0,r.is)(u,n.Xs)?u.decoder:u.sql.decoder;let d=e;for(let[e,n]of o.entries())if(e<o.length-1)n in d||(d[n]={}),d=d[n];else{let e=t[c],f=d[n]=null===e?null:h.mapFromDriverValue(e);if(i&&(0,r.is)(u,s.V)&&2===o.length){let e=o[0];e in l?"string"==typeof l[e]&&l[e]!==(0,a.Io)(u.table)&&(l[e]=!1):l[e]=null===f&&(0,a.Io)(u.table)}}return e},{});if(i&&Object.keys(l).length>0)for(let[e,t]of Object.entries(l))"string"!=typeof t||i[t]||(o[e]=null);return o}function c(e,t){let i=Object.keys(e),s=Object.keys(t);if(i.length!==s.length)return!1;for(let[e,t]of i.entries())if(t!==s[e])return!1;return!0}function h(e,t){let i=Object.entries(t).filter(([,e])=>void 0!==e).map(([t,i])=>(0,r.is)(i,n.Xs)||(0,r.is)(i,s.V)?[t,i]:[t,new n.Iw(i,e[a.XI.Symbol.Columns][t])]);if(0===i.length)throw Error("No values to set");return Object.fromEntries(i)}function d(e,t){for(let i of t)for(let t of Object.getOwnPropertyNames(i.prototype))"constructor"!==t&&Object.defineProperty(e.prototype,t,Object.getOwnPropertyDescriptor(i.prototype,t)||Object.create(null))}function f(e){return e[a.XI.Symbol.Columns]}function m(e){return(0,r.is)(e,l.n)?e._.alias:(0,r.is)(e,n.Ss)?e[o.n].name:(0,r.is)(e,n.Xs)?void 0:e[a.XI.Symbol.IsAlias]?e[a.XI.Symbol.Name]:e[a.XI.Symbol.BaseName]}function g(e,t){return{name:"string"==typeof e&&e.length>0?e:"",config:"object"==typeof e?e:t}}},4996:(e,t,i)=>{"use strict";i.d(t,{n:()=>s});let s=Symbol.for("drizzle:ViewBaseConfig")}}]);
//# sourceMappingURL=702.js.map