{"version": 3, "file": "app/api/auth/[...auth]/route.js", "mappings": "qFAAA,6DCAA,mHGAA,yQFIO,IAAMA,EAAU,OAAM,ECG7B,IAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,iCACA,+BACA,iBACA,yCACA,CAAK,CACL,gGACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,0EACA,EAFA,4BAEA,4BACA,MACI,QAA8B,EAClC,iCACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,CAdN,CAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,uIAiBnxJ,CAAC,CAAC,EAAC,kHCwBI,IAAMC,EAAY,UAEvB,IAAMC,EAASC,CADK,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,EAAAA,EACNC,GAAG,CAAC,aAE/B,GAAIH,EAAQ,OAAOA,EAEnB,IAAMI,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAE1B,OAAOD,GAASE,KAAKC,EACvB,EAAC,oOCxDD,IAAMC,EAAS,CACb,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACD,eCGD,IAAMC,EAA0C,CAC9C,CAACC,EAAAA,EAAKA,CAACC,OAAO,CAAC,CAAE,YACjB,CAACD,EAAAA,EAAKA,CAACE,IAAI,CAAC,CAAE,WACd,CAACF,EAAAA,EAAKA,CAACG,MAAM,CAAC,CAAE,WAChB,CAACH,EAAAA,EAAKA,CAACI,QAAQ,CAAC,CAAE,UACpB,EAEMC,EAAiB,UACrB,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACC,WAAW,CAAChB,GAAG,CAAC,uBAElE,IACkBO,EAAAA,EAAKA,CAACE,IAAI,EAC1BI,IAAgBN,EAAAA,EAAKA,CAACG,MAAM,EAC5BG,IAAgBN,EAAAA,EAAKA,CAACI,QAAQ,CAEvBE,CADP,CAIKN,EAAAA,EAAKA,CAACI,QAAQ,EAGvB,eAAeM,EAAiBC,CAAM,CAAEC,CAAc,EACpD,IAAIC,EAAO,MAAMF,EAAGG,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC,CACxCC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,KAAKA,CAACI,IAAI,CAAEP,EACxB,GAEA,GAAI,CAACC,EAAM,CACT,GAAM,CAACO,EAAQ,CAAG,MAAMT,EAAGU,MAAM,CAACN,EAAAA,KAAKA,EACpCO,MAAM,CAAC,CACNH,KAAMP,EACNW,YAAaxB,CAAiB,CAACa,EACjC,GACCY,SAAS,GACZX,EAAOO,CACT,CAEA,OAAOP,CACT,CAEO,eAAeY,EAAiBd,CAAM,CAAErB,CAAc,CAAEoC,CAAc,EAC3E,MAAMf,EAAGgB,MAAM,CAACC,EAAAA,SAASA,EACtBX,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACtC,MAAM,CAAEA,IAE9B,MAAMqB,EAAGU,MAAM,CAACO,EAAAA,SAASA,EACtBN,MAAM,CAAC,QACNhC,SACAoC,CACF,EACJ,CAEO,eAAeG,EAAYvC,CAAc,EAC9C,IAAMqB,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,MAAOC,CAJiB,MAAMpB,EAAGG,KAAK,CAACc,SAAS,CAACI,QAAQ,CAAC,CACxDf,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACtC,MAAM,CAAEA,GAC5B2C,KAAM,CAAEpB,MAAM,CAAK,CACrB,GACsB,CAAC,EAAE,CAACA,IAAI,CAACM,IAAI,CAG9B,eAAee,EAAgBC,CAAsB,EAC1D,IAAM7C,EAAS,MAAMD,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,GAE9B,GAAI,CAACC,EAAQ,OAAO,EAEpB,IAAMqB,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMbM,EAAgBL,CALE,MAAMpB,EAAGG,KAAK,CAACc,SAAS,CAACI,QAAQ,CAAC,CACxDf,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACtC,MAAM,CAAEA,GAC5B2C,KAAM,CAAEpB,MAAM,CAAK,CACrB,IAEsCwB,GAAG,CAACC,GAAMA,EAAGzB,IAAI,CAACM,IAAI,EAC5D,MAAOoB,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAACH,EAAyBD,EAChD,CAEO,GAAM,CACXK,SAAU,KAAEC,CAAG,MAAEC,CAAI,CAAE,MACvB/C,CAAI,CACJgD,QAAM,SACNC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,IAAO,EAClBC,OAAQC,QAAQvC,GAAG,CAACwC,WAAW,CAC/BC,QAASC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACpB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAAI,CAClCqB,WAAYC,EAAAA,KAAKA,CACjBC,cAAeC,EAAAA,QAAQA,GAEzBC,UAAW,CACTC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC,CACLC,SAAUV,QAAQvC,GAAG,CAACkD,cAAc,CACpCC,aAAcZ,QAAQvC,GAAG,CAACoD,kBAAkB,GAE9CC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CAClB1C,KAAM,cACN2C,YAAa,CACXC,SAAU,CAAEC,MAAO,MAAOC,KAAM,OAAQC,YAAa,QAAS,EAC9DC,SAAU,CAAEH,MAAO,KAAMC,KAAM,WAAYC,YAAa,OAAQ,CAClE,EACA,MAAME,UAAUN,CAAW,EACzB,GAAI,CAACA,EACH,MAAM,KADU,CACA,aAGlB,GAAM,UAAEC,CAAQ,UAAEI,CAAQ,CAAE,CAAGL,EAE/B,GAAI,CACFO,EAAAA,CAAUA,CAACC,KAAK,CAAC,UAAEP,WAAUI,CAAS,EAExC,CAAE,MAAOI,EAAO,CACd,MAAM,MAAU,UAClB,CAEA,IAAM5D,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAEblC,EAAO,MAAMe,EAAGG,KAAK,CAACsC,KAAK,CAACpC,SAAS,CAAC,CAC1CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACkC,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAEA,GAAI,CAACnE,GAKD,CADY,EAJL,IAIW4E,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAACL,EAAoBvE,EAAKuE,QAAQ,EAHrE,MAAM,MAAU,YAQlB,MAAO,CACL,GAAGvE,CAAI,CACPuE,cAAUM,CACZ,CACF,CACF,GACD,CACDC,OAAQ,CACN,MAAM/B,OAAO,MAAE/C,CAAI,CAAE,EACnB,GAAKA,CAAD,CAAMC,EAAE,CAEZ,CAFc,EAEV,CACF,IAAMc,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAKnB,GAJqB,CAIjB6C,KAJuBhE,EAAGG,KAAK,CAACc,SAAS,CAACZ,SAAS,CAAC,CACtDC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACtC,MAAM,CAAEM,EAAKC,EAAE,CACrC,GAEkB,OAElB,IAAMS,EAAc,MAAMD,IACpBQ,EAAO,MAAMH,EAAiBC,EAAIL,EACxC,OAAMmB,EAAiBd,EAAIf,EAAKC,EAAE,CAAEgB,EAAKhB,EAAE,CAC7C,CAAE,MAAO0E,EAAO,CACdK,QAAQL,KAAK,CAAC,wBAAyBA,EACzC,CACF,CACF,EACAM,UAAW,CACHC,IAAN,MAAU,OAAEC,CAAK,MAAEnF,CAAI,CAAE,IACnBA,IACFmF,EADQ,EACA,CAAGnF,EAAKC,EAAE,CAClBkF,EAAM5D,IAAI,CAAGvB,EAAKuB,IAAI,EAAIvB,EAAKmE,QAAQ,CACvCgB,EAAMhB,QAAQ,CAAGnE,EAAKmE,QAAQ,CAC9BgB,EAAMC,KAAK,CAAGpF,EAAKoF,KAAK,ED/JzB,SAA2B7D,CAAY,EAC5C,IAAM8D,CC8J6CC,CD9JnC/D,CAAI,CAAC,EAAE,CAACgE,WAAW,GAE7BC,EAAaC,MAAMC,IAAI,CAACnE,GAAMoE,MAAM,CACxC,CAACC,EAAKC,IAASD,EAAMC,EAAKC,UAAU,CAAC,GAAI,GACvC5F,EAAO6F,MAAM,CAEXC,EAAkB9F,CAAM,CAACsF,EAAW,CAEpCS,EAAM,CAAC;;2CAE4B,EAAED,EAAgB;;;;;;;;;;;;;QAarD,EAAEX,QAAQ;;;EAGhB,CAAC,CAACa,IAAI,GAGAC,EAAQC,IADMC,cACEC,MAAM,CAACL,GACvBM,EAASC,EAAOd,IAADc,CAAML,GAAOM,QAAQ,CAAC,UAE3C,MAAO,CAAC,0BAA0B,EAAEF,EAAAA,CAAQ,EC8HQpB,EAAM5D,IAAI,GAEnD4D,GAET,MAAMrF,QAAQ,SAAEA,CAAO,OAAEqF,CAAK,CAAE,EAC9B,GAAIA,GAASrF,EAAQE,IAAI,CAAE,CACzBF,EAAQE,IAAI,CAACC,EAAE,CAAGkF,EAAMlF,EAAE,CAC1BH,EAAQE,IAAI,CAACuB,IAAI,CAAG4D,EAAM5D,IAAI,CAC9BzB,EAAQE,IAAI,CAACmE,QAAQ,CAAGgB,EAAMhB,QAAQ,CACtCrE,EAAQE,IAAI,CAACoF,KAAK,CAAGD,EAAMC,KAAK,CAEhC,IAAMrE,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACfC,EAAkB,MAAMpB,EAAGG,KAAK,CAACc,SAAS,CAACI,QAAQ,CAAC,CACtDf,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACU,EAAAA,SAASA,CAACtC,MAAM,CAAEI,EAAQE,IAAI,CAACC,EAAE,EAC3CoC,KAAM,CAAEpB,MAAM,CAAK,CACrB,GAEA,GAAI,CAACkB,EAAgB4D,MAAM,CAAE,CAC3B,IAAMrF,EAAc,MAAMD,IACpBQ,EAAO,MAAMH,EAAiBC,EAAIL,EACxC,OAAMmB,EAAiBd,EAAIjB,EAAQE,IAAI,CAACC,EAAE,CAAEgB,EAAKhB,EAAE,EACnDkC,EAAkB,CAAC,CACjBzC,OAAQI,EAAQE,IAAI,CAACC,EAAE,CACvB6B,OAAQb,EAAKhB,EAAE,CACfyG,UAAW,IAAIC,KACf1F,KAAMA,CACR,EAAE,CAGJnB,EAAQE,IAAI,CAACmB,KAAK,CAAGgB,EAAgBM,GAAG,CAACC,GAAO,EAC9CnB,KAAMmB,EAAGzB,IAAI,CAACM,IAAI,CACpB,EACF,CAEA,OAAOzB,CACT,CACF,EACAA,QAAS,CACP8G,SAAU,KACZ,EACF,GAEO,CAFJ,cAEmBC,EAAS1C,CAAgB,CAAEI,CAAgB,EAC/D,IAAMxD,EAAKmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAMnB,GAJiB,CAIb4E,KAJmB/F,EAAGG,GAIZ,EAJiB,CAACsC,KAAK,CAACpC,SAAS,CAAC,CAC9CC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACkC,EAAAA,KAAKA,CAACW,QAAQ,CAAEA,EAC5B,GAGE,MAAM,MAAU,UAGlB,IAAM4C,EAAiB,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACzC,GAEpC,CAACvE,EAAK,CAAG,MAAMe,EAAGU,MAAM,CAAC+B,EAAAA,KAAKA,EACjC9B,MAAM,CAAC,UACNyC,EACAI,SAAUwC,CACZ,GACCnF,SAAS,GAEZ,OAAO5B,CACT,gFCvOO,IAAMkC,EAAW,IAAM+E,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAACtG,CAAAA,EAAAA,EAAAA,iBAAAA,CAAiBA,GAAGC,GAAG,CAACsG,EAAE,CAAE,CAAEC,MAAMA,CAAAA,CAAC,GAAE,gCAAHA,gCCJnE,IAAM/G,EAAQ,CACnBC,QAAS,UACTC,KAAM,OACNC,OAAQ,SACRC,SAAU,UACZ,EAAW,EAIgB,CACzB4G,aAAc,eACdC,eAAgB,iBAChBC,aAAc,eACdC,cAAe,gBACfC,eAAgB,gBAClB,EAAW,EAIiD,CAC1D,CAACpH,EAAMC,OAAO,CAAC,CAAEoH,OAAO/F,MAAM,CAACgG,GAC/B,CAACtH,EAAME,IAAI,CAAC,CAAE,CACZoH,EAAYN,YAAY,CACxBM,EAAYL,cAAc,CAC1BK,EAAYF,cAAc,CAC3B,CACD,CAACpH,EAAMG,MAAM,CAAC,CAAE,CACdmH,EAAYN,YAAY,CACxBM,EAAYL,cAAc,CAC3B,CACD,CAACjH,EAAMI,QAAQ,CAAC,CAAE,EAAE,EACX,SAEKmC,EAAcX,CAAiB,CAAEO,CAAsB,EACrE,OAAOP,EAAU2F,IAAI,CAAC1G,GAAQ2G,CAAgB,CAAC3G,EAAK,EAAE4G,SAAStF,GACjE,kVC9BO,IAAMiB,EAAQsE,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvC7H,GAAI8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MACNC,UAAU,GACVC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IACrC5G,KAAMwG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXK,MAAOL,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SAASM,MAAM,GAC3BC,cAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,gBAAiB,CAAEC,KAAM,cAAe,GAC/DpD,MAAO2C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZ5D,SAAU4D,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYM,MAAM,GACjC9D,SAAUwD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACjB,GAAE,EACsBD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CACjC,UACA,CACEpI,OAAQqI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UACVU,OAAO,GACPC,UAAU,CAAC,IAAMlF,EAAMvD,EAAE,CAAE,CAAE0I,SAAU,SAAU,GACpDtE,KAAM0D,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQa,KAAK,GAAuBH,OAAO,GACtDI,SAAUd,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YAAYU,OAAO,GAClCK,kBAAmBf,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,qBAAqBU,OAAO,GACpDM,cAAehB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,iBACpBiB,aAAcjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACnBkB,WAAYV,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cACpBW,WAAYnB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,cACjBoB,MAAOpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,SACZqB,SAAUrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,YACfsB,cAAetB,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBACtB,EACA,GAAc,EACZuB,KADY,OACCtB,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CACtBuB,QAAS,CAACC,EAAQX,QAAQ,CAAEW,EAAQV,iBAAiB,CAAC,GAE1D,GAGWW,CAFZ,CAEqB3B,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,QAAS,CACzC7H,GAAI8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DuB,QAAS3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGJ,MAAM,GACzC3I,OAAQqI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,UAAUW,UAAU,CAAC,IAAMlF,EAAMvD,EAAE,CAAE,CAAE0I,SAAU,SAAU,GACxEjC,UAAW6B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAItB,MACxBgD,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbmB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAMJ,SAAS,EAChE,GAEaK,CAFV,CAEqBlC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7C7H,GAAI8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D8B,QAASlC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACXU,OAAO,GACPC,UAAU,CAAC,IAAMe,EAAOxJ,EAAE,CAAE,CAAE0I,SAAU,SAAU,GACrDuB,YAAanC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,gBAAgBU,OAAO,GACzC0B,QAASpC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC2B,QAASrC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAChC4B,KAAMtC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QACXuC,WAAY/B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,cAAe,CAAEC,KAAM,cAAe,GACvDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAItB,KAC1B,EAAG,GAAY,EACb4D,GADa,QACDV,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,wBAAwBC,EAAE,CAACC,EAAME,OAAO,EAC5D,GAEaO,CAFV,CAEqB1C,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,UAAW,CAC7C7H,GAAI8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DzI,OAAQqI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WACVU,OAAO,GACPC,UAAU,CAAC,IAAMlF,EAAMvD,EAAE,CAAE,CAAE0I,SAAU,SAAU,GACpD8B,IAAK1C,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GACxBiC,QAASnC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGkC,OAAO,EAAC,GACnEjE,UAAW6B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAItB,MACxBiE,UAAWrC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GACrDC,OAAO,GACPR,UAAU,CAAC,IAAM,IAAItB,KAC1B,GAEaxF,EAAQ2G,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,OAAQ,CACvC7H,GAAI8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9D5G,KAAMwG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1B9G,YAAaoG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,eAClBrB,UAAW6B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAItB,MAC7EiE,UAAWrC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAItB,KAC/E,GAAG,EAEsBmB,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,YAAa,CAChDpI,OAAQqI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMlF,EAAMvD,EAAE,CAAE,CAAE0I,SAAU,SAAU,GACnF7G,OAAQiG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMvH,EAAMlB,EAAE,CAAE,CAAE0I,SAAU,SAAU,GACnFjC,UAAW6B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAItB,KAC/E,EAAIoD,GAAW,EACbc,GAAI7C,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,CAAC,CAAEuB,QAAS,CAACQ,EAAMrK,MAAM,CAAEqK,EAAMjI,MAAM,CAAC,GACxD,GAEagJ,CAFT,CAEmBhD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,WAAY,CAC7C7H,GAAI8H,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,MAAMC,UAAU,GAAGC,UAAU,CAAC,IAAMC,OAAOC,UAAU,IAC9DzI,OAAQqI,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,WAAWU,OAAO,GAAGC,UAAU,CAAC,IAAMlF,EAAMvD,EAAE,EAC3DsB,KAAMwG,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,QAAQU,OAAO,GAC1BsC,IAAKhD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOU,OAAO,GAAGJ,MAAM,GACjC3B,UAAW6B,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GAAGP,UAAU,CAAC,IAAM,IAAItB,MAC7EgD,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,WAAY,GACrDkC,QAASnC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,UAAW,CAAEC,KAAM,SAAU,GAAGC,OAAO,GAAGkC,OAAO,EAAC,EACrE,EAAG,GAAY,EACbK,GADa,cACKC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,uBAAuBnB,EAAE,CAACC,EAAMxI,IAAI,CAAEwI,EAAMrK,MAAM,EAClF,GAEawL,CAFT,CAE+BpD,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC,qBAAsB,CACnEqD,IAAKpD,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAAC,OAAOC,UAAU,GAC3B2B,UAAWpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC,aAAc,CAAEC,KAAM,cAAe,GAAGC,OAAO,EACpE,EAAG,GAAY,EACbmB,GADa,UACCC,CAAAA,EAAAA,EAAAA,EAAAA,CAAKA,CAAC,0BAA0BC,EAAE,CAACC,EAAMJ,SAAS,EAClE,GAEayB,CAFT,CAE4BC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACP,EAAS,CAAC,CAAEQ,KAAG,CAAE,GAAM,EAC/DtL,KAAMsL,EAAI9H,EAAO,CACf+H,OAAQ,CAACT,EAAQpL,MAAM,CAAC,CACxBgJ,WAAY,CAAClF,EAAMvD,EAAE,CAAC,GAE1B,GAEauL,CAFT,CAE8BH,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACrJ,EAAW,CAAC,CAAEsJ,KAAG,CAAE,GAAM,EACnEtL,KAAMsL,EAAI9H,EAAO,CACf+H,OAAQ,CAACvJ,EAAUtC,MAAM,CAAC,CAC1BgJ,WAAY,CAAClF,EAAMvD,EAAE,CAAC,GAExBgB,KAAMqK,EAAInK,EAAO,CACfoK,OAAQ,CAACvJ,EAAUF,MAAM,CAAC,CAC1B4G,WAAY,CAACvH,EAAMlB,EAAE,CAAC,GAE1B,GAEawL,CAFT,CAE0BJ,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAC7H,EAAO,CAAC,MAAEkI,CAAI,CAAE,GAAM,EAC5D1J,UAAW0J,EAAK1J,GAChB8I,QAASY,EAAKZ,GAChB,GAEaa,CAFT,CAE0BN,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAClK,EAAO,CAAC,MAAEuK,CAAI,CAAE,GAAM,EAC5D1J,UAAW0J,EAAK1J,GAClB,IAAI,sFC5IG,SAAS4J,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,CAEO,eAAe7E,EAAazC,CAAgB,EACjD,IAAM6B,EAAU,IAAIC,YACd2F,EAAO7I,QAAQvC,GAAG,CAACwC,WAAW,EAAI,GAClC6I,EAAO7F,EAAQE,MAAM,CAAC/B,EAAWyH,GAEvC,OAAOE,KAAKC,OAAOC,YAAY,IAAI,IAAIC,WAD1B,MAAMnE,OAAOoE,MAAM,CAACC,MAAM,CAAC,UAAWN,KAErD,CAEO,eAAerH,EAAgBL,CAAgB,CAAEwC,CAAsB,EAE5E,OADa,MAAMC,EAAazC,KAChBwC,CAClB,8DChBO,IAAMtC,EAAa+H,EAAAA,CAAAA,CAAAA,MAAQ,CAAC,CACjCrI,SAAUqI,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,WACPC,GAAG,CAAC,GAAI,gBACRC,KAAK,CAAC,mBAAoB,uBAC1BC,MAAM,CAACC,GAAO,CAACA,EAAIhF,QAAQ,CAAC,KAAM,cACrCtD,SAAUiI,EAAAA,CAAAA,CAAAA,MAAQ,GACfC,GAAG,CAAC,EAAG,eACZ,GAAE", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/api/auth/[...auth]/route.ts", "webpack://_N_E/./app/api/auth/[...auth]/route.ts?c7e4", "webpack://_N_E/?38d1", "webpack://_N_E/./app/lib/apiKey.ts", "webpack://_N_E/./app/lib/avatar.ts", "webpack://_N_E/./app/lib/auth.ts", "webpack://_N_E/./app/lib/db.ts", "webpack://_N_E/./app/lib/permissions.ts", "webpack://_N_E/./app/lib/schema.ts", "webpack://_N_E/./app/lib/utils.ts", "webpack://_N_E/./app/lib/validation.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { GET, POST } from \"@/lib/auth\"\r\n\r\nexport { GET, POST }\r\n\r\nexport const runtime = 'edge'", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\auth\\\\[...auth]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...auth]/route\",\n        pathname: \"/api/auth/[...auth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...auth]/route\"\n    },\n    resolvedPagePath: \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\api\\\\auth\\\\[...auth]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fauth%2F%5B...auth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...auth%5D%2Froute&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...auth%5D%2Froute.ts&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Fapi%2Fauth%2F%5B...auth%5D%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/auth/[...auth]/route.ts?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/auth/[...auth]/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/auth/[...auth]/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "import { createDb } from \"./db\"\r\nimport { api<PERSON><PERSON>s } from \"./schema\"\r\nimport { eq, and, gt } from \"drizzle-orm\"\r\nimport { NextResponse } from \"next/server\"\r\nimport type { User } from \"next-auth\"\r\nimport { auth } from \"./auth\"\r\nimport { headers } from \"next/headers\"\r\n\r\nasync function getUserByApiKey(key: string): Promise<User | null> {\r\n  const db = createDb()\r\n  const apiKey = await db.query.apiKeys.findFirst({\r\n    where: and(\r\n      eq(apiKeys.key, key),\r\n      eq(apiKeys.enabled, true),\r\n      gt(apiKeys.expiresAt, new Date())\r\n    ),\r\n    with: {\r\n      user: true\r\n    }\r\n  })\r\n\r\n  if (!apiKey) return null\r\n\r\n  return apiKey.user\r\n}\r\n\r\nexport async function handleApiKeyAuth(apiKey: string, pathname: string) {\r\n  if (!pathname.startsWith('/api/emails') && !pathname.startsWith('/api/config')) {\r\n    return NextResponse.json(\r\n      { error: \"无权限查看\" },\r\n      { status: 403 }\r\n    )\r\n  }\r\n\r\n  const user = await getUserByApiKey(apiKey)\r\n  if (!user?.id) {\r\n    return NextResponse.json(\r\n      { error: \"无效的 API Key\" },\r\n      { status: 401 }\r\n    )\r\n  }\r\n\r\n  const response = NextResponse.next()\r\n  response.headers.set(\"X-User-Id\", user.id)\r\n  return response\r\n}\r\n\r\nexport const getUserId = async () => {\r\n  const headersList = await headers()\r\n  const userId = headersList.get(\"X-User-Id\")\r\n  \r\n  if (userId) return userId\r\n\r\n  const session = await auth()\r\n\r\n  return session?.user.id\r\n}\r\n", "const COLORS = [\r\n  '#2196F3', // 蓝色\r\n  '#009688', // 青色\r\n  '#9C27B0', // 紫色\r\n  '#F44336', // 红色\r\n  '#673AB7', // 深紫色\r\n  '#3F51B5', // 靛蓝\r\n  '#4CAF50', // 绿色\r\n  '#FF5722', // 深橙\r\n  '#795548', // 棕色\r\n  '#607D8B', // 蓝灰\r\n];\r\n\r\nexport function generateAvatarUrl(name: string): string {\r\n  const initial = name[0].toUpperCase();\r\n  \r\n  const colorIndex = Array.from(name).reduce(\r\n    (acc, char) => acc + char.charCodeAt(0), 0\r\n  ) % COLORS.length;\r\n  \r\n  const backgroundColor = COLORS[colorIndex];\r\n  \r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\" width=\"100\" height=\"100\">\r\n      <rect width=\"100\" height=\"100\" fill=\"${backgroundColor}\"/>\r\n      <text \r\n        x=\"50%\" \r\n        y=\"50%\" \r\n        fill=\"white\" \r\n        font-family=\"system-ui, -apple-system, sans-serif\" \r\n        font-size=\"45\" \r\n        font-weight=\"500\"\r\n        text-anchor=\"middle\"\r\n        alignment-baseline=\"central\"\r\n        dominant-baseline=\"central\"\r\n        style=\"text-transform: uppercase\"\r\n      >\r\n        ${initial}\r\n      </text>\r\n    </svg>\r\n  `.trim();\r\n\r\n  const encoder = new TextEncoder();\r\n  const bytes = encoder.encode(svg);\r\n  const base64 = Buffer.from(bytes).toString('base64');\r\n  \r\n  return `data:image/svg+xml;base64,${base64}`;\r\n} ", "import NextAuth from \"next-auth\"\r\nimport GitHub from \"next-auth/providers/github\"\r\nimport { Dr<PERSON>zleAdapter } from \"@auth/drizzle-adapter\"\r\nimport { createDb, Db } from \"./db\"\r\nimport { accounts, users, roles, userRoles } from \"./schema\"\r\nimport { eq } from \"drizzle-orm\"\r\nimport { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { Permission, hasPermission, ROLES, Role } from \"./permissions\"\r\nimport CredentialsProvider from \"next-auth/providers/credentials\"\r\nimport { hashPassword, comparePassword } from \"@/lib/utils\"\r\nimport { authSchema } from \"@/lib/validation\"\r\nimport { generateAvatarUrl } from \"./avatar\"\r\nimport { getUserId } from \"./apiKey\"\r\n\r\nconst ROLE_DESCRIPTIONS: Record<Role, string> = {\r\n  [ROLES.EMPEROR]: \"皇帝（网站所有者）\",\r\n  [ROLES.DUKE]: \"公爵（超级用户）\",\r\n  [ROLES.KNIGHT]: \"骑士（高级用户）\",\r\n  [ROLES.CIVILIAN]: \"平民（普通用户）\",\r\n}\r\n\r\nconst getDefaultRole = async (): Promise<Role> => {\r\n  const defaultRole = await getRequestContext().env.SITE_CONFIG.get(\"DEFAULT_ROLE\")\r\n\r\n  if (\r\n    defaultRole === ROLES.DUKE ||\r\n    defaultRole === ROLES.KNIGHT ||\r\n    defaultRole === ROLES.CIVILIAN\r\n  ) {\r\n    return defaultRole as Role\r\n  }\r\n  \r\n  return ROLES.CIVILIAN\r\n}\r\n\r\nasync function findOrCreateRole(db: Db, roleName: Role) {\r\n  let role = await db.query.roles.findFirst({\r\n    where: eq(roles.name, roleName),\r\n  })\r\n\r\n  if (!role) {\r\n    const [newRole] = await db.insert(roles)\r\n      .values({\r\n        name: roleName,\r\n        description: ROLE_DESCRIPTIONS[roleName],\r\n      })\r\n      .returning()\r\n    role = newRole\r\n  }\r\n\r\n  return role\r\n}\r\n\r\nexport async function assignRoleToUser(db: Db, userId: string, roleId: string) {\r\n  await db.delete(userRoles)\r\n    .where(eq(userRoles.userId, userId))\r\n\r\n  await db.insert(userRoles)\r\n    .values({\r\n      userId,\r\n      roleId,\r\n    })\r\n}\r\n\r\nexport async function getUserRole(userId: string) {\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n  return userRoleRecords[0].role.name\r\n}\r\n\r\nexport async function checkPermission(permission: Permission) {\r\n  const userId = await getUserId()\r\n\r\n  if (!userId) return false\r\n\r\n  const db = createDb()\r\n  const userRoleRecords = await db.query.userRoles.findMany({\r\n    where: eq(userRoles.userId, userId),\r\n    with: { role: true },\r\n  })\r\n\r\n  const userRoleNames = userRoleRecords.map(ur => ur.role.name)\r\n  return hasPermission(userRoleNames as Role[], permission)\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  auth,\r\n  signIn,\r\n  signOut\r\n} = NextAuth(() => ({\r\n  secret: process.env.AUTH_SECRET,\r\n  adapter: DrizzleAdapter(createDb(), {\r\n    usersTable: users,\r\n    accountsTable: accounts,\r\n  }),\r\n  providers: [\r\n    GitHub({\r\n      clientId: process.env.AUTH_GITHUB_ID,\r\n      clientSecret: process.env.AUTH_GITHUB_SECRET,\r\n    }),\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        username: { label: \"用户名\", type: \"text\", placeholder: \"请输入用户名\" },\r\n        password: { label: \"密码\", type: \"password\", placeholder: \"请输入密码\" },\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials) {\r\n          throw new Error(\"请输入用户名和密码\")\r\n        }\r\n\r\n        const { username, password } = credentials\r\n\r\n        try {\r\n          authSchema.parse({ username, password })\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        } catch (error) {\r\n          throw new Error(\"输入格式不正确\")\r\n        }\r\n\r\n        const db = createDb()\r\n\r\n        const user = await db.query.users.findFirst({\r\n          where: eq(users.username, username as string),\r\n        })\r\n\r\n        if (!user) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        const isValid = await comparePassword(password as string, user.password as string)\r\n        if (!isValid) {\r\n          throw new Error(\"用户名或密码错误\")\r\n        }\r\n\r\n        return {\r\n          ...user,\r\n          password: undefined,\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  events: {\r\n    async signIn({ user }) {\r\n      if (!user.id) return\r\n\r\n      try {\r\n        const db = createDb()\r\n        const existingRole = await db.query.userRoles.findFirst({\r\n          where: eq(userRoles.userId, user.id),\r\n        })\r\n\r\n        if (existingRole) return\r\n\r\n        const defaultRole = await getDefaultRole()\r\n        const role = await findOrCreateRole(db, defaultRole)\r\n        await assignRoleToUser(db, user.id, role.id)\r\n      } catch (error) {\r\n        console.error('Error assigning role:', error)\r\n      }\r\n    },\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id\r\n        token.name = user.name || user.username\r\n        token.username = user.username\r\n        token.image = user.image || generateAvatarUrl(token.name as string)\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token && session.user) {\r\n        session.user.id = token.id as string\r\n        session.user.name = token.name as string\r\n        session.user.username = token.username as string\r\n        session.user.image = token.image as string\r\n\r\n        const db = createDb()\r\n        let userRoleRecords = await db.query.userRoles.findMany({\r\n          where: eq(userRoles.userId, session.user.id),\r\n          with: { role: true },\r\n        })\r\n  \r\n        if (!userRoleRecords.length) {\r\n          const defaultRole = await getDefaultRole()\r\n          const role = await findOrCreateRole(db, defaultRole)\r\n          await assignRoleToUser(db, session.user.id, role.id)\r\n          userRoleRecords = [{\r\n            userId: session.user.id,\r\n            roleId: role.id,\r\n            createdAt: new Date(),\r\n            role: role\r\n          }]\r\n        }\r\n  \r\n        session.user.roles = userRoleRecords.map(ur => ({\r\n          name: ur.role.name,\r\n        }))\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: \"jwt\",\r\n  },\r\n}))\r\n\r\nexport async function register(username: string, password: string) {\r\n  const db = createDb()\r\n  \r\n  const existing = await db.query.users.findFirst({\r\n    where: eq(users.username, username)\r\n  })\r\n\r\n  if (existing) {\r\n    throw new Error(\"用户名已存在\")\r\n  }\r\n\r\n  const hashedPassword = await hashPassword(password)\r\n  \r\n  const [user] = await db.insert(users)\r\n    .values({\r\n      username,\r\n      password: hashedPassword,\r\n    })\r\n    .returning()\r\n\r\n  return user\r\n}\r\n", "import { getRequestContext } from \"@cloudflare/next-on-pages\"\r\nimport { drizzle } from \"drizzle-orm/d1\"\r\nimport * as schema from \"./schema\"\r\n\r\nexport const createDb = () => drizzle(getRequestContext().env.DB, { schema })\r\n\r\nexport type Db = ReturnType<typeof createDb>\r\n", "export const ROLES = {\r\n  EMPEROR: 'emperor',\r\n  <PERSON>U<PERSON>: 'duke',\r\n  <PERSON><PERSON><PERSON><PERSON>: 'knight',\r\n  CIVILIAN: 'civilian',\r\n} as const;\r\n\r\nexport type Role = typeof ROLES[keyof typeof ROLES];\r\n\r\nexport const PERMISSIONS = {\r\n  MANAGE_EMAIL: 'manage_email',\r\n  <PERSON><PERSON>GE_WEBHOOK: 'manage_webhook',\r\n  PROMOTE_USER: 'promote_user',\r\n  MANAGE_CONFIG: 'manage_config',\r\n  MANAGE_API_KEY: 'manage_api_key',\r\n} as const;\r\n\r\nexport type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];\r\n\r\nexport const ROLE_PERMISSIONS: Record<Role, Permission[]> = {\r\n  [ROLES.EMPEROR]: Object.values(PERMISSIONS),\r\n  [ROLES.DUKE]: [\r\n    PERMISSIONS.MANAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n    PERMISSIONS.MANAGE_API_KEY,\r\n  ],\r\n  [ROLES.KNIGHT]: [\r\n    PERMISSIONS.<PERSON>NAGE_EMAIL,\r\n    PERMISSIONS.MANAGE_WEBHOOK,\r\n  ],\r\n  [ROLES.CIVILIAN]: [],\r\n} as const;\r\n\r\nexport function hasPermission(userRoles: Role[], permission: Permission): boolean {\r\n  return userRoles.some(role => ROLE_PERMISSIONS[role]?.includes(permission));\r\n} ", "import { integer, sqliteTable, text, primaryKey, uniqueIndex, index } from \"drizzle-orm/sqlite-core\"\r\nimport type { AdapterAccountType } from \"next-auth/adapters\"\r\nimport { relations } from 'drizzle-orm';\r\n\r\n// https://authjs.dev/getting-started/adapters/drizzle\r\nexport const users = sqliteTable(\"user\", {\r\n  id: text(\"id\")\r\n    .primaryKey()\r\n    .$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\"),\r\n  email: text(\"email\").unique(),\r\n  emailVerified: integer(\"emailVerified\", { mode: \"timestamp_ms\" }),\r\n  image: text(\"image\"),\r\n  username: text(\"username\").unique(),\r\n  password: text(\"password\"),\r\n})\r\nexport const accounts = sqliteTable(\r\n  \"account\",\r\n  {\r\n    userId: text(\"userId\")\r\n      .notNull()\r\n      .references(() => users.id, { onDelete: \"cascade\" }),\r\n    type: text(\"type\").$type<AdapterAccountType>().notNull(),\r\n    provider: text(\"provider\").notNull(),\r\n    providerAccountId: text(\"providerAccountId\").notNull(),\r\n    refresh_token: text(\"refresh_token\"),\r\n    access_token: text(\"access_token\"),\r\n    expires_at: integer(\"expires_at\"),\r\n    token_type: text(\"token_type\"),\r\n    scope: text(\"scope\"),\r\n    id_token: text(\"id_token\"),\r\n    session_state: text(\"session_state\"),\r\n  },\r\n  (account) => ({\r\n    compoundKey: primaryKey({\r\n      columns: [account.provider, account.providerAccountId],\r\n    }),\r\n  })\r\n)\r\n\r\nexport const emails = sqliteTable(\"email\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  address: text(\"address\").notNull().unique(),\r\n  userId: text(\"userId\").references(() => users.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp_ms\" }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"email_expires_at_idx\").on(table.expiresAt),\r\n}))\r\n\r\nexport const messages = sqliteTable(\"message\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  emailId: text(\"emailId\")\r\n    .notNull()\r\n    .references(() => emails.id, { onDelete: \"cascade\" }),\r\n  fromAddress: text(\"from_address\").notNull(),\r\n  subject: text(\"subject\").notNull(),\r\n  content: text(\"content\").notNull(),\r\n  html: text(\"html\"),\r\n  receivedAt: integer(\"received_at\", { mode: \"timestamp_ms\" })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  emailIdIdx: index(\"message_email_id_idx\").on(table.emailId),\r\n}))\r\n\r\nexport const webhooks = sqliteTable('webhook', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id')\r\n    .notNull()\r\n    .references(() => users.id, { onDelete: \"cascade\" }),\r\n  url: text('url').notNull(),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n  createdAt: integer('created_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })\r\n    .notNull()\r\n    .$defaultFn(() => new Date()),\r\n})\r\n\r\nexport const roles = sqliteTable(\"role\", {\r\n  id: text(\"id\").primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  name: text(\"name\").notNull(),\r\n  description: text(\"description\"),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n});\r\n\r\nexport const userRoles = sqliteTable(\"user_role\", {\r\n  userId: text(\"user_id\").notNull().references(() => users.id, { onDelete: \"cascade\" }),\r\n  roleId: text(\"role_id\").notNull().references(() => roles.id, { onDelete: \"cascade\" }),\r\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).$defaultFn(() => new Date()),\r\n}, (table) => ({\r\n  pk: primaryKey({ columns: [table.userId, table.roleId] }),\r\n}));\r\n\r\nexport const apiKeys = sqliteTable('api_keys', {\r\n  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),\r\n  userId: text('user_id').notNull().references(() => users.id),\r\n  name: text('name').notNull(),\r\n  key: text('key').notNull().unique(),\r\n  createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp' }),\r\n  enabled: integer('enabled', { mode: 'boolean' }).notNull().default(true),\r\n}, (table) => ({\r\n  nameUserIdUnique: uniqueIndex('name_user_id_unique').on(table.name, table.userId)\r\n}));\r\n\r\nexport const revoked_credentials = sqliteTable('revoked_credential', {\r\n  jti: text('jti').primaryKey(),\r\n  expiresAt: integer('expires_at', { mode: 'timestamp_ms' }).notNull(),\r\n}, (table) => ({\r\n  expiresAtIdx: index(\"revoked_expires_at_idx\").on(table.expiresAt),\r\n}));\r\n\r\nexport const apiKeysRelations = relations(apiKeys, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [apiKeys.userId],\r\n    references: [users.id],\r\n  }),\r\n}));\r\n\r\nexport const userRolesRelations = relations(userRoles, ({ one }) => ({\r\n  user: one(users, {\r\n    fields: [userRoles.userId],\r\n    references: [users.id],\r\n  }),\r\n  role: one(roles, {\r\n    fields: [userRoles.roleId],\r\n    references: [roles.id],\r\n  }),\r\n}));\r\n\r\nexport const usersRelations = relations(users, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n  apiKeys: many(apiKeys),\r\n}));\r\n\r\nexport const rolesRelations = relations(roles, ({ many }) => ({\r\n  userRoles: many(userRoles),\r\n}));", "import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport async function hashPassword(password: string): Promise<string> {\r\n  const encoder = new TextEncoder()\r\n  const salt = process.env.AUTH_SECRET || ''\r\n  const data = encoder.encode(password + salt)\r\n  const hash = await crypto.subtle.digest('SHA-256', data)\r\n  return btoa(String.fromCharCode(...new Uint8Array(hash)))\r\n}\r\n\r\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\r\n  const hash = await hashPassword(password)\r\n  return hash === hashedPassword\r\n}", "import { z } from \"zod\"\r\n\r\nexport const authSchema = z.object({\r\n  username: z.string()\r\n    .min(1, \"用户名不能为空\")\r\n    .max(20, \"用户名不能超过20个字符\")\r\n    .regex(/^[a-zA-Z0-9_-]+$/, \"用户名只能包含字母、数字、下划线和横杠\")\r\n    .refine(val => !val.includes('@'), \"用户名不能是邮箱格式\"),\r\n  password: z.string()\r\n    .min(8, \"密码长度必须大于等于8位\")\r\n})\r\n\r\nexport type AuthSchema = z.infer<typeof authSchema>"], "names": ["runtime", "getUserId", "userId", "headersList", "headers", "get", "session", "auth", "user", "id", "COLORS", "ROLE_DESCRIPTIONS", "ROLES", "EMPEROR", "DUKE", "KNIGHT", "CIVILIAN", "getDefaultRole", "defaultRole", "getRequestContext", "env", "SITE_CONFIG", "findOrCreateRole", "db", "<PERSON><PERSON><PERSON>", "role", "query", "roles", "<PERSON><PERSON><PERSON><PERSON>", "where", "eq", "name", "newRole", "insert", "values", "description", "returning", "assignRoleToUser", "roleId", "delete", "userRoles", "getUserRole", "createDb", "userRoleRecords", "find<PERSON>any", "with", "checkPermission", "permission", "userRoleNames", "map", "ur", "hasPermission", "handlers", "GET", "POST", "signIn", "signOut", "NextAuth", "secret", "process", "AUTH_SECRET", "adapter", "DrizzleAdapter", "usersTable", "users", "accountsTable", "accounts", "providers", "GitHub", "clientId", "AUTH_GITHUB_ID", "clientSecret", "AUTH_GITHUB_SECRET", "CredentialsProvider", "credentials", "username", "label", "type", "placeholder", "password", "authorize", "authSchema", "parse", "error", "comparePassword", "undefined", "events", "existingRole", "console", "callbacks", "jwt", "token", "image", "initial", "generateAvatarUrl", "toUpperCase", "colorIndex", "Array", "from", "reduce", "acc", "char", "charCodeAt", "length", "backgroundColor", "svg", "trim", "bytes", "encoder", "TextEncoder", "encode", "base64", "<PERSON><PERSON><PERSON>", "toString", "createdAt", "Date", "strategy", "register", "existing", "hashedPassword", "hashPassword", "drizzle", "DB", "schema", "MANAGE_EMAIL", "MANAGE_WEBHOOK", "PROMOTE_USER", "MANAGE_CONFIG", "MANAGE_API_KEY", "Object", "PERMISSIONS", "some", "ROLE_PERMISSIONS", "includes", "sqliteTable", "text", "<PERSON><PERSON><PERSON>", "$defaultFn", "crypto", "randomUUID", "email", "unique", "emailVerified", "integer", "mode", "notNull", "references", "onDelete", "$type", "provider", "providerAccountId", "refresh_token", "access_token", "expires_at", "token_type", "scope", "id_token", "session_state", "<PERSON><PERSON><PERSON>", "columns", "account", "emails", "address", "expiresAt", "expiresAtIdx", "index", "on", "table", "messages", "emailId", "fromAddress", "subject", "content", "html", "receivedAt", "emailIdIdx", "webhooks", "url", "enabled", "default", "updatedAt", "pk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "nameUserIdUnique", "uniqueIndex", "revoked_credentials", "jti", "apiKeysRelations", "relations", "one", "fields", "userRolesRelations", "usersRelations", "many", "rolesRelations", "cn", "inputs", "twMerge", "clsx", "salt", "data", "btoa", "String", "fromCharCode", "Uint8Array", "subtle", "digest", "z", "min", "max", "regex", "refine", "val"], "sourceRoot": "", "ignoreList": []}