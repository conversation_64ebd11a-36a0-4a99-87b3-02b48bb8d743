{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/hmr-refresh-reducer.ts"], "sourcesContent": ["import { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  ReadonlyReducerState,\n  ReducerState,\n  HmrRefreshAction,\n  Mutable,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { handleMutable } from '../handle-mutable'\nimport { applyFlightData } from '../apply-flight-data'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\n\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(\n  state: ReadonlyReducerState,\n  action: HmrRefreshAction\n): ReducerState {\n  const { origin } = action\n  const mutable: Mutable = {}\n  const href = state.canonicalUrl\n\n  mutable.preserveCustomHistoryState = false\n\n  const cache: CacheNode = createEmptyCacheNode()\n  // If the current tree was intercepted, the nextUrl should be included in the request.\n  // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n  const includeNextUrl = hasInterceptionRouteInCurrentTree(state.tree)\n\n  // TODO-APP: verify that `href` is not an external url.\n  // Fetch data from the root of the tree.\n  cache.lazyData = fetchServerResponse(new URL(href, origin), {\n    flightRouterState: [state.tree[0], state.tree[1], state.tree[2], 'refetch'],\n    nextUrl: includeNextUrl ? state.nextUrl : null,\n    isHmrRefresh: true,\n  })\n\n  return cache.lazyData.then(\n    ({ flightData, canonicalUrl: canonicalUrlOverride }) => {\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      // Remove cache.lazyData as it has been resolved at this point.\n      cache.lazyData = null\n\n      let currentTree = state.tree\n      let currentCache = state.cache\n\n      for (const normalizedFlightData of flightData) {\n        const { tree: treePatch, isRootRender } = normalizedFlightData\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('REFRESH FAILED')\n          return state\n        }\n\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            href,\n            state.pushRef.pendingPush\n          )\n        }\n\n        const canonicalUrlOverrideHref = canonicalUrlOverride\n          ? createHrefFromUrl(canonicalUrlOverride)\n          : undefined\n\n        if (canonicalUrlOverride) {\n          mutable.canonicalUrl = canonicalUrlOverrideHref\n        }\n        const applied = applyFlightData(\n          currentCache,\n          cache,\n          normalizedFlightData\n        )\n\n        if (applied) {\n          mutable.cache = cache\n          currentCache = cache\n        }\n\n        mutable.patchedTree = newTree\n        mutable.canonicalUrl = href\n\n        currentTree = newTree\n      }\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n\nfunction hmrRefreshReducerNoop(\n  state: ReadonlyReducerState,\n  _action: HmrRefreshAction\n): ReducerState {\n  return state\n}\n\nexport const hmrRefreshReducer =\n  process.env.NODE_ENV === 'production'\n    ? hmrRefreshReducerNoop\n    : hmrRefreshReducerImpl\n"], "names": ["hmrRefreshReducer", "hmrRefreshReducerImpl", "state", "action", "origin", "mutable", "href", "canonicalUrl", "preserveCustomHistoryState", "cache", "createEmptyCacheNode", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "tree", "lazyData", "fetchServerResponse", "URL", "flightRouterState", "nextUrl", "isHmrRefresh", "then", "flightData", "canonicalUrlOverride", "handleExternalUrl", "pushRef", "pendingPush", "currentTree", "currentCache", "normalizedFlightData", "treePatch", "isRootRender", "console", "log", "newTree", "applyRouterStatePatchToTree", "handleSegmentMismatch", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "applied", "applyFlightData", "patchedTree", "handleMutable", "hmrRefreshReducerNoop", "_action", "process", "env", "NODE_ENV"], "mappings": ";;;;+BA6HaA;;;eAAAA;;;qCA7HuB;mCACF;6CACU;6CACA;iCAOV;+BACJ;iCACE;2BAEK;uCACC;mDACY;AAElD,wFAAwF;AACxF,SAASC,sBACPC,KAA2B,EAC3BC,MAAwB;IAExB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/BF,QAAQG,0BAA0B,GAAG;IAErC,MAAMC,QAAmBC,IAAAA,+BAAoB;IAC7C,sFAAsF;IACtF,sHAAsH;IACtH,MAAMC,iBAAiBC,IAAAA,oEAAiC,EAACV,MAAMW,IAAI;IAEnE,uDAAuD;IACvD,wCAAwC;IACxCJ,MAAMK,QAAQ,GAAGC,IAAAA,wCAAmB,EAAC,IAAIC,IAAIV,MAAMF,SAAS;QAC1Da,mBAAmB;YAACf,MAAMW,IAAI,CAAC,EAAE;YAAEX,MAAMW,IAAI,CAAC,EAAE;YAAEX,MAAMW,IAAI,CAAC,EAAE;YAAE;SAAU;QAC3EK,SAASP,iBAAiBT,MAAMgB,OAAO,GAAG;QAC1CC,cAAc;IAChB;IAEA,OAAOV,MAAMK,QAAQ,CAACM,IAAI,CACxB;YAAC,EAAEC,UAAU,EAAEd,cAAce,oBAAoB,EAAE;QACjD,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOE,IAAAA,kCAAiB,EACtBrB,OACAG,SACAgB,YACAnB,MAAMsB,OAAO,CAACC,WAAW;QAE7B;QAEA,+DAA+D;QAC/DhB,MAAMK,QAAQ,GAAG;QAEjB,IAAIY,cAAcxB,MAAMW,IAAI;QAC5B,IAAIc,eAAezB,MAAMO,KAAK;QAE9B,KAAK,MAAMmB,wBAAwBP,WAAY;YAC7C,MAAM,EAAER,MAAMgB,SAAS,EAAEC,YAAY,EAAE,GAAGF;YAC1C,IAAI,CAACE,cAAc;gBACjB,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAO9B;YACT;YAEA,MAAM+B,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJR,aACAG,WACA3B,MAAMK,YAAY;YAGpB,IAAI0B,YAAY,MAAM;gBACpB,OAAOE,IAAAA,4CAAqB,EAACjC,OAAOC,QAAQ0B;YAC9C;YAEA,IAAIO,IAAAA,wDAA2B,EAACV,aAAaO,UAAU;gBACrD,OAAOV,IAAAA,kCAAiB,EACtBrB,OACAG,SACAC,MACAJ,MAAMsB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMY,2BAA2Bf,uBAC7BgB,IAAAA,oCAAiB,EAAChB,wBAClBiB;YAEJ,IAAIjB,sBAAsB;gBACxBjB,QAAQE,YAAY,GAAG8B;YACzB;YACA,MAAMG,UAAUC,IAAAA,gCAAe,EAC7Bd,cACAlB,OACAmB;YAGF,IAAIY,SAAS;gBACXnC,QAAQI,KAAK,GAAGA;gBAChBkB,eAAelB;YACjB;YAEAJ,QAAQqC,WAAW,GAAGT;YACtB5B,QAAQE,YAAY,GAAGD;YAEvBoB,cAAcO;QAChB;QACA,OAAOU,IAAAA,4BAAa,EAACzC,OAAOG;IAC9B,GACA,IAAMH;AAEV;AAEA,SAAS0C,sBACP1C,KAA2B,EAC3B2C,OAAyB;IAEzB,OAAO3C;AACT;AAEO,MAAMF,oBACX8C,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrBJ,wBACA3C"}