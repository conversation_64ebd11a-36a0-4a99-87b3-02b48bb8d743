{"version": 3, "file": "app/login/page.js", "mappings": "qFAAA,6DCAA,mHEAA,0nCDWA,MACA,CACA,GACA,CACA,UACA,QACA,CACA,uBAAiC,EACjC,MAfA,IAAoB,sCAAkG,CAetH,iEAES,EACF,CACP,CAEA,UACA,sBAAoC,uCAAqP,aACzR,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QApCA,IAAsB,qCAA6F,CAoCnH,4DACA,aApCA,IAAsB,sCAAgF,CAoCtG,+CACA,WApCA,IAAsB,sCAAgF,CAoCtG,+CACA,cApCA,IAAsB,sCAAmF,CAoCzG,kDACA,UACA,sBAAoC,uCAAqP,aACzR,SACA,aACA,WACA,eACA,CACA,EACA,CAEA,oEAKO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAGO,MAAwB,oBAAkB,EACjD,YACA,KAAc,GAAS,UACvB,mBACA,kBAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,mCC5DD,iCAEA,gCALA,CAEA,CAGA,EAWA,gCACA,wBACA,oCACA,wDACA,gCAEA,+BACA,oDACA,MACI,QAA8B,EAClC,mBACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEL,IAAM,EAAS,OAAS,EACxB,UAAe,GAAU,KACzB,IAvBA,GAwBA,mBACA,OA9BA,KA+BA,OAAW,GACX,SA/BA,KAgCA,YA/BA,KAgCA,SAnCA,KAoCA,gBACA,YAAgB,KAChB,wBACA,0BACA,wBACA,cAlCA,OAmCA,6BA5BA,OA6BA,OAnCA,CAAoB,MAAQ,SAAW,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uDAA2D,0DAA8D,iBAAsB,gBAAkB,sEAAgF,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,+EAAmF,WAAa,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,ywBAA+5B,+CAAiD,kNAAqQ,qBAAyB,s+CAA0/C,wIAoCnxJ,oCACA,mBACA,wBAtDA,KAuDA,2BACA,CAAC,EACM,EAAqB,EACb,cACf,MAAW,OAAO,EAClB,KACA,IAJmC,YAIX,KACxB,QAAiB,CACjB,CAAK,CACL,kBC1EA,sCAAkJ,iBCAlJ,sCAAkJ,gICGlJ,IAAMA,EAAOC,EAAAA,UAAgB,CAG3B,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACC,MAAAA,CACCD,IAAKA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,2DACAJ,GAED,GAAGC,CAAK,IAGbH,EAAKO,WAAW,CAAG,OAEnB,IAAMC,EAAaP,EAAAA,UAAgB,CAGjC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACC,MAAAA,CACCD,IAAKA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAC9C,GAAGC,CAAK,IAGbK,EAAWD,WAAW,CAAG,aAEzB,IAAME,EAAYR,EAAAA,UAAgB,CAGhC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACM,KAAAA,CACCN,IAAKA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,qDACAJ,GAED,GAAGC,CAAK,IAGbM,EAAUF,WAAW,CAAG,YAExB,IAAMI,EAAkBV,EAAAA,UAAgB,CAGtC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACQ,IAAAA,CACCR,IAAKA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAC9C,GAAGC,CAAK,IAGbQ,EAAgBJ,WAAW,CAAG,kBAE9B,IAAMM,EAAcZ,EAAAA,UAAgB,CAGlC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACC,MAAAA,CAAID,IAAKA,EAAKF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,WAAYJ,GAAa,GAAGC,CAAK,GAEhEU,GAAYN,WAAW,CAAG,cAY1BO,EAVmBb,UAAgB,CAGjC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACC,MAAAA,CACCD,IAAKA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BJ,GAC3C,GAAGC,CAAK,IAGFI,WAAW,CAAG,gGC1DnB,EAAY,OAGZ,CAAC,EAAmB,EAAe,CAAI,OAAkB,CAAC,EAAW,CACzE,CADuC,CACvC,EAA2B,CAC5B,EACK,EAA2B,QAA2B,CAAC,EAWvD,CAAC,EAAc,EAAc,CAAI,EAAoC,GA6BrE,EAAa,IA7BgB,QA6BhB,CACjB,CAAC,EAA+B,KAC9B,GAAM,aACJ,EACA,MAAO,EACP,6BACA,cACA,EAAc,iBACd,iBACA,EAAiB,YACjB,GAAG,EACL,CAAI,EACE,EAAY,QAAY,CAAC,GAAG,CAC3B,EAAO,EAAQ,CAAI,KAAJ,CAAI,CAAoB,CAAC,CAC7C,KAAM,EACN,SAAU,EACV,YAAa,GAAgB,GAC7B,OAAQ,CACV,CAAC,EAED,MACE,UAAC,GACC,MAAO,EACP,OAAQ,OAAK,CAAC,QACd,EACA,cAAe,cACf,EACA,IAAK,EACL,iBAEA,mBAAC,IAAS,CAAC,IAAV,CACC,IAAK,EACL,mBAAkB,EACjB,GAAG,EACJ,IAAK,GACP,EAGN,GAGF,EAAK,YAAc,EAMnB,IAAM,EAAgB,WAOhB,EAAiB,aACrB,CAAC,EAAmC,KAClC,GAAM,aAAE,OAAa,GAAO,EAAM,GAAG,EAAU,CAAI,EAC7C,EAAU,EAAe,CADgB,CACD,GACxC,EAAwB,EAAyB,GACvD,CAFyD,KAGvD,EAFgE,CAEhE,OAAkB,KAAjB,CACC,SAAO,EACN,GAAG,EACJ,YAAa,EAAQ,YACrB,IAAK,EAAQ,SACb,EAEA,mBAAC,IAAS,CAAC,IAAV,CACC,KAAK,UACL,mBAAkB,EAAQ,YACzB,GAAG,EACJ,IAAK,GACP,EAGN,GAGF,EAAS,YAAc,EAMvB,IAAM,EAAe,cAQf,EAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,aAAE,QAAa,WAAO,GAAW,EAAO,GAAG,EAAa,CAAI,EAC5D,EAAU,EAAe,EAAc,EADiB,CAExD,EAAwB,EAAyB,GACjD,CAFkD,CAEtC,EAAc,EAAQ,EAD0B,IAC1B,CAAQ,GAC1C,EAD+C,EACrB,EAAQ,OAAQ,GAC1C,EAD+C,IACxB,EAAQ,MACrC,MACE,UAAkB,KAAjB,CACC,SAAO,EACN,GAAG,EACJ,UAAW,CAAC,EACZ,OAAQ,EAER,GJ7HI,MI6HJ,UAAC,IAAS,CAAC,OAAV,CACC,KAAK,SACL,KAAK,MACL,gBAAe,EACf,gBAAe,EACf,aAAY,EAAa,SAAW,WACpC,gBAAe,EAAW,GAAK,gBAC/B,EACA,GAAI,EACH,GAAG,EACJ,IAAK,EACL,YAAa,OAAoB,CAAC,EAAM,YAAa,IAG/C,GAA8B,IAAjB,EAAM,SAAkC,IAAlB,EAAM,CAAmB,MAAnB,CAI3C,EAAM,eAAe,EAHrB,EAAQ,cAAc,EAK1B,CAAC,EACD,UAAW,OAAoB,CAAC,EAAM,UAAW,IAC3C,CAAC,IAAK,OAAO,EAAE,SAAS,EAAM,GAAG,EAAG,GAAQ,cAAc,EAChE,CAAC,EADoE,QAE5D,OAAoB,CAAC,EAAM,QAAS,KAG3C,IAAM,EAAmD,WAA3B,EAAQ,eACjC,GAAe,IAAY,GAC9B,EAAQ,EADS,WACT,CAAc,EAE1B,CAAC,EAF8B,CAGjC,EAGN,GAGF,EAAY,YAAc,EAM1B,IAAM,EAAe,cAaf,EAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,aAAE,QAAa,aAAO,WAAY,EAAU,GAAG,EAAa,CAAI,EAChE,EAAU,EAAe,EAAc,EADqB,CAE5D,EAAY,EAAc,EAAQ,EADgB,IAChB,CAAQ,GAC1C,EAD+C,EACrB,EAAQ,OAAQ,GAC1C,EAD+C,IACxB,EAAQ,MAC/B,EAAqC,SAAO,GAOlD,OAP4D,EAEtD,UAAU,KACd,IAAM,EAAM,sBAAsB,IAAO,EAA6B,SAAU,GAChF,EADsF,IAC/E,IAAM,qBAAqB,EACpC,CADuC,CACpC,CAAC,CAAC,EAGH,UAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAC9B,UAAC,SAAE,EAAQ,GACV,UAAC,IAAS,CAAC,IAAV,CACC,aAAY,EAAa,SAAW,WACpC,mBAAkB,EAAQ,YAC1B,KAAK,WACL,kBAAiB,EACjB,OAAQ,CAAC,EACT,GAAI,EACJ,SAAU,EACT,GAAG,EACJ,IAAK,EACL,MAAO,CACL,GAAG,EAAM,MACT,kBAAmB,EAA6B,QAAU,KAAO,MACnE,EAEC,YAAW,GACd,CAEJ,CAEJ,GAOF,SAAS,EAAc,EAAgB,GAAe,MAC7C,GAAG,EAAM,WAAY,EAAK,EAGnC,CAHmC,QAG1B,EAAc,EAAgB,GAAe,MAC7C,GAAG,EAAM,WAAY,EAAK,EATnC,CASmC,CATvB,YAAc,ECnQ1B,IAAMQ,EAAWd,EAAAA,SAAHc,CAAmB,CAG/B,CAAC,WAAEb,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACY,EAAkB,CACjBZ,CADiB,GACZA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,6FACAJ,GAED,GAAGC,CAAK,IAGbY,EAASR,WAADQ,CAAeC,EAAmBT,EAAD,SAAY,CAErD,IAAMU,EAAchB,EAAAA,UAAgB,CAGlC,CAAC,WAAEC,CAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACY,EAAqB,CACpBZ,IAAKA,EACLF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,sYACAJ,GAED,GAAGC,CAAK,IAGbc,EAAYV,WAAW,CAAGS,EAAfC,KAAoC,MAAY,CAE3D,IAAMC,EAAcjB,EAAAA,UAAgB,CAGlC,CAAC,CAAEC,WAAS,CAAE,GAAGC,EAAO,CAAEC,IAC1B,UAACY,EAAqB,CACpBZ,IADoB,EAEpBF,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,kIACAJ,GAED,GAAGC,CAAK,IAGbe,EAAYX,WAAW,CAAGS,EAAfE,KAAoC,MAAY,eCtCrD,MAAW,YAAgB,CAAC,UAAY,EAC5C,CACE,OACA,CACE,CAAG,8KACH,GAAK,SACP,EACF,CACA,CAAC,SAAU,CAAE,GAAI,CAAQ,SAAI,CAAO,OAAG,IAAM,MAAM,cAAgB,KAAK,SAAU,EACnF,0BCOM,SAASC,IACd,GAAM,CAACC,EAAUC,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnC,CAACC,EAAUC,EAAY,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnC,CAACG,EAAiBC,EAAmB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjD,CAACK,EAASC,EAAW,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACO,EAAQC,EAAU,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAa,CAAC,GAC5C,OAAES,CAAK,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAQA,GAEpBC,EAAoB,KACxB,IAAMC,EAAwB,CAAC,EAM/B,OALKd,IAAUc,EAAUd,QAAQ,CAAG,UAC/BG,IAAUW,EAAUX,QAAQ,CAAG,SAChCH,EAASe,QAAQ,CAAC,MAAMD,GAAUd,QAAQ,CAAG,gBAC7CG,GAAYA,EAASa,MAAM,CAAG,IAAGF,EAAUX,QAAQ,CAAG,gBAC1DO,EAAUI,GAC+B,IAAlCG,OAAOC,IAAI,CAACJ,GAAWE,MAAM,EAGhCG,EAAuB,KAC3B,IAAML,EAAwB,CAAC,EAQ/B,OAPKd,IAAUc,EAAUd,QAAQ,CAAG,UAC/BG,GAAUW,GAAUX,QAAQ,CAAG,SAChCH,EAASe,QAAQ,CAAC,OAAMD,EAAUd,QAAQ,CAAG,gBAC7CG,GAAYA,EAASa,MAAM,CAAG,IAAGF,EAAUX,QAAQ,CAAG,gBACrDE,IAAiBS,EAAUT,eAAe,CAAG,SAC9CF,IAAaE,IAAiBS,EAAUT,eAAe,CAAG,cAC9DK,EAAUI,GAC+B,IAAlCG,OAAOC,IAAI,CAACJ,GAAWE,MAAM,EAGhCI,EAAc,UAClB,GAAKP,CAAD,IAEJL,GAAW,GACX,GAAI,CACF,IAAMa,EAJkB,MAIHC,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,CAAC,cAAe,UACzCtB,WACAG,EACAoB,SAAU,EACZ,GAEA,GAAIF,GAAQG,MAAO,CACjBb,EAAM,CACJc,MAAO,OACPC,YAAa,WACbC,QAAS,aACX,GACAnB,GAAW,GACX,MACF,CAEAoB,OAAOC,QAAQ,CAACC,IAAI,CAAG,GACzB,CAAE,MAAON,EAAO,CACdb,EAAM,CACJc,MAAO,OACPC,YAAaF,aAAiBO,MAAQP,EAAMQ,OAAO,CAAG,QACtDL,QAAS,aACX,GACAnB,GAAW,EACb,EACF,EAEMyB,EAAiB,UACrB,GAAKd,CAAD,IAEJX,GAAW,GACX,GAAI,CACF,IAAM0B,EAAW,GAJU,GAIJC,MAAM,qBAAsB,CACjDC,OAAQ,OACRC,QAAS,CAAE,eAAgB,kBAAmB,EAC9CC,KAAMC,KAAKC,SAAS,CAAC,UAAExC,EAAUG,UAAS,EAC5C,GAEMsC,EAAO,MAAMP,EAASQ,IAAI,GAEhC,GAAI,CAACR,EAASS,EAAE,CAAE,CAChBhC,EAAM,CACJc,MAAO,OACPC,YAAae,EAAKjB,KAAK,EAAI,QAC3BG,QAAS,aACX,GACAnB,GAAW,GACX,MACF,CAGA,IAAMa,EAAS,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,CAAC,cAAe,UACzCtB,WACAG,EACAoB,UAAU,CACZ,GAEA,GAAIF,GAAQG,MAAO,CACjBb,EAAM,CACJc,MAAO,OACPC,YAAa,eACbC,QAAS,aACX,GACAnB,GAAW,GACX,MACF,CAEAoB,OAAOC,QAAQ,CAACC,IAAI,CAAG,GACzB,CAAE,MAAON,EAAO,CACdb,EAAM,CACJc,MAAO,OACPC,YAAaF,aAAiBO,MAAQP,EAAMQ,OAAO,CAAG,QACtDL,QAAS,aACX,GACAnB,GAAW,EACb,EACF,EAaA,MACE,WAAC5B,EAAIA,CAACE,CAADF,SAAW,wDACd,WAACQ,EAAUA,CAACN,OAADM,GAAW,sBACpB,UAACC,EAASA,CAACP,MAADO,IAAW,0GAAiG,iBAGtH,UAACE,EAAeA,CAACT,UAAU,EAAXS,qBAAyB,yBAI3C,UAACE,EAAWA,CAACX,QAADW,EAAW,gBACrB,WAACmD,EAAIA,CAACC,MAADD,OAAc,QAAQ9D,UAAU,SAASgE,cAlBlC,CAkBiDC,IAjBjE9C,EAAY,IACZG,EAAY,IACZE,EAAmB,IACnBI,EAAU,CAAC,EACb,YAcQ,WAACf,EAAQA,CAACb,UAADa,yCACP,UAACE,EAAWA,CAACmD,MAAM,OAAPnD,UAAe,OAC3B,UAACA,EAAWA,CAACmD,MAAM,OAAPnD,aAAkB,UAEhC,WAACZ,MAAAA,CAAIH,UAAU,0BACb,WAACgB,EAAWA,CAACkD,MAAM,OAAPlD,CAAehB,UAAU,2BACnC,WAACG,MAAAA,CAAIH,UAAU,sBACb,WAACG,MAAAA,CAAIH,UAAU,wBACb,WAACG,MAAAA,CAAIH,UAAU,qBACb,UAACG,MAAAA,CAAIH,UAAU,yDACb,IPvGC,COuGD,KAACmE,EAAAA,CAAKA,CAAAA,CAACnE,UAAU,cAEnB,UAACoE,EAAAA,CAAKA,CAAAA,CACJpE,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gBACAuB,EAAOT,QAAQ,EAAI,qDAErBmD,YAAY,MACZH,MAAOhD,EACPoD,SAAU,IACRnD,EAAYoD,EAAEC,MAAM,CAACN,KAAK,EAC1BtC,EAAU,CAAC,EACb,EACA6C,SAAUhD,OAGbE,EAAOT,QAAQ,EACd,UAACR,IAAAA,CAAEV,UAAU,oCAA4B2B,EAAOT,QAAQ,MAG5D,WAACf,MAAAA,CAAIH,UAAU,wBACb,WAACG,MAAAA,CAAIH,UAAU,qBACb,UAACG,MAAAA,CAAIH,UAAU,yDACb,UAAC0E,EAAQA,CAAC1E,KAAD0E,KAAW,cAEtB,UAACN,EAAAA,CAAKA,CAAAA,CACJpE,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gBACAuB,EAAON,QAAQ,EAAI,qDAErBsD,KAAK,WACLN,YAAY,KACZH,MAAO7C,EACPiD,SAAU,IACRhD,EAAYiD,EAAEC,MAAM,CAACN,KAAK,EAC1BtC,EAAU,CAAC,EACb,EACA6C,SAAUhD,OAGbE,EAAON,QAAQ,EACd,UAACX,IAAAA,CAAEV,UAAU,oCAA4B2B,EAAON,QAAQ,SAK9D,WAAClB,MAAAA,CAAIH,UAAU,2BACb,WAAC4E,EAAAA,CAAMA,CAAAA,CACL5E,UAAU,SACV6E,QAASvC,EACTmC,SAAUhD,YAETA,GAAW,UAACqD,EAAAA,CAAOA,CAAAA,CAAC9E,UAAU,8BAA+B,QAIhE,WAACG,MAAAA,CAAIH,UAAU,qBACb,UAACG,MAAAA,CAAIH,UAAU,8CACb,UAAC+E,OAAAA,CAAK/E,UAAU,sBAElB,UAACG,MAAAA,CAAIH,UAAU,0DACb,UAAC+E,OAAAA,CAAK/E,UAAU,oDAA2C,YAM/D,WAAC4E,EAAAA,CAAMA,CAAAA,CACL/B,QAAQ,UACR7C,UAAU,SACV6E,QAvGU,CAuGDG,IAtGvBxC,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,CAAC,SAAU,CAAEyC,YAAa,GAAI,EACtC,YAuGgB,UAACC,EAAAA,CAAMA,CAAAA,CAAClF,UAAU,iBAAiB,0BAKzC,WAACgB,EAAWA,CAACkD,MAAM,OAAPlD,IAAkBhB,UAAU,2BACtC,WAACG,MAAAA,CAAIH,UAAU,sBACb,WAACG,MAAAA,CAAIH,UAAU,wBACb,WAACG,MAAAA,CAAIH,UAAU,qBACb,UAACG,MAAAA,CAAIH,UAAU,yDACb,UAACmE,EAAAA,CAAKA,CAAAA,CAACnE,UAAU,cAEnB,UAACoE,EAAAA,CAAKA,CAAAA,CACJpE,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gBACAuB,EAAOT,QAAQ,EAAI,qDAErBmD,YAAY,MACZH,MAAOhD,EACPoD,SAAU,IACRnD,EAAYoD,EAAEC,MAAM,CAACN,KAAK,EAC1BtC,EAAU,CAAC,EACb,EACA6C,SAAUhD,OAGbE,EAAOT,QAAQ,EACd,UAACR,IAAAA,CAAEV,UAAU,oCAA4B2B,EAAOT,QAAQ,MAG5D,WAACf,MAAAA,CAAIH,UAAU,wBACb,WAACG,MAAAA,CAAIH,UAAU,qBACb,UAACG,MAAAA,CAAIH,UAAU,yDACb,UAAC0E,EAAQA,CAAC1E,KAAD0E,KAAW,cAEtB,UAACN,EAAAA,CAAKA,CAAAA,CACJpE,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gBACAuB,EAAON,QAAQ,EAAI,qDAErBsD,KAAK,WACLN,YAAY,KACZH,MAAO7C,EACPiD,SAAU,IACRhD,EAAYiD,EAAEC,MAAM,CAACN,KAAK,EAC1BtC,EAAU,CAAC,EACb,EACA6C,SAAUhD,OAGbE,EAAON,QAAQ,EACd,UAACX,IAAAA,CAAEV,UAAU,oCAA4B2B,EAAON,QAAQ,MAG5D,WAAClB,MAAAA,CAAIH,UAAU,wBACb,WAACG,MAAAA,CAAIH,UAAU,qBACb,UAACG,MAAAA,CAAIH,UAAU,yDACb,UAAC0E,EAAQA,CAAC1E,KAAD0E,KAAW,cAEtB,UAACN,EAAAA,CAAKA,CAAAA,CACJpE,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gBACAuB,EAAOJ,eAAe,EAAI,qDAE5BoD,KAAK,WACLN,YAAY,OACZH,MAAO3C,EACP+C,SAAU,IACR9C,EAAmB+C,EAAEC,MAAM,CAACN,KAAK,EACjCtC,EAAU,CAAC,EACb,EACA6C,SAAUhD,OAGbE,EAAOJ,eAAe,EACrB,UAACb,IAAAA,CAAEV,UAAU,oCAA4B2B,EAAOJ,eAAe,SAKrE,UAACpB,MAAAA,CAAIH,UAAU,0BACb,WAAC4E,EAAAA,CAAMA,CAAAA,CACL5E,UAAU,SACV6E,QAAS1B,EACTsB,SAAUhD,YAETA,GAAW,UAACqD,EAAAA,CAAOA,CAAAA,CAAC9E,UAAU,8BAA+B,uBAUhF,kFC/UA,IAAMoE,EAAQrE,EAAAA,UAAgB,CAC5B,CAAC,WAAEC,CAAS,MAAE2E,CAAI,CAAE,GAAG1E,EAAO,CAAEC,IAE5B,UAACiF,QAAAA,CACCR,KAAMA,EACN3E,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,wUACAJ,GAEFE,IAAKA,EACJ,GAAGD,CAAK,IAKjBmE,EAAM/D,WAAW,CAAG,sDCVd,MAAe,YAAgB,CAAC,cAAgB,EACpD,CAAC,MAAQ,EAAE,EAAG,CAA+B,iCAAK,SAAU,EAC7D,gDCFK,MAAY,YAAgB,CAAC,WAAa,EAC9C,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,MAAK,CAAG,KAAK,GAAK,UAAU,EACvD,CAAC,MAAQ,EAAE,EAAG,CAAyB,2BAAK,SAAU,EACvD,whBCZM,IAAM+E,EAAU,OAAM,eAECC,IAC5B,IAAMC,EAAU,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,GAM1B,OAJID,GAASE,MAAM,CACjB/C,EAAAA,EAAAA,EAAAA,CAAQA,CAAC,KAIT,UAACtC,MAAAA,CAAIH,UAAU,uIACb,UAACiB,EAAAA,SAASA,CAAAA,CAAAA,IAGhB,uECfA,MAAuB,eAAmB,SAK1C,cACA,MAAoB,YAAgB,IACpC,kBACA,mLCEM,EAAc,gCACd,EAAgB,CAAE,SAAS,EAAO,YAAY,CAAK,EAMnD,EAAa,mBAGb,CAAC,EAAY,EAAe,EAAqB,CAAI,OAAgB,CAGzE,GAGI,CAAC,EAA+B,EAA2B,CAAI,CAHzD,EAGyD,IAAkB,CACrF,EACA,CAAC,EAAqB,EA+BlB,CAAC,EAAqB,EAAqB,CAC/C,EAAkD,CAlCa,EAuC3D,EAAyB,IArCP,CAgCsC,MADb,CAMlB,CAC7B,CAAC,EAA2C,IAExC,UAAC,EAAW,SAAX,CAAoB,MAAO,EAAM,wBAChC,mBAAC,EAAW,KAAX,CAAgB,MAAO,EAAM,wBAC5B,mBAAC,GAAsB,GAAG,EAAO,IAAK,EAAc,EACtD,EACF,GAKN,EAAiB,YAAc,EAgB/B,IAAM,EAA6B,aAGjC,CAAC,EAA+C,KAChD,GAAM,yBACJ,cACA,OACA,GAAO,MACP,EACA,iBAAkB,0BAClB,2BACA,eACA,4BACA,GAA4B,EAC5B,GAAG,EACL,CAAI,EACE,EAAY,SAAoC,IAAI,EACpD,EAAe,OAAe,CAAC,EAAc,GAAG,EACpC,QAAY,CAAC,GACzB,CAAC,EAAkB,EAAmB,CAAI,OAAoB,CAAC,CACnE,KAAM,EADoC,YAE7B,GAA2B,KACxC,SAAU,EACV,OAAQ,CACV,CAAC,EACK,CAAC,EAAkB,EAAmB,CAAU,YAAS,GACzD,CADsC,CAAwB,CAC3C,MAAc,CAAC,GAClC,EAAW,EAAc,GACzB,EAAwB,SAAO,IAC/B,CADoC,EACd,EAF0B,CAEM,WAAS,CAAC,EAUtE,KAVkD,EAE5C,YAAU,KACd,IAAM,EAAO,EAAI,QACjB,GAAI,EAEF,IAFQ,GACR,EAAK,iBAAiB,EAAa,GAC5B,IAAM,EAAK,OADiC,YACjC,CAAoB,EAAa,EAEvD,EAAG,CAAC,EAAiB,EAGnB,OALqE,CAKrE,EAAC,GACC,MAAO,cACP,EACA,IAAK,OACL,mBACA,EACA,YAAmB,cACjB,GAAe,EAAoB,GACnC,CAAC,EAAmB,EAEtB,CAH8C,cAGxB,cAAY,IAAM,GAAoB,GAAO,CAAH,CAAK,EACrE,mBAA0B,cACxB,IAAM,EAAuB,GAAe,EAAY,CAAC,EACzD,CAAC,GAEH,sBAA6B,cAC3B,IAAM,EAAuB,GAAe,EAAY,CAAC,EACzD,CAAC,GAGH,mBAAC,IAAS,CAAC,IAAV,CACC,SAAU,GAA4C,IAAxB,EAA4B,GAAK,EAC/D,mBAAkB,EACjB,GAAG,EACJ,IAAK,EACL,MAAO,CAAE,QAAS,OAAQ,GAAG,EAAM,OACnC,YAAa,OAAoB,CAAC,EAAM,YAAa,KACnD,EAAgB,SAAU,CAC5B,CAAC,EACD,QAAS,OAAoB,CAAC,EAAM,QAAS,IAK3C,IAAM,EAAkB,CAAC,EAAgB,QAEzC,GAAI,EAAM,SAAW,EAAM,eAAiB,GAAmB,CAAC,EAAkB,CAChF,IAAM,EAAkB,IAAI,YAAY,EAAa,GAGrD,GAFA,EAAM,KAD4D,QAC5D,CAAc,cAAc,GAE9B,CAAC,EAAgB,SAF4B,OAE5B,CAAkB,CACrC,IAAM,EAAQ,IAAW,KAAF,CAAE,CAAO,GAAU,EAAK,SAAS,EAOxD,EAJuB,CAFJ,EAAM,KAAK,CAMnB,EAN6B,EAAK,MAAM,EAC/B,EAAM,KAAK,GAAU,EAAK,KAAO,MACD,EAAK,CAAE,EAAF,IAAE,CADU,SAI/B,IAAI,GAAU,EAAK,IAAI,OAAQ,EAC1C,EAC7B,CACF,CAEA,EAAgB,SAAU,CAC5B,CAAC,EACD,MAN0D,CAMlD,OAAoB,CAAC,EAAM,OAAQ,IAAM,EAAoB,IAAM,CAAD,CAAC,EAInF,CAAC,EAMK,EAAY,uBAaZ,EAA6B,aACjC,CAAC,EAA0C,KACzC,GAAM,yBACJ,YACA,GAAY,SACZ,GAAS,EACT,qBACA,EACA,GAAG,EACL,CAAI,EACE,EAAS,OAAK,CAAC,EACf,EAAK,GAAa,EAClB,EAAU,EAAsB,EAAW,GAC3C,EAAmB,EAAQ,gBADuC,GAClB,EAChD,EAAW,EAAc,GAEzB,oBAFgD,CAE9C,uBAAoB,mBAAuB,EAAiB,CAAI,EASxE,OAPM,YAAU,KACd,GAAI,EAEF,OADA,EADa,EAEN,IAAM,GAEjB,EAAG,CAAC,EAAW,EAAoB,CAHZ,CAGkC,EAGvD,QALqC,EAKpC,EAAW,KAH0C,GAG1C,CAAX,CACC,MAAO,KACP,EACA,YACA,SAEA,mBAAC,IAAS,CAAC,KAAV,CACC,SAAU,EAAmB,EAAI,GACjC,mBAAkB,EAAQ,YACzB,GAAG,EACJ,IAAK,EACL,YAAa,OAAoB,CAAC,EAAM,YAAa,IAG9C,EAEA,EAAQ,MAFG,KAEH,CAAY,EAAE,CAFX,EAAM,eAAe,CAGvC,CAAC,EACD,QAAS,OAAoB,CAAC,EAAM,QAAS,IAAM,EAAQ,YAAY,EAAE,CAAC,CAC1E,UAAW,OAAoB,CAAC,EAAM,UAAY,IAChD,GAAkB,QAAd,EAAM,KAAiB,EAAM,SAAU,CACzC,EAAQ,eAAe,EACvB,MACF,CAEA,GAAI,EAAM,SAAW,EAAM,cAAe,OAE1C,IAAM,EAAc,SAqDvB,CAAe,CAA4B,EAA2B,GAAiB,IAPlE,EAQ5B,GARyC,CAQnC,IARoD,CAQzB,CAArB,CAA2B,IAPvC,MAAmB,EAOyB,EAPlB,CAOqB,CANhC,cAAR,EAAsB,aAAuB,eAAR,EAAuB,YAAc,GAOjF,KAAoB,aAAhB,GAA8B,CAAC,YAAa,YAAY,EAAE,SAAS,EAAG,EAAG,GACzD,KADgE,UAChF,GAAgC,CAAC,UAAW,WAAW,EAAE,SAAS,EAAG,EAAG,OACrE,CAD4E,CACpD,EACjC,CADoC,CAzDW,EAAO,EAAQ,YAAa,EAAQ,GAAG,EAE1E,GAAI,KAAgB,MAAW,CAC7B,GAAI,EAAM,SAAW,EAAM,SAAW,EAAM,QAAU,EAAM,SAAU,OACtE,EAAM,eAAe,EAErB,IAAI,EAAiB,IADI,KAAF,CAAE,CAAO,GAAU,EAAK,SAAS,EAC7B,IAAI,GAAU,EAAK,IAAI,OAAQ,EAE1D,GAAoB,OAAQ,EAAxB,EAAwB,EAAe,QAAQ,UAC1B,SAAhB,GAA0C,SAAhB,EAAwB,CACrC,OAAQ,EAAxB,GAAwB,EAAe,QAAQ,EACnD,IAAM,EAAe,EAAe,QAAQ,EAAM,aAAa,EAC/D,EAAiB,EAAQ,KACrB,SA6DX,CAAa,CAAY,GAAoB,OAC7C,EAAM,IAAO,CAAC,EAAG,IAAU,EAAO,GAAa,GAAS,EAAM,MAAM,CAAE,CAC/E,EA/D8B,EAAgB,EAAe,CAAC,EAC1C,EAAe,MAAM,EAAe,CAAC,CAC3C,CAMA,WAAW,IAAM,EAAW,GAC9B,CACF,CAAC,EAEA,OAJ6C,CAAC,CAI9C,mBAAO,EACJ,EAAS,kBAAE,EAAkB,WAAgC,MAApB,CAAyB,CAAC,EACnE,GACN,EAGN,GAGF,EAAqB,YAAc,EAKnC,IAAM,EAAuD,CAC3D,UAAW,OAAQ,QAAS,OAC5B,WAAY,OAAQ,UAAW,OAC/B,OAAQ,QAAS,KAAM,QACvB,SAAU,OAAQ,IAAK,MACzB,EAgBA,SAAS,EAAW,EAA2B,GAAgB,GAAO,IAC9D,EAA6B,SAAS,cAC5C,QAAW,KAAa,EAEtB,GAAI,IAAc,EAFgB,EAGlC,EAAU,MAAM,eAD8B,CACd,CAAC,EAC7B,SAAS,gBAAkB,GAFe,MAIlD,CAUA,IAAM,EAAO,EACP,EAAO,OAbkD", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./app/login/page.tsx?34dd", "webpack://_N_E/|ssr?5401", "webpack://_N_E/?0883", "webpack://_N_E/?3ba2", "webpack://_N_E/./app/components/ui/card.tsx", "webpack://_N_E/../src/tabs.tsx", "webpack://_N_E/./app/components/ui/tabs.tsx", "webpack://_N_E/../../../src/icons/key-round.ts", "webpack://_N_E/./app/components/auth/login-form.tsx", "webpack://_N_E/./app/components/ui/input.tsx", "webpack://_N_E/../../../src/icons/loader-circle.ts", "webpack://_N_E/../../../src/icons/user-round.ts", "webpack://_N_E/./app/login/page.tsx", "webpack://_N_E/./node_modules/.pnpm/@radix-ui+react-direction@1_3592e6c0211cc9386e16e527fb15e091/node_modules/@radix-ui/react-direction/dist/index.mjs", "webpack://_N_E/../src/roving-focus-group.tsx"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/not-found-error\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page4 = () => import(/* webpackMode: \"eager\" */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\login\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\";\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\login\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\";\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\";\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "var _self___RSC_MANIFEST;\nimport \"next/dist/server/web/globals\";\nimport { adapter } from \"next/dist/server/web/adapter\";\nimport { getRender } from \"next/dist/build/webpack/loaders/next-edge-ssr-loader/render\";\nimport { IncrementalCache } from \"next/dist/server/lib/incremental-cache\";\nimport { renderToHTMLOrFlight as renderToHTML } from \"next/dist/server/app-render/app-render\";\nimport * as pageMod from \"next-app-loader?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=F%3A%5CCODE%5CProject%5CMail%5Cmoemail_source%5Capp&appPaths=%2Flogin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/login/page.tsx?__next_edge_ssr_entry__\";\nimport { PAGE_TYPES } from \"next/dist/lib/page-types\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nconst incrementalCacheHandler = null\nconst cacheHandlers = {\n\n};\nif (!globalThis.__nextCacheHandlers) {\n    ;\n    globalThis.__nextCacheHandlers = cacheHandlers;\n}\nconst Document = null;\nconst appMod = null;\nconst errorMod = null;\nconst error500Mod = null;\nconst sriEnabled = false\nconst isServerComponent = true\nconst dev = false\nconst serverActions = undefined\nconst nextConfig = {\"env\":{},\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"avatars.githubusercontent.com\"}],\"unoptimized\":false},\"devIndicators\":{\"appIsrStatus\":true,\"buildActivity\":true,\"buildActivityPosition\":\"bottom-right\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\",\"experimental\":{\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":11,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"turbo\":{\"root\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\"},\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"reactOwnerStack\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"bundlePagesRouterDependencies\":false,\"configFile\":\"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\next.config.ts\",\"configFileName\":\"next.config.ts\"}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst buildManifest = self.__BUILD_MANIFEST;\nconst reactLoadableManifest = maybeJSONParse(self.__REACT_LOADABLE_MANIFEST);\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/login/page\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nconst subresourceIntegrityManifest = sriEnabled ? maybeJSONParse(self.__SUBRESOURCE_INTEGRITY_MANIFEST) : undefined;\nconst nextFontManifest = maybeJSONParse(self.__NEXT_FONT_MANIFEST);\nconst interceptionRouteRewrites = maybeJSONParse(self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST) ?? [];\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/login/page\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nconst render = getRender({\n    pagesType: PAGE_TYPES.APP,\n    dev,\n    page: \"/login/page\",\n    appMod,\n    pageMod,\n    errorMod,\n    error500Mod,\n    Document,\n    buildManifest,\n    renderToHTML,\n    reactLoadableManifest,\n    clientReferenceManifest: isServerComponent ? rscManifest : null,\n    serverActionsManifest: isServerComponent ? rscServerManifest : null,\n    serverActions: isServerComponent ? serverActions : undefined,\n    subresourceIntegrityManifest,\n    config: nextConfig,\n    buildId: process.env.__NEXT_BUILD_ID,\n    nextFontManifest,\n    incrementalCacheHandler,\n    interceptionRouteRewrites\n});\nexport const ComponentMod = pageMod;\nexport default function nHandler(opts) {\n    return adapter({\n        ...opts,\n        IncrementalCache,\n        handler: render\n    });\n}\n\n//# sourceMappingURL=edge-ssr-app.js.map", "import(/* webpackMode: \"eager\", webpackExports: [\"LoginForm\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\auth\\\\login-form.tsx\");\n", "import(/* webpackMode: \"eager\", webpackExports: [\"LoginForm\"] */ \"F:\\\\CODE\\\\Project\\\\Mail\\\\moemail_source\\\\app\\\\components\\\\auth\\\\login-form.tsx\");\n", "import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent } ", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ComponentRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: TABS_NAME,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent } ", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name KeyRound\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi41ODYgMTcuNDE0QTIgMiAwIDAgMCAyIDE4LjgyOFYyMWExIDEgMCAwIDAgMSAxaDNhMSAxIDAgMCAwIDEtMXYtMWExIDEgMCAwIDEgMS0xaDFhMSAxIDAgMCAwIDEtMXYtMWExIDEgMCAwIDEgMS0xaC4xNzJhMiAyIDAgMCAwIDEuNDE0LS41ODZsLjgxNC0uODE0YTYuNSA2LjUgMCAxIDAtNC00eiIgLz4KICA8Y2lyY2xlIGN4PSIxNi41IiBjeT0iNy41IiByPSIuNSIgZmlsbD0iY3VycmVudENvbG9yIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/key-round\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst KeyRound = createLucideIcon('KeyRound', [\n  [\n    'path',\n    {\n      d: 'M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z',\n      key: '1s6t7t',\n    },\n  ],\n  ['circle', { cx: '16.5', cy: '7.5', r: '.5', fill: 'currentColor', key: 'w0ekpg' }],\n]);\n\nexport default KeyRound;\n", "\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { signIn } from \"next-auth/react\"\r\nimport { useToast } from \"@/components/ui/use-toast\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\"\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>bsContent,\r\n  TabsList,\r\n  TabsTrigger,\r\n} from \"@/components/ui/tabs\"\r\nimport { Github, Loader2, KeyRound, User2 } from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\ninterface FormErrors {\r\n  username?: string\r\n  password?: string\r\n  confirmPassword?: string\r\n}\r\n\r\nexport function LoginForm() {\r\n  const [username, setUsername] = useState(\"\")\r\n  const [password, setPassword] = useState(\"\")\r\n  const [confirmPassword, setConfirmPassword] = useState(\"\")\r\n  const [loading, setLoading] = useState(false)\r\n  const [errors, setErrors] = useState<FormErrors>({})\r\n  const { toast } = useToast()\r\n\r\n  const validateLoginForm = () => {\r\n    const newErrors: FormErrors = {}\r\n    if (!username) newErrors.username = \"请输入用户名\"\r\n    if (!password) newErrors.password = \"请输入密码\"\r\n    if (username.includes('@')) newErrors.username = \"用户名不能包含 @ 符号\"\r\n    if (password && password.length < 8) newErrors.password = \"密码长度必须大于等于8位\"\r\n    setErrors(newErrors)\r\n    return Object.keys(newErrors).length === 0\r\n  }\r\n\r\n  const validateRegisterForm = () => {\r\n    const newErrors: FormErrors = {}\r\n    if (!username) newErrors.username = \"请输入用户名\"\r\n    if (!password) newErrors.password = \"请输入密码\"\r\n    if (username.includes('@')) newErrors.username = \"用户名不能包含 @ 符号\"\r\n    if (password && password.length < 8) newErrors.password = \"密码长度必须大于等于8位\"\r\n    if (!confirmPassword) newErrors.confirmPassword = \"请确认密码\"\r\n    if (password !== confirmPassword) newErrors.confirmPassword = \"两次输入的密码不一致\"\r\n    setErrors(newErrors)\r\n    return Object.keys(newErrors).length === 0\r\n  }\r\n\r\n  const handleLogin = async () => {\r\n    if (!validateLoginForm()) return\r\n\r\n    setLoading(true)\r\n    try {\r\n      const result = await signIn(\"credentials\", {\r\n        username,\r\n        password,\r\n        redirect: false,\r\n      })\r\n\r\n      if (result?.error) {\r\n        toast({\r\n          title: \"登录失败\",\r\n          description: \"用户名或密码错误\",\r\n          variant: \"destructive\",\r\n        })\r\n        setLoading(false)\r\n        return\r\n      }\r\n\r\n      window.location.href = \"/\"\r\n    } catch (error) {\r\n      toast({\r\n        title: \"登录失败\",\r\n        description: error instanceof Error ? error.message : \"请稍后重试\",\r\n        variant: \"destructive\",\r\n      })\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleRegister = async () => {\r\n    if (!validateRegisterForm()) return\r\n\r\n    setLoading(true)\r\n    try {\r\n      const response = await fetch(\"/api/auth/register\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ username, password }),\r\n      })\r\n\r\n      const data = await response.json() as { error?: string }\r\n\r\n      if (!response.ok) {\r\n        toast({\r\n          title: \"注册失败\",\r\n          description: data.error || \"请稍后重试\",\r\n          variant: \"destructive\",\r\n        })\r\n        setLoading(false)\r\n        return\r\n      }\r\n\r\n      // 注册成功后自动登录\r\n      const result = await signIn(\"credentials\", {\r\n        username,\r\n        password,\r\n        redirect: false,\r\n      })\r\n\r\n      if (result?.error) {\r\n        toast({\r\n          title: \"登录失败\",\r\n          description: \"自动登录失败，请手动登录\",\r\n          variant: \"destructive\",\r\n        })\r\n        setLoading(false)\r\n        return\r\n      }\r\n\r\n      window.location.href = \"/\"\r\n    } catch (error) {\r\n      toast({\r\n        title: \"注册失败\",\r\n        description: error instanceof Error ? error.message : \"请稍后重试\",\r\n        variant: \"destructive\",\r\n      })\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const handleGithubLogin = () => {\r\n    signIn(\"github\", { callbackUrl: \"/\" })\r\n  }\r\n\r\n  const clearForm = () => {\r\n    setUsername(\"\")\r\n    setPassword(\"\")\r\n    setConfirmPassword(\"\")\r\n    setErrors({})\r\n  }\r\n\r\n  return (\r\n    <Card className=\"w-[95%] max-w-lg border-2 border-primary/20\">\r\n      <CardHeader className=\"space-y-2\">\r\n        <CardTitle className=\"text-2xl text-center bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent\">\r\n          欢迎使用 MoeMail\r\n        </CardTitle>\r\n        <CardDescription className=\"text-center\">\r\n          萌萌哒临时邮箱服务 (。・∀・)ノ\r\n        </CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"px-6\">\r\n        <Tabs defaultValue=\"login\" className=\"w-full\" onValueChange={clearForm}>\r\n          <TabsList className=\"grid w-full grid-cols-2 mb-6\">\r\n            <TabsTrigger value=\"login\">登录</TabsTrigger>\r\n            <TabsTrigger value=\"register\">注册</TabsTrigger>\r\n          </TabsList>\r\n          <div className=\"min-h-[220px]\">\r\n            <TabsContent value=\"login\" className=\"space-y-4 mt-0\">\r\n              <div className=\"space-y-3\">\r\n                <div className=\"space-y-1.5\">\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute left-2.5 top-2 text-muted-foreground\">\r\n                      <User2 className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <Input\r\n                      className={cn(\r\n                        \"h-9 pl-9 pr-3\",\r\n                        errors.username && \"border-destructive focus-visible:ring-destructive\"\r\n                      )}\r\n                      placeholder=\"用户名\"\r\n                      value={username}\r\n                      onChange={(e) => {\r\n                        setUsername(e.target.value)\r\n                        setErrors({})\r\n                      }}\r\n                      disabled={loading}\r\n                    />\r\n                  </div>\r\n                  {errors.username && (\r\n                    <p className=\"text-xs text-destructive\">{errors.username}</p>\r\n                  )}\r\n                </div>\r\n                <div className=\"space-y-1.5\">\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute left-2.5 top-2 text-muted-foreground\">\r\n                      <KeyRound className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <Input\r\n                      className={cn(\r\n                        \"h-9 pl-9 pr-3\",\r\n                        errors.password && \"border-destructive focus-visible:ring-destructive\"\r\n                      )}\r\n                      type=\"password\"\r\n                      placeholder=\"密码\"\r\n                      value={password}\r\n                      onChange={(e) => {\r\n                        setPassword(e.target.value)\r\n                        setErrors({})\r\n                      }}\r\n                      disabled={loading}\r\n                    />\r\n                  </div>\r\n                  {errors.password && (\r\n                    <p className=\"text-xs text-destructive\">{errors.password}</p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-3 pt-1\">\r\n                <Button\r\n                  className=\"w-full\"\r\n                  onClick={handleLogin}\r\n                  disabled={loading}\r\n                >\r\n                  {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n                  登录\r\n                </Button>\r\n\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-0 flex items-center\">\r\n                    <span className=\"w-full border-t\" />\r\n                  </div>\r\n                  <div className=\"relative flex justify-center text-xs uppercase\">\r\n                    <span className=\"bg-background px-2 text-muted-foreground\">\r\n                      或者\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"w-full\"\r\n                  onClick={handleGithubLogin}\r\n                >\r\n                  <Github className=\"mr-2 h-4 w-4\" />\r\n                  使用 GitHub 账号登录\r\n                </Button>\r\n              </div>\r\n            </TabsContent>\r\n            <TabsContent value=\"register\" className=\"space-y-4 mt-0\">\r\n              <div className=\"space-y-3\">\r\n                <div className=\"space-y-1.5\">\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute left-2.5 top-2 text-muted-foreground\">\r\n                      <User2 className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <Input\r\n                      className={cn(\r\n                        \"h-9 pl-9 pr-3\",\r\n                        errors.username && \"border-destructive focus-visible:ring-destructive\"\r\n                      )}\r\n                      placeholder=\"用户名\"\r\n                      value={username}\r\n                      onChange={(e) => {\r\n                        setUsername(e.target.value)\r\n                        setErrors({})\r\n                      }}\r\n                      disabled={loading}\r\n                    />\r\n                  </div>\r\n                  {errors.username && (\r\n                    <p className=\"text-xs text-destructive\">{errors.username}</p>\r\n                  )}\r\n                </div>\r\n                <div className=\"space-y-1.5\">\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute left-2.5 top-2 text-muted-foreground\">\r\n                      <KeyRound className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <Input\r\n                      className={cn(\r\n                        \"h-9 pl-9 pr-3\",\r\n                        errors.password && \"border-destructive focus-visible:ring-destructive\"\r\n                      )}\r\n                      type=\"password\"\r\n                      placeholder=\"密码\"\r\n                      value={password}\r\n                      onChange={(e) => {\r\n                        setPassword(e.target.value)\r\n                        setErrors({})\r\n                      }}\r\n                      disabled={loading}\r\n                    />\r\n                  </div>\r\n                  {errors.password && (\r\n                    <p className=\"text-xs text-destructive\">{errors.password}</p>\r\n                  )}\r\n                </div>\r\n                <div className=\"space-y-1.5\">\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute left-2.5 top-2 text-muted-foreground\">\r\n                      <KeyRound className=\"h-5 w-5\" />\r\n                    </div>\r\n                    <Input\r\n                      className={cn(\r\n                        \"h-9 pl-9 pr-3\",\r\n                        errors.confirmPassword && \"border-destructive focus-visible:ring-destructive\"\r\n                      )}\r\n                      type=\"password\"\r\n                      placeholder=\"确认密码\"\r\n                      value={confirmPassword}\r\n                      onChange={(e) => {\r\n                        setConfirmPassword(e.target.value)\r\n                        setErrors({})\r\n                      }}\r\n                      disabled={loading}\r\n                    />\r\n                  </div>\r\n                  {errors.confirmPassword && (\r\n                    <p className=\"text-xs text-destructive\">{errors.confirmPassword}</p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-3 pt-1\">\r\n                <Button\r\n                  className=\"w-full\"\r\n                  onClick={handleRegister}\r\n                  disabled={loading}\r\n                >\r\n                  {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n                  注册\r\n                </Button>\r\n              </div>\r\n            </TabsContent>\r\n          </div>\r\n        </Tabs>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}", "import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  className?: string\r\n}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input } ", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('LoaderCircle', [\n  ['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }],\n]);\n\nexport default LoaderCircle;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserRound\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjUiIC8+CiAgPHBhdGggZD0iTTIwIDIxYTggOCAwIDAgMC0xNiAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/user-round\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserRound = createLucideIcon('UserRound', [\n  ['circle', { cx: '12', cy: '8', r: '5', key: '1hypcn' }],\n  ['path', { d: 'M20 21a8 8 0 0 0-16 0', key: 'rfgkzh' }],\n]);\n\nexport default UserRound;\n", "import { LoginForm } from \"@/components/auth/login-form\"\r\nimport { auth } from \"@/lib/auth\"\r\nimport { redirect } from \"next/navigation\"\r\n\r\nexport const runtime = \"edge\"\r\n\r\nexport default async function LoginPage() {\r\n  const session = await auth()\r\n  \r\n  if (session?.user) {\r\n    redirect(\"/\")\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800\">\r\n      <LoginForm />\r\n    </div>\r\n  )\r\n} ", "// packages/react/direction/src/direction.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DirectionContext = React.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ jsx(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\nexport {\n  DirectionProvider,\n  Provider,\n  useDirection\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n"], "names": ["Card", "React", "className", "props", "ref", "div", "cn", "displayName", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "h3", "CardDescription", "p", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "TabsList", "TabsPrimitive", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoginForm", "username", "setUsername", "useState", "password", "setPassword", "confirmPassword", "setConfirmPassword", "loading", "setLoading", "errors", "setErrors", "toast", "useToast", "validateLoginForm", "newErrors", "includes", "length", "Object", "keys", "validateRegisterForm", "handleLogin", "result", "signIn", "redirect", "error", "title", "description", "variant", "window", "location", "href", "Error", "message", "handleRegister", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "Tabs", "defaultValue", "onValueChange", "clearForm", "value", "User2", "Input", "placeholder", "onChange", "e", "target", "disabled", "KeyRound", "type", "<PERSON><PERSON>", "onClick", "Loader2", "span", "handleGithubLogin", "callbackUrl", "<PERSON><PERSON><PERSON>", "input", "runtime", "LoginPage", "session", "auth", "user"], "sourceRoot": "", "ignoreList": [15]}