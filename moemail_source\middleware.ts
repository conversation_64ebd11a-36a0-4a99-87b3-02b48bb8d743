import { auth } from "@/lib/auth"
import { NextResponse } from "next/server"
import { PERMISSIONS } from "@/lib/permissions"
import { checkPermission } from "@/lib/auth"
import { Permission } from "@/lib/permissions"
import { handleApi<PERSON>eyAuth } from "@/lib/apiKey"
import { verifyAddressCredential } from "@/lib/edge-auth"

const API_PERMISSIONS: Record<string, Permission> = {
  '/api/emails': PERMISSIONS.MANAGE_EMAIL,
  '/api/webhook': PERMISSIONS.MANAGE_WEBHOOK,
  '/api/roles/promote': PERMISSIONS.PROMOTE_USER,
  '/api/config': PERMISSIONS.MANAGE_CONFIG,
  '/api/api-keys': PERMISSIONS.MANAGE_API_KEY,
}

export async function middleware(request: Request) {
  const { pathname } = new URL(request.url)

  // Address Credential Auth
  const authHeader = request.headers.get("Authorization");
  if (authHeader?.startsWith("Bearer ")) {
    const token = authHeader.substring(7);
    const payload = await verifyAddressCredential(token);

    if (!payload) {
      // Token was provided but is invalid (expired, bad signature, or revoked)
      return NextResponse.json({ error: "Invalid credential" }, { status: 401 });
    }

    if (payload.sub) {
      const addressFromToken = payload.sub;
      // Extract address carefully, assuming path is /api/emails/{address}/...
      const pathParts = pathname.split('/');
      if (pathParts.length > 3 && pathParts[1] === 'api' && pathParts[2] === 'emails') {
        const addressFromUrl = decodeURIComponent(pathParts[3]);
        if (addressFromToken === addressFromUrl) {
          // Token is valid for this address, allow access
          return NextResponse.next();
        }
      }
    }
    
    // Token is valid but for a different address or malformed path
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  // API Key 认证
  request.headers.delete("X-User-Id")
  const apiKey = request.headers.get("X-API-Key")
  if (apiKey) {
    return handleApiKeyAuth(apiKey, pathname)
  }

  // Session 认证
  const session = await auth()
  if (!session?.user) {
    return NextResponse.json(
      { error: "未授权" },
      { status: 401 }
    )
  }

  if (pathname === '/api/config' && request.method === 'GET') {
    return NextResponse.next()
  }

  for (const [route, permission] of Object.entries(API_PERMISSIONS)) {
    if (pathname.startsWith(route)) {
      const hasAccess = await checkPermission(permission)

      if (!hasAccess) {
        return NextResponse.json(
          { error: "权限不足" },
          { status: 403 }
        )
      }
      break
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    '/api/webhook/:path*',
    '/api/roles/:path*',
    '/api/config/:path*',
    '/api/api-keys/:path*',
    // Specifically match routes that need credential auth
    '/api/emails/:address/messages/:path*',
    '/api/emails/:address/credentials/:path*',
  ]
} 