#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/tailwindcss@3.4.16_ts-node@_c4ece2b75503767f74407284ba3eed2c/node_modules/tailwindcss/lib/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/tailwindcss@3.4.16_ts-node@_c4ece2b75503767f74407284ba3eed2c/node_modules/tailwindcss/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/tailwindcss@3.4.16_ts-node@_c4ece2b75503767f74407284ba3eed2c/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/tailwindcss@3.4.16_ts-node@_c4ece2b75503767f74407284ba3eed2c/node_modules/tailwindcss/lib/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/tailwindcss@3.4.16_ts-node@_c4ece2b75503767f74407284ba3eed2c/node_modules/tailwindcss/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/tailwindcss@3.4.16_ts-node@_c4ece2b75503767f74407284ba3eed2c/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../tailwindcss@3.4.16_ts-node@_c4ece2b75503767f74407284ba3eed2c/node_modules/tailwindcss/lib/cli.js" "$@"
else
  exec node  "$basedir/../../../../../tailwindcss@3.4.16_ts-node@_c4ece2b75503767f74407284ba3eed2c/node_modules/tailwindcss/lib/cli.js" "$@"
fi
