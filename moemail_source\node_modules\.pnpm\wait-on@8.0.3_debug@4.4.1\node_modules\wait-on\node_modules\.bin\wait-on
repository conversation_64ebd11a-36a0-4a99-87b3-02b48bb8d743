#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/wait-on@8.0.3_debug@4.4.1/node_modules/wait-on/bin/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/wait-on@8.0.3_debug@4.4.1/node_modules/wait-on/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/wait-on@8.0.3_debug@4.4.1/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/wait-on@8.0.3_debug@4.4.1/node_modules/wait-on/bin/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/wait-on@8.0.3_debug@4.4.1/node_modules/wait-on/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/wait-on@8.0.3_debug@4.4.1/node_modules:/proc/cygdrive/f/CODE/Project/Mail/moemail_source/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/wait-on" "$@"
else
  exec node  "$basedir/../../bin/wait-on" "$@"
fi
