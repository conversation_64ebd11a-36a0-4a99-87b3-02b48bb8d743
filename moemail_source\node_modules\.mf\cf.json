{"clientTcpRtt": 180, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 9808, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "CN", "isEUCountry": false, "region": "Shanghai", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "6b0GFkTFD7qdOmtwE3Hh43FZGh72VFsdvMC3w4SMN2o=", "tlsExportedAuthenticator": {"clientFinished": "4f019b9b35c366b786cb11fb3fb8592b3462b6e4ff2d9a98bf03e535126fabb85b6f00ff5dda3fa84151dc5cdc3810d1", "clientHandshake": "c54465a45f239d48da04be2369052e910d2124842156f77b58e6c6bee224512ed7a43915ec29ed750a45a8030b042a3b", "serverHandshake": "7335af20ae39aeeac3600f6df9e6e70f977fbabc5d12cb21e2ddd9397fb6fd16af78646835f4e7ea150c8d126a7ac08c", "serverFinished": "33b96f6f575c655188205cd3c4a90e7da78c8a7f2d3a469bf81a9aa6893043d00be14aa04c93a86ff2ca61e554685d28"}, "tlsClientHelloLength": "386", "colo": "HKG", "timezone": "Asia/Shanghai", "longitude": "121.45806", "latitude": "31.22222", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "200000", "city": "Shanghai", "tlsVersion": "TLSv1.3", "regionCode": "SH", "asOrganization": "China Mobile Communications Corporation", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}