(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9],{5521:e=>{"use strict";e.exports=require("node:async_hooks")},5356:e=>{"use strict";e.exports=require("node:buffer")},7630:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{ComponentMod:()=>ew,default:()=>eS});var s,i={};r.r(i),r.d(i,{POST:()=>el,runtime:()=>ed});var n={};r.r(n),r.d(n,{patchFetch:()=>ey,routeModule:()=>eu,serverHooks:()=>ef,workAsyncStorage:()=>ep,workUnitAsyncStorage:()=>em});var o=r(793),c=r(6590),d=r(3439),l=r(4651),u=r(6292),p=r(8498),m=r(658),f=r(1639),y=r(615),h=r(9230),g=r(9066);let w=new TextEncoder,S=new TextDecoder;function E(e){let t=e;return("string"==typeof t&&(t=w.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class A extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class b extends A{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class v extends A{static code="ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class _ extends A{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class C extends A{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}let R=(e,t)=>{let r=`SHA-${e.slice(-3)}`;switch(e){case"HS256":case"HS384":case"HS512":return{hash:r,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:r,name:"RSA-PSS",saltLength:parseInt(e.slice(-3),10)>>3};case"RS256":case"RS384":case"RS512":return{hash:r,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:r,name:"ECDSA",namedCurve:t.namedCurve};case"Ed25519":case"EdDSA":return{name:"Ed25519"};default:throw new b(`alg ${e} is not supported either by JOSE or your javascript runtime`)}},I=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}};function P(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function T(e,t){return e.name===t}function K(e){return parseInt(e.name.slice(4),10)}function k(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let x=(e,...t)=>k("Key must be ",e,...t);function H(e,t,...r){return k(`Key for the ${e} algorithm must be `,t,...r)}let D=async(e,t,r)=>{if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError(x(t,"CryptoKey","KeyObject","JSON Web Key"));return crypto.subtle.importKey("raw",t,{hash:`SHA-${e.slice(-3)}`,name:"HMAC"},!1,[r])}return!function(e,t,r){switch(t){case"HS256":case"HS384":case"HS512":{if(!T(e.algorithm,"HMAC"))throw P("HMAC");let r=parseInt(t.slice(2),10);if(K(e.algorithm.hash)!==r)throw P(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!T(e.algorithm,"RSASSA-PKCS1-v1_5"))throw P("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if(K(e.algorithm.hash)!==r)throw P(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!T(e.algorithm,"RSA-PSS"))throw P("RSA-PSS");let r=parseInt(t.slice(2),10);if(K(e.algorithm.hash)!==r)throw P(`SHA-${r}`,"algorithm.hash");break}case"Ed25519":case"EdDSA":if(!T(e.algorithm,"Ed25519"))throw P("Ed25519");break;case"ES256":case"ES384":case"ES512":{if(!T(e.algorithm,"ECDSA"))throw P("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw P(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}(function(e,t){if(t&&!e.usages.includes(t))throw TypeError(`CryptoKey does not support this operation, its usages must include ${t}.`)})(e,r)}(t,e,r),t},O=async(e,t,r)=>{let a=await D(e,t,"sign");return I(e,a),new Uint8Array(await crypto.subtle.sign(R(e,a.algorithm),a,r))},q=(...e)=>{let t;let r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0};function N(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function M(e){return e?.[Symbol.toStringTag]==="KeyObject"}let j=e=>N(e)||M(e),U=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function $(e){return U(e)&&"string"==typeof e.kty}let W=e=>e?.[Symbol.toStringTag],F=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let a;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):a=r;break;case e.startsWith("PBES2"):a="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):a=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):a="wrapKey";break;case"decrypt"===r:a=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(a&&t.key_ops?.includes?.(a)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${a}" when present`)}return!0},B=(e,t,r)=>{if(!(t instanceof Uint8Array)){if($(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&F(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!j(t))throw TypeError(H(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${W(t)} instances for symmetric algorithms must be of type "secret"`)}},Q=(e,t,r)=>{if($(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&F(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&F(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!j(t))throw TypeError(H(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${W(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${W(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${W(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${W(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${W(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},J=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?B(e,t,r):Q(e,t,r)},G=(e,t,r,a,s)=>{let i;if(void 0!==s.crit&&a?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!a||void 0===a.crit)return new Set;if(!Array.isArray(a.crit)||0===a.crit.length||a.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let n of(i=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,a.crit)){if(!i.has(n))throw new b(`Extension Header Parameter "${n}" is not recognized`);if(void 0===s[n])throw new e(`Extension Header Parameter "${n}" is missing`);if(i.get(n)&&void 0===a[n])throw new e(`Extension Header Parameter "${n}" MUST be integrity protected`)}return new Set(a.crit)},L=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new b('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new b('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new b('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new b('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),a={...e};return delete a.alg,delete a.use,crypto.subtle.importKey("jwk",a,t,e.ext??!e.d,e.key_ops??r)},z=async(e,t,r,s=!1)=>{let i=(a||=new WeakMap).get(e);if(i?.[r])return i[r];let n=await L({...t,alg:r});return s&&Object.freeze(e),i?i[r]=n:a.set(e,{[r]:n}),n},V=(e,t)=>{let r;let s=(a||=new WeakMap).get(e);if(s?.[t])return s[t];let i="public"===e.type,n=!!i;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,n,i?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,n,[i?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let a;switch(t){case"RSA-OAEP":a="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":a="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":a="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":a="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:a},n,i?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:a},n,[i?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let a=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!a)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===a&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:a},n,[i?"verify":"sign"])),"ES384"===t&&"P-384"===a&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:a},n,[i?"verify":"sign"])),"ES512"===t&&"P-521"===a&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:a},n,[i?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:a},n,i?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return s?s[t]=r:a.set(e,{[t]:r}),r},Y=async(e,t)=>{if(e instanceof Uint8Array||N(e))return e;if(M(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return V(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return z(e,r,t)}if($(e))return e.k?function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:S.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=S.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return function(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64(e);let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}(e.k):z(e,e,t,!0);throw Error("unreachable")};class Z{#e;#t;#r;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this.#e=e}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setUnprotectedHeader(e){if(this.#r)throw TypeError("setUnprotectedHeader can only be called once");return this.#r=e,this}async sign(e,t){let r;if(!this.#t&&!this.#r)throw new v("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!q(this.#t,this.#r))throw new v("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let a={...this.#t,...this.#r},s=G(v,new Map([["b64",!0]]),t?.crit,this.#t,a),i=!0;if(s.has("b64")&&"boolean"!=typeof(i=this.#t.b64))throw new v('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:n}=a;if("string"!=typeof n||!n)throw new v('JWS "alg" (Algorithm) Header Parameter missing or invalid');J(n,e,"sign");let o=this.#e;i&&(o=w.encode(E(o)));let c=function(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let a of e)t.set(a,r),r+=a.length;return t}(r=this.#t?w.encode(E(JSON.stringify(this.#t))):w.encode(""),w.encode("."),o),d=await Y(e,n),l={signature:E(await O(n,d,c)),payload:""};return i&&(l.payload=S.decode(o)),this.#r&&(l.header=this.#r),this.#t&&(l.protected=S.decode(r)),l}}class X{#a;constructor(e){this.#a=new Z(e)}setProtectedHeader(e){return this.#a.setProtectedHeader(e),this}async sign(e,t){let r=await this.#a.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}let ee=e=>Math.floor(e.getTime()/1e3),et=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,er=e=>{let t;let r=et.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let a=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(a);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*a);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*a);break;case"day":case"days":case"d":t=Math.round(86400*a);break;case"week":case"weeks":case"w":t=Math.round(604800*a);break;default:t=Math.round(0x1e187e0*a)}return"-"===r[1]||"ago"===r[4]?-t:t};function ea(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}class es{#e;constructor(e){if(!U(e))throw TypeError("JWT Claims Set MUST be an object");this.#e=structuredClone(e)}data(){return w.encode(JSON.stringify(this.#e))}get iss(){return this.#e.iss}set iss(e){this.#e.iss=e}get sub(){return this.#e.sub}set sub(e){this.#e.sub=e}get aud(){return this.#e.aud}set aud(e){this.#e.aud=e}set jti(e){this.#e.jti=e}set nbf(e){"number"==typeof e?this.#e.nbf=ea("setNotBefore",e):e instanceof Date?this.#e.nbf=ea("setNotBefore",ee(e)):this.#e.nbf=ee(new Date)+er(e)}set exp(e){"number"==typeof e?this.#e.exp=ea("setExpirationTime",e):e instanceof Date?this.#e.exp=ea("setExpirationTime",ee(e)):this.#e.exp=ee(new Date)+er(e)}set iat(e){void 0===e?this.#e.iat=ee(new Date):e instanceof Date?this.#e.iat=ea("setIssuedAt",ee(e)):"string"==typeof e?this.#e.iat=ea("setIssuedAt",ee(new Date)+er(e)):this.#e.iat=ea("setIssuedAt",e)}}class ei{#t;#s;constructor(e={}){this.#s=new es(e)}setIssuer(e){return this.#s.iss=e,this}setSubject(e){return this.#s.sub=e,this}setAudience(e){return this.#s.aud=e,this}setJti(e){return this.#s.jti=e,this}setNotBefore(e){return this.#s.nbf=e,this}setExpirationTime(e){return this.#s.exp=e,this}setIssuedAt(e){return this.#s.iat=e,this}setProtectedHeader(e){return this.#t=e,this}async sign(e,t){let r=new X(this.#s.data());if(r.setProtectedHeader(this.#t),Array.isArray(this.#t?.crit)&&this.#t.crit.includes("b64")&&!1===this.#t.b64)throw new _("JWTs MUST NOT use unencoded payload");return r.sign(e,t)}}var en=r(3922);let eo=process.env.JWT_ADDRESS_SECRET;async function ec(e,t){if(!eo)throw Error("JWT_ADDRESS_SECRET is not set in .env");let r=new TextEncoder().encode(eo);return await new ei({}).setProtectedHeader({alg:"HS256"}).setSubject(e).setJti((0,en.Ak)()).setIssuedAt().setIssuer("moemail").setAudience(e).setExpirationTime(0===t?"365d":`${t}s`).sign(r)}let ed="edge";async function el(e,{params:t}){let r=await (0,f.j2)();if(!r?.user?.id)return m.Rp.json({error:"Unauthorized"},{status:401});let a=(0,y.d)(),s=decodeURIComponent(t.address);try{let t=await a.query.emails.findFirst({where:(0,g.Uo)((0,g.eq)(h.emails.address,s),(0,g.eq)(h.emails.userId,r.user.id))});if(!t)return m.Rp.json({error:"Email not found or access denied"},{status:404});let{expiresIn:i=3600}=await e.json().catch(()=>({})),n=0===i?0:1e3*i,o=await ec(t.address,i),c=new Date(0===i?"9999-01-01T00:00:00.000Z":Date.now()+n);return m.Rp.json({credential:o,expiresAt:c.toISOString()})}catch(e){return console.error("Failed to generate credential:",e),m.Rp.json({error:"Failed to generate credential"},{status:500})}}let eu=new l.AppRouteRouteModule({definition:{kind:u.A.APP_ROUTE,page:"/api/emails/[address]/credentials/route",pathname:"/api/emails/[address]/credentials",filename:"route",bundlePath:"app/api/emails/[address]/credentials/route"},resolvedPagePath:"F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\[address]\\credentials\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:ep,workUnitAsyncStorage:em,serverHooks:ef}=eu;function ey(){return(0,p.V5)({workAsyncStorage:ep,workUnitAsyncStorage:em})}let eh=null==(s=self.__RSC_MANIFEST)?void 0:s["/api/emails/[address]/credentials/route"],eg=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);eh&&eg&&(0,c.fQ)({page:"/api/emails/[address]/credentials/route",clientReferenceManifest:eh,serverActionsManifest:eg,serverModuleMap:(0,o.e)({serverActionsManifest:eg})});let ew=n,eS=d.s.wrap(eu,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\Mail\\moemail_source",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\Mail\\moemail_source"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\Mail\\moemail_source\\next.config.ts",configFileName:"next.config.ts"}})},7032:()=>{},6760:()=>{},1514:(e,t,r)=>{"use strict";r.d(t,{F:()=>i}),r(615),r(9230),r(658);var a=r(1639),s=r(144);let i=async()=>{let e=(await (0,s.b3)()).get("X-User-Id");if(e)return e;let t=await (0,a.j2)();return t?.user.id}},1639:(e,t,r)=>{"use strict";r.d(t,{fG:()=>b,LO:()=>v,iz:()=>E,j2:()=>_,Yj:()=>A,kz:()=>I});var a=r(3757),s=r(7031),i=r(1049),n=r(615),o=r(9230),c=r(9066),d=r(888),l=r(789),u=r(236),p=r(2596),m=r(2342),f=r(5356).Buffer;let y=["#2196F3","#009688","#9C27B0","#F44336","#673AB7","#3F51B5","#4CAF50","#FF5722","#795548","#607D8B"];var h=r(1514);let g={[l.gg.EMPEROR]:"皇帝（网站所有者）",[l.gg.DUKE]:"公爵（超级用户）",[l.gg.KNIGHT]:"骑士（高级用户）",[l.gg.CIVILIAN]:"平民（普通用户）"},w=async()=>{let e=await (0,d.getRequestContext)().env.SITE_CONFIG.get("DEFAULT_ROLE");return e===l.gg.DUKE||e===l.gg.KNIGHT||e===l.gg.CIVILIAN?e:l.gg.CIVILIAN};async function S(e,t){let r=await e.query.roles.findFirst({where:(0,c.eq)(o.roles.name,t)});if(!r){let[a]=await e.insert(o.roles).values({name:t,description:g[t]}).returning();r=a}return r}async function E(e,t,r){await e.delete(o.userRoles).where((0,c.eq)(o.userRoles.userId,t)),await e.insert(o.userRoles).values({userId:t,roleId:r})}async function A(e){let t=await (0,h.F)();if(!t)return!1;let r=(0,n.d)(),a=(await r.query.userRoles.findMany({where:(0,c.eq)(o.userRoles.userId,t),with:{role:!0}})).map(e=>e.role.name);return(0,l._m)(a,e)}let{handlers:{GET:b,POST:v},auth:_,signIn:C,signOut:R}=(0,a.Ay)(()=>({secret:process.env.AUTH_SECRET,adapter:(0,i._)((0,n.d)(),{usersTable:o.users,accountsTable:o.accounts}),providers:[(0,s.A)({clientId:process.env.AUTH_GITHUB_ID,clientSecret:process.env.AUTH_GITHUB_SECRET}),(0,u.A)({name:"Credentials",credentials:{username:{label:"用户名",type:"text",placeholder:"请输入用户名"},password:{label:"密码",type:"password",placeholder:"请输入密码"}},async authorize(e){if(!e)throw Error("请输入用户名和密码");let{username:t,password:r}=e;try{m.Q.parse({username:t,password:r})}catch(e){throw Error("输入格式不正确")}let a=(0,n.d)(),s=await a.query.users.findFirst({where:(0,c.eq)(o.users.username,t)});if(!s||!await (0,p.b)(r,s.password))throw Error("用户名或密码错误");return{...s,password:void 0}}})],events:{async signIn({user:e}){if(e.id)try{let t=(0,n.d)();if(await t.query.userRoles.findFirst({where:(0,c.eq)(o.userRoles.userId,e.id)}))return;let r=await w(),a=await S(t,r);await E(t,e.id,a.id)}catch(e){console.error("Error assigning role:",e)}}},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.name=t.name||t.username,e.username=t.username,e.image=t.image||function(e){let t=e[0].toUpperCase(),r=Array.from(e).reduce((e,t)=>e+t.charCodeAt(0),0)%y.length,a=y[r],s=`
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
      <rect width="100" height="100" fill="${a}"/>
      <text 
        x="50%" 
        y="50%" 
        fill="white" 
        font-family="system-ui, -apple-system, sans-serif" 
        font-size="45" 
        font-weight="500"
        text-anchor="middle"
        alignment-baseline="central"
        dominant-baseline="central"
        style="text-transform: uppercase"
      >
        ${t}
      </text>
    </svg>
  `.trim(),i=new TextEncoder().encode(s),n=f.from(i).toString("base64");return`data:image/svg+xml;base64,${n}`}(e.name)),e),async session({session:e,token:t}){if(t&&e.user){e.user.id=t.id,e.user.name=t.name,e.user.username=t.username,e.user.image=t.image;let r=(0,n.d)(),a=await r.query.userRoles.findMany({where:(0,c.eq)(o.userRoles.userId,e.user.id),with:{role:!0}});if(!a.length){let t=await w(),s=await S(r,t);await E(r,e.user.id,s.id),a=[{userId:e.user.id,roleId:s.id,createdAt:new Date,role:s}]}e.user.roles=a.map(e=>({name:e.role.name}))}return e}},session:{strategy:"jwt"}}));async function I(e,t){let r=(0,n.d)();if(await r.query.users.findFirst({where:(0,c.eq)(o.users.username,e)}))throw Error("用户名已存在");let a=await (0,p.E)(t),[s]=await r.insert(o.users).values({username:e,password:a}).returning();return s}},615:(e,t,r)=>{"use strict";r.d(t,{d:()=>n});var a=r(888),s=r(7384),i=r(9230);let n=()=>(0,s.f)((0,a.getRequestContext)().env.DB,{schema:i})},789:(e,t,r)=>{"use strict";r.d(t,{Jj:()=>s,_m:()=>n,gg:()=>a});let a={EMPEROR:"emperor",DUKE:"duke",KNIGHT:"knight",CIVILIAN:"civilian"},s={MANAGE_EMAIL:"manage_email",MANAGE_WEBHOOK:"manage_webhook",PROMOTE_USER:"promote_user",MANAGE_CONFIG:"manage_config",MANAGE_API_KEY:"manage_api_key"},i={[a.EMPEROR]:Object.values(s),[a.DUKE]:[s.MANAGE_EMAIL,s.MANAGE_WEBHOOK,s.MANAGE_API_KEY],[a.KNIGHT]:[s.MANAGE_EMAIL,s.MANAGE_WEBHOOK],[a.CIVILIAN]:[]};function n(e,t){return e.some(e=>i[e]?.includes(t))}},9230:(e,t,r)=>{"use strict";r.r(t),r.d(t,{accounts:()=>l,apiKeys:()=>h,apiKeysRelations:()=>w,emails:()=>u,messages:()=>p,revoked_credentials:()=>g,roles:()=>f,rolesRelations:()=>A,userRoles:()=>y,userRolesRelations:()=>S,users:()=>d,usersRelations:()=>E,webhooks:()=>m});var a=r(7243),s=r(1939),i=r(3268),n=r(3797),o=r(1870),c=r(652);let d=(0,a.D)("user",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,s.Qq)("name"),email:(0,s.Qq)("email").unique(),emailVerified:(0,i.nd)("emailVerified",{mode:"timestamp_ms"}),image:(0,s.Qq)("image"),username:(0,s.Qq)("username").unique(),password:(0,s.Qq)("password")}),l=(0,a.D)("account",{userId:(0,s.Qq)("userId").notNull().references(()=>d.id,{onDelete:"cascade"}),type:(0,s.Qq)("type").$type().notNull(),provider:(0,s.Qq)("provider").notNull(),providerAccountId:(0,s.Qq)("providerAccountId").notNull(),refresh_token:(0,s.Qq)("refresh_token"),access_token:(0,s.Qq)("access_token"),expires_at:(0,i.nd)("expires_at"),token_type:(0,s.Qq)("token_type"),scope:(0,s.Qq)("scope"),id_token:(0,s.Qq)("id_token"),session_state:(0,s.Qq)("session_state")},e=>({compoundKey:(0,n.ie)({columns:[e.provider,e.providerAccountId]})})),u=(0,a.D)("email",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),address:(0,s.Qq)("address").notNull().unique(),userId:(0,s.Qq)("userId").references(()=>d.id,{onDelete:"cascade"}),createdAt:(0,i.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),expiresAt:(0,i.nd)("expires_at",{mode:"timestamp_ms"}).notNull()},e=>({expiresAtIdx:(0,o.Pe)("email_expires_at_idx").on(e.expiresAt)})),p=(0,a.D)("message",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),emailId:(0,s.Qq)("emailId").notNull().references(()=>u.id,{onDelete:"cascade"}),fromAddress:(0,s.Qq)("from_address").notNull(),subject:(0,s.Qq)("subject").notNull(),content:(0,s.Qq)("content").notNull(),html:(0,s.Qq)("html"),receivedAt:(0,i.nd)("received_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)},e=>({emailIdIdx:(0,o.Pe)("message_email_id_idx").on(e.emailId)})),m=(0,a.D)("webhook",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,s.Qq)("user_id").notNull().references(()=>d.id,{onDelete:"cascade"}),url:(0,s.Qq)("url").notNull(),enabled:(0,i.nd)("enabled",{mode:"boolean"}).notNull().default(!0),createdAt:(0,i.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),updatedAt:(0,i.nd)("updated_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)}),f=(0,a.D)("role",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,s.Qq)("name").notNull(),description:(0,s.Qq)("description"),createdAt:(0,i.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),updatedAt:(0,i.nd)("updated_at",{mode:"timestamp"}).$defaultFn(()=>new Date)}),y=(0,a.D)("user_role",{userId:(0,s.Qq)("user_id").notNull().references(()=>d.id,{onDelete:"cascade"}),roleId:(0,s.Qq)("role_id").notNull().references(()=>f.id,{onDelete:"cascade"}),createdAt:(0,i.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date)},e=>({pk:(0,n.ie)({columns:[e.userId,e.roleId]})})),h=(0,a.D)("api_keys",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,s.Qq)("user_id").notNull().references(()=>d.id),name:(0,s.Qq)("name").notNull(),key:(0,s.Qq)("key").notNull().unique(),createdAt:(0,i.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),expiresAt:(0,i.nd)("expires_at",{mode:"timestamp"}),enabled:(0,i.nd)("enabled",{mode:"boolean"}).notNull().default(!0)},e=>({nameUserIdUnique:(0,o.GL)("name_user_id_unique").on(e.name,e.userId)})),g=(0,a.D)("revoked_credential",{jti:(0,s.Qq)("jti").primaryKey(),expiresAt:(0,i.nd)("expires_at",{mode:"timestamp_ms"}).notNull()},e=>({expiresAtIdx:(0,o.Pe)("revoked_expires_at_idx").on(e.expiresAt)})),w=(0,c.K1)(h,({one:e})=>({user:e(d,{fields:[h.userId],references:[d.id]})})),S=(0,c.K1)(y,({one:e})=>({user:e(d,{fields:[y.userId],references:[d.id]}),role:e(f,{fields:[y.roleId],references:[f.id]})})),E=(0,c.K1)(d,({many:e})=>({userRoles:e(y),apiKeys:e(h)})),A=(0,c.K1)(f,({many:e})=>({userRoles:e(y)}))},2596:(e,t,r)=>{"use strict";r.d(t,{E:()=>n,b:()=>o,cn:()=>i});var a=r(8649),s=r(5588);function i(...e){return(0,s.QP)((0,a.$)(e))}async function n(e){let t=new TextEncoder,r=process.env.AUTH_SECRET||"",a=t.encode(e+r);return btoa(String.fromCharCode(...new Uint8Array(await crypto.subtle.digest("SHA-256",a))))}async function o(e,t){return await n(e)===t}},2342:(e,t,r)=>{"use strict";r.d(t,{Q:()=>s});var a=r(9267);let s=a.z.object({username:a.z.string().min(1,"用户名不能为空").max(20,"用户名不能超过20个字符").regex(/^[a-zA-Z0-9_-]+$/,"用户名只能包含字母、数字、下划线和横杠").refine(e=>!e.includes("@"),"用户名不能是邮箱格式"),password:a.z.string().min(8,"密码长度必须大于等于8位")})},3922:(e,t,r)=>{"use strict";r.d(t,{Ak:()=>a});let a=(e=21)=>{let t="",r=crypto.getRandomValues(new Uint8Array(e|=0));for(;e--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&r[e]];return t}}},e=>{var t=t=>e(e.s=t);e.O(0,[810,702,267,658,51,92],()=>t(7630));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/emails/[address]/credentials/route"]=r}]);
//# sourceMappingURL=route.js.map