(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9],{5521:e=>{"use strict";e.exports=require("node:async_hooks")},5356:e=>{"use strict";e.exports=require("node:buffer")},9534:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ComponentMod:()=>M,default:()=>O});var a,r={};i.r(r),i.d(r,{POST:()=>S,runtime:()=>v});var s={};i.r(s),i.d(s,{patchFetch:()=>R,routeModule:()=>y,serverHooks:()=>P,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>C});var o=i(793),n=i(6590),c=i(3439),l=i(4651),d=i(6292),u=i(8498),f=i(658),m=i(1639),p=i(615),g=i(9230),h=i(9066),x=i(132);let v="edge";async function S(e,{params:t}){let i=await (0,m.j2)();if(!i?.user?.id)return f.Rp.json({error:"Unauthorized"},{status:401});let a=(0,p.d)(),r=decodeURIComponent(t.address);try{let t=await a.query.emails.findFirst({where:(0,h.Uo)((0,h.eq)(g.emails.address,r),(0,h.eq)(g.emails.userId,i.user.id))});if(!t)return f.Rp.json({error:"Email not found or access denied"},{status:404});let{expiresIn:s=3600}=await e.json().catch(()=>({})),o=0===s?0:1e3*s,n=await (0,x.Z)(t.address,s),c=new Date(0===s?"9999-01-01T00:00:00.000Z":Date.now()+o);return f.Rp.json({credential:n,expiresAt:c.toISOString()})}catch(e){return console.error("Failed to generate credential:",e),f.Rp.json({error:"Failed to generate credential"},{status:500})}}let y=new l.AppRouteRouteModule({definition:{kind:d.A.APP_ROUTE,page:"/api/emails/[address]/credentials/route",pathname:"/api/emails/[address]/credentials",filename:"route",bundlePath:"app/api/emails/[address]/credentials/route"},resolvedPagePath:"F:\\CODE\\Project\\Mail\\moemail_source\\app\\api\\emails\\[address]\\credentials\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:b,workUnitAsyncStorage:C,serverHooks:P}=y;function R(){return(0,u.V5)({workAsyncStorage:b,workUnitAsyncStorage:C})}let w=null==(a=self.__RSC_MANIFEST)?void 0:a["/api/emails/[address]/credentials/route"],E=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);w&&E&&(0,n.fQ)({page:"/api/emails/[address]/credentials/route",clientReferenceManifest:w,serverActionsManifest:E,serverModuleMap:(0,o.e)({serverActionsManifest:E})});let M=s,O=c.s.wrap(y,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"avatars.githubusercontent.com"}],unoptimized:!1},devIndicators:{appIsrStatus:!0,buildActivity:!0,buildActivityPosition:"bottom-right"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"F:\\CODE\\Project\\Mail\\moemail_source",experimental:{cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:11,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,turbo:{root:"F:\\CODE\\Project\\Mail\\moemail_source"},typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,reactOwnerStack:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},bundlePagesRouterDependencies:!1,configFile:"F:\\CODE\\Project\\Mail\\moemail_source\\next.config.ts",configFileName:"next.config.ts"}})}},e=>{var t=t=>e(e.s=t);e.O(0,[810,702,267,658,51,92,137,815],()=>t(9534));var i=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/emails/[address]/credentials/route"]=i}]);
//# sourceMappingURL=route.js.map