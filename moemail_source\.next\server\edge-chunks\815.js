(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[815],{7032:()=>{},6760:()=>{},1514:(e,t,r)=>{"use strict";r.d(t,{F:()=>n}),r(615),r(9230),r(658);var s=r(1639),a=r(144);let n=async()=>{let e=(await (0,a.b3)()).get("X-User-Id");if(e)return e;let t=await (0,s.j2)();return t?.user.id}},1639:(e,t,r)=>{"use strict";r.d(t,{fG:()=>h,LO:()=>N,iz:()=>g,j2:()=>D,Yj:()=>E,uG:()=>q,kz:()=>x});var s=r(3757),a=r(7031),n=r(1049),i=r(615),d=r(9230),o=r(9066),l=r(888),u=r(789),c=r(236),m=r(2596),p=r(2342),_=r(5356).Buffer;let f=["#2196F3","#009688","#9C27B0","#F44336","#673AB7","#3F51B5","#4CAF50","#FF5722","#795548","#607D8B"];var w=r(1514);let I={[u.gg.EMPEROR]:"皇帝（网站所有者）",[u.gg.DUKE]:"公爵（超级用户）",[u.gg.KNIGHT]:"骑士（高级用户）",[u.gg.CIVILIAN]:"平民（普通用户）"},y=async()=>{let e=await (0,l.getRequestContext)().env.SITE_CONFIG.get("DEFAULT_ROLE");return e===u.gg.DUKE||e===u.gg.KNIGHT||e===u.gg.CIVILIAN?e:u.gg.CIVILIAN};async function A(e,t){let r=await e.query.roles.findFirst({where:(0,o.eq)(d.roles.name,t)});if(!r){let[s]=await e.insert(d.roles).values({name:t,description:I[t]}).returning();r=s}return r}async function g(e,t,r){await e.delete(d.userRoles).where((0,o.eq)(d.userRoles.userId,t)),await e.insert(d.userRoles).values({userId:t,roleId:r})}async function q(e){let t=(0,i.d)();return(await t.query.userRoles.findMany({where:(0,o.eq)(d.userRoles.userId,e),with:{role:!0}}))[0].role.name}async function E(e){let t=await (0,w.F)();if(!t)return!1;let r=(0,i.d)(),s=(await r.query.userRoles.findMany({where:(0,o.eq)(d.userRoles.userId,t),with:{role:!0}})).map(e=>e.role.name);return(0,u._m)(s,e)}let{handlers:{GET:h,POST:N},auth:D,signIn:Q,signOut:v}=(0,s.Ay)(()=>({secret:process.env.AUTH_SECRET,adapter:(0,n._)((0,i.d)(),{usersTable:d.users,accountsTable:d.accounts}),providers:[(0,a.A)({clientId:process.env.AUTH_GITHUB_ID,clientSecret:process.env.AUTH_GITHUB_SECRET}),(0,c.A)({name:"Credentials",credentials:{username:{label:"用户名",type:"text",placeholder:"请输入用户名"},password:{label:"密码",type:"password",placeholder:"请输入密码"}},async authorize(e){if(!e)throw Error("请输入用户名和密码");let{username:t,password:r}=e;try{p.Q.parse({username:t,password:r})}catch(e){throw Error("输入格式不正确")}let s=(0,i.d)(),a=await s.query.users.findFirst({where:(0,o.eq)(d.users.username,t)});if(!a||!await (0,m.b)(r,a.password))throw Error("用户名或密码错误");return{...a,password:void 0}}})],events:{async signIn({user:e}){if(e.id)try{let t=(0,i.d)();if(await t.query.userRoles.findFirst({where:(0,o.eq)(d.userRoles.userId,e.id)}))return;let r=await y(),s=await A(t,r);await g(t,e.id,s.id)}catch(e){console.error("Error assigning role:",e)}}},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.name=t.name||t.username,e.username=t.username,e.image=t.image||function(e){let t=e[0].toUpperCase(),r=Array.from(e).reduce((e,t)=>e+t.charCodeAt(0),0)%f.length,s=f[r],a=`
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
      <rect width="100" height="100" fill="${s}"/>
      <text 
        x="50%" 
        y="50%" 
        fill="white" 
        font-family="system-ui, -apple-system, sans-serif" 
        font-size="45" 
        font-weight="500"
        text-anchor="middle"
        alignment-baseline="central"
        dominant-baseline="central"
        style="text-transform: uppercase"
      >
        ${t}
      </text>
    </svg>
  `.trim(),n=new TextEncoder().encode(a),i=_.from(n).toString("base64");return`data:image/svg+xml;base64,${i}`}(e.name)),e),async session({session:e,token:t}){if(t&&e.user){e.user.id=t.id,e.user.name=t.name,e.user.username=t.username,e.user.image=t.image;let r=(0,i.d)(),s=await r.query.userRoles.findMany({where:(0,o.eq)(d.userRoles.userId,e.user.id),with:{role:!0}});if(!s.length){let t=await y(),a=await A(r,t);await g(r,e.user.id,a.id),s=[{userId:e.user.id,roleId:a.id,createdAt:new Date,role:a}]}e.user.roles=s.map(e=>({name:e.role.name}))}return e}},session:{strategy:"jwt"}}));async function x(e,t){let r=(0,i.d)();if(await r.query.users.findFirst({where:(0,o.eq)(d.users.username,e)}))throw Error("用户名已存在");let s=await (0,m.E)(t),[a]=await r.insert(d.users).values({username:e,password:s}).returning();return a}},615:(e,t,r)=>{"use strict";r.d(t,{d:()=>i});var s=r(888),a=r(7384),n=r(9230);let i=()=>(0,a.f)((0,s.getRequestContext)().env.DB,{schema:n})},132:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var s=r(3390),a=r(3922);r(615),r(9230);let n=process.env.JWT_ADDRESS_SECRET;async function i(e,t){if(!n)throw Error("JWT_ADDRESS_SECRET is not set in .env");let r=new TextEncoder().encode(n);return await new s.P({}).setProtectedHeader({alg:"HS256"}).setSubject(e).setJti((0,a.Ak)()).setIssuedAt().setIssuer("moemail").setAudience(e).setExpirationTime(0===t?"365d":`${t}s`).sign(r)}},789:(e,t,r)=>{"use strict";r.d(t,{Jj:()=>a,_m:()=>i,gg:()=>s});let s={EMPEROR:"emperor",DUKE:"duke",KNIGHT:"knight",CIVILIAN:"civilian"},a={MANAGE_EMAIL:"manage_email",MANAGE_WEBHOOK:"manage_webhook",PROMOTE_USER:"promote_user",MANAGE_CONFIG:"manage_config",MANAGE_API_KEY:"manage_api_key"},n={[s.EMPEROR]:Object.values(a),[s.DUKE]:[a.MANAGE_EMAIL,a.MANAGE_WEBHOOK,a.MANAGE_API_KEY],[s.KNIGHT]:[a.MANAGE_EMAIL,a.MANAGE_WEBHOOK],[s.CIVILIAN]:[]};function i(e,t){return e.some(e=>n[e]?.includes(t))}},9230:(e,t,r)=>{"use strict";r.r(t),r.d(t,{accounts:()=>u,apiKeys:()=>w,apiKeysRelations:()=>y,emails:()=>c,messages:()=>m,revoked_credentials:()=>I,roles:()=>_,rolesRelations:()=>q,userRoles:()=>f,userRolesRelations:()=>A,users:()=>l,usersRelations:()=>g,webhooks:()=>p});var s=r(7243),a=r(1939),n=r(3268),i=r(3797),d=r(1870),o=r(652);let l=(0,s.D)("user",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,a.Qq)("name"),email:(0,a.Qq)("email").unique(),emailVerified:(0,n.nd)("emailVerified",{mode:"timestamp_ms"}),image:(0,a.Qq)("image"),username:(0,a.Qq)("username").unique(),password:(0,a.Qq)("password")}),u=(0,s.D)("account",{userId:(0,a.Qq)("userId").notNull().references(()=>l.id,{onDelete:"cascade"}),type:(0,a.Qq)("type").$type().notNull(),provider:(0,a.Qq)("provider").notNull(),providerAccountId:(0,a.Qq)("providerAccountId").notNull(),refresh_token:(0,a.Qq)("refresh_token"),access_token:(0,a.Qq)("access_token"),expires_at:(0,n.nd)("expires_at"),token_type:(0,a.Qq)("token_type"),scope:(0,a.Qq)("scope"),id_token:(0,a.Qq)("id_token"),session_state:(0,a.Qq)("session_state")},e=>({compoundKey:(0,i.ie)({columns:[e.provider,e.providerAccountId]})})),c=(0,s.D)("email",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),address:(0,a.Qq)("address").notNull().unique(),userId:(0,a.Qq)("userId").references(()=>l.id,{onDelete:"cascade"}),createdAt:(0,n.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),expiresAt:(0,n.nd)("expires_at",{mode:"timestamp_ms"}).notNull()},e=>({expiresAtIdx:(0,d.Pe)("email_expires_at_idx").on(e.expiresAt)})),m=(0,s.D)("message",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),emailId:(0,a.Qq)("emailId").notNull().references(()=>c.id,{onDelete:"cascade"}),fromAddress:(0,a.Qq)("from_address").notNull(),subject:(0,a.Qq)("subject").notNull(),content:(0,a.Qq)("content").notNull(),html:(0,a.Qq)("html"),receivedAt:(0,n.nd)("received_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)},e=>({emailIdIdx:(0,d.Pe)("message_email_id_idx").on(e.emailId)})),p=(0,s.D)("webhook",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,a.Qq)("user_id").notNull().references(()=>l.id,{onDelete:"cascade"}),url:(0,a.Qq)("url").notNull(),enabled:(0,n.nd)("enabled",{mode:"boolean"}).notNull().default(!0),createdAt:(0,n.nd)("created_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date),updatedAt:(0,n.nd)("updated_at",{mode:"timestamp_ms"}).notNull().$defaultFn(()=>new Date)}),_=(0,s.D)("role",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,a.Qq)("name").notNull(),description:(0,a.Qq)("description"),createdAt:(0,n.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),updatedAt:(0,n.nd)("updated_at",{mode:"timestamp"}).$defaultFn(()=>new Date)}),f=(0,s.D)("user_role",{userId:(0,a.Qq)("user_id").notNull().references(()=>l.id,{onDelete:"cascade"}),roleId:(0,a.Qq)("role_id").notNull().references(()=>_.id,{onDelete:"cascade"}),createdAt:(0,n.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date)},e=>({pk:(0,i.ie)({columns:[e.userId,e.roleId]})})),w=(0,s.D)("api_keys",{id:(0,a.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),userId:(0,a.Qq)("user_id").notNull().references(()=>l.id),name:(0,a.Qq)("name").notNull(),key:(0,a.Qq)("key").notNull().unique(),createdAt:(0,n.nd)("created_at",{mode:"timestamp"}).$defaultFn(()=>new Date),expiresAt:(0,n.nd)("expires_at",{mode:"timestamp"}),enabled:(0,n.nd)("enabled",{mode:"boolean"}).notNull().default(!0)},e=>({nameUserIdUnique:(0,d.GL)("name_user_id_unique").on(e.name,e.userId)})),I=(0,s.D)("revoked_credential",{jti:(0,a.Qq)("jti").primaryKey(),expiresAt:(0,n.nd)("expires_at",{mode:"timestamp_ms"}).notNull()},e=>({expiresAtIdx:(0,d.Pe)("revoked_expires_at_idx").on(e.expiresAt)})),y=(0,o.K1)(w,({one:e})=>({user:e(l,{fields:[w.userId],references:[l.id]})})),A=(0,o.K1)(f,({one:e})=>({user:e(l,{fields:[f.userId],references:[l.id]}),role:e(_,{fields:[f.roleId],references:[_.id]})})),g=(0,o.K1)(l,({many:e})=>({userRoles:e(f),apiKeys:e(w)})),q=(0,o.K1)(_,({many:e})=>({userRoles:e(f)}))},2596:(e,t,r)=>{"use strict";r.d(t,{E:()=>i,b:()=>d,cn:()=>n});var s=r(8649),a=r(5588);function n(...e){return(0,a.QP)((0,s.$)(e))}async function i(e){let t=new TextEncoder,r=process.env.AUTH_SECRET||"",s=t.encode(e+r);return btoa(String.fromCharCode(...new Uint8Array(await crypto.subtle.digest("SHA-256",s))))}async function d(e,t){return await i(e)===t}},2342:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a});var s=r(9267);let a=s.z.object({username:s.z.string().min(1,"用户名不能为空").max(20,"用户名不能超过20个字符").regex(/^[a-zA-Z0-9_-]+$/,"用户名只能包含字母、数字、下划线和横杠").refine(e=>!e.includes("@"),"用户名不能是邮箱格式"),password:s.z.string().min(8,"密码长度必须大于等于8位")})}}]);
//# sourceMappingURL=815.js.map